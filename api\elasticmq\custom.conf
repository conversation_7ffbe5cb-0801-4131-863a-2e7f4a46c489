include classpath("application.conf")
node-address {
    protocol = http
    host = localhost
    port = 9324
    context-path = ""
}
rest-sqs {
    enabled = true
    bind-port = 9324
    bind-hostname = "0.0.0.0"
    sqs-limits = strict
}
generate-node-address = false
   queues {
       DevPaymentService {
           defaultVisibilityTimeout = 30 seconds
           delay = 30 seconds
           receiveMessageWait = 30 seconds
       },
       UatNidanaCreateEMR {
           defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       },
       UatNidanaSendDocuments {
           defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       },
       UatNidanaInvoiceTasks {
           defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       },
       UatNidanaAvailabilityUpdate {
           defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       },
       UatNidanaAvailabilityMaintenance {
           defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       },
       UatNidanaDeadLetterQueue {
           defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       },
       UatNidanaGoogleCalendarSync {
          defaultVisibilityTimeout = 30 seconds
           delay = 0 seconds
           receiveMessageWait = 20 seconds
       } 
   }

