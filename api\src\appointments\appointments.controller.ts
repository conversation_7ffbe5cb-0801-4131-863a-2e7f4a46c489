import {
	Body,
	Controller,
	DefaultValuePipe,
	Get,
	HttpException,
	HttpStatus,
	ParseIntPipe,
	ParseBoolPipe,
	Put,
	Post,
	Query,
	Param,
	UsePipes,
	ValidationPipe,
	NotFoundException,
	Patch,
	BadRequestException,
	Delete,
	UseGuards,
	Req,
	Res
} from '@nestjs/common';
import { CreateAppointmentDto } from './dto/create/create-appointment.dto';
import { UpdateAppointmentDetailsDto } from './dto/details/update-appointment-details.dto';
import { AppointmentsService } from './appointments.service';
import {
	ApiOkResponse,
	ApiQuery,
	ApiTags,
	ApiParam,
	ApiOperation,
	ApiResponse
} from '@nestjs/swagger';
import { AppointmentEntity } from './entities/appointment.entity';
import { UpdateAppointmentsDto } from './dto/create/update-appointment.dto';
import { UpdateAppointmentFeildsDto } from './dto/create/update-appointmentField.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { EnumInvoiceType } from '../invoice/enums/enum-invoice-types';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Response } from 'express';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import * as fs from 'fs';
import { ParseArrayPipe } from '../utils/pipes/parse-array.pipe';

@ApiTags('Appointments')
@Controller('appointments')
@UseGuards(JwtAuthGuard, RolesGuard)
export class AppointmentsController extends ApiDocumentationBase {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly appointmentsService: AppointmentsService
	) {
		super();
	}

	@ApiOkResponse({
		description: 'Creates a new appointment',
		type: AppointmentEntity
	})
	@Post()
	@UsePipes(ValidationPipe)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createAppointment-appointments')
	async createAppointment(
		@Body() createAppointmentDto: CreateAppointmentDto,
		@Req() req: { user: { id: string; clinicId: string; brandId: string } }
	) {
		try {
			return await this.appointmentsService.createAppointment(
				createAppointmentDto,
				req.user.brandId,
				req.user.id // Pass the user ID for Google Calendar sync
			);
		} catch (error) {
			this.logger.error('Error creating new appointment', {
				error,
				createAppointmentDto,
				userId: req.user.id
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns the list of appointments',
		isArray: true,
		type: AppointmentEntity
	})
	@ApiQuery({ name: 'page', type: 'number', required: false })
	@ApiQuery({ name: 'limit', type: 'number', required: false })
	@ApiQuery({ name: 'orderBy', type: 'string', required: false })
	@ApiQuery({ name: 'date', type: 'string', required: false })
	@ApiQuery({ name: 'search', type: 'string', required: false })
	@ApiQuery({ name: 'includeGoogleEvents', type: 'boolean', required: false })
	// @ApiQuery({ name: 'doctorId', type: 'string', required: false })
	@Get()
	@ApiOperation({ summary: 'Get all appointments of a clinic' })
	@ApiResponse({
		status: 200,
		description: 'Appointments returned successfully.',
		type: [AppointmentEntity]
	})
	@TrackMethod('getAllAppointments-appointments')
	async getAllAppointments(
		@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
		@Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
		@Query('orderBy', new DefaultValuePipe('DESC')) orderBy: string,
		@Query('date', new DefaultValuePipe('')) date: string,
		@Query('search', new DefaultValuePipe('')) search: string,
		@Query('doctors', new DefaultValuePipe([]), ParseArrayPipe)
		doctors: string[],
		@Query('status', new DefaultValuePipe([]), ParseArrayPipe)
		status: string[],
		@Query('onlyPrimary', new DefaultValuePipe(false), ParseBoolPipe)
		onlyPrimary: boolean,
		@Query(
			'includeGoogleEvents',
			new DefaultValuePipe(false),
			ParseBoolPipe
		)
		includeGoogleEvents: boolean,
		@Req() req: any
	) {
		const appointments = await this.appointmentsService.getAllAppointments(
			page,
			limit,
			orderBy,
			date,
			search,
			doctors,
			status,
			onlyPrimary,
			req.user.clinicId,
			includeGoogleEvents,
			req.user.id // pass viewer user id for Google event privacy
		);

		return appointments;
	}

	@Get('/patients/:patientId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({
		description: 'Returns the latest appointments for a patient',
		type: [AppointmentEntity]
	})
	@ApiParam({
		name: 'patientId',
		type: 'string',
		description: 'ID of the patient'
	})
	@TrackMethod('getAppointmentsForPatient-appointments')
	async getAppointmentsForPatient(@Param('patientId') patientId: string) {
		try {
			const appointments =
				await this.appointmentsService.getAppointmentsForPatient(
					patientId
				);

			return appointments.map(appointment => ({
				id: appointment.id,
				status: appointment.status,
				priority: appointment.triage,
				date: appointment.date,
				startTime: appointment.startTime,
				endTime: appointment.endTime,
				visitType: appointment.type,
				reason: appointment.reason,
				updatedAt: appointment.updatedAt,
				createdAt: appointment.createdAt,
				appointmentDoctors: appointment.appointmentDoctors.map(ad => ({
					id: ad.id,
					appointmentId: appointment.id,
					doctorId: ad.clinicUser?.id,
					primary: ad.primary,
					doctor: {
						id: ad.clinicUser?.id,
						firstName: ad.clinicUser?.user?.firstName,
						lastName: ad.clinicUser?.user?.lastName,
						email: ad.clinicUser?.user?.email
					}
				})),
				room: appointment.room,
				providers: appointment.appointmentDoctors
					.map(ad => ad.clinicUser?.user?.firstName || '')
					.filter(name => name !== '')
					.join(', '),
				weight: appointment.weight,
				appointmentDetails: appointment.appointmentDetails,
				patientId: appointment.patientId,
				triage: appointment.triage,
				treatmentFiles:
					appointment?.cart?.invoice?.find(
						inv => inv.invoiceType === EnumInvoiceType.Invoice
					)?.fileUrl ?? null
			}));
		} catch (error) {
			this.logger.error(
				'Error fetching latest appointments for patient',
				{ error, patientId }
			);
			if (error instanceof NotFoundException) {
				throw new HttpException(error.message, HttpStatus.NOT_FOUND);
			}
			throw new HttpException(
				'Error fetching latest appointments',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get(':appointmentId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get appointment details' })
	@ApiResponse({
		status: 200,
		description: 'Returns the appointment details.'
	})
	@ApiResponse({ status: 404, description: 'Appointment details not found.' })
	@ApiParam({ name: 'appointmentId', type: 'string' })
	@TrackMethod('getAppointmentDetails-appointments')
	async getAppointmentDetails(@Param('appointmentId') appointmentId: string) {
		try {
			const appointmentDetails =
				await this.appointmentsService.getAppointmentDetails(
					appointmentId
				);
			return appointmentDetails;
		} catch (error) {
			this.logger.error('Error fetching appointment details', {
				error,
				appointmentId
			});
			throw new HttpException(
				'Error fetching appointment details',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Put(':id/details')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('updateAppointmentDetails-appointments')
	async updateAppointmentDetails(
		@Param('id') id: string,
		@Body() updateAppointmentDetailsDto: UpdateAppointmentDetailsDto,
		@Req() req: { user: { id: string; clinicId: string; brandId: string } }
	) {
		try {
			// Get current appointment to log its status
			const appointment =
				await this.appointmentsService.getAppointmentDetails(id);
			return await this.appointmentsService.updateAppointmentDetails(
				id,
				updateAppointmentDetailsDto
			);
		} catch (error) {
			this.logger.error('Error updating appointment details', {
				error,
				userId: req.user?.id,
				callSiteId: updateAppointmentDetailsDto?.callSiteId
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Put(':id/status')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update appointment status' })
	@ApiResponse({
		status: 200,
		description: 'Appointment status updated successfully.'
	})
	@ApiResponse({ status: 400, description: 'Invalid status transition.' })
	@ApiResponse({ status: 404, description: 'Appointment not found.' })
	@TrackMethod('updateAppointmentStatus-appointments')
	async updateAppointmentStatus(
		@Param('id') id: string,
		@Body() updateAppointmentDto: UpdateAppointmentsDto
	) {
		try {
			const updatedAppointment =
				await this.appointmentsService.updateAppointmentStatus(
					id,
					updateAppointmentDto
				);
			return {
				message: `Appointment status updated to ${updatedAppointment.status}`,
				appointment: updatedAppointment
			};
		} catch (error) {
			if (error instanceof NotFoundException) {
				throw error;
			}
			if (error instanceof BadRequestException) {
				throw error;
			}
			throw new BadRequestException(
				'Failed to update appointment status'
			);
		}
	}

	@ApiOkResponse({
		description: 'Deletes the appointment for a valid id else gives error'
	})
	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteAppointment-appointments')
	async deleteAppointment(@Param('id') id: string) {
		try {
			return await this.appointmentsService.deleteAppointment(id);
		} catch (error) {
			this.logger.error('Error deleting appointment by ID', { error });
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Put(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update appointment fields' })
	@ApiResponse({
		status: 200,
		description: 'Appointment Feilds updated successfully.'
	})
	@ApiResponse({ status: 404, description: 'Appointment not found.' })
	@ApiResponse({ status: 400, description: 'Something goes wrong.' })
	@TrackMethod('updateAppointmentFields-appointments')
	async updateAppointmentFields(
		@Param('id') id: string,
		@Body() updateAppointmentFeildsDto: UpdateAppointmentFeildsDto
	) {
		try {
			const updatedAppointment =
				await this.appointmentsService.updateAppointment(
					id,
					updateAppointmentFeildsDto
				);
			return {
				message: `Appointment feilds updated to ${updatedAppointment.status}`,
				appointment: updatedAppointment
			};
		} catch (error) {
			if (error instanceof NotFoundException) {
				throw error;
			}
			if (error instanceof BadRequestException) {
				throw error;
			}
			throw new BadRequestException(
				'Failed to update appointment status'
			);
		}
	}

	@Get('/patients/:patientId/check-ongoing-appointment')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({
		description: 'Returns an on going appointment if present',
		type: [AppointmentEntity]
	})
	@ApiParam({
		name: 'patientId',
		type: 'string',
		description: 'ID of the patient'
	})
	@TrackMethod('checkOngoingAppointment-appointments')
	async checkOngoingAppointment(@Param('patientId') patientId: string) {
		try {
			const result =
				await this.appointmentsService.checkPatientOnGoingAppointment(
					patientId
				);

			return result;
		} catch (error) {
			this.logger.error('Error checking ongoing appointment', {
				error,
				patientId
			});
			if (error instanceof NotFoundException) {
				throw new HttpException(error.message, HttpStatus.NOT_FOUND);
			}
			throw new HttpException(
				'Error checking on going appointment appointments',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('/clinic/today-appointment')
	@ApiOperation({ summary: "Download today's appointment PDF" })
	@ApiResponse({
		status: 200,
		description: 'PDF file generated successfully.',
		content: { 'application/pdf': {} }
	})
	@ApiResponse({ status: 404, description: 'Appointment details not found.' })
	@ApiQuery({ name: 'date', type: 'string', required: true })
	@ApiQuery({ name: 'clinicId', type: 'string', required: true })
	@TrackMethod('downloadTodaysAppointment-appointments')
	async downloadTodaysAppointment(
		@Res() res: Response,
		@Query('date') date: string,
		@Query('clinicId') clinicId: string
	) {
		try {
			const pdfBuffer =
				await this.appointmentsService.downloadTodaysAppointment(
					clinicId,
					date
				);

			if (!pdfBuffer) {
				throw new NotFoundException('No appointments found for today.');
			}
			fs.readFile(pdfBuffer, function (err, data) {
				res.set({
					'Content-Type': 'application/pdf',
					'Content-Disposition': `attachment; filename="todays_appointments_${new Date().toISOString().slice(0, 10)}.pdf"`
				});
				res.send(data);
			});
			fs.unlink(pdfBuffer, unlinkErr => {
				if (unlinkErr) {
					console.error('Error deleting the PDF file:', unlinkErr);
				} else {
					console.log('Temporary PDF file deleted successfully.');
				}
			});
		} catch (error) {
			console.error('Error generating PDF:', error);
			throw new NotFoundException('Error generating appointment PDF.');
		}
	}
}
