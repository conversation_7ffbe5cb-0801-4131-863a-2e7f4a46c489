import {
	<PERSON><PERSON><PERSON>,
	<PERSON>reateDate<PERSON><PERSON><PERSON>n,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	ManyToOne,
	OneToMany,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { EnumAppointmentTriage } from '../enums/enum-appointment-triage';
import { EnumAppointmentType } from '../enums/enum-appointment-type';
import { EnumAppointmentStatus } from '../enums/enum-appointment-status';
import { Patient } from '../../patients/entities/patient.entity';
import { AppointmentDoctorsEntity } from './appointment-doctor.entity';
import { ClinicRoomEntity } from '../../clinics/entities/clinic-room.entity';
import { AppointmentDetailsEntity } from './appointment-details.entity';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { PatientVaccination } from '../../patient-vaccinations/entities/patient-vaccinations.entity';
import { CartEntity } from '../../carts/entites/cart.entity';
import { NumericTransformer } from '../../utils/common/numeric-transformer';
import { Emr } from '../../emr/entities/emr.entity';
import { DiagnosticNote } from '../../diagnostic-notes-templates/entities/diagnostic-note.entity';

@Entity('appointments')
export class AppointmentEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ type: 'uuid', name: 'patient_id' })
	patientId!: string;

	@Column({ type: 'uuid', name: 'room_id', nullable: true })
	roomId!: string;

	@Column({ name: 'reason' })
	reason!: string;

	@Column({
		type: 'enum',
		enum: EnumAppointmentType,
		nullable: true,
		name: 'type'
	})
	type!: EnumAppointmentType;

	@Column({
		type: 'enum',
		enum: EnumAppointmentTriage,
		nullable: true,
		name: 'triage'
	})
	triage!: EnumAppointmentTriage;

	@Column({ name: 'date' })
	date!: Date;

	@Column({ type: 'timestamptz', name: 'start_time' })
	startTime!: Date;

	@Column({ type: 'timestamptz', nullable: true, name: 'end_time' })
	endTime!: Date;

	@Column({ type: 'timestamptz', nullable: true, name: 'deleted_at' })
	deletedAt?: Date;

	@Column({
		nullable: true,
		name: 'weight',
		type: 'decimal',
		precision: 7,
		scale: 2,
		transformer: new NumericTransformer()
	})
	weight?: number;

	@Column({
		type: 'jsonb',
		nullable: true,
		name: 'notes'
	})
	notes?: object;

	@Column({
		type: 'jsonb',
		nullable: true,
		name: 'pre_visit_questions'
	})
	preVisitQuestions?: object;

	@Column({
		type: 'jsonb',
		nullable: true,
		name: 'reminder_tracking',
		default: '{}'
	})
	reminderTracking?: {
		lastProcessedAt?: string;
		emailStatus?: 'sent' | 'failed';
		emailSentAt?: string;
		whatsappStatus?: 'sent' | 'failed';
		whatsappSentAt?: string;
		retryCount?: number;
		error?: string;
		ownerNotifications?: {
			[ownerId: string]: {
				emailStatus?: 'sent' | 'failed';
				emailSentAt?: string;
				emailError?: string;
				whatsappStatus?: 'sent' | 'failed';
				whatsappSentAt?: string;
				whatsappError?: string;
			};
		};
	};

	@Column({ nullable: true, name: 'is_blocked' })
	isBlocked?: boolean;

	@Column({
		type: 'enum',
		enum: EnumAppointmentStatus,
		nullable: true,
		name: 'status'
	})
	status?: EnumAppointmentStatus;

	@CreateDateColumn({ name: 'created_at' })
	createdAt?: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt?: Date;

	@Column({ type: 'uuid', nullable: true, name: 'created_by' })
	createdBy?: string;

	@Column({ type: 'uuid', nullable: true, name: 'updated_by' })
	updatedBy?: string;

	@Column({ nullable: true, name: 'initial_notes' })
	initialNotes?: string;

	@Column({ nullable: true, type: 'timestamptz', name: 'checkin_time' })
	checkinTime!: Date;

	@Column({
		nullable: true,
		type: 'timestamptz',
		name: 'receiving_care_time'
	})
	receivingCareTime!: Date;

	@Column({ nullable: true, type: 'timestamptz', name: 'checkout_time' })
	checkoutTime!: Date;

	@ManyToOne(() => Patient, patient => patient.appointments)
	@JoinColumn({ name: 'patient_id' })
	patient!: Patient;

	@ManyToOne(() => ClinicEntity, clinc => clinc)
	@JoinColumn({ name: 'clinic_id' })
	clinic!: ClinicEntity;

	@OneToMany(
		() => AppointmentDoctorsEntity,
		appointmentDoctor => appointmentDoctor.appointment
	)
	appointmentDoctors!: AppointmentDoctorsEntity[];

	@OneToOne(() => ClinicRoomEntity, room => room)
	@JoinColumn({ name: 'room_id' })
	room!: ClinicRoomEntity;

	@OneToOne(
		() => AppointmentDetailsEntity,
		appointmentDetailsEntity => appointmentDetailsEntity.appointment
	)
	appointmentDetails!: AppointmentDetailsEntity;

	@OneToMany(() => LabReport, labReport => labReport.appointment)
	labReports!: LabReport[];

	@OneToMany(
		() => PatientVaccination,
		patientVaccination => patientVaccination.appointment
	)
	patientVaccinations?: PatientVaccination[];

	@OneToOne(() => CartEntity, cartEntity => cartEntity.appointment)
	cart!: CartEntity;

	@OneToOne(() => Emr, emr => emr.appointment)
	emr?: Emr;

	@OneToMany(() => DiagnosticNote, note => note.appointment)
	diagnosticNotes?: DiagnosticNote[];

	@Column({ type: 'varchar', nullable: false, default: 'Clinic' })
	mode!: string;

	// Google Calendar Integration Field
	@Column({ nullable: true, name: 'google_event_id' })
	googleEventId?: string;
}
