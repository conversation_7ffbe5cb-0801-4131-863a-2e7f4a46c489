import {
	Injectable,
	UnauthorizedException,
	NotFoundException,
	InternalServerErrorException
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { RoleService } from '../roles/role.service';
import { VerifyOtpDto, PinLoginDto } from './dto/auth.dto';
import { User } from '../users/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { Role } from '../roles/role.enum';
import { UserOtpsService } from '../user-otps/user-otps.service';
import { ValidateOtpDto } from '../user-otps/dto/validate-user-otp.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { DEV_SES_EMAIL } from '../utils/constants';
import { isProduction } from '../utils/common/get-login-url';

@Injectable()
export class AuthService {
	constructor(
		private usersService: UsersService,
		private jwtService: JwtService,
		private roleService: RoleService,
		private readonly userOtpsService: UserOtpsService,
		private readonly userService: UsersService,
		@InjectRepository(User)
		private usersRepository: Repository<User>,
		private readonly mailService: SESMailService
	) {}

	async loginByEmail(
		validateEmailDto: ValidateOtpDto
	): Promise<{ token: string; roleName: string }> {
		const { email, otp } = validateEmailDto;
		const { token, roleName } = await this.userOtpsService.validateOtp({
			email,
			otp
		});
		return { token, roleName };
	}

	async verifyOtp(
		verifyOtpDto: VerifyOtpDto
	): Promise<{ access_token: string }> {
		const user = await this.usersService.findOneByEmail(verifyOtpDto.email);
		if (!user) {
			throw new UnauthorizedException('User not found');
		}
		const userRole = await this.roleService.findById(user.roleId);
		if (
			userRole.name !== Role.SUPER_ADMIN &&
			userRole.name !== Role.ADMIN
		) {
			throw new UnauthorizedException(
				'OTP verification is only for admin users'
			);
		}
		// TODO: Verify OTP
		const payload = {
			email: user.email,
			sub: user.id,
			roleId: user.roleId
		};
		return {
			access_token: this.jwtService.sign(payload)
		};
	}

	async loginPin(pinLoginDto: PinLoginDto): Promise<any> {
		try {
			const users = await this.usersService.findAll(pinLoginDto.brandId);
			let matchedUser: User | undefined;
			for (const user of users) {
				if (user.pin) {
					const isPinValid = await bcrypt.compare(
						pinLoginDto.pin,
						user.pin
					);
					if (isPinValid) {
						matchedUser = user;
						break;
					}
				}
			}

			if (!matchedUser) {
				throw new UnauthorizedException('The PIN is invalid');
			}

			const role = await this.roleService.findById(matchedUser.roleId);
			const isFirstLogin = !matchedUser.registered;

			// Generate JWT token

			const clinicUsers = await this.usersService.findByUserId(
				matchedUser.id
			);
			const isMultiClinic = clinicUsers.length > 1; // make it > 1
			const isFullyOnboarded = clinicUsers.every(cu => cu.isOnboarded);

			// Find the clinic that matches the brandId from the login request
			const selectedClinic =
				clinicUsers.find(cu => cu.brandId === pinLoginDto.brandId) ||
				null;

			// If no matching clinic was found, throw an exception
			if (!selectedClinic) {
				throw new UnauthorizedException(
					'User not associated with the provided brand'
				);
			}

			const payload = {
				sub: matchedUser?.id,
				email: matchedUser?.email,
				role: role?.name,
				clinicId: selectedClinic?.clinic?.id,
				brandId: pinLoginDto.brandId
			};
			const token = this.jwtService.sign(payload);
			return {
				token,
				userId: selectedClinic?.id, // clinic_user_id
				isFirstLogin,
				isMultiClinic,
				isFullyOnboarded,
				clinicName: selectedClinic?.clinic?.name,
				clinicId: selectedClinic?.clinic?.id,
				role: role.name,
				brandId: selectedClinic?.brandId,
				username: matchedUser.firstName,
				globalUserId: matchedUser.id,
				brandName: selectedClinic.clinic.brand.name
			};
		} catch (error) {
			if (error instanceof UnauthorizedException) {
				throw error;
			}
			throw new InternalServerErrorException('Failed to login');
		}
	}

	async resetPin(email: string): Promise<void> {
		const user = await this.usersRepository.findOne({ where: { email } });
		if (!user) {
			throw new NotFoundException('User not found');
		}

		const newPin = await this.userService.generateUniquePin();
		user.pin = await bcrypt.hash(newPin, 10);

		try {
			await this.usersRepository.save(user);
			// await this.sendPinResetEmail(email, newPin);
			const subject = 'Reset login pin';
			const body = `Dear User,
            Your PIN has been reset as requested. 
            Your new PIN is: ${newPin}
			`;
			console.log(`Mock email sent to ${email} with new PIN: ${newPin}`);
			if (isProduction() && email) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: email
				});
			} else if (!isProduction()) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: DEV_SES_EMAIL //email
				});
			}
		} catch (error) {
			throw new InternalServerErrorException('Failed to reset PIN');
		}
	}

	async verifyPin(
		pin: string,
		userId: string
	): Promise<{ success: boolean; message?: string }> {
		try {
			// Find the user by ID
			const user = await this.usersRepository.findOne({
				where: { id: userId }
			});

			if (!user) {
				throw new NotFoundException('User not found');
			}

			if (!user.pin) {
				throw new UnauthorizedException('No PIN set for this user');
			}

			// Verify the PIN
			const isPinValid = await bcrypt.compare(pin, user.pin);

			if (!isPinValid) {
				throw new UnauthorizedException('Invalid PIN');
			}

			return {
				success: true,
				message: 'PIN verified successfully'
			};
		} catch (error) {
			if (
				error instanceof NotFoundException ||
				error instanceof UnauthorizedException
			) {
				throw error;
			}
			throw new InternalServerErrorException('Failed to verify PIN');
		}
	}
}
