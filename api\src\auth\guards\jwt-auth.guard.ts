import { Injectable, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { IS_PUBLIC_KEY } from './public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
	private readonly logger = new Logger(JwtAuthGuard.name);

	constructor(private reflector: Reflector) {
		super();
	}

	canActivate(context: ExecutionContext) {
		const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
			context.getHandler(),
			context.getClass(),
		]);
		
		const request = context.switchToHttp().getRequest();
		
		if (isPublic) {
			this.logger.debug(`🔓 [AUTH] Public endpoint accessed: ${request.method} ${request.url}`);
			return true;
		}
		
		this.logger.debug(`🔐 [AUTH] Protected endpoint accessed: ${request.method} ${request.url}`);
		return super.canActivate(context);
	}
}

// Helper function for testing
export const createJwtAuthGuard = () => {
	return new JwtAuthGuard(new Reflector());
};
