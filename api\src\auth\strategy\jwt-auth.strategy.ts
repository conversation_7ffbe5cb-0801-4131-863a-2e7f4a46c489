import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor(private configService: ConfigService) {
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			ignoreExpiration: false,
			secretOrKey: configService.get<string>('JWT_SECRET')
		});
	}

	async validate(payload: any) {
		return {
			id: payload.sub,
			email: payload.email,
			roleId: payload.role,
			owner: payload.owner,
			userId: payload.sub,
			role: payload.role,
			clinicId: payload.clinicId,
			brandId: payload.brandId
		};
	}
}
