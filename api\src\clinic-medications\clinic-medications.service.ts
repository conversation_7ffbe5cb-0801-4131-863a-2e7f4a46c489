import {
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { ClinicMedicationEntity } from './entities/clinic-medication.entity';
import { ILike, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateClinicMedicationDto } from './dto/create-clinic-medication.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { UpdateClinicMedicationDto } from './dto/update-clinic-medication.dto';

export interface BulkOperationDto {
	clinicId: string;
	brandId: string;
	uniqueId: string;
	name?: string;
	productName?: string;
	chargeablePrice?: number;
	tax?: number;
	currentStock?: number;
	minimumQuantity?: number;
	isRestricted?: string;
	isAddedByUser?: boolean;
}

@Injectable()
export class ClinicMedicationsService {
	constructor(
		@InjectRepository(ClinicMedicationEntity)
		private readonly clinicMedicationRepository: Repository<ClinicMedicationEntity>,
		private readonly logger: WinstonLogger
	) {}

	private async generateUniqueId(
		prefix: string,
		clinicId: string
	): Promise<string> {
		const count = await this.clinicMedicationRepository.count({
			where: { clinicId }
		});
		const nextNumber = count + 1;
		return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
	}

	async getMedications(
		clinicId: string,
		searchKeyword?: string,
		all?: boolean
	): Promise<ClinicMedicationEntity[]> {
		if (all) {
			if (searchKeyword) {
				return this.clinicMedicationRepository.find({
					where: {
						name: ILike(`%${searchKeyword}%`),
						clinicId
					}
				});
			}
			return this.clinicMedicationRepository.find({
				where: { clinicId }
			});
		}
		if (searchKeyword) {
			return this.clinicMedicationRepository.find({
				where: {
					name: ILike(`%${searchKeyword}%`),
					isAddedByUser: false,
					clinicId
				}
			});
		}
		return this.clinicMedicationRepository.find({
			where: { isAddedByUser: false, clinicId }
		});
	}

	async createNewMedication(
		createClinicMedicationDto: CreateClinicMedicationDto
	): Promise<ClinicMedicationEntity> {
		try {
			const uniqueId = await this.generateUniqueId(
				'M-',
				createClinicMedicationDto.clinicId
			);
			const medication = this.clinicMedicationRepository.create({
				...createClinicMedicationDto,
				uniqueId
			});

			const createdMedication =
				await this.clinicMedicationRepository.save(medication);

			if (!createdMedication) {
				throw new InternalServerErrorException(
					'Failed to create medication'
				);
			}

			return createdMedication;
		} catch (error) {
			this.logger.error('Error creating medication', { error });
			throw new InternalServerErrorException(
				'Failed to create medication'
			);
		}
	}

	async findOne(id: string): Promise<ClinicMedicationEntity> {
		const medication = await this.clinicMedicationRepository.findOne({
			where: { id }
		});
		if (!medication) {
			throw new NotFoundException(`Medication with ID ${id} not found`);
		}
		return medication;
	}

	async update(
		id: string,
		updateClinicMedicationDto: UpdateClinicMedicationDto
	): Promise<ClinicMedicationEntity> {
		try {
			const medication = await this.findOne(id);

			Object.assign(medication, updateClinicMedicationDto);

			return await this.clinicMedicationRepository.save(medication);
		} catch (error) {
			this.logger.error('Error updating medication', { error });
			throw error;
		}
	}

	async remove(id: string): Promise<void> {
		try {
			const result = await this.clinicMedicationRepository.delete(id);

			if (result.affected === 0) {
				throw new NotFoundException(
					`Medication with ID ${id} not found`
				);
			}
		} catch (error) {
			this.logger.error('Error deleting medication', { error });
			throw error;
		}
	}

	async bulkInsert(items: BulkOperationDto[]): Promise<string> {
		try {
			for (const item of items) {
				// Check if item exists for the clinic
				const existingMedication =
					await this.clinicMedicationRepository.findOne({
						where: {
							clinicId: item.clinicId,
							name: item.name || item.productName
						}
					});

				if (existingMedication) {
					// Update existing item
					Object.assign(existingMedication, {
						...item,
						name: item.name || item.productName,
						isAddedByUser: item.isAddedByUser ?? true,
						isRestricted: item.isRestricted || 'NO'
					});
					await this.clinicMedicationRepository.save(
						existingMedication
					);
				} else {
					// Create new item with unique ID
					const uniqueId = await this.generateUniqueId(
						'M-',
						item.clinicId
					);
					await this.clinicMedicationRepository.save({
						...item,
						name: item.name || item.productName,
						isAddedByUser: item.isAddedByUser ?? true,
						isRestricted: item.isRestricted || 'NO',
						uniqueId
					});
				}
			}

			return `Bulk insert of ${items.length} medications completed successfully`;
		} catch (error) {
			this.logger.error('Error in bulk insert', { error });
			throw new InternalServerErrorException(
				'Failed to perform bulk insert'
			);
		}
	}

	async findOneEntry(criteria: { name: string; clinicId: string }) {
		return this.clinicMedicationRepository.findOne({ where: criteria });
	}

	async deleteItem(itemId: string) {
		return this.clinicMedicationRepository.delete(itemId);
	}
}
