import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateClinicMedicationDto {
	@ApiProperty({
		description: 'The clinic id to which the lab report belongs.',
		example: '123e4567-e89b-12d3-a456-426614174000'
	})
	@IsNotEmpty({ message: 'The clinic id should be provided.' })
	clinicId!: string;

	@ApiProperty({
		description: 'The name of the lab report.',
		example: 'Blood Test'
	})
	@IsNotEmpty({ message: 'The lab report should have a name.' })
	name!: string;

	@ApiProperty({
		description:
			'If the medication is getting added by the user. The default valus is true',
		example: true,
		required: false
	})
	isAddedByUser: boolean = true;
}
