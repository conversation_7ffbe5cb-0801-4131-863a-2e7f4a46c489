import { uuid } from 'aws-sdk/clients/customerprofiles';
import {
	Column,
	CreateDateColumn,
	Entity,
	JoinColumn,
	ManyToOne,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { User } from '../../users/entities/user.entity';
import { Optional } from '@nestjs/common';
import { LongTermMedicationEntity } from '../../long-term-medications/entities/long-term-medication.entity';
import { CartItemEntity } from '../../cart-items/entities/cart-item.entity';
import { NumericTransformer } from '../../utils/common/numeric-transformer';

@Entity({ name: 'clinic_medications' })
export class ClinicMedicationEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@ManyToOne(() => ClinicEntity)
	@JoinColumn({ name: 'clinic_id' })
	@Column('uuid', { nullable: false, name: 'clinic_id' })
	clinicId!: uuid;

	@Column({ nullable: false })
	name!: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@Column({ nullable: false, name: 'is_restricted' })
	isRestricted!: string;

	@Column({ type: 'boolean', name: 'is_added_by_user' })
	@Optional()
	isAddedByUser!: boolean;

	@OneToOne(() => LongTermMedicationEntity, medication => medication)
	longTermMedication!: LongTermMedicationEntity;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ name: 'unique_id' })
	uniqueId!: string;

	@Column({ name: 'chargeable_price' })
	chargeablePrice!: number;

	@Column({ name: 'tax' })
	tax!: number;

	@Column({ name: 'current_stock' })
	currentStock!: number;

	@Column({ name: 'minimum_quantity' })
	minimumQuantity!: number;

	@OneToOne(() => CartItemEntity, cart => cart.product)
	cart?: CartItemEntity;
}
