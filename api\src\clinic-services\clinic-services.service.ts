import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { ClinicServiceEntity } from './entities/clinic-service.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateServiceDto } from './dto/create-services.dto';
import { UpdateServiceDto } from './dto/update-services.dto';

@Injectable()
export class ClinicServicesService {
	constructor(
		@InjectRepository(ClinicServiceEntity)
		private readonly servicesRepository: Repository<ClinicServiceEntity>,
		private readonly logger: <PERSON><PERSON>ogger
	) {}

	private async generateUniqueId(
		prefix: string,
		clinicId: string
	): Promise<string> {
		const count = await this.servicesRepository.count({
			where: { clinicId }
		});
		const nextNumber = count + 1;
		return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
	}

	async getServices(clinicId: string, searchKeyword?: string) {
		if (searchKeyword) {
			if (clinicId) {
				return this.servicesRepository.find({
					where: {
						serviceName: ILike(`%${searchKeyword}%`),
						clinicId
					}
				});
			}

			return this.servicesRepository.find({
				where: { serviceName: ILike(`%${searchKeyword}%`) }
			});
		}

		return this.servicesRepository.find({
			where: { clinicId }
		});
	}

	async bulkInsert(clinicServices: CreateServiceDto[]): Promise<string> {
		try {
			for (const service of clinicServices) {
				// Check if item exists for the clinic
				const existingService = await this.servicesRepository.findOne({
					where: {
						clinicId: service.clinicId,
						serviceName: service.serviceName
					}
				});

				if (existingService) {
					// Update existing item
					Object.assign(existingService, service);
					await this.servicesRepository.save(existingService);
				} else {
					// Create new item with unique ID
					const uniqueId = await this.generateUniqueId(
						'S_',
						service.clinicId
					);
					await this.servicesRepository.save({
						...service,
						uniqueId
					});
				}
			}

			this.logger.log(
				`Bulk insert completed. Processed ${clinicServices.length} services.`
			);
			return `Bulk insert of ${clinicServices.length} services completed successfully`;
		} catch (error) {
			this.logger.error('Error during bulk insert of services', {
				error
			});
			throw new Error('Failed to insert services');
		}
	}

	async create(
		createServiceDto: CreateServiceDto
	): Promise<ClinicServiceEntity> {
		try {
			const uniqueId = await this.generateUniqueId(
				'S_',
				createServiceDto.clinicId
			);
			const service = this.servicesRepository.create({
				...createServiceDto,
				uniqueId
			});
			return await this.servicesRepository.save(service);
		} catch (error) {
			this.logger.error('Error creating service', { error });
			throw error;
		}
	}

	async update(
		id: string,
		updateServiceDto: UpdateServiceDto
	): Promise<ClinicServiceEntity> {
		const service = await this.servicesRepository.findOneBy({ id });
		if (!service) {
			const error = new NotFoundException(
				`Service with ID ${id} not found`
			);
			this.logger.error('Error updating service', { error });
			throw error;
		}

		try {
			Object.assign(service, updateServiceDto);
			return await this.servicesRepository.save(service);
		} catch (error) {
			this.logger.error('Error updating service', { error });
			throw error;
		}
	}

	async findOne(id: string): Promise<ClinicServiceEntity> {
		const service = await this.servicesRepository.findOneBy({ id });
		if (!service) {
			throw new NotFoundException(`Service with ID ${id} not found`);
		}
		return service;
	}

	async remove(id: string): Promise<void> {
		const result = await this.servicesRepository.delete(id);
		if (result.affected === 0) {
			throw new NotFoundException(`Service with ID ${id} not found`);
		}
	}

	async findOneEntry(criteria: { serviceName: string; clinicId: string }) {
		return this.servicesRepository.findOne({ where: criteria });
	}

	async deleteItem(itemId: string) {
		return this.servicesRepository.delete(itemId);
	}
}
