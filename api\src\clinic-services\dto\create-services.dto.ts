import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>U<PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateServiceDto {
	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	clinicId!: string;

	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	brandId!: string;

	// @ApiProperty()
	// @IsString()
	// @IsNotEmpty()
	// uniqueId!: string;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	serviceName!: string;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	chargeablePrice!: number;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	tax!: number;
}
