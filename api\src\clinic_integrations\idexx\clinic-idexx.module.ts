import { Module, forwardRef } from '@nestjs/common';
import { ClinicIdexxService } from './clinic-idexx.service';
import { ClinicIdexxController } from './clinic-idexx.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { HttpModule } from '@nestjs/axios';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { PatientsService } from '../../patients/patients.service';
import { Patient } from '../../patients/entities/patient.entity';
import { PatientOwner } from '../../patients/entities/patient-owner.entity';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
import { GlobalOwner } from '../../owners/entities/global-owner.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { OwnersService } from '../../owners/owners.service';
import { ClinicLabReportModule } from '../../clinic-lab-report/clinic-lab-report.module';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { ClinicIdexxUtilsService } from '../../utils/idexx/clinic-idexx-utils.service';
import { AppointmentsService } from '../../appointments/appointments.service';
import { AppointmentDoctorsEntity } from '../../appointments/entities/appointment-doctor.entity';
import { AppointmentDetailsEntity } from '../../appointments/entities/appointment-details.entity';
import { SESMailService } from '../../utils/aws/ses/send-mail-service';
import { TasksService } from '../../tasks/tasks.service';
import { Task } from '../../tasks/entities/tasks.entity';
import { WhatsappService } from '../../utils/whatsapp-integration/whatsapp.service';
import { EmrModule } from '../../emr/emr.module';
import { PatientRemindersService } from '../../patient-reminders/patient-reminder.service';
import { PatientReminder } from '../../patient-reminders/entities/patient-reminder.entity';
import { PatientReminderHistory } from '../../patient-reminders/entities/patient-reminder-history.entity';
import { SqsService } from '../../utils/aws/sqs/sqs.service';
import { GlobalReminderModule } from '../../patient-global-reminders/global-reminders.module';
import { RoleModule } from '../../roles/role.module';
import { Brand } from '../../brands/entities/brand.entity';
import { PetTransferHistory } from '../../owners/entities/pet-transfer-history.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
import { SESModule } from '../../utils/aws/ses/ses.module';
import { WhatsappModule } from '../../utils/whatsapp-integration/whatsapp.module';
import { AvailabilityModule } from '../../availability/availability.module';
import { LongTermMedicationsModule } from '../../long-term-medications/long-term-medications.module';
import { SocketModule } from '../../socket/socket.module';
import { GoogleCalendarModule } from '../../google-calendar/google-calendar.module';
import { User } from '../../users/entities/user.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			CreateClinicIdexxEntity,
			ClinicLabReport,
			LabReport,
			Patient,
			PatientOwner,
			OwnerBrand,
			GlobalOwner,
			ClinicEntity,
			AppointmentEntity,
			AppointmentDoctorsEntity,
			AppointmentDetailsEntity,
			Task,
			PatientReminder,
			PatientReminderHistory,
			Brand,
			PetTransferHistory,
			PaymentDetailsEntity,
			User
		]),
		HttpModule,
		ClinicLabReportModule,
		EmrModule,
		GlobalReminderModule,
		RoleModule,
		SESModule,
		WhatsappModule,
		LongTermMedicationsModule,
		GoogleCalendarModule,
		forwardRef(() => AvailabilityModule),
		forwardRef(() => SocketModule)
	],
	controllers: [ClinicIdexxController],
	providers: [
		ClinicIdexxService,
		PatientsService,
		OwnersService,
		S3Service,
		ClinicIdexxUtilsService,
		AppointmentsService,
		SESMailService,
		TasksService,
		WhatsappService,
		PatientRemindersService,
		SqsService
	]
})
export class ClinicIdexxModule {}
