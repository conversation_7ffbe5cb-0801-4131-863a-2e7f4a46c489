/* eslint-disable @typescript-eslint/no-unused-vars */
import {
	Body,
	Controller,
	DefaultValuePipe,
	Get,
	ParseIntPipe,
	Post,
	Query,
	UsePipes,
	ValidationPipe,
	Param,
	HttpException,
	HttpStatus,
	Put,
	UseGuards,
	Req,
	UploadedFile,
	UseInterceptors,
	Res,
	Delete
} from '@nestjs/common';
import { ClinicService } from './clinic.service';
import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiOkResponse,
	ApiOperation,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { ClinicEntity } from './entities/clinic.entity';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { Request, Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { WorkingHoursDto } from './dto/working-hours.dto';
import {
	CreateClinicRoomDto,
	UpdateClinicRoomDto
} from './dto/create-clinic-room.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { UpdateClientBookingSettingsDto } from './dto/update-client-booking-settings.dto';
import { ClientBookingSettingsResponseDto } from './dto/client-booking-settings-response.dto';

@ApiTags('Clinic')
@Controller('clinics')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicController extends ApiDocumentationBase {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly clinicService: ClinicService
	) {
		super();
	}

	@ApiOkResponse({
		description: 'Creates a new clinic',
		type: ClinicEntity
	})
	@Roles(Role.SUPER_ADMIN)
	@Post()
	@UsePipes(new ValidationPipe())
	@TrackMethod('createClinic-clinics')
	async createClinic(
		@Body() createClinicDto: CreateClinicDto,
		@Req() req: Request
	) {
		try {
			this.logger.log('Creating new clinic', { dto: createClinicDto });
			const user = req.user as { userId: string };
			return await this.clinicService.createClinic(
				createClinicDto,
				user.userId
			);
		} catch (error) {
			this.logger.error('Error creating new clinic', {
				error,
				createClinicDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns the clinic with the given ID',
		type: ClinicEntity
	})
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@Get(':id')
	@TrackMethod('getClinicById-clinics')
	async getClinicById(@Param('id') id: string) {
		try {
			this.logger.log('Fetching clinic by ID', { id });

			return await this.clinicService.getClinicById(id);
		} catch (error) {
			this.logger.error('Error fetching clinic by ID', { error });

			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@ApiOkResponse({
		description: 'Updates the Basic clinic Deatils with the given ID',
		type: ClinicEntity
	})
	@Roles(Role.SUPER_ADMIN)
	@Put('/basic/:id')
	@TrackMethod('updateBasicClinic-clinics')
	async updateBasicClinic(
		@Param('id') id: string,
		@Body() updateBasicClinicDto: UpdateBasicClinicDto,
		@Req() req: Request
	) {
		const user = req.user as { userId: string; email: string };
		return this.clinicService.updateBasicClinicInfo(
			id,
			updateBasicClinicDto,
			user.userId
		);
	}

	@ApiOkResponse({
		description: 'Deactivate the Clinic using the Id'
	})
	@Roles(Role.SUPER_ADMIN)
	@Put(':id/deactivate')
	@TrackMethod('deactivateClinic-clinics')
	async deactivateClinic(@Param('id') id: string) {
		try {
			this.logger.log('Deactivating clinic', { id });

			return await this.clinicService.deactivateClinic(id);
		} catch (error) {
			this.logger.error('Error deactivating clinic', { error });

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Updates the clinic with the given ID',
		type: ClinicEntity
	})
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@Put(':id')
	@TrackMethod('updateClinic-clinics')
	async updateClinic(
		@Param('id') id: string,
		@Body() updateClinicDto: UpdateClinicDto,
		@Req() req: Request
	) {
		const user = req.user as { userId: string; email: string };
		return this.clinicService.updateClinic(
			id,
			updateClinicDto,
			user.userId
		);
	}

	@ApiOkResponse({
		description: 'Returns the list of clinics',
		isArray: true,
		type: ClinicEntity
	})
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@Get()
	@TrackMethod('getAllClinics-clinics')
	async getAllClinics(
		@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
		@Query('limit', new DefaultValuePipe(10), ParseIntPipe)
		limit: number = 10,
		@Query('orderBy', new DefaultValuePipe('DESC')) orderBy: string = 'DESC'
	) {
		try {
			this.logger.log('Fetching all clinics', {
				page,
				limit,
				orderBy
			});

			return await this.clinicService.getAllClinics(page, limit, orderBy);
		} catch (error) {
			this.logger.error('Error fetching clinics', { error });

			throw new HttpException(
				'Error fetching all the clinics',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns the list of clinic rooms',
		isArray: true,
		type: ClinicRoomEntity
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@Get(':id/rooms')
	@TrackMethod('getClinicRooms-clinics')
	async getClinicRooms(@Param('id') id: string) {
		try {
			this.logger.log('Fetching all clinic rooms', { id });

			return await this.clinicService.getClinicRooms(id);
		} catch (error) {
			this.logger.error('Error fetching clinic rooms', { error });

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Post('rooms')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({
		description: 'The clinic room has been successfully created.',
		type: ClinicRoomEntity
	})
	@TrackMethod('createClinicRoom-clinics')
	async createClinicRoom(
		@Body() createClinicRoomDto: CreateClinicRoomDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Creating new clinic room', {
				dto: createClinicRoomDto
			});
			const room = await this.clinicService.createClinicRoom(
				createClinicRoomDto,
				req.user.brandId
			);
			this.logger.log('Clinic room created successfully', {
				roomId: room.id
			});
			return room;
		} catch (error) {
			this.logger.error('Error creating clinic room', { error });
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Put('rooms/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({
		description: 'The clinic room has been successfully updated.',
		type: ClinicRoomEntity
	})
	@TrackMethod('updateClinicRoom-clinics')
	async updateClinicRoom(
		@Param('id') id: string,
		@Body() updateClinicRoomDto: UpdateClinicRoomDto
	) {
		try {
			this.logger.log('Updating clinic room', {
				id,
				dto: updateClinicRoomDto
			});
			const room = await this.clinicService.updateClinicRoom(
				id,
				updateClinicRoomDto
			);
			this.logger.log('Clinic room updated successfully', {
				roomId: room.id
			});
			return room;
		} catch (error) {
			this.logger.error('Error updating clinic room', { error });
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Delete('rooms/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiResponse({ status: 200, description: 'Room deleted successfully.' })
	@ApiResponse({ status: 404, description: 'Room not found.' })
	@TrackMethod('deleteRoom-clinics')
	async deleteRoom(@Param('id') id: string) {
		try {
			await this.clinicService.deleteRoom(id);
			return { message: 'Room deleted successfully' };
		} catch (error) {
			if (error instanceof HttpException) {
				throw error;
			}
			throw new HttpException(
				'Failed to delete room',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@Post('bulk-upload')
	@UseInterceptors(FileInterceptor('file'))
	@TrackMethod('uploadFile-clinics')
	async uploadFile(
		@UploadedFile() file: Express.Multer.File,
		@Query('clinicId') clinicId: string,
		@Query('brandId') brandId: string,
		@Req() request: Request
	) {
		if (!file) {
			throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
		}

		if (!clinicId || !brandId) {
			throw new HttpException(
				'Clinic ID and Brand ID are required',
				HttpStatus.BAD_REQUEST
			);
		}

		// const userId = (request.user as any)?.id;
		// if (!userId) {
		//   throw new HttpException('User not authenticated', HttpStatus.UNAUTHORIZED);
		// }

		try {
			this.logger.log('Starting bulk Excel file processing');
			const result = await this.clinicService.processBulkUpload(
				file,
				clinicId,
				brandId
			);
			return result;
		} catch (error) {
			this.logger.error('Error processing bulk Excel file', { error });
			throw new HttpException(
				'Error processing bulk Excel file',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('inventory/download/:clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('downloadInventory-clinics')
	async downloadInventory(
		@Param('clinicId') clinicId: string,
		@Res() res: Response
	) {
		const buffer =
			await this.clinicService.generateInventoryExcel(clinicId);

		res.set({
			'Content-Type':
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'Content-Disposition': `attachment; filename="inventory_${clinicId}.xlsx"`,
			'Content-Length': buffer.length
		});

		res.end(buffer);
	}

	@Delete('/inventory/:itemType/:itemId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteInventoryItem-clinics')
	async deleteInventoryItem(
		@Param('itemType') itemType: string,
		@Param('itemId') itemId: string
	) {
		return await this.clinicService.deleteInventoryItem(itemType, itemId);
	}

	// @Get(':clinicId/working-hours')
	// @Roles(Role.ADMIN, Role.SUPER_ADMIN)
	// @ApiOperation({ summary: 'Get clinic working hours' })
	// @ApiResponse({
	// 	status: 200,
	// 	description: 'Returns the working hours',
	// 	type: WorkingHoursDto
	// })
	// async getWorkingHours(@Param('clinicId') clinicId: string) {
	// 	return this.clinicService.getWorkingHours(clinicId);
	// }

	// @Put(':clinicId/working-hours')
	// @Roles(Role.ADMIN, Role.SUPER_ADMIN)
	// @ApiOperation({ summary: 'Update clinic working hours' })
	// @ApiResponse({
	// 	status: 200,
	// 	description: 'Working hours updated successfully',
	// 	type: WorkingHoursDto
	// })
	// async updateWorkingHours(
	// 	@Param('clinicId') clinicId: string,
	// 	@Body() workingHoursDto: WorkingHoursDto
	// ) {
	// 	return this.clinicService.updateWorkingHours(clinicId, workingHoursDto);
	// }

	// Endpoints for Client Booking Settings
	@Get(':id/client-booking-settings')
	@Roles(
		Role.ADMIN,
		Role.SUPER_ADMIN,
		Role.DOCTOR,
		Role.RECEPTIONIST,
		Role.LAB_TECHNICIAN
	) // Allow Super Admin and Clinic Admin
	@ApiOperation({ summary: 'Get client booking settings for a clinic' })
	@ApiOkResponse({
		description:
			'Returns the client booking settings including allowed doctor names.',
		type: ClientBookingSettingsResponseDto
	})
	@ApiResponse({ status: 404, description: 'Clinic not found' })
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('getClientBookingSettings-clinics')
	async getClientBookingSettings(
		@Param('id') id: string
	): Promise<ClientBookingSettingsResponseDto | null> {
		try {
			this.logger.log('Getting client booking settings', {
				clinicId: id
			});
			const settings =
				await this.clinicService.getClientBookingSettings(id);
			// Service method throws NotFoundException if clinic doesn't exist
			return settings;
		} catch (error: any) {
			this.logger.error('Error getting client booking settings', {
				clinicId: id,
				error
			});
			if (error instanceof HttpException) {
				throw error;
			}
			throw new HttpException(
				'Failed to retrieve client booking settings',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Put(':id/client-booking-settings')
	@Roles(
		Role.ADMIN,
		Role.SUPER_ADMIN,
		Role.DOCTOR,
		Role.RECEPTIONIST,
		Role.LAB_TECHNICIAN
	) // Allow Super Admin and Clinic Admin
	@UsePipes(
		new ValidationPipe({ whitelist: true, skipMissingProperties: true })
	)
	@ApiOperation({ summary: 'Update client booking settings for a clinic' })
	@ApiOkResponse({
		description: 'Client booking settings updated successfully.',
		type: ClinicEntity // Return the updated clinic entity
	})
	@ApiResponse({ status: 404, description: 'Clinic not found' })
	@ApiResponse({ status: 400, description: 'Validation failed' })
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('updateClientBookingSettings-clinics')
	async updateClientBookingSettings(
		@Param('id') id: string,
		@Body() dto: UpdateClientBookingSettingsDto, // Use the new DTO
		@Req() req: Request // Use standard Request type
	) {
		try {
			const user = req.user as { userId: string }; // Extract userId from JWT payload
			if (!user || !user.userId) {
				// This should ideally be caught by AuthGuard, but double-check
				throw new HttpException(
					'Unauthorized',
					HttpStatus.UNAUTHORIZED
				);
			}
			this.logger.log('Updating client booking settings', {
				clinicId: id,
				dto,
				updatedBy: user.userId
			});
			return await this.clinicService.updateClientBookingSettings(
				id,
				dto,
				user.userId
			);
		} catch (error: any) {
			this.logger.error('Error updating client booking settings', {
				clinicId: id,
				dto,
				error
			});
			if (error instanceof HttpException) {
				throw error;
			}
			throw new HttpException(
				'Failed to update client booking settings',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
