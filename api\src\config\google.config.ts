import { registerAs } from '@nestjs/config';

export default registerAs('google', () => ({
	firebase: {
		projectId: process.env.GOOGLE_FIREBASE_PROJECT_ID || '',
		privateKey: process.env.GOOGLE_FIREBASE_PRIVATE_KEY || '',
		clientEmail: process.env.GOOGLE_FIREBASE_CLIENT_EMAIL || ''
	},
	calendar: {
		clientId: process.env.GOOGLE_CLIENT_ID || '',
		clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
		redirectUri: process.env.GOOGLE_EXTERNAL_BASE_URL + "/api/google-calendar/auth/callback" || '',
		webhookUrl: process.env.GOOGLE_EXTERNAL_BASE_URL + "/api/google-calendar/webhook"|| '',
		scopes: [
			'https://www.googleapis.com/auth/calendar.events',
			'https://www.googleapis.com/auth/calendar.readonly'
		],
		apiKey: process.env.GOOGLE_API_KEY || ''
	}
}));
