import {
	<PERSON><PERSON>ty,
	Column,
	PrimaryGeneratedColumn,
	Index,
	ManyToOne,
	JoinColumn,
	CreateDateColumn,
	UpdateDateColumn
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

/**
 * Local cache of Google Calendar events.
 * Each record represents a single Google event instance (non-recurring).
 *
 * Indices:
 *  - (user_id, event_id) unique ⇒ fast upsert
 *  - (user_id, start_time)    ⇒ range queries for calendar views
 */
@Entity({ name: 'google_calendar_events' })
@Index('idx_gc_events_user_event', ['userId', 'eventId'], { unique: true })
@Index('idx_gc_events_user_start', ['userId', 'startTime'])
export class GoogleCalendarEventEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'user_id' })
	userId!: string;

	@ManyToOne(() => User, { onDelete: 'CASCADE' })
	@JoinColumn({ name: 'user_id' })
	user!: User;

	@Column({ type: 'varchar', name: 'event_id' })
	eventId!: string;

	@Column({ type: 'varchar', name: 'calendar_id', nullable: true })
	calendarId?: string;

	@Column({ type: 'varchar', length: 1024, name: 'summary', nullable: true })
	summary?: string;

	@Column({ type: 'text', name: 'description', nullable: true })
	description?: string;

	@Column({ type: 'timestamptz', name: 'start_time' })
	startTime!: Date;

	@Column({ type: 'timestamptz', name: 'end_time' })
	endTime!: Date;

	@Column({ type: 'varchar', length: 50, name: 'status', nullable: true })
	status?: string;

	@Column({ type: 'jsonb', name: 'raw', nullable: true })
	raw?: any;

	@CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
	updatedAt!: Date;
} 