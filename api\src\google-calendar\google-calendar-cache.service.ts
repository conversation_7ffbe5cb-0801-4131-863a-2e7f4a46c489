import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GoogleCalendarEventEntity } from './entities/google-calendar-event.entity';

interface GoogleEventPayload {
  id: string;
  calendarId?: string;
  summary?: string;
  description?: string;
  start: { dateTime?: string; date?: string };
  end: { dateTime?: string; date?: string };
  status?: string;
  [key: string]: any;
}

@Injectable()
export class GoogleCalendarCacheService {
  constructor(
    @InjectRepository(GoogleCalendarEventEntity)
    private readonly cacheRepo: Repository<GoogleCalendarEventEntity>,
  ) {}

  /* Persist or update a batch of events */
  async saveEvents(userId: string, events: GoogleEventPayload[]): Promise<void> {
    if (!events || events.length === 0) return;

    const entities: GoogleCalendarEventEntity[] = events.map(e => this.mapToEntity(userId, e));
    await this.cacheRepo.upsert(entities, [ 'userId', 'eventId' ]);
  }

  /* Upsert a single event */
  async upsertEvent(userId: string, event: GoogleEventPayload): Promise<void> {
    const entity = this.mapToEntity(userId, event);
    await this.cacheRepo.upsert(entity, [ 'userId', 'eventId' ]);
  }

  /* Remove all cached events for a user */
  async clearUserEvents(userId: string): Promise<void> {
    await this.cacheRepo.delete({ userId });
  }

  /* Query cached events for a date range (inclusive) */
  async getEventsRange(userId: string, start: Date, end: Date): Promise<GoogleCalendarEventEntity[]> {
    return this.cacheRepo.createQueryBuilder('e')
      .where('e.userId = :userId', { userId })
      .andWhere('e.startTime < :end', { end })
      .andWhere('e.endTime > :start', { start })
      .orderBy('e.startTime', 'ASC')
      .getMany();
  }

  /* Helpers */
  private mapToEntity(userId: string, event: GoogleEventPayload): GoogleCalendarEventEntity {
    const startIso = event.start?.dateTime || event.start?.date;
    const endIso = event.end?.dateTime || event.end?.date;
    return this.cacheRepo.create({
      userId,
      eventId: event.id,
      calendarId: event.calendarId,
      summary: event.summary,
      description: event.description,
      startTime: new Date(startIso!),
      endTime: new Date(endIso!),
      status: event.status,
      raw: event,
    });
  }
} 