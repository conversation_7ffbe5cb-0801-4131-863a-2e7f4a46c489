import {
	Controller,
	Get,
	Post,
	Delete,
	Req,
	Res,
	Query,
	Body,
	UseGuards,
	HttpStatus,
	Logger,
	Headers,
	UnauthorizedException,
	HttpCode
} from '@nestjs/common';
import { Request, Response } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GoogleCalendarService } from './google-calendar.service';
import { ConnectCalendarDto } from './dto/connect-calendar.dto';
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth
} from '@nestjs/swagger';
import { User } from '../users/entities/user.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { Brand } from '../brands/entities/brand.entity';
import { AuthGuard } from '@nestjs/passport';
import { AuthenticatedUser } from '../auth/interfaces/authenticated-user.interface';
import { Public } from '../auth/guards/public.decorator';
import { getAdminPortalUrl } from '../utils/common/get-login-url';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EncryptionService } from '../utils/encryption/encryption.service';

@ApiTags('Google Calendar')
@Controller('google-calendar')
export class GoogleCalendarController {
	private readonly logger = new Logger(GoogleCalendarController.name);

	constructor(
		private readonly googleCalendarService: GoogleCalendarService,
		@InjectRepository(User)
		private readonly userRepository: Repository<User>,
		@InjectRepository(ClinicUser)
		private readonly clinicUserRepository: Repository<ClinicUser>,
		@InjectRepository(Brand)
		private readonly brandRepository: Repository<Brand>,
		private readonly encryptionService: EncryptionService
	) {}

	@Get('auth')
	@UseGuards(AuthGuard('jwt'))
	@ApiBearerAuth()
	@ApiOperation({ summary: 'Get Google Auth URL' })
	async getAuthUrl(@Req() req: { user: AuthenticatedUser }) {
		const url = await this.googleCalendarService.getAuthUrl(req.user.id);
		return { url };
	}

	@Public()
	@Get('auth/callback')
	@ApiOperation({ summary: 'Handle Google OAuth Callback' })
	async handleOAuthCallback(
		@Query('code') code: string,
		@Query('state') state: string,
		@Res() res: Response
	) {
		if (!code || !state) {
			return res.status(HttpStatus.BAD_REQUEST).send('Missing code or state');
		}

		try {
			await this.googleCalendarService.handleOAuthCallback(code, state);

			// Dynamically resolve brand slug -> admin portal URL
			let baseAdminUrl = getAdminPortalUrl(); // default
			try {
				const user = await this.userRepository.findOne({ where: { id: state }, relations: ['brand'] });

				// Attempt to resolve brand slug directly from the user entity
				let brandSlug = user?.brand?.slug;

				// Fallback: derive brand slug via ClinicUser relation
				if (!brandSlug) {
					const clinicUser = await this.clinicUserRepository.findOne({ where: { userId: state } });
					if (clinicUser?.brandId) {
						const brand = await this.brandRepository.findOne({ where: { id: clinicUser.brandId } });
						brandSlug = brand?.slug;
					}
				}

				if (brandSlug) {
					baseAdminUrl = getAdminPortalUrl(brandSlug);
				}
			} catch (err) {
				this.logger.warn('Could not determine brand slug for OAuth redirect', err);
			}

			// Redirect to a success page in the correct domain
			return res.redirect(`${baseAdminUrl}/profile/hours?google-auth=success`);
		} catch (error) {
			this.logger.error('OAuth callback failed', error);
			const baseAdminUrl = getAdminPortalUrl();
			// Redirect to a failure page in the frontend
			return res.redirect(`${baseAdminUrl}/profile/hours?google-auth=failed`);
		}
	}

	@Get('calendars')
	@UseGuards(AuthGuard('jwt'))
	@ApiBearerAuth()
	@ApiOperation({ summary: 'List User Calendars' })
	async listCalendars(@Req() req: { user: AuthenticatedUser }) {
		return this.googleCalendarService.getUserCalendars(req.user.id);
	}

	@Post('connect')
	@UseGuards(AuthGuard('jwt'))
	@ApiBearerAuth()
	@ApiOperation({ summary: 'Connect to a Calendar' })
	async connectCalendar(
		@Req() req: { user: AuthenticatedUser },
		@Body('calendarId') calendarId: string
	) {
		return this.googleCalendarService.connectCalendar(
			req.user.id,
			calendarId
		);
	}

	@Get('status')
	@UseGuards(AuthGuard('jwt'))
	@ApiBearerAuth()
	@ApiOperation({ summary: 'Get Connection Status' })
	async getConnectionStatus(@Req() req: { user: AuthenticatedUser }) {
		return this.googleCalendarService.getConnectionStatus(req.user.id);
	}

	@Delete('disconnect')
	@UseGuards(AuthGuard('jwt'))
	@ApiBearerAuth()
	@HttpCode(HttpStatus.NO_CONTENT)
	@ApiOperation({ summary: 'Disconnect from Google Calendar' })
	async disconnectCalendar(@Req() req: { user: AuthenticatedUser }) {
		return this.googleCalendarService.disconnectCalendar(req.user.id);
	}

	@Public()
	@Post('webhook')
	@HttpCode(HttpStatus.OK)
	@ApiOperation({ summary: 'Handle Google Calendar Webhook' })
	async handleWebhook(
		@Headers('x-goog-channel-id') channelId: string,
		@Headers('x-goog-resource-id') resourceId: string,
		@Headers('x-goog-resource-state') resourceState: string,
		@Headers('x-goog-channel-token') channelToken: string,
		@Headers('x-goog-channel-expiration') channelExpiration: string,
		@Headers() allHeaders: any
	) {
		if (!channelId || !resourceId || !resourceState) {
			throw new UnauthorizedException('Missing Google webhook headers');
		}

		// Security: Validate the webhook token if we have one stored
		try {
			const user = await this.userRepository.findOne({ where: { googleWebhookId: channelId } });
			if (user?.googleWebhookToken) {
				let expectedToken: string;
				try {
					expectedToken = this.encryptionService.decrypt(
						user.googleWebhookToken
					);
				} catch (decryptErr) {
					// Token might still be stored in plain text (legacy data)
					expectedToken = user.googleWebhookToken;
				}

				if (!channelToken || channelToken !== expectedToken) {
					this.logger.warn(
						`Invalid webhook token for channel ${channelId}. Provided=${channelToken}, Expected=${expectedToken}`
					);
					throw new UnauthorizedException('Invalid webhook token');
				}
			}
		} catch (err) {
			throw err;
		}

		this.logger.log(
			`🔔 WEBHOOK RECEIVED: channelId=${channelId}, resourceId=${resourceId}, state=${resourceState}, token=${channelToken}, expiration=${channelExpiration}`
		);
		
		// Log all headers for debugging
		this.logger.debug('Full webhook headers:', allHeaders);

		// Queue this for processing instead of handling it inline
		this.googleCalendarService.processWebhookNotification(channelId, resourceId, resourceState);

		return { success: true, timestamp: new Date().toISOString() };
	}
} 