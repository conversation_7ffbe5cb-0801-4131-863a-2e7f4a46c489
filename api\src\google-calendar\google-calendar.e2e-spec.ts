import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../app.module';
import { GoogleCalendarService } from './google-calendar.service';

const mockGoogleCalendarService = {
  getAuthUrl: jest.fn().mockResolvedValue('https://mock.auth.url'),
  handleOAuthCallback: jest.fn().mockResolvedValue({ success: true }),
  getUserCalendars: jest.fn().mockResolvedValue([{ id: 'cal1', summary: 'Test Calendar' }]),
  connectCalendar: jest.fn().mockResolvedValue({ success: true }),
  getConnectionStatus: jest.fn().mockResolvedValue({ isConnected: true }),
  disconnectCalendar: jest.fn().mockResolvedValue({ success: true }),
};

describe('GoogleCalendarController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
    .overrideProvider(GoogleCalendarService)
    .useValue(mockGoogleCalendarService)
    .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/google-calendar/auth (GET) should redirect', () => {
    return request(app.getHttpServer())
      .get('/google-calendar/auth')
      .expect(302)
      .expect('Location', 'https://mock.auth.url');
  });

  it('/google-calendar/auth/callback (GET) should handle callback', () => {
    return request(app.getHttpServer())
      .get('/google-calendar/auth/callback?code=mock_code&state=mock_state')
      .expect(302)
      .expect('Location', '/settings?google-auth=success');
  });

  it('/google-calendar/calendars (GET) should return calendars', () => {
    return request(app.getHttpServer())
      .get('/google-calendar/calendars')
      .expect(200)
      .expect(res => {
        expect(res.body).toEqual([{ id: 'cal1', summary: 'Test Calendar' }]);
      });
  });

  it('/google-calendar/status (GET) should return connection status', () => {
    return request(app.getHttpServer())
      .get('/google-calendar/status')
      .expect(200)
      .expect(res => {
        expect(res.body).toEqual({ isConnected: true });
      });
  });

  afterAll(async () => {
    await app.close();
  });
}); 