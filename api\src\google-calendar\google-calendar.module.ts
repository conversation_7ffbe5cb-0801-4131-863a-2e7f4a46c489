import { Module, forwardRef } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { GoogleCalendarController } from './google-calendar.controller';
import { GoogleCalendarService } from './google-calendar.service';
import { User } from '../users/entities/user.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { EncryptionModule } from '../utils/encryption/encryption.module';
import { SqsModule } from '../utils/aws/sqs/sqs.module';
import { GoogleCalendarEventEntity } from './entities/google-calendar-event.entity';
import { GoogleCalendarCacheService } from './google-calendar-cache.service';
import { Brand } from '../brands/entities/brand.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { GoogleCalendarErrorHandler } from '../utils/google-calendar/google-calendar-error.handler';

@Module({
	imports: [
		TypeOrmModule.forFeature([User, AppointmentEntity, GoogleCalendarEventEntity, Brand, ClinicUser]),
		ConfigModule,
		EncryptionModule,
		ScheduleModule,
		forwardRef(() => SqsModule)
	],
	controllers: [GoogleCalendarController],
	providers: [GoogleCalendarService, GoogleCalendarCacheService, GoogleCalendarErrorHandler],
	exports: [GoogleCalendarService, GoogleCalendarCacheService]
})
export class GoogleCalendarModule {} 