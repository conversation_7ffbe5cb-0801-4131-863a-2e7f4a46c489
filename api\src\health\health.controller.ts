import { Controller, Get } from '@nestjs/common';
import { HealthService } from './health.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Health Checks')
@Controller('healtz')
export class HealthController {
	constructor(private readonly healthService: HealthService) {}

	@Get('/db')
	@ApiOperation({ summary: 'Check database health' })
	@ApiResponse({ status: 200, description: 'Database is healthy' })
	@ApiResponse({ status: 500, description: 'Database health check failed' })
	@TrackMethod('checkDatabase-health')
	checkDatabase() {
		return this.healthService.checkDatabase();
	}

	@Get('/db/migration')
	@ApiOperation({ summary: 'Check database migrations' })
	@ApiResponse({ status: 200, description: 'Migrations are up to date' })
	@ApiResponse({ status: 500, description: 'Migration check failed' })
	@TrackMethod('checkMigrations-health')
	checkMigrations() {
		return this.healthService.checkMigrations();
	}

	@Get('/redis')
	@ApiOperation({ summary: 'Check Redis connection' })
	@ApiResponse({ status: 200, description: 'Redis is connected' })
	@ApiResponse({ status: 500, description: 'Redis connection check failed' })
	@TrackMethod('checkRedis-health')
	checkRedis() {
		return this.healthService.checkRedis();
	}

	@Get('/redis/locks')
	@ApiOperation({ summary: 'Check Redis lock states' })
	@ApiResponse({
		status: 200,
		description: 'Redis lock states retrieved successfully'
	})
	@ApiResponse({ status: 500, description: 'Redis lock check failed' })
	@TrackMethod('checkRedisLocks-health')
	checkRedisLocks() {
		return this.healthService.checkRedisLocks();
	}

	@Get('/all')
	@ApiOperation({ summary: 'Check all services' })
	@ApiResponse({ status: 200, description: 'All services are healthy' })
	@ApiResponse({
		status: 500,
		description: 'One or more service checks failed'
	})
	@TrackMethod('checkAll-health')
	checkAll() {
		return this.healthService.checkAll();
	}

	@Get()
	@ApiOperation({ summary: 'Check general application health' })
	@ApiResponse({ status: 200, description: 'Application is healthy' })
	@ApiResponse({
		status: 500,
		description: 'Application health check failed'
	})
	@TrackMethod('checkGeneralHealth-health')
	checkGeneralHealth() {
		return this.healthService.checkGeneralHealth();
	}
}
