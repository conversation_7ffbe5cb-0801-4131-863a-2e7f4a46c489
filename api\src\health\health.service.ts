import { Injectable } from '@nestjs/common';
import {
	HealthCheckService,
	TypeOrmHealthIndicator,
	HealthIndicatorResult,
	HealthCheckResult
} from '@nestjs/terminus';
import { RedisHealthIndicator } from './redis.health';
import { dataSource } from '../database/data-source';
import { RedisService } from '../utils/redis/redis.service';

@Injectable()
export class HealthService {
	constructor(
		private health: HealthCheckService,
		private db: TypeOrmHealthIndicator,
		private redis: RedisHealthIndicator,
		private readonly redisService: RedisService
	) {}

	checkDatabase(): Promise<HealthCheckResult> {
		return this.health.check([async () => this.db.pingCheck('database')]);
	}

	async checkMigrations(): Promise<HealthCheckResult> {
		return this.health.check([
			async () => {
				if (!dataSource.isInitialized) {
					await dataSource.initialize();
				}

				const hasPendingMigrations = await dataSource.showMigrations();
				const isHealthy = !hasPendingMigrations;

				const pendingMigrations = hasPendingMigrations
					? await dataSource.runMigrations({ transaction: 'none' })
					: [];

				const result: HealthIndicatorResult = {
					migration: {
						status: isHealthy ? 'up' : 'down',
						pendingMigrations: pendingMigrations.map(
							migration => migration.name
						)
					}
				};

				return result;
			}
		]);
	}

	checkRedis(): Promise<HealthCheckResult> {
		return this.health.check([
			async () => {
				try {
					return await this.redis.isHealthy('redis');
				} catch (error) {
					// Log the error but return a degraded status instead of failing
					return {
						redis: {
							status: 'down',
							message:
								'Redis connection failed but service can continue operating'
						}
					};
				}
			}
		]);
	}

	async checkRedisLocks() {
		try {
			// List of key lock names to check
			const lockKeys = [
				'reminder_cron_lock',
				'upcoming_appointment_reminder_cron_lock'
			];

			const lockStatus = await this.redisService.getLockStatus(lockKeys);

			// Create a health check result
			const result: HealthIndicatorResult = {
				redisLocks: {
					status: 'up',
					locks: lockStatus
				}
			};

			return {
				status: 'ok',
				info: result
			};
		} catch (error: any) {
			console.error('Redis locks health check failed:', error);
			return {
				status: 'error',
				error: {
					message: 'Redis locks health check failed',
					details: error.message || 'Unknown error'
				}
			};
		}
	}

	checkAll(): Promise<HealthCheckResult> {
		return this.health.check([
			async () => this.db.pingCheck('database'),
			() => this.redis.isHealthy('redis'),
			async () => {
				const isHealthy = true;
				const result: HealthIndicatorResult = {
					migration: {
						status: isHealthy ? 'up' : 'down'
					}
				};
				return result;
			}
		]);
	}

	async checkGeneralHealth(): Promise<{
		isHealthy: boolean;
		message: string;
	}> {
		return {
			isHealthy: true,
			message: 'Successfully checked health api!'
		};
	}
}
