import { Injectable } from '@nestjs/common';
import {
	HealthCheckError,
	HealthIndicator,
	HealthIndicatorResult
} from '@nestjs/terminus';
import { RedisService } from '../utils/redis/redis.service';

@Injectable()
export class RedisHealthIndicator extends HealthIndicator {
	constructor(private readonly redisService: RedisService) {
		super();
	}

	async isHealthy(key: string): Promise<HealthIndicatorResult> {
		try {
			// Use the Redis client from our centralized service
			const client = this.redisService.getClient();

			// Check if Redis is responding
			const ping = await client.ping();
			const isHealthy = ping === 'PONG';

			return this.getStatus(key, isHealthy);
		} catch (err) {
			console.error('Redis health check failed:', err);
			throw new HealthCheckError('Redis health check failed', err);
		}
	}
}
