import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	ManyToOne,
	JoinColumn
} from 'typeorm';
import { InvoiceEntity } from './invoice.entity';
import { User } from '../../users/entities/user.entity';

export enum AuditLogOperationType {
	CREATE = 'CREATE',
	UPDATE = 'UPDATE',
	DELETE = 'DELETE',
	WRITE_OFF = 'WRITE_OFF'
}

// Interface for structured audit operations
export interface AuditOperation {
	type:
		| 'ADD_ITEM'
		| 'REMOVE_ITEM'
		| 'CHANGE_QUANTITY'
		| 'APPLY_DISCOUNT'
		| 'CHANGE_DISCOUNT'
		| 'REMOVE_DISCOUNT'
		| 'WRITE_OFF_INVOICE'
		| 'CANCEL_INVOICE';
	item?: {
		id: string;
		name: string;
	};
	before?: any;
	after?: any;
	description: string;
	reason?: string; // Add optional reason field
	writeoffAmount?: number; // Add optional writeoff amount field
	cancellationAmount?: number; // Add optional cancellation amount field
}

@Entity('invoice_audit_log')
export class InvoiceAuditLogEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'invoice_id' })
	invoiceId!: string;

	@ManyToOne(() => InvoiceEntity, { onDelete: 'SET NULL', nullable: true })
	@JoinColumn({ name: 'invoice_id' })
	invoice?: InvoiceEntity;

	@Column({ type: 'uuid', name: 'user_id', nullable: true })
	userId!: string | null;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'user_id' })
	user!: User;

	@Column({
		type: 'enum',
		enum: AuditLogOperationType,
		name: 'operation_type'
	})
	operationType!: AuditLogOperationType;

	@Column({ type: 'jsonb', name: 'changes', nullable: true })
	changes?: {
		before: Record<string, any>;
		after: Record<string, any>;
	} | null;

	@Column({ type: 'jsonb', name: 'changed_fields_summary', nullable: true })
	changedFieldsSummary?: AuditOperation[] | null;

	@CreateDateColumn({
		name: 'timestamp',
		type: 'timestamp with time zone'
	})
	timestamp!: Date;
}
