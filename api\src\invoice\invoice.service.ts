import {
	HttpException,
	HttpStatus,
	Injectable,
	forwardRef,
	Inject
} from '@nestjs/common';
import { InvoiceEntity } from './entities/invoice.entity';
import { Repository, IsNull } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateInvoiceDto, UpdateInvoiceDto } from './dto/invoice.dto';
import {
	InvoiceAuditLogEntity,
	AuditLogOperationType,
	AuditOperation
} from './entities/invoice-audit-log.entity';
import { PatientsService } from '../patients/patients.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import * as moment from 'moment';
import { uuidv4 } from 'uuidv7';
import { PatientVaccinationsService } from '../patient-vaccinations/patient-vaccinations.service';
// import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { Patient } from '../patients/entities/patient.entity';
import { EnumPlanType } from '../clinic-plans/enums/enum-plan-type';
import { EnumInvoiceType } from './enums/enum-invoice-types';
import { ClinicProductEntity } from '../clinic-products/entities/clinic-product.entity';
import { ClinicVaccinationEntity } from '../clinic-vaccinations/entities/clinic-vaccination.entity';
import { ClinicMedicationEntity } from '../clinic-medications/entities/clinic-medication.entity';
import { ClinicConsumableEntity } from '../clinic-consumables/entities/clinic-consumable.entity';
// import { DEV_SES_EMAIL } from '../utils/constants';
import { CartsService } from '../carts/carts.service';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
// import { isProduction } from '../utils/common/get-login-url';
import { generateUniqueCode } from '../utils/common/generate_alpha-numeric_code';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientRemindersService } from '../patient-reminders/patient-reminder.service';
import { ReminderStatus } from '../patient-reminders/enums/reminder.enum';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { PatientVaccination } from '../patient-vaccinations/entities/patient-vaccinations.entity';
import { EnumInvoiceStatus } from './enums/enum-invoice-status';
import { EntityManager, DataSource, QueryRunner } from 'typeorm';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { PaymentDetailsService } from '../payment-details/payment-details.service';
import { CreateInvoiceWithPaymentDto } from './dto/create-invoice-with-payment.dto';
import { WriteOffInvoiceDto } from './dto/write-off-invoice.dto';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentsService } from '../appointments/appointments.service';
import { CartItemEntity } from '../cart-items/entities/cart-item.entity';
import { AvailabilityService } from '../availability/availability.service';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { User } from '../users/entities/user.entity';

import { ModuleRef } from '@nestjs/core';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import { EnumAmountType } from '../payment-details/enums/enum-credit-types';
import { EnumPaymentType } from './enums/enum-payment-types';

/**
 * InvoiceService - Core service for invoice operations
 *
 * CRITICAL FIXES IMPLEMENTED:
 *
 * 1. CIRCULAR DEPENDENCY RESOLUTION:
 *    - Using forwardRef to inject PaymentDetailsService directly
 *    - Standard dependency injection approach
 *
 * 2. SQS TASK TIMING FIX:
 *    - Created executePostTransactionTasks() method
 *    - Background tasks now execute AFTER transaction commit for atomic operations
 *    - Maintains existing behavior for non-transactional calls
 *    - Ensures business workflows (reminders, PDFs, vaccinations, inventory) are preserved
 *    - Added comprehensive error handling that doesn't fail the main operation
 *
 * 3. BACKWARD COMPATIBILITY:
 *    - All existing functionality preserved
 *    - Optional queryRunner parameter maintains existing API
 *    - No breaking changes to existing endpoints
 *
 * 4. ATOMIC OPERATIONS:
 *    - Transaction safety for invoice + payment creation
 *    - Proper rollback on failures
 *    - Appointment status updates within same transaction
 *    - Background task execution post-commit ensures data consistency
 */
@Injectable()
export class InvoiceService {
	constructor(
		@InjectRepository(InvoiceEntity)
		private invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(InvoiceAuditLogEntity)
		private invoiceAuditLogRepository: Repository<InvoiceAuditLogEntity>,
		@InjectRepository(PaymentDetailsEntity)
		private paymentDetailsRepository: Repository<PaymentDetailsEntity>,
		@InjectRepository(CartEntity)
		private cartRepository: Repository<CartEntity>,
		private patientService: PatientsService,
		private s3Service: S3Service,
		private readonly patientVaccination: PatientVaccinationsService,
		@InjectRepository(PatientVaccination)
		private patientVaccinationRepository: Repository<PatientVaccination>,
		// private readonly mailService: SESMailService,
		private readonly whatsappService: WhatsappService,
		private readonly logger: WinstonLogger,
		@InjectRepository(ClinicProductEntity)
		private clinicProduct: Repository<ClinicProductEntity>,
		@InjectRepository(ClinicVaccinationEntity)
		private clinicVaccination: Repository<ClinicVaccinationEntity>,
		@InjectRepository(ClinicMedicationEntity)
		private clinicMedication: Repository<ClinicMedicationEntity>,
		@InjectRepository(ClinicConsumableEntity)
		private clinicConsumableEntity: Repository<ClinicConsumableEntity>,
		private readonly cartService: CartsService,
		@InjectRepository(AppointmentDoctorsEntity)
		private appointmentDoctorsEntity: Repository<AppointmentDoctorsEntity>,
		private readonly patientRemindersService: PatientRemindersService,
		private readonly globalReminderService: GlobalReminderService,
		@InjectRepository(AppointmentDetailsEntity)
		private appointmentDetailsEntity: Repository<AppointmentDetailsEntity>,
		@InjectRepository(CartItemEntity)
		private cartItemRepository: Repository<CartItemEntity>,
		private readonly entityManager: EntityManager,
		private readonly dataSource: DataSource,
		@Inject(forwardRef(() => SqsService))
		private readonly sqsService: SqsService,
		@Inject(forwardRef(() => AppointmentsService))
		private readonly appointmentsService: AppointmentsService,
		@Inject(forwardRef(() => AvailabilityService))
		private readonly availabilityService: AvailabilityService,
		@InjectRepository(AppointmentEntity)
		private appointmentRepository: Repository<AppointmentEntity>,
		private readonly moduleRef: ModuleRef,
		@Inject(forwardRef(() => PaymentDetailsService))
		private readonly paymentDetailsService: PaymentDetailsService,
		private readonly clinicLabReportService: ClinicLabReportService
	) {}

	getClinicAddress(patientDetail: Patient) {
		const addressParts = [];

		if (patientDetail?.clinic?.addressLine1) {
			addressParts.push(patientDetail.clinic.addressLine1);
		}

		if (patientDetail?.clinic?.city) {
			addressParts.push(patientDetail.clinic.city);
		}

		if (patientDetail?.clinic?.addressPincode) {
			addressParts.push(`- ${patientDetail.clinic.addressPincode}`);
		}

		if (patientDetail?.clinic?.state) {
			addressParts.push(patientDetail.clinic.state);
		}

		this.logger.log('Clinic Address formatted', {
			addressParts
		});
		return addressParts.join(', ').trim();
	}

	// private async sendMail(
	// 	body: string,
	// 	buffers: Buffer[],
	// 	fileName: string[],
	// 	email: string,
	// 	subject?: string
	// ) {
	// 	try {
	// 		if (isProduction() && email) {
	// 			this.mailService.sendMail({
	// 				body: body,
	// 				subject: subject ?? 'Invoice attachments',
	// 				pdfBuffers: buffers,
	// 				pdfFileNames: fileName,
	// 				toMailAddress: email
	// 			});
	// 			this.logger.log('Production Mail Log', {
	// 				body: body,
	// 				subject: subject ?? 'Invoice attachments',
	// 				pdfFileNames: fileName,
	// 				toMailAddress: DEV_SES_EMAIL //email
	// 			});
	// 		} else if (!isProduction()) {
	// 			this.mailService.sendMail({
	// 				body: body,
	// 				subject: subject ?? 'Invoice attachments',
	// 				pdfBuffers: buffers,
	// 				pdfFileNames: fileName,
	// 				toMailAddress: DEV_SES_EMAIL //email
	// 			});
	// 			this.logger.log('UAT Mail Log', {
	// 				body: body,
	// 				subject: subject ?? 'Invoice attachments',
	// 				pdfFileNames: fileName,
	// 				toMailAddress: DEV_SES_EMAIL //email
	// 			});
	// 		}
	// 	} catch (error) {
	// 		this.logger.log('Send Mail Error ', {
	// 			error
	// 		});
	// 	}
	// }

	async createInvoice(
		createInvoiceDto: CreateInvoiceDto,
		clinicId: string,
		brandId: string,
		userId: string,
		queryRunner?: QueryRunner
	): Promise<InvoiceEntity> {
		try {
			// NOTE: As per business requirements, we no longer generate invoice and credit note PDFs.
			// We still generate prescription and vaccination certificate files.

			// Determine whether to use transaction or regular repository methods
			const repository = queryRunner
				? queryRunner.manager
				: this.invoiceRepository;
			const entityManager = queryRunner
				? queryRunner.manager
				: this.entityManager;

			// 1. Validate request - Check for existing invoice with the same cartId
			if (
				createInvoiceDto.cartId?.length &&
				createInvoiceDto.invoiceType === EnumInvoiceType.Invoice
			) {
				const existingInvoice = queryRunner
					? await queryRunner.manager.findOne(InvoiceEntity, {
							where: {
								cartId: createInvoiceDto.cartId,
								invoiceType: EnumInvoiceType.Invoice
							}
						})
					: await this.invoiceRepository.findOne({
							where: {
								cartId: createInvoiceDto.cartId, //  here cartId is cart_item primary key "my observation"
								invoiceType: EnumInvoiceType.Invoice
							}
						});

				if (existingInvoice) {
					throw new HttpException(
						'An invoice with this cartId already exists',
						HttpStatus.CONFLICT
					);
				}
			}

			// 2. Find existing invoice or create a cart if needed
			let invoiceItem;
			if (createInvoiceDto.cartId.length) {
				// Ideally this scenario should not occur.
				invoiceItem = queryRunner
					? await queryRunner.manager.findOne(InvoiceEntity, {
							where: {
								cartId: createInvoiceDto.cartId
							}
						})
					: await this.invoiceRepository.findOne({
							where: {
								cartId: createInvoiceDto.cartId
							}
						});
			} else {
				// Create an empty cart
				const appointmentId = createInvoiceDto.appointmentId;
				if (appointmentId) {
					try {
						const cartItem =
							await this.cartService.createCart(appointmentId);
						createInvoiceDto = {
							...createInvoiceDto,
							cartId: cartItem.id
						};
					} catch (cartError: any) {
						throw new HttpException(
							`Failed to create cart: ${cartError.message}`,
							HttpStatus.INTERNAL_SERVER_ERROR
						);
					}
				}
			}

			// 3. Using existing appointmentId or handling impromptu flag (when already processed at cart level)
			if (!createInvoiceDto.appointmentId && createInvoiceDto.impromptu) {
				// For impromptu invoices, the appointment should already be created at the cart level
				// We just need to ensure there's a valid cartId with associated items
				try {
					const cartItems = await this.cartItemRepository.find({
						where: { cartId: createInvoiceDto.cartId }
					});

					if (
						cartItems.length === 0 ||
						!cartItems[0]?.appointmentId
					) {
						throw new HttpException(
							'No appointment found for this impromptu invoice. The appointment should be created at cart level.',
							HttpStatus.BAD_REQUEST
						);
					}

					// Use the appointment ID from the cart items
					createInvoiceDto.appointmentId = cartItems[0].appointmentId;
				} catch (cartItemError: any) {
					throw new HttpException(
						`Failed to fetch cart items: ${cartItemError.message}`,
						cartItemError.status || HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}

			// 4. Generate unique ID for the invoice
			const invoiceId = uuidv4();

			// 5. Get patient and doctor information (needed for DB entry and background tasks)
			const patientId = createInvoiceDto.patientId!;
			let patientDetail;
			try {
				patientDetail =
					await this.patientService.getPatientDetails(patientId);
			} catch (patientError: any) {
				throw new HttpException(
					`Failed to fetch patient details: ${patientError.message}`,
					patientError.status || HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			// Get doctor information - may be null for impromptu appointments
			let appointmentDoctorResponse;
			if (createInvoiceDto.appointmentId) {
				try {
					appointmentDoctorResponse = queryRunner
						? await queryRunner.manager.findOne(
								AppointmentDoctorsEntity,
								{
									where: {
										appointmentId:
											createInvoiceDto.appointmentId,
										primary: true
									},
									relations: [
										'clinicUser',
										'clinicUser.user',
										'clinicUser.clinic.brand'
									]
								}
							)
						: await this.appointmentDoctorsEntity.findOne({
								where: {
									appointmentId:
										createInvoiceDto.appointmentId,
									primary: true
								},
								relations: [
									'clinicUser',
									'clinicUser.user',
									'clinicUser.clinic.brand'
								]
							});
				} catch (doctorError: any) {
					// Don't throw here as doctor info might not be required for all invoice types
				}
			}

			// Set default values for doctor information if not available (impromptu appointments)
			let licenseNumber = '';
			let doctorName = '';

			if (appointmentDoctorResponse?.clinicUser?.user) {
				licenseNumber =
					appointmentDoctorResponse.clinicUser.user.licenseNumber ||
					'';
				doctorName =
					`${appointmentDoctorResponse.clinicUser.user.firstName || ''} ${appointmentDoctorResponse.clinicUser.user.lastName || ''}`.trim();
			}

			const { ...requiredInvoiceDetails } = createInvoiceDto;

			// Set default invoice values - will be updated by payment-details API
			const status = EnumInvoiceStatus.PENDING;
			const paidAmount = 0;
			const balanceDue = Number(createInvoiceDto.amountPayable);

			// 6. Create the invoice record in database
			let createdInvoiceData;
			try {
				createdInvoiceData = queryRunner
					? queryRunner.manager.create(InvoiceEntity, {
							...requiredInvoiceDetails,
							id: invoiceId,
							invoiceAmount: createInvoiceDto.invoiceAmount || 0,
							clinicId,
							brandId,
							status,
							paidAmount,
							balanceDue,
							createdBy: userId,
							updatedBy: userId,
							metadata: createInvoiceDto.metadata || {}
						})
					: this.invoiceRepository.create({
							...requiredInvoiceDetails,
							id: invoiceId,
							invoiceAmount: createInvoiceDto.invoiceAmount || 0,
							clinicId,
							brandId,
							status,
							paidAmount,
							balanceDue,
							createdBy: userId,
							updatedBy: userId,
							metadata: createInvoiceDto.metadata || {}
						});
			} catch (createError: any) {
				throw new HttpException(
					`Failed to create invoice entity: ${createError.message}`,
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			let savedInvoice;
			try {
				savedInvoice = queryRunner
					? await queryRunner.manager.save(
							InvoiceEntity,
							createdInvoiceData
						)
					: await this.invoiceRepository.save(createdInvoiceData);
			} catch (saveError: any) {
				throw new HttpException(
					`Failed to save invoice to database: ${saveError.message}`,
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			// 6.5. Log invoice creation in audit trail
			try {
				await this.logChange(
					savedInvoice.id,
					userId,
					AuditLogOperationType.CREATE,
					null, // No before/after changes for creation
					'Invoice created',
					queryRunner // Pass queryRunner for transactional audit logging
				);
			} catch (auditError: any) {
				// Log the audit error but don't fail the invoice creation
				this.logger.error(
					'Failed to create audit log for invoice creation',
					{
						invoiceId: savedInvoice.id,
						userId,
						error: auditError.message
					}
				);
			}

			// 7. Generate unique IDs for invoice and prescription (needed for database updates)
			let alphaNumericPrescriptionReferenceId = '';
			let alphaNumericReferenceId;
			try {
				alphaNumericReferenceId = await generateUniqueCode(
					'referenceAlphaId',
					this.invoiceRepository
				);
			} catch (referenceIdError: any) {
				throw new HttpException(
					`Failed to generate reference ID: ${referenceIdError.message}`,
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			// Create file keys in advance
			const prescriptionFileKey = `prescription/${uuidv4()}`;
			let creditNoteFileKey = '';

			if (createInvoiceDto.invoiceType === EnumInvoiceType.Refund) {
				creditNoteFileKey = `creditNote/${uuidv4()}`;
			}

			// 8. Check if we need to generate a prescription ID
			if (
				createInvoiceDto.invoiceType === EnumInvoiceType.Invoice &&
				((createInvoiceDto?.dischargeInstructions?.length &&
					createInvoiceDto?.dischargeInstructions?.trim().length >
						0) ||
					(createInvoiceDto?.prescriptionDetails &&
						createInvoiceDto?.prescriptionDetails.length > 0 &&
						createInvoiceDto?.prescriptionDetails[0].name.length >
							0))
			) {
				try {
					alphaNumericPrescriptionReferenceId =
						await generateUniqueCode(
							'prescriptionReferenceAlphaId',
							this.invoiceRepository
						);

					// Update appointment details with prescription creation timestamp
					if (createInvoiceDto?.appointmentId) {
						try {
							const updatedTimestamp = new Date();
							if (queryRunner) {
								await queryRunner.manager.update(
									AppointmentDetailsEntity,
									{
										appointmentId:
											createInvoiceDto.appointmentId
									},
									{
										updatedAt: updatedTimestamp,
										prescriptionCreatedAt: updatedTimestamp
									}
								);
							} else {
								await this.appointmentDetailsEntity.update(
									{
										appointmentId:
											createInvoiceDto.appointmentId
									},
									{
										updatedAt: updatedTimestamp,
										prescriptionCreatedAt: updatedTimestamp
									}
								);
							}
						} catch (timestampError: any) {
							// Don't throw here as this is a non-critical update
						}
					}
				} catch (prescriptionIdError: any) {
					throw new HttpException(
						`Failed to generate prescription reference ID: ${prescriptionIdError.message}`,
						HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}

			// 9. Update invoice with reference IDs and file URLs (keys)
			const fileUrl: any = {};

			// Only add prescription file URL if there's prescription content
			if (alphaNumericPrescriptionReferenceId) {
				fileUrl.prescriptionFileKey = prescriptionFileKey;
			}

			try {
				if (queryRunner) {
					await queryRunner.manager.update(
						InvoiceEntity,
						savedInvoice.id,
						{
							referenceAlphaId: alphaNumericReferenceId,
							prescriptionReferenceAlphaId:
								alphaNumericPrescriptionReferenceId,
							invoiceAmount: createInvoiceDto.invoiceAmount || 0,
							fileUrl
						}
					);
				} else {
					await this.invoiceRepository.update(savedInvoice.id, {
						referenceAlphaId: alphaNumericReferenceId,
						prescriptionReferenceAlphaId:
							alphaNumericPrescriptionReferenceId,
						invoiceAmount: createInvoiceDto.invoiceAmount || 0,
						fileUrl
					});
				}
			} catch (updateError: any) {
				throw new HttpException(
					`Failed to update invoice with reference IDs: ${updateError.message}`,
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			// 10. Dispatch background tasks via SQS (only for non-transactional calls to avoid premature task execution)
			if (!queryRunner) {
				// 10.1 Reminder Processing Task
				if (
					createInvoiceDto.details &&
					createInvoiceDto.details.length > 0 &&
					!createInvoiceDto.appointmentId
				) {
					this.sqsService
						.sendMessage({
							queueKey: 'NidanaInvoiceTasks',
							messageBody: {
								data: {
									taskType: 'processReminders',
									patientId,
									clinicId:
										patientDetail?.clinic?.id || clinicId,
									brandId,
									invoiceDetails: createInvoiceDto.details,
									reminderStatus: ReminderStatus.PENDING
								}
							},
							deduplicationId: `reminder-${savedInvoice.id}-${Date.now()}`
						})
						.catch(error => {
							this.logger.error(
								'Failed to queue reminder processing task',
								{
									error,
									invoiceId: savedInvoice.id
								}
							);
						});
				}

				// 10.2 PDF Generation Task
				if (createInvoiceDto.invoiceType === EnumInvoiceType.Invoice) {
					// Prescription Generation
					this.sqsService
						.sendMessage({
							queueKey: 'NidanaInvoiceTasks',
							messageBody: {
								data: {
									appointmentId:
										createInvoiceDto.appointmentId,
									taskType: 'generatePrescriptionPdfs',
									patientId: patientDetail.id,
									invoiceId: savedInvoice.id,
									alphaNumericReferenceId,
									alphaNumericPrescriptionReferenceId,
									prescriptionFileKey,
									doctorName,
									licenseNumber,
									doctorId:
										appointmentDoctorResponse?.clinicUser
											?.user?.id || null,
									dischargeInstructions:
										createInvoiceDto.dischargeInstructions ||
										''
								}
							},
							deduplicationId: `pdf-invoice-${savedInvoice.id}-${Date.now()}`
						})
						.catch(error => {
							this.logger.error(
								'Failed to queue invoice/prescription generation task',
								{
									error,
									invoiceId: savedInvoice.id,
									prescriptionReferenceId:
										alphaNumericPrescriptionReferenceId
								}
							);
						});
				}

				// 10.3 Vaccination Task
				if (
					createInvoiceDto.details &&
					createInvoiceDto.invoiceType === EnumInvoiceType.Invoice &&
					createInvoiceDto.appointmentId
				) {
					const vaccination = createInvoiceDto?.details.filter(
						list => list.itemType === EnumPlanType.Vaccination
					);

					if (vaccination.length > 0) {
						this.sqsService
							.sendMessage({
								queueKey: 'NidanaInvoiceTasks',
								messageBody: {
									data: {
										taskType: 'generateVaccination',
										// Only include essential identifiers
										patientId: patientDetail.id,
										invoiceId: savedInvoice.id,
										// Add minimal doctor info
										doctorName,
										licenseNumber,
										// Remove digitalSignature, pass doctorId instead if available
										doctorId:
											appointmentDoctorResponse
												?.clinicUser?.user?.id || null
									}
								},
								deduplicationId: `vaccination-${savedInvoice.id}-${Date.now()}`
							})
							.catch(error => {
								this.logger.error(
									'Failed to queue vaccination task',
									{
										error,
										invoiceId: savedInvoice.id
									}
								);
							});
					}
				}

				// 10.4 Inventory Update Task
				this.sqsService
					.sendMessage({
						queueKey: 'NidanaInvoiceTasks',
						messageBody: {
							data: {
								taskType: 'updateInventory',
								// Only send essential data - the invoice ID
								invoiceId: savedInvoice.id
							}
						},
						deduplicationId: `inventory-${savedInvoice.id}-${Date.now()}`
					})
					.catch(error => {
						this.logger.error(
							'Failed to queue inventory update task',
							{
								error,
								invoiceId: savedInvoice.id
							}
						);
					});
			}

			// 11. For existing invoice updates
			if (
				invoiceItem &&
				createInvoiceDto.invoiceType !== EnumInvoiceType.Refund
			) {
				const { ...finalInvoiceItem } = createInvoiceDto;

				try {
					if (queryRunner) {
						await queryRunner.manager.update(
							InvoiceEntity,
							invoiceItem.id,
							{
								discount: finalInvoiceItem.discount,
								totalPrice: finalInvoiceItem.totalPrice,
								totalDiscount: finalInvoiceItem.totalDiscount,
								priceAfterDiscount:
									finalInvoiceItem.priceAfterDiscount,
								totalTax: finalInvoiceItem.totalTax,
								totalCredit: finalInvoiceItem.totalCredit,
								amountPayable: finalInvoiceItem.amountPayable,
								amountPaid: finalInvoiceItem.amountPaid,
								paymentMode: finalInvoiceItem.paymentMode,
								cartId: finalInvoiceItem.cartId,
								invoiceType: finalInvoiceItem.invoiceType,
								fileUrl: finalInvoiceItem.fileUrl,
								ownerId: finalInvoiceItem.ownerId,
								patientId: finalInvoiceItem.patientId,
								invoiceAmount:
									finalInvoiceItem.invoiceAmount || 0,
								status,
								balanceDue,
								paidAmount,
								updatedBy: userId
							}
						);

						// Fetch the updated invoice entity after the update
						const updatedInvoiceItem =
							await queryRunner.manager.findOne(InvoiceEntity, {
								where: { id: invoiceItem.id }
							});
						if (!updatedInvoiceItem) {
							throw new HttpException(
								'Failed to retrieve updated invoice',
								HttpStatus.INTERNAL_SERVER_ERROR
							);
						}

						return updatedInvoiceItem;
					} else {
						await this.invoiceRepository.update(invoiceItem.id, {
							discount: finalInvoiceItem.discount,
							totalPrice: finalInvoiceItem.totalPrice,
							totalDiscount: finalInvoiceItem.totalDiscount,
							priceAfterDiscount:
								finalInvoiceItem.priceAfterDiscount,
							totalTax: finalInvoiceItem.totalTax,
							totalCredit: finalInvoiceItem.totalCredit,
							amountPayable: finalInvoiceItem.amountPayable,
							amountPaid: finalInvoiceItem.amountPaid,
							paymentMode: finalInvoiceItem.paymentMode,
							cartId: finalInvoiceItem.cartId,
							invoiceType: finalInvoiceItem.invoiceType,
							fileUrl: finalInvoiceItem.fileUrl,
							ownerId: finalInvoiceItem.ownerId,
							patientId: finalInvoiceItem.patientId,
							invoiceAmount: finalInvoiceItem.invoiceAmount || 0,
							status,
							balanceDue,
							paidAmount,
							updatedBy: userId
						});

						// Fetch the updated invoice entity after the update
						const updatedInvoiceItem =
							await this.invoiceRepository.findOne({
								where: { id: invoiceItem.id }
							});
						if (!updatedInvoiceItem) {
							throw new HttpException(
								'Failed to retrieve updated invoice',
								HttpStatus.INTERNAL_SERVER_ERROR
							);
						}

						return updatedInvoiceItem;
					}
				} catch (updateExistingError: any) {
					throw new HttpException(
						`Failed to update existing invoice: ${updateExistingError.message}`,
						updateExistingError.status ||
							HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}

			// Return the updated invoice for transactional calls
			if (queryRunner) {
				try {
					const updatedInvoice = await queryRunner.manager.findOne(
						InvoiceEntity,
						{
							where: { id: savedInvoice.id }
						}
					);

					if (!updatedInvoice) {
						throw new HttpException(
							'Failed to retrieve created invoice',
							HttpStatus.INTERNAL_SERVER_ERROR
						);
					}

					return updatedInvoice;
				} catch (fetchError: any) {
					throw new HttpException(
						`Failed to fetch created invoice: ${fetchError.message}`,
						HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}

			return savedInvoice;
		} catch (err: any) {
			// Re-throw HttpExceptions as they are (preserve status and message)
			if (err instanceof HttpException) {
				throw err;
			}

			// For other errors, wrap them in a more descriptive HttpException
			throw new HttpException(
				`Invoice creation failed: ${err.message || 'Unknown error'}`,
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	/**
	 * Atomically create invoice with payment and complete appointment
	 * This ensures data consistency by using database transactions
	 */
	async createInvoiceWithPayment(
		createInvoiceWithPaymentDto: CreateInvoiceWithPaymentDto,
		clinicId: string,
		brandId: string,
		userId: string
	) {
		const queryRunner = this.dataSource.createQueryRunner();

		try {
			await queryRunner.connect();
			await queryRunner.startTransaction();

			// Step 1: Create invoice using the refactored method with transaction support
			const invoice = await this.createInvoice(
				createInvoiceWithPaymentDto.invoiceData,
				clinicId,
				brandId,
				userId,
				queryRunner
			);

			if (!invoice) {
				throw new HttpException(
					'Failed to create invoice - no invoice returned',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			// Step 2: Update appointment status to Completed (if appointmentId provided)
			let updatedAppointment = null;
			if (
				createInvoiceWithPaymentDto.appointmentId &&
				createInvoiceWithPaymentDto.invoiceData.invoiceType ===
					EnumInvoiceType.Invoice &&
				createInvoiceWithPaymentDto.invoiceData.impromptu !== true
			) {
				try {
					updatedAppointment =
						await this.updateAppointmentStatusInTransaction(
							queryRunner,
							createInvoiceWithPaymentDto.appointmentId,
							EnumAppointmentStatus.Completed,
							userId,
							invoice.id
						);
				} catch (appointmentError: any) {
					throw appointmentError;
				}
			}

			// Step 3: Create payment details within the same transaction (ATOMIC)
			// Validate that invoice has an ID before proceeding
			if (!invoice.id) {
				throw new HttpException(
					'Invoice creation failed - no ID generated',
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			let payments;
			try {
				payments =
					await this.paymentDetailsService.createPaymentDetailsWithTransaction(
						{
							...createInvoiceWithPaymentDto.paymentData,
							invoiceId: invoice.id
						},
						clinicId,
						brandId,
						userId,
						queryRunner
					);
			} catch (paymentError: any) {
				throw paymentError;
			}

			// Step 4: Create vaccination database entries within the same transaction (NEW)
			let vaccinationRecords: PatientVaccination[] = [];
			if (
				createInvoiceWithPaymentDto.invoiceData.details &&
				createInvoiceWithPaymentDto.invoiceData.invoiceType ===
					EnumInvoiceType.Invoice &&
				createInvoiceWithPaymentDto.invoiceData.appointmentId
			) {
				// Check if there are vaccination items in the invoice
				const vaccination =
					createInvoiceWithPaymentDto.invoiceData.details.filter(
						list => list.itemType === EnumPlanType.Vaccination
					);

				if (vaccination.length > 0) {
					try {
						vaccinationRecords =
							await this.createVaccinationRecordsInTransaction(
								createInvoiceWithPaymentDto.invoiceData,
								invoice,
								queryRunner
							);
					} catch (vaccinationError: any) {
						this.logger.error(
							'Error creating vaccination records in transaction',
							{
								invoiceId: invoice.id,
								error:
									vaccinationError?.message || 'Unknown error'
							}
						);
						throw vaccinationError;
					}
				}
			}

			// Step 5: Commit all operations together (invoice, appointment, payment, vaccinations)
			await queryRunner.commitTransaction();

			// Step 6: Execute background tasks AFTER successful transaction commit
			try {
				await this.executePostTransactionTasks(
					invoice,
					createInvoiceWithPaymentDto.invoiceData,
					clinicId,
					brandId,
					vaccinationRecords // Pass vaccination records for PDF generation
				);
			} catch (taskError: any) {
				// Log the error but don't fail the entire operation since the transaction is already committed
				this.logger.error(
					'Error executing post-transaction tasks (non-critical)',
					{
						invoiceId: invoice.id,
						taskError: taskError?.message || 'Unknown error'
					}
				);
			}

			return {
				status: true,
				data: {
					invoice,
					payments,
					appointment: updatedAppointment,
					vaccinationRecords
				}
			};
		} catch (error: any) {
			// Rollback transaction if it's still active
			try {
				if (queryRunner.isTransactionActive) {
					await queryRunner.rollbackTransaction();
				}
			} catch (rollbackError: any) {
				// Log rollback error but don't throw it to preserve original error
				this.logger.error('Failed to rollback transaction', {
					originalError: error?.message || 'Unknown error',
					rollbackError:
						rollbackError?.message || 'Unknown rollback error'
				});
			}

			throw new HttpException(
				error.message || 'Failed to create invoice with payment',
				error.status || HttpStatus.INTERNAL_SERVER_ERROR
			);
		} finally {
			// Always release the queryRunner, even if there were errors
			try {
				if (queryRunner) {
					await queryRunner.release();
				}
			} catch (releaseError: any) {
				// Log release error but don't throw it
				this.logger.error('Failed to release QueryRunner', {
					releaseError:
						releaseError?.message || 'Unknown release error'
				});
			}
		}
	}

	/**
	 * Create vaccination database records within a transaction
	 * This ensures vaccination records are created atomically with the invoice
	 */
	private async createVaccinationRecordsInTransaction(
		createInvoiceDto: CreateInvoiceDto,
		invoice: InvoiceEntity,
		queryRunner: QueryRunner
	): Promise<PatientVaccination[]> {
		const vaccinationRecords: PatientVaccination[] = [];

		try {
			// Filter vaccination items from invoice details
			const vaccination =
				createInvoiceDto.details?.filter(
					list => list.itemType === EnumPlanType.Vaccination
				) || [];

			if (vaccination.length === 0) {
				return vaccinationRecords;
			}

			this.logger.log('Creating vaccination records in transaction', {
				invoiceId: invoice.id,
				vaccinationCount: vaccination.length
			});

			// Get patient details for vaccination records
			const patientDetail = await this.patientService.getPatientDetails(
				createInvoiceDto.patientId!
			);

			// Get doctor information
			let doctorName = '';
			let licenseNumber = '';
			if (createInvoiceDto.appointmentId) {
				const appointmentDoctorResponse =
					await queryRunner.manager.findOne(
						AppointmentDoctorsEntity,
						{
							where: {
								appointmentId: createInvoiceDto.appointmentId,
								primary: true
							},
							relations: ['clinicUser', 'clinicUser.user']
						}
					);

				if (appointmentDoctorResponse?.clinicUser?.user) {
					licenseNumber =
						appointmentDoctorResponse.clinicUser.user
							.licenseNumber || '';
					doctorName =
						`${appointmentDoctorResponse.clinicUser.user.firstName || ''} ${appointmentDoctorResponse.clinicUser.user.lastName || ''}`.trim();
				}
			}

			// Get existing vaccination mappings to avoid duplicates
			const existingMappings =
				(invoice.metadata as any)?.cartToVaccinationMapping || {};
			const cartToVaccinationMapping: Record<string, string> = {
				...existingMappings
			};

			// Create vaccination records for each vaccination item
			for (let i = 0; i < vaccination.length; i++) {
				const cartItemId = vaccination[i]?.id;

				// Check if this vaccination has already been generated
				if (cartItemId && existingMappings[cartItemId]) {
					this.logger.log(
						'Skipping vaccination record creation - already exists in metadata',
						{
							invoiceId: invoice.id,
							cartItemId,
							vaccinationName: vaccination[i]?.name,
							existingPatientVaccinationId:
								existingMappings[cartItemId]
						}
					);
					continue;
				}

				// Generate unique vaccine ID
				const vaccineId = await generateUniqueCode(
					'vaccineId',
					this.patientVaccinationRepository
				);

				// Create vaccination record
				const vaccinationRecord = {
					patientId: createInvoiceDto.patientId!,
					systemGenerated: true,
					vaccinationDate: new Date(),
					vaccineName: vaccination[i]?.name || 'Vaccination',
					reportUrl: '', // Will be updated by SQS after PDF generation
					doctorName: doctorName,
					urlMeta: {
						fileType: 'PDF',
						fileName: `VAC_${vaccineId}.pdf`
					},
					vaccineId: vaccineId,
					appointmentId: createInvoiceDto.appointmentId,
					vaccinationId: vaccination[i]?.inventoryId // Use inventoryId which corresponds to clinic_vaccinations.id
				};

				// Save vaccination record within transaction
				const savedVaccinationRecord = await queryRunner.manager.save(
					PatientVaccination,
					queryRunner.manager.create(
						PatientVaccination,
						vaccinationRecord
					)
				);

				vaccinationRecords.push(savedVaccinationRecord);

				// Map cart item ID to vaccination record ID
				if (cartItemId && savedVaccinationRecord.id) {
					cartToVaccinationMapping[cartItemId] =
						savedVaccinationRecord.id;

					this.logger.log(
						'Successfully created vaccination record in transaction',
						{
							invoiceId: invoice.id,
							cartItemId,
							patientVaccinationId: savedVaccinationRecord.id,
							vaccinationName: vaccination[i]?.name
						}
					);
				}
			}

			// Update invoice metadata with vaccination mapping within transaction
			if (
				Object.keys(cartToVaccinationMapping).length >
				Object.keys(existingMappings).length
			) {
				const currentMetadata = (invoice.metadata as any) || {};
				const updatedMetadata = {
					...currentMetadata,
					cartToVaccinationMapping
				};

				await queryRunner.manager.update(InvoiceEntity, invoice.id, {
					metadata: updatedMetadata
				});

				this.logger.log(
					'Updated invoice metadata with vaccination mappings in transaction',
					{
						invoiceId: invoice.id,
						totalMappings: Object.keys(cartToVaccinationMapping)
							.length,
						newMappings:
							Object.keys(cartToVaccinationMapping).length -
							Object.keys(existingMappings).length
					}
				);
			}

			return vaccinationRecords;
		} catch (error) {
			this.logger.error(
				'Error creating vaccination records in transaction',
				{
					invoiceId: invoice.id,
					error
				}
			);
			throw error;
		}
	}

	/**
	 * Create vaccination database records for newly added vaccination items in invoice update
	 * This ensures vaccination records are created atomically with the invoice update
	 */
	private async createVaccinationRecordsForNewItems(
		originalDetails: any[],
		finalDetails: any[],
		invoice: InvoiceEntity,
		entityManager: EntityManager
	): Promise<PatientVaccination[]> {
		const vaccinationRecords: PatientVaccination[] = [];

		try {
			// Find newly added vaccination items
			const newlyAddedItems = this.findNewlyAddedItems(
				originalDetails,
				finalDetails
			);
			const newVaccinationItems = newlyAddedItems.filter(
				item => item.itemType === EnumPlanType.Vaccination
			);

			if (newVaccinationItems.length === 0) {
				return vaccinationRecords;
			}

			this.logger.log(
				'Creating vaccination records for newly added items in update',
				{
					invoiceId: invoice.id,
					newVaccinationCount: newVaccinationItems.length
				}
			);

			// Get patient details for vaccination records
			const patientDetail = await this.patientService.getPatientDetails(
				invoice.patientId
			);

			// Get doctor information
			let doctorName = '';
			let licenseNumber = '';
			if (invoice.cart?.appointmentId) {
				const appointmentDoctorResponse = await entityManager.findOne(
					AppointmentDoctorsEntity,
					{
						where: {
							appointmentId: invoice.cart.appointmentId,
							primary: true
						},
						relations: ['clinicUser', 'clinicUser.user']
					}
				);

				if (appointmentDoctorResponse?.clinicUser?.user) {
					licenseNumber =
						appointmentDoctorResponse.clinicUser.user
							.licenseNumber || '';
					doctorName =
						`${appointmentDoctorResponse.clinicUser.user.firstName || ''} ${appointmentDoctorResponse.clinicUser.user.lastName || ''}`.trim();
				}
			}

			// Get existing vaccination mappings to avoid duplicates
			const existingMappings =
				(invoice.metadata as any)?.cartToVaccinationMapping || {};
			const cartToVaccinationMapping: Record<string, string> = {
				...existingMappings
			};

			// Create vaccination records for each newly added vaccination item
			for (let i = 0; i < newVaccinationItems.length; i++) {
				const vaccinationItem = newVaccinationItems[i];
				const cartItemId = vaccinationItem?.id;

				// Check if this vaccination has already been generated
				if (cartItemId && existingMappings[cartItemId]) {
					this.logger.log(
						'Skipping vaccination record creation - already exists in metadata',
						{
							invoiceId: invoice.id,
							cartItemId,
							vaccinationName: vaccinationItem?.name,
							existingPatientVaccinationId:
								existingMappings[cartItemId]
						}
					);
					continue;
				}

				// Generate unique vaccine ID
				const vaccineId = await generateUniqueCode(
					'vaccineId',
					this.patientVaccinationRepository
				);

				// Create vaccination record
				const vaccinationRecord = {
					patientId: invoice.patientId,
					systemGenerated: true,
					vaccinationDate: new Date(),
					vaccineName: vaccinationItem?.name || 'Vaccination',
					reportUrl: '', // Will be updated by SQS after PDF generation
					doctorName: doctorName,
					urlMeta: {
						fileType: 'PDF',
						fileName: `VAC_${vaccineId}.pdf`
					},
					vaccineId: vaccineId,
					appointmentId: invoice.cart?.appointmentId,
					vaccinationId: vaccinationItem?.inventoryId // Use inventoryId which corresponds to clinic_vaccinations.id
				};

				// Save vaccination record within transaction
				const savedVaccinationRecord = await entityManager.save(
					PatientVaccination,
					entityManager.create(PatientVaccination, vaccinationRecord)
				);

				vaccinationRecords.push(savedVaccinationRecord);

				// Map cart item ID to vaccination record ID
				if (cartItemId && savedVaccinationRecord.id) {
					cartToVaccinationMapping[cartItemId] =
						savedVaccinationRecord.id;

					this.logger.log(
						'Successfully created vaccination record for newly added item in update',
						{
							invoiceId: invoice.id,
							cartItemId,
							patientVaccinationId: savedVaccinationRecord.id,
							vaccinationName: vaccinationItem?.name
						}
					);
				}
			}

			// Update invoice metadata with vaccination mapping within transaction
			if (
				Object.keys(cartToVaccinationMapping).length >
				Object.keys(existingMappings).length
			) {
				const currentMetadata = (invoice.metadata as any) || {};
				const updatedMetadata = {
					...currentMetadata,
					cartToVaccinationMapping
				};

				await entityManager.update(InvoiceEntity, invoice.id, {
					metadata: updatedMetadata
				});

				this.logger.log(
					'Updated invoice metadata with new vaccination mappings in update transaction',
					{
						invoiceId: invoice.id,
						totalMappings: Object.keys(cartToVaccinationMapping)
							.length,
						newMappings:
							Object.keys(cartToVaccinationMapping).length -
							Object.keys(existingMappings).length
					}
				);
			}

			return vaccinationRecords;
		} catch (error) {
			this.logger.error(
				'Error creating vaccination records for newly added items in update transaction',
				{
					invoiceId: invoice.id,
					error
				}
			);
			throw error;
		}
	}

	/**
	 * Execute background tasks after successful transaction commit
	 * This ensures all necessary business processes are triggered for atomic operations
	 */
	private async executePostTransactionTasks(
		invoice: InvoiceEntity,
		createInvoiceDto: CreateInvoiceDto,
		clinicId: string,
		brandId: string,
		vaccinationRecords?: PatientVaccination[]
	): Promise<void> {
		try {
			// Get patient details for tasks
			const patientDetail = await this.patientService.getPatientDetails(
				createInvoiceDto.patientId!
			);

			// Get doctor information if appointment exists
			let appointmentDoctorResponse = null;
			let doctorName = '';
			let licenseNumber = '';

			if (createInvoiceDto.appointmentId) {
				appointmentDoctorResponse =
					await this.appointmentDoctorsEntity.findOne({
						where: {
							appointmentId: createInvoiceDto.appointmentId,
							primary: true
						},
						relations: [
							'clinicUser',
							'clinicUser.user',
							'clinicUser.clinic.brand'
						]
					});

				if (appointmentDoctorResponse?.clinicUser?.user) {
					licenseNumber =
						appointmentDoctorResponse.clinicUser.user
							.licenseNumber || '';
					doctorName =
						`${appointmentDoctorResponse.clinicUser.user.firstName || ''} ${appointmentDoctorResponse.clinicUser.user.lastName || ''}`.trim();
				}
			}

			// Task 1: Reminder Processing
			if (
				createInvoiceDto.details &&
				createInvoiceDto.details.length > 0 &&
				createInvoiceDto.impromptu
			) {
				this.sqsService
					.sendMessage({
						queueKey: 'NidanaInvoiceTasks',
						messageBody: {
							data: {
								taskType: 'processReminders',
								patientId: createInvoiceDto.patientId,
								clinicId: patientDetail?.clinic?.id || clinicId,
								brandId,
								invoiceDetails: createInvoiceDto.details,
								reminderStatus: ReminderStatus.PENDING,
								invoiceId: invoice.id,
								appointmentId: createInvoiceDto.appointmentId
							}
						},
						deduplicationId: `reminder-${invoice.id}-${Date.now()}`
					})
					.catch(error => {
						this.logger.error(
							'Failed to queue reminder processing task in post-transaction',
							{
								error,
								invoiceId: invoice.id
							}
						);
					});
			}

			// Task 2: PDF Generation
			if (
				createInvoiceDto.invoiceType === EnumInvoiceType.Invoice &&
				createInvoiceDto.impromptu !== true
			) {
				this.sqsService
					.sendMessage({
						queueKey: 'NidanaInvoiceTasks',
						messageBody: {
							data: {
								appointmentId: createInvoiceDto.appointmentId,
								taskType: 'generatePrescriptionPdfs',
								patientId: patientDetail.id,
								invoiceId: invoice.id,
								alphaNumericReferenceId:
									invoice.referenceAlphaId,
								alphaNumericPrescriptionReferenceId:
									invoice.prescriptionReferenceAlphaId,
								prescriptionFileKey: (invoice.fileUrl as any)
									?.prescriptionFileKey,
								doctorName,
								licenseNumber,
								doctorId:
									appointmentDoctorResponse?.clinicUser?.user
										?.id || null,
								dischargeInstructions:
									createInvoiceDto.dischargeInstructions || ''
							}
						},
						deduplicationId: `pdf-invoice-${invoice.id}-${Date.now()}`
					})
					.catch(error => {
						this.logger.error(
							'Failed to queue PDF generation task in post-transaction',
							{
								error,
								invoiceId: invoice.id
							}
						);
					});
			}

			// Task 3: Vaccination PDF Generation (only if vaccination records were created)
			if (vaccinationRecords && vaccinationRecords.length > 0) {
				// Send vaccination record IDs to SQS for PDF generation and database updates
				const vaccinationRecordIds = vaccinationRecords.map(
					record => record.id
				);

				this.sqsService
					.sendMessage({
						queueKey: 'NidanaInvoiceTasks',
						messageBody: {
							data: {
								taskType: 'generateVaccinationPdfs',
								patientId: patientDetail.id,
								invoiceId: invoice.id,
								vaccinationRecordIds, // Pass the created vaccination record IDs
								doctorName,
								licenseNumber,
								doctorId:
									appointmentDoctorResponse?.clinicUser?.user
										?.id || null
							}
						},
						deduplicationId: `vaccination-pdf-${invoice.id}-${Date.now()}`
					})
					.catch(error => {
						this.logger.error(
							'Failed to queue vaccination PDF generation task in post-transaction',
							{
								error,
								invoiceId: invoice.id,
								vaccinationRecordIds
							}
						);
					});
			}

			// Task 4: Inventory Update
			this.sqsService
				.sendMessage({
					queueKey: 'NidanaInvoiceTasks',
					messageBody: {
						data: {
							taskType: 'updateInventory',
							invoiceId: invoice.id
						}
					},
					deduplicationId: `inventory-${invoice.id}-${Date.now()}`
				})
				.catch(error => {
					this.logger.error(
						'Failed to queue inventory update task in post-transaction',
						{
							error,
							invoiceId: invoice.id
						}
					);
				});
		} catch (error) {
			this.logger.error('Error in executePostTransactionTasks', {
				error,
				invoiceId: invoice.id
			});
			throw error;
		}
	}

	/**
	 * Execute background tasks after successful invoice update transaction commit
	 * This ensures all necessary business processes are triggered for invoice updates
	 */
	private async executePostUpdateTransactionTasks(
		updatedInvoice: InvoiceEntity,
		originalDetails: any[],
		finalDetails: any[],
		userId: string,
		invoice: InvoiceEntity,
		newVaccinationRecords?: PatientVaccination[]
	): Promise<void> {
		try {
			this.logger.log('Executing post-update transaction tasks', {
				invoiceId: updatedInvoice.id,
				originalItemsCount: originalDetails.length,
				finalItemsCount: finalDetails.length
			});

			// 0. Handle line item changes (vaccinations and reminders for removed items)
			if (finalDetails !== undefined) {
				await this.handleLineItemChangesWithOriginalDetails(
					originalDetails,
					finalDetails,
					invoice
				);
			}

			// Task 1: Queue inventory adjustments (asynchronous)
			if (finalDetails.length > 0) {
				await this.queueInventoryAdjustments(
					{ details: originalDetails, id: updatedInvoice.id },
					finalDetails,
					userId
				);
			}

			// Task 2: Queue vaccination PDF generation for newly created vaccination records and reminder processing (asynchronous)
			if (finalDetails.length > 0) {
				await this.queueNewItemProcessingWithVaccinationRecords(
					originalDetails,
					finalDetails,
					updatedInvoice,
					newVaccinationRecords || []
				);
			}

			this.logger.log('Post-update transaction tasks completed', {
				invoiceId: updatedInvoice.id
			});
		} catch (error) {
			this.logger.error('Error in executePostUpdateTransactionTasks', {
				error,
				invoiceId: updatedInvoice.id
			});
			throw error;
		}
	}

	private async updateAppointmentStatusInTransaction(
		queryRunner: QueryRunner,
		appointmentId: string,
		status: EnumAppointmentStatus,
		userId: string,
		invoiceId?: string
	): Promise<AppointmentEntity> {
		// Check current status first to avoid unnecessary processing
		const appointment = await queryRunner.manager.findOne(
			AppointmentEntity,
			{
				where: { id: appointmentId },
				relations: ['patient']
			}
		);

		if (!appointment) {
			throw new HttpException(
				`Appointment with ID "${appointmentId}" not found`,
				HttpStatus.NOT_FOUND
			);
		}

		const currentStatus =
			appointment.status || EnumAppointmentStatus.Scheduled;

		// Allow completion for both Scheduled and Completed appointments
		// This handles cases where:
		// 1. Auto-created appointments for invoicing (Scheduled -> Completed)
		// 2. Already completed appointments (skip the update gracefully)
		if (currentStatus === EnumAppointmentStatus.Completed) {
			return appointment; // Return the existing appointment without error
		}

		// Use the appointment service's updateAppointmentStatus method for proper business logic
		// Note: This will run outside the current transaction as the appointment service
		// has its own transaction management and business logic (reminders, EMR, etc.)
		try {
			const updatedAppointment =
				await this.appointmentsService.updateAppointmentStatus(
					appointmentId,
					{
						status,
						userId
					},
					invoiceId
				);

			this.logger.log(
				'Appointment status updated via appointment service',
				{
					appointmentId,
					previousStatus: currentStatus,
					newStatus: status,
					userId
				}
			);

			return updatedAppointment;
		} catch (error: any) {
			this.logger.error(
				'Error updating appointment status via appointment service',
				{
					appointmentId,
					status,
					userId,
					error: error.message
				}
			);
			throw new HttpException(
				`Failed to update appointment status: ${error.message}`,
				error.status || HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	async findInvoice(cartId: string) {
		const response = await this.invoiceRepository.findOne({
			where: {
				cartId,
				invoiceType: EnumInvoiceType.Invoice
			}
		});

		return response;
	}

	/**
	 * Update an existing invoice (only allowed for pending invoices)
	 * @param invoiceId ID of the invoice to update
	 * @param updateInvoiceDto Data to update
	 * @param userId ID of the user making the update
	 * @returns Updated invoice entity
	 */
	async updateInvoice(
		invoiceId: string,
		updateInvoiceDto: UpdateInvoiceDto,
		userId: string
	): Promise<InvoiceEntity> {
		// Variables to store data for post-transaction tasks
		let originalDetails: any[] = [];
		let finalDetails: any[] = [];
		let updatedInvoice: InvoiceEntity | undefined;
		let newVaccinationRecords: PatientVaccination[] = [];

		// Execute the main transaction
		const transactionResult = await this.dataSource.transaction(
			async (entityManager: EntityManager) => {
				try {
					this.logger.log('Update invoice service called', {
						invoiceId,
						updateInvoiceDto,
						userId
					});

					// 1. Fetch the invoice by ID with cart relation
					const invoice = await entityManager.findOne(InvoiceEntity, {
						where: { id: invoiceId, deletedAt: IsNull() },
						relations: ['cart']
					});

					if (!invoice) {
						throw new HttpException(
							'Invoice not found',
							HttpStatus.NOT_FOUND
						);
					}

					// 2. Check if invoice status is 'pending'
					if (invoice.status !== EnumInvoiceStatus.PENDING) {
						throw new HttpException(
							'Only pending invoices can be updated',
							HttpStatus.FORBIDDEN
						);
					}

					// 3. Check if invoice type is not 'Refund'
					if (invoice.invoiceType === EnumInvoiceType.Refund) {
						throw new HttpException(
							'Refund invoices cannot be updated',
							HttpStatus.FORBIDDEN
						);
					}

					// 3. Capture 'before' state for audit logging
					const beforeState: Record<string, any> = {};
					const fieldsToTrack = [
						'discount',
						'totalPrice',
						'totalDiscount',
						'priceAfterDiscount',
						'totalTax',
						'totalCredit',
						'amountPayable',
						'invoiceAmount',
						'paymentMode',
						'details',
						'metadata'
					];

					fieldsToTrack.forEach(field => {
						if ((invoice as any)[field] !== undefined) {
							beforeState[field] = (invoice as any)[field];
						}
					});

					// 4. Handle line item changes will be done after cart item creation

					// 5. Apply changes from UpdateInvoiceDto
					const updateData: Partial<InvoiceEntity> = {
						updatedBy: userId
					};

					if (updateInvoiceDto.discount !== undefined) {
						updateData.discount = updateInvoiceDto.discount;
					}
					if (updateInvoiceDto.totalPrice !== undefined) {
						updateData.totalPrice = updateInvoiceDto.totalPrice;
					}
					if (updateInvoiceDto.totalDiscount !== undefined) {
						updateData.totalDiscount =
							updateInvoiceDto.totalDiscount;
					}
					if (updateInvoiceDto.priceAfterDiscount !== undefined) {
						updateData.priceAfterDiscount =
							updateInvoiceDto.priceAfterDiscount;
					}
					if (updateInvoiceDto.totalTax !== undefined) {
						updateData.totalTax = updateInvoiceDto.totalTax;
					}
					if (updateInvoiceDto.totalCredit !== undefined) {
						updateData.totalCredit = updateInvoiceDto.totalCredit;
					}
					if (updateInvoiceDto.amountPayable !== undefined) {
						updateData.amountPayable =
							updateInvoiceDto.amountPayable;
						// Update balance due when amount payable changes
						updateData.balanceDue =
							updateInvoiceDto.amountPayable -
							(invoice.paidAmount || 0);
					}
					if (updateInvoiceDto.invoiceAmount !== undefined) {
						updateData.invoiceAmount =
							updateInvoiceDto.invoiceAmount;
					}
					if (updateInvoiceDto.paymentMode !== undefined) {
						updateData.paymentMode = updateInvoiceDto.paymentMode;
					}
					if (updateInvoiceDto.metadata !== undefined) {
						updateData.metadata = updateInvoiceDto.metadata;
					}
					// 5.1. Capture original details BEFORE invoice update for comparison
					originalDetails = (invoice.details as any[]) || [];

					// // 5.2. Create cart items for newly added items and update their IDs
					finalDetails = updateInvoiceDto.details || [];
					updateData.details = finalDetails;

					// 5.3. Create vaccination records for newly added vaccination items within transaction
					if (
						originalDetails &&
						finalDetails &&
						invoice.cart?.appointmentId
					) {
						try {
							newVaccinationRecords =
								await this.createVaccinationRecordsForNewItems(
									originalDetails,
									finalDetails,
									invoice,
									entityManager
								);
						} catch (vaccinationError: any) {
							this.logger.error(
								'Error creating vaccination records for new items in update transaction',
								{
									invoiceId: invoice.id,
									error:
										vaccinationError?.message ||
										'Unknown error'
								}
							);
							throw vaccinationError;
						}
					}

					// 5.4. Merge the updated metadata after vaccination record creation
					// This ensures we preserve any vaccination mappings that were added
					// while also preserving any metadata updates from the request
					if (newVaccinationRecords.length > 0) {
						const updatedInvoiceWithMetadata =
							await entityManager.findOne(InvoiceEntity, {
								where: { id: invoiceId }
							});
						if (updatedInvoiceWithMetadata?.metadata) {
							// Merge the vaccination mappings with any user-provided metadata
							const vaccinationMetadata =
								updatedInvoiceWithMetadata.metadata;
							const userProvidedMetadata =
								updateInvoiceDto.metadata || {};

							// Preserve vaccination mappings and merge with user metadata
							updateData.metadata = {
								...vaccinationMetadata,
								...userProvidedMetadata,
								// Ensure vaccination mappings are not overwritten
								cartToVaccinationMapping:
									(vaccinationMetadata as any)
										?.cartToVaccinationMapping || {}
							};
						}
					}

					// 6. Save the updated invoice
					await entityManager.update(
						InvoiceEntity,
						invoiceId,
						updateData
					);

					// 7. Fetch the updated invoice
					const fetchedUpdatedInvoice = await entityManager.findOne(
						InvoiceEntity,
						{
							where: { id: invoiceId },
							relations: ['cart']
						}
					);

					if (!fetchedUpdatedInvoice) {
						throw new HttpException(
							'Failed to fetch updated invoice',
							HttpStatus.INTERNAL_SERVER_ERROR
						);
					}

					// Store for post-transaction tasks
					updatedInvoice = fetchedUpdatedInvoice;

					// 8. Capture 'after' state and log audit
					const afterState: Record<string, any> = {};
					fieldsToTrack.forEach(field => {
						if ((updatedInvoice as any)[field] !== undefined) {
							afterState[field] = (updatedInvoice as any)[field];
						}
					});

					const changeSummary = this.generateChangeSummary(
						beforeState,
						afterState
					);
					if (changeSummary) {
						// Create a queryRunner-like object from entityManager for audit logging
						const queryRunnerForAudit = {
							manager: entityManager
						} as QueryRunner;

						await this.logChange(
							invoiceId,
							userId,
							AuditLogOperationType.UPDATE,
							{ before: beforeState, after: afterState },
							changeSummary,
							queryRunnerForAudit
						);
					}

					this.logger.log(
						'Invoice updated successfully in transaction',
						{
							invoiceId,
							changeSummary
						}
					);

					return updatedInvoice;
				} catch (error) {
					this.logger.error('Invoice update error in transaction', {
						error,
						invoiceId,
						updateInvoiceDto
					});

					if (error instanceof HttpException) {
						throw error;
					}

					throw new HttpException(
						'Failed to update invoice',
						HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}
		);

		// Execute background tasks AFTER successful transaction commit
		if (updatedInvoice) {
			try {
				const invoice = await this.invoiceRepository.findOne({
					where: { id: invoiceId },
					relations: ['cart']
				});
				if (!invoice) {
					throw new HttpException(
						'Invoice not found',
						HttpStatus.NOT_FOUND
					);
				}
				// Pass the vaccination records that were created in the transaction for PDF generation
				await this.executePostUpdateTransactionTasks(
					updatedInvoice,
					originalDetails,
					finalDetails,
					userId,
					invoice,
					newVaccinationRecords // Pass the actual vaccination records created in transaction
				);
			} catch (taskError: any) {
				// Log the error but don't fail the entire operation since the transaction is already committed
				this.logger.error(
					'Error executing post-update transaction tasks (non-critical)',
					{
						invoiceId: updatedInvoice.id,
						taskError: taskError?.message || 'Unknown error'
					}
				);
			}

			this.logger.log('Invoice update completed successfully', {
				invoiceId: updatedInvoice.id
			});
		}

		return transactionResult;
	}

	/**
	 * Soft delete an invoice and related entities (only allowed for pending invoices)
	 * @param invoiceId ID of the invoice to delete
	 * @param userId ID of the user performing the deletion
	 * @param reason Optional reason for deletion
	 * @returns Success message
	 */
	async deleteInvoice(
		invoiceId: string,
		userId: string,
		reason?: string
	): Promise<{ message: string }> {
		return await this.dataSource.transaction(
			async (entityManager: EntityManager) => {
				try {
					this.logger.log('Delete invoice service called', {
						invoiceId,
						userId
					});

					// 1. Fetch the invoice by ID with all necessary relations
					const invoice = await entityManager.findOne(InvoiceEntity, {
						where: { id: invoiceId },
						relations: ['cart', 'paymentDetail']
					});

					if (!invoice) {
						throw new HttpException(
							'Invoice not found',
							HttpStatus.NOT_FOUND
						);
					}

					// 2. Check if invoice status is 'pending'
					if (invoice.status !== EnumInvoiceStatus.PENDING) {
						throw new HttpException(
							'Only pending invoices can be deleted',
							HttpStatus.FORBIDDEN
						);
					}

					// 3. Check if invoice type is not 'Refund'
					if (invoice.invoiceType === EnumInvoiceType.Refund) {
						throw new HttpException(
							'Refund invoices cannot be deleted',
							HttpStatus.FORBIDDEN
						);
					}

					this.logger.log('Starting invoice deletion process', {
						invoiceId,
						hasCart: !!invoice.cart,
						hasPaymentDetail: !!invoice.paymentDetail,
						invoiceDetails: invoice.details
							? (invoice.details as any[]).length
							: 0
					});

					// 3. Handle vaccination and lab report management (mark as removed from invoice)
					// Use the same specific approach as edit invoice to target exact vaccinations/lab reports
					if (invoice.details && Array.isArray(invoice.details)) {
						for (const item of invoice.details as any[]) {
							if (item.itemType === 'Vaccination') {
								// Use metadata mapping to find specific patient vaccination ID
								let patientVaccinationId: string | null = null;

								if (
									invoice.metadata &&
									invoice.metadata.cartToVaccinationMapping
								) {
									patientVaccinationId =
										invoice.metadata
											.cartToVaccinationMapping[item.id];
								}

								if (patientVaccinationId) {
									this.logger.log(
										'Marking specific patient vaccination as removed from invoice during deletion',
										{
											invoiceId: invoice.id,
											cartItemId: item.id,
											patientVaccinationId,
											itemName: item.name
										}
									);
									await this.patientVaccination.markAsRemovedFromInvoiceByPatientVaccinationId(
										patientVaccinationId,
										entityManager
									);
								} else {
									this.logger.warn(
										'Patient vaccination ID not found in metadata mapping during deletion',
										{
											invoiceId: invoice.id,
											cartItemId: item.id,
											itemName: item.name,
											hasMetadata: !!invoice.metadata,
											hasMapping:
												!!invoice.metadata
													?.cartToVaccinationMapping
										}
									);
								}
							} else if (
								item.itemType === 'Diagnostic' ||
								item.itemType === 'Labreport'
							) {
								// Use metadata mapping to find specific lab report ID
								let labReportId: string | null = null;

								if (
									invoice.metadata &&
									invoice.metadata.cartToLabReportMapping
								) {
									labReportId =
										invoice.metadata.cartToLabReportMapping[
											item.id
										];
								}

								if (
									labReportId &&
									invoice.cart?.appointmentId
								) {
									this.logger.log(
										'Marking specific lab report as removed from invoice during deletion',
										{
											invoiceId: invoice.id,
											cartItemId: item.id,
											labReportId,
											itemName: item.name,
											appointmentId:
												invoice.cart.appointmentId
										}
									);
									await this.clinicLabReportService.markAsRemovedFromInvoiceByAppointmentAndLabReportId(
										invoice.cart.appointmentId,
										labReportId,
										entityManager
									);
								} else {
									this.logger.warn(
										'Lab report ID not found in metadata mapping during deletion',
										{
											invoiceId: invoice.id,
											cartItemId: item.id,
											itemName: item.name,
											hasMetadata: !!invoice.metadata,
											hasMapping:
												!!invoice.metadata
													?.cartToLabReportMapping,
											appointmentId:
												invoice.cart?.appointmentId
										}
									);
								}
							}
						}
					}

					// 5. Handle inventory reversion (revert stock for all items)
					if (invoice.details && Array.isArray(invoice.details)) {
						await this.queueInventoryReversion(invoice, userId);
					}

					// 6. Soft delete related payment details if they exist
					if (invoice.paymentDetail) {
						await entityManager.softDelete(
							PaymentDetailsEntity,
							invoice.paymentDetail.id
						);
					}
					// 4. Handle reminder management
					// For impromptu appointments: undo reminder completions that were completed by this appointment
					if (invoice.cart?.appointmentId) {
						await this.patientRemindersService.undoCompletionsByAppointmentId(
							invoice.cart.appointmentId,
							entityManager
						);
						// Hard delete reminders that were created from this invoice
						await this.patientRemindersService.hardDeleteByInvoiceId(
							invoiceId,
							entityManager
						);
						await entityManager.softDelete(
							CartEntity,
							invoice.cart.id
						); // true = soft delete
					}

					// 8. Get user information for cancellation metadata
					const cancellationUser = await entityManager.findOne(User, {
						where: { id: userId },
						select: ['id', 'firstName', 'lastName']
					});

					const cancellationUserName = cancellationUser
						? `${cancellationUser.firstName || ''} ${cancellationUser.lastName || ''}`.trim()
						: 'Unknown User';

					// 9. Calculate cancelled amount (total invoice amount)
					const cancelledAmount = invoice.invoiceAmount || 0;

					// 10. Update invoice metadata with cancellation information
					const currentMetadata = (invoice.metadata as any) || {};
					const updatedMetadata = {
						...currentMetadata,
						cancellation: {
							amount: cancelledAmount,
							date: new Date().toISOString(),
							reason: reason || 'No reason provided',
							by: cancellationUserName,
							userId: userId
						}
					};

					// 11. Update invoice status, balances, and metadata
					await entityManager.update(InvoiceEntity, invoiceId, {
						status: EnumInvoiceStatus.CANCELLED,
						balanceDue: 0,
						paidAmount: 0,
						metadata: updatedMetadata,
						updatedAt: new Date()
					});

					// 9. Log the cancellation in audit trail
					// Create a queryRunner-like object from entityManager for audit logging
					const queryRunnerForAudit = {
						manager: entityManager
					} as QueryRunner;

					await this.logChange(
						invoiceId,
						userId,
						AuditLogOperationType.DELETE,
						{
							before: {},
							after: { metadata: updatedMetadata }
						},
						'Invoice cancelled',
						queryRunnerForAudit,
						reason
					);

					this.logger.log('Invoice deleted successfully', {
						invoiceId,
						userId
					});

					return { message: 'Invoice deleted successfully' };
				} catch (error) {
					this.logger.error('Invoice deletion error', {
						error,
						invoiceId,
						userId
					});

					if (error instanceof HttpException) {
						throw error;
					}

					throw new HttpException(
						'Failed to delete invoice',
						HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}
		);
	}

	/**
	 * Handle invoice document operations (share or download)
	 * @param referenceAlphaId Reference alpha ID for the invoice
	 * @param action Whether to share or download
	 * @param shareMethod Method to use for sharing (if applicable)
	 * @param brandId Brand ID
	 * @param userId User ID
	 * @param recipient Recipient of the document (client or custom)
	 * @param email Custom email address for sharing
	 * @param phoneNumber Custom phone number for sharing
	 * @returns Response with status and data
	 */
	async handleInvoiceDocument(
		referenceAlphaId: string,
		action: 'share' | 'download',
		shareMethod: 'email' | 'whatsapp' | 'both' | undefined,
		brandId: string,
		userId: string,
		recipient: 'client' | 'other' | undefined = 'client',
		email?: string,
		phoneNumber?: string
	) {
		this.logger.log('Invoice service: handleInvoiceDocument called', {
			referenceAlphaId,
			action,
			shareMethod,
			brandId,
			userId,
			recipient,
			email,
			phoneNumber
		});

		try {
			// Find the invoice by reference alpha ID and brandId
			const invoice = await this.invoiceRepository.findOne({
				where: {
					referenceAlphaId,
					brandId
				}
			});

			if (!invoice) {
				this.logger.error('Invoice not found', {
					referenceAlphaId,
					brandId
				});
				throw new HttpException(
					'Invoice not found',
					HttpStatus.NOT_FOUND
				);
			}

			// Get patient details (needed for validation)
			const patientDetails = await this.patientService.getPatientDetails(
				invoice.patientId
			);
			if (!patientDetails) {
				throw new HttpException(
					'Patient details not found',
					HttpStatus.NOT_FOUND
				);
			}

			// Check if we already have a file key in the invoice.fileUrl
			const isInvoice = invoice.invoiceType === EnumInvoiceType.Invoice;
			const fileUrl = (invoice.fileUrl as Record<string, string>) || {};
			const existingFileKey = isInvoice
				? fileUrl.invoiceFileKey
				: fileUrl.creditNoteFileKey;

			// Check if we have an existing permanent file (not a temporary one)
			// Permanent files start with 'invoice/' for invoices or 'creditNote/' for credit notes
			const hasPermanentFile =
				existingFileKey &&
				((isInvoice && existingFileKey.startsWith('invoice/')) ||
					(!isInvoice && existingFileKey.startsWith('creditNote/')));

			// Check if we have a temporary file
			// Temporary files start with 'invoices_temp/' for invoices or 'credit_notes_temp/' for credit notes
			const hasTempFile =
				existingFileKey &&
				((isInvoice && existingFileKey.startsWith('invoices_temp/')) ||
					(!isInvoice &&
						existingFileKey.startsWith('credit_notes_temp/')));

			this.logger.log('File key analysis in invoice service', {
				referenceAlphaId,
				invoiceId: invoice.id,
				existingFileKey,
				hasPermanentFile,
				hasTempFile,
				isInvoice,
				action
			});

			// If a temporary file exists, delete it from S3 and remove the reference from the DB
			if (hasTempFile) {
				this.logger.log('Removing temporary file before proceeding', {
					fileKey: existingFileKey,
					invoiceId: invoice.id
				});

				// Handle temp file deletion before proceeding
				const updatedFileUrl = await this.handleTemporaryFile(
					invoice.id,
					existingFileKey,
					isInvoice,
					fileUrl
				);

				// Update local fileUrl variable with the cleaned version
				Object.assign(fileUrl, updatedFileUrl);
			}

			// For download requests, check if the file is already generated
			if (action === 'download') {
				// First check if we have a permanent file to return immediately
				if (hasPermanentFile) {
					this.logger.log(
						'Using existing permanent file for download',
						{
							fileKey: existingFileKey,
							invoiceId: invoice.id
						}
					);

					// Return the existing permanent file download URL
					try {
						const downloadUrl =
							await this.s3Service.getDownloadPreSignedUrl(
								existingFileKey,
								null
							);

						return {
							status: 'success',
							data: {
								downloadUrl,
								fileName: `${patientDetails.patientName}_${invoice.referenceAlphaId}.pdf`
							}
						};
					} catch (error) {
						this.logger.error(
							'Error generating download URL for permanent file',
							{
								error,
								fileKey: existingFileKey,
								invoiceId: invoice.id
							}
						);

						throw new HttpException(
							'Failed to generate download URL',
							HttpStatus.INTERNAL_SERVER_ERROR
						);
					}
				} else {
					// No existing file, queue task to generate a new one
					this.logger.log(
						'No existing file, queueing task to generate new file',
						{
							invoiceId: invoice.id,
							action
						}
					);

					await this.queueInvoiceDocumentTask(
						invoice.id,
						action,
						shareMethod,
						userId
					);
				}
			} else if (action === 'share') {
				// For share operations, always pass the existing permanent file key if available
				if (hasPermanentFile) {
					this.logger.log(
						'Using existing permanent file for sharing',
						{
							fileKey: existingFileKey,
							invoiceId: invoice.id,
							shareMethod,
							recipient,
							email,
							phoneNumber
						}
					);

					await this.queueInvoiceDocumentTask(
						invoice.id,
						action,
						shareMethod,
						userId,
						existingFileKey,
						recipient,
						email,
						phoneNumber
					);
				} else {
					// No existing file, queue task to generate a new one
					this.logger.log(
						'No existing file, queueing task to generate new file for sharing',
						{
							invoiceId: invoice.id,
							action,
							shareMethod,
							recipient,
							email,
							phoneNumber
						}
					);

					await this.queueInvoiceDocumentTask(
						invoice.id,
						action,
						shareMethod,
						userId,
						undefined,
						recipient,
						email,
						phoneNumber
					);
				}
			}

			// Return immediate response for the client
			return {
				status: 'processing',
				data: {
					message: `Document ${action} request is being processed`,
					invoiceId: invoice.id
				}
			};
		} catch (error) {
			this.logger.error('Error in handleInvoiceDocument', {
				error,
				referenceAlphaId,
				action,
				shareMethod
			});

			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				'Failed to process document request',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	/**
	 * Check if an invoice PDF has been generated and is ready
	 * @param invoiceId ID of the invoice to check
	 * @returns Status and URL if ready
	 */
	async checkInvoiceDocumentStatus(invoiceId: string) {
		this.logger.log('Checking invoice document status', { invoiceId });

		try {
			const invoice = await this.invoiceRepository.findOne({
				where: { id: invoiceId }
			});

			if (!invoice) {
				throw new HttpException(
					'Invoice not found',
					HttpStatus.NOT_FOUND
				);
			}

			const isInvoice = invoice.invoiceType === EnumInvoiceType.Invoice;
			const fileUrl = (invoice.fileUrl as Record<string, string>) || {};

			// Check if a document is currently being generated
			if (fileUrl.isGenerating === 'true') {
				return {
					status: true,
					data: {
						isReady: false,
						invoiceType: invoice.invoiceType
					},
					message: 'Document is still being generated'
				};
			}

			const fileKey = isInvoice
				? fileUrl.invoiceFileKey
				: fileUrl.creditNoteFileKey;

			if (!fileKey) {
				// Document still being generated
				return {
					status: true,
					data: {
						isReady: false,
						invoiceType: invoice.invoiceType
					},
					message: 'Document is still being generated'
				};
			}

			// Document is ready, generate a download URL
			const patientDetails = await this.patientService.getPatientDetails(
				invoice.patientId
			);

			if (!patientDetails) {
				throw new HttpException(
					'Patient details not found',
					HttpStatus.NOT_FOUND
				);
			}

			const fileName = this.generateFileName(patientDetails, invoice);
			const url = await this.s3Service.getDownloadPreSignedUrl(
				fileKey,
				fileName
			);

			return {
				status: true,
				data: {
					isReady: true,
					url,
					fileName,
					invoiceType: invoice.invoiceType
				},
				message: 'Document is ready for download'
			};
		} catch (error) {
			this.logger.error('Error checking invoice document status', {
				error,
				invoiceId
			});

			throw error;
		}
	}

	/**
	 * Queue a task for invoice document generation and/or sharing
	 * @param invoiceId ID of the invoice
	 * @param action Whether to share or download
	 * @param shareMethod Method to use for sharing (if applicable)
	 * @param userId User ID
	 * @param fileKey Optional file key to use for the document
	 * @param recipient Recipient of the document (client or custom)
	 * @param email Custom email address for sharing
	 * @param phoneNumber Custom phone number for sharing
	 */
	private async queueInvoiceDocumentTask(
		invoiceId: string,
		action: 'share' | 'download',
		shareMethod: 'email' | 'whatsapp' | 'both' | undefined,
		userId: string,
		fileKey?: string,
		recipient: 'client' | 'other' | undefined = 'client',
		email?: string,
		phoneNumber?: string
	): Promise<void> {
		try {
			// Validate fileKey if hasPermanentFile was true
			if (
				fileKey &&
				(fileKey.startsWith('invoice/') ||
					fileKey.startsWith('creditNote/')) &&
				!fileKey.includes(invoiceId)
			) {
				this.logger.warn(
					'Permanent fileKey does not contain invoiceId',
					{
						invoiceId,
						fileKey
					}
				);
			}

			// If no permanent file exists, we need to update the invoice to indicate generation is in progress
			if (!fileKey) {
				// Get current fileUrl and set generation flag
				const invoice = await this.invoiceRepository.findOne({
					where: { id: invoiceId }
				});

				if (invoice) {
					const currentFileUrl =
						(invoice.fileUrl as Record<string, string>) || {};
					currentFileUrl.isGenerating = 'true';

					await this.invoiceRepository.update(invoiceId, {
						fileUrl: currentFileUrl
					});

					this.logger.log('Set generation flag before queuing task', {
						invoiceId,
						currentFileUrl
					});
				}
			}

			// Log detailed info about the request
			this.logger.log('Queueing invoice document task', {
				invoiceId,
				action,
				shareMethod,
				fileKey,
				recipient,
				email,
				phoneNumber,
				isPermanentFile:
					fileKey &&
					(fileKey.startsWith('invoice/') ||
						fileKey.startsWith('creditNote/')),
				hasFileKey: !!fileKey,
				fileKeyLength: fileKey?.length || 0
			});

			await this.sqsService.sendMessage({
				queueKey: 'NidanaInvoiceTasks',
				messageBody: {
					data: {
						taskType: 'processInvoiceDocument',
						invoiceId,
						action,
						shareMethod,
						userId,
						fileKey,
						recipient,
						email,
						phoneNumber,
						// Include a flag to clear the generation flag when complete
						clearGeneratingFlag: true
					}
				},
				deduplicationId: `invoice-document-${invoiceId}-${Date.now()}`
			});

			this.logger.log('Invoice document task queued successfully', {
				invoiceId,
				action,
				fileKey
			});
		} catch (error) {
			this.logger.error('Failed to queue invoice document task', {
				error,
				invoiceId,
				action
			});

			throw new HttpException(
				'Failed to process document request',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	/**
	 * Generate a consistent filename for the invoice document
	 * @param patientDetails Patient details
	 * @param invoice Invoice entity
	 * @returns Formatted filename
	 */
	private generateFileName(
		patientDetails: Patient,
		invoice: InvoiceEntity
	): string {
		// Get owner details
		const patientOwner = patientDetails.patientOwners?.[0];
		const ownerLastName = patientOwner?.ownerBrand?.lastName || 'owner';

		// Format pet name and owner last name for filename
		const petNameForFile = patientDetails.patientName || 'pet';
		const ownerLastNameForFile = ownerLastName;

		const formattedPetName = petNameForFile
			.replace(/[^a-zA-Z0-9]/g, '_')
			.toLowerCase();
		const formattedOwnerLastName = ownerLastNameForFile
			.replace(/[^a-zA-Z0-9]/g, '_')
			.toLowerCase();

		// Create the appropriate prefix based on invoice type
		const fileNamePrefix =
			invoice.invoiceType === EnumInvoiceType.Invoice
				? `${formattedPetName}_${formattedOwnerLastName}_invoice`
				: `${formattedPetName}_${formattedOwnerLastName}_creditnote`;

		return `${fileNamePrefix}_${invoice.referenceAlphaId}.pdf`;
	}

	/**
	 * Handle temporary file deletion and database updates
	 * @param invoiceId ID of the invoice
	 * @param fileKey File key to delete
	 * @param isInvoice Whether this is an invoice (vs credit note)
	 * @param fileUrl Current fileUrl object from the invoice
	 * @returns Updated fileUrl object
	 * @private
	 */
	private async handleTemporaryFile(
		invoiceId: string,
		fileKey: string,
		isInvoice: boolean,
		fileUrl: Record<string, string>
	): Promise<Record<string, string>> {
		try {
			// Delete file from S3
			await this.s3Service.deleteFile(fileKey);

			// Create a clean copy of fileUrl
			const updatedFileUrl = { ...fileUrl };

			// Remove the file reference based on invoice type
			if (isInvoice) {
				delete updatedFileUrl.invoiceFileKey;
			} else {
				delete updatedFileUrl.creditNoteFileKey;
			}

			// Add a generation flag to indicate a new document is being created
			updatedFileUrl.isGenerating = 'true';

			// Update the invoice record and await completion
			await this.invoiceRepository.update(invoiceId, {
				fileUrl: updatedFileUrl
			});

			this.logger.log(
				'Temporary file removed successfully and set generation flag',
				{
					invoiceId,
					updatedFileUrl
				}
			);

			// Return the updated fileUrl for further processing
			return updatedFileUrl;
		} catch (error) {
			this.logger.error(
				'Error removing temporary file, continuing with process',
				{
					error,
					fileKey,
					invoiceId
				}
			);
			// Return the original fileUrl if there was an error
			return fileUrl;
		}
	}

	/**
	 * Log changes to an invoice for audit purposes
	 * @param invoiceId ID of the invoice
	 * @param userId ID of the user making the change
	 * @param operationType Type of operation (UPDATE or DELETE)
	 * @param changes Before and after values for updates, null for deletes
	 * @param changedFieldsSummary Human-readable summary of changes
	 */
	/**
	 * Generate structured operations from invoice changes
	 */
	private generateAuditOperations(changes: {
		before: Record<string, any>;
		after: Record<string, any>;
	}): AuditOperation[] {
		const operations: AuditOperation[] = [];
		const { before, after } = changes;

		// Helper functions
		const getFloatSafe = (value: any, defaultValue = 0): number => {
			if (
				value === null ||
				value === undefined ||
				String(value).trim() === ''
			) {
				return defaultValue;
			}
			try {
				return parseFloat(String(value));
			} catch {
				return defaultValue;
			}
		};

		const getIntSafe = (value: any, defaultValue = 0): number => {
			if (
				value === null ||
				value === undefined ||
				String(value).trim() === ''
			) {
				return defaultValue;
			}
			try {
				return parseInt(String(value), 10);
			} catch {
				return defaultValue;
			}
		};

		// Process item-level changes
		const beforeItems: Record<string, any> = {};
		const afterItems: Record<string, any> = {};

		// Build lookup maps for items with valid id and name
		(before?.details || []).forEach((item: any) => {
			if (item?.id && item?.name && String(item.name).trim()) {
				beforeItems[item.id] = item;
			}
		});

		(after?.details || []).forEach((item: any) => {
			if (item?.id && item?.name && String(item.name).trim()) {
				afterItems[item.id] = item;
			}
		});

		// 1. Items added
		Object.entries(afterItems).forEach(([itemId, afterItem]) => {
			if (!beforeItems[itemId]) {
				operations.push({
					type: 'ADD_ITEM',
					item: {
						id: itemId,
						name: afterItem.name
					},
					after: afterItem.quantity || 1,
					description: `${afterItem.name} added`
				});
			}
		});

		// 2. Items removed
		Object.entries(beforeItems).forEach(([itemId, beforeItem]) => {
			if (!afterItems[itemId]) {
				operations.push({
					type: 'REMOVE_ITEM',
					item: {
						id: itemId,
						name: beforeItem.name
					},
					before: beforeItem.quantity || 1,
					description: `${beforeItem.name} removed`
				});
			}
		});

		// 3. Items modified (quantity changes)
		Object.entries(afterItems).forEach(([itemId, afterItem]) => {
			if (beforeItems[itemId]) {
				const beforeItem = beforeItems[itemId];
				const beforeQty = getIntSafe(beforeItem.quantity);
				const afterQty = getIntSafe(afterItem.quantity);

				if (beforeQty !== afterQty) {
					operations.push({
						type: 'CHANGE_QUANTITY',
						item: {
							id: itemId,
							name: afterItem.name
						},
						before: beforeQty,
						after: afterQty,
						description: `${afterItem.name} quantity changed from ${beforeQty} to ${afterQty}`
					});
				}
			}
		});

		// 4. Overall invoice discount changes
		const beforeDiscount = getFloatSafe(before?.discount);
		const afterDiscount = getFloatSafe(after?.discount);

		if (beforeDiscount !== afterDiscount) {
			if (beforeDiscount === 0 && afterDiscount > 0) {
				// Discount applied
				operations.push({
					type: 'APPLY_DISCOUNT',
					before: beforeDiscount,
					after: afterDiscount,
					description: `Discount of ${afterDiscount}% applied`
				});
			} else if (beforeDiscount > 0 && afterDiscount === 0) {
				// Discount removed
				operations.push({
					type: 'REMOVE_DISCOUNT',
					before: beforeDiscount,
					after: afterDiscount,
					description: `Discount of ${beforeDiscount}% removed`
				});
			} else {
				// Discount changed
				operations.push({
					type: 'CHANGE_DISCOUNT',
					before: beforeDiscount,
					after: afterDiscount,
					description: `Discount changed from ${beforeDiscount}% to ${afterDiscount}%`
				});
			}
		}

		return operations;
	}

	private async logChange(
		invoiceId: string,
		userId: string,
		operationType: AuditLogOperationType,
		changes?: {
			before: Record<string, any>;
			after: Record<string, any>;
		} | null,
		legacySummary?: string,
		queryRunner?: QueryRunner,
		reason?: string // Add optional reason parameter
	): Promise<void> {
		try {
			// Generate structured operations if we have changes
			let operations: AuditOperation[] = [];
			if (changes && operationType === AuditLogOperationType.UPDATE) {
				operations = this.generateAuditOperations(changes);
			} else if (operationType === AuditLogOperationType.DELETE) {
				// For deletions (cancellations), create operation with cancellation amount information
				const cancellationAmount =
					changes?.after?.metadata?.cancellation?.amount || 0;
				operations = [
					{
						type: 'REMOVE_ITEM', // This represents the whole invoice being deleted
						description: legacySummary || 'Invoice cancelled',
						reason: reason,
						cancellationAmount: cancellationAmount
					}
				];
			} else if (operationType === AuditLogOperationType.CREATE) {
				// For creation, create a simple operation
				operations = [
					{
						type: 'ADD_ITEM', // This represents the whole invoice being created
						description: legacySummary || 'Invoice created'
					}
				];
			} else if (operationType === AuditLogOperationType.WRITE_OFF) {
				// For write-off, create operation with writeoff amount information
				const writeoffAmount =
					changes?.after?.metadata?.writeoff?.amount || 0;
				operations = [
					{
						type: 'WRITE_OFF_INVOICE', // This represents the invoice being written off
						description: legacySummary || 'Invoice written off',
						reason: reason,
						writeoffAmount: writeoffAmount
					}
				];
			}

			// Use transaction manager if queryRunner is provided, otherwise use regular repository
			if (queryRunner) {
				const auditLog = queryRunner.manager.create(
					InvoiceAuditLogEntity,
					{
						invoiceId,
						userId,
						operationType,
						changes,
						changedFieldsSummary: operations
					}
				);

				await queryRunner.manager.save(InvoiceAuditLogEntity, auditLog);
			} else {
				const auditLog = this.invoiceAuditLogRepository.create({
					invoiceId,
					userId,
					operationType,
					changes,
					changedFieldsSummary: operations
				});

				await this.invoiceAuditLogRepository.save(auditLog);
			}

			this.logger.log('Invoice audit log created', {
				invoiceId,
				userId,
				operationType,
				operationsCount: operations.length,
				usingTransaction: !!queryRunner,
				reason: reason || 'No reason provided'
			});
		} catch (error) {
			this.logger.error('Failed to create invoice audit log', {
				error,
				invoiceId,
				userId,
				operationType,
				usingTransaction: !!queryRunner
			});
			// Don't throw error to avoid breaking the main operation
		}
	}

	/**
	 * Generate a human-readable summary of field changes
	 * @param before Object with before values
	 * @param after Object with after values
	 * @returns Human-readable summary string
	 */
	private generateChangeSummary(
		before: Record<string, any>,
		after: Record<string, any>
	): string {
		const changes: string[] = [];

		for (const [key, afterValue] of Object.entries(after)) {
			const beforeValue = before[key];
			if (beforeValue !== afterValue) {
				changes.push(`${key} from ${beforeValue} to ${afterValue}`);
			}
		}

		return changes.join(', ');
	}

	/**
	 * Handle line item changes for vaccinations and reminders
	 * @param originalDetails Original invoice details (before update)
	 * @param newDetails New invoice details (after update)
	 * @param invoice Invoice entity
	 * @param entityManager Transaction entity manager
	 */
	private async handleLineItemChangesWithOriginalDetails(
		originalDetails: any[],
		newDetails: any[],
		invoice: InvoiceEntity
	): Promise<void> {
		const oldDetails = originalDetails;

		// Find completely removed items and mark them as removed from invoice
		for (const oldItem of oldDetails) {
			// Use the cart item ID (primary key) for accurate comparison
			const newItem = newDetails.find(item => item.id === oldItem.id);

			// If item is completely removed (not just quantity reduced)
			if (!newItem) {
				const appointmentId = invoice.cart?.appointmentId;
				if (appointmentId) {
					// Handle vaccination items - mark as removed from invoice instead of deleting
					if (oldItem.itemType === 'Vaccination') {
						// First try to get patient vaccination ID from invoice metadata mapping
						let patientVaccinationId: string | null = null;
						if (
							invoice.metadata &&
							invoice.metadata.cartToVaccinationMapping
						) {
							patientVaccinationId =
								invoice.metadata.cartToVaccinationMapping[
									oldItem.id
								];
							this.logger.log(
								'Found patient vaccination ID from invoice metadata mapping',
								{
									appointmentId,
									cartItemId: oldItem.id,
									patientVaccinationId,
									oldItemName: oldItem.name
								}
							);
						}

						// If we have a patient vaccination ID from mapping, use it directly
						if (patientVaccinationId) {
							this.logger.log(
								'Marking patient vaccination as removed from invoice using metadata mapping',
								{
									appointmentId,
									patientVaccinationId,
									cartItemId: oldItem.id,
									oldItemName: oldItem.name,
									sourceMethod: 'metadata_mapping'
								}
							);
							// Mark the specific patient vaccination record as removed
							await this.patientVaccination.markAsRemovedFromInvoiceByPatientVaccinationId(
								patientVaccinationId
							);
						}
					}
					// Handle diagnostic/lab report items - mark as removed from invoice instead of deleting
					else if (
						oldItem.itemType === 'Diagnostic' ||
						oldItem.itemType === 'Labreport'
					) {
						// First try to get lab report ID from invoice metadata mapping
						let labReportId: string | null = null;

						if (
							invoice.metadata &&
							invoice.metadata.cartToLabReportMapping
						) {
							labReportId =
								invoice.metadata.cartToLabReportMapping[
									oldItem.id
								];
							this.logger.log(
								'Found lab report ID from invoice metadata mapping',
								{
									appointmentId,
									cartItemId: oldItem.id,
									labReportId,
									oldItemName: oldItem.name
								}
							);
						}

						if (labReportId) {
							this.logger.log(
								'Marking lab report as removed from invoice',
								{
									appointmentId,
									labReportId,
									cartItemId: oldItem.id,
									oldItemName: oldItem.name,
									sourceMethod: invoice.metadata
										?.cartToLabReportMapping
										? 'metadata_mapping'
										: 'cart_item_query'
								}
							);
							await this.clinicLabReportService.markAsRemovedFromInvoiceByAppointmentAndLabReportId(
								appointmentId,
								labReportId
							);
						} else {
							this.logger.warn(
								'Lab report ID not found for removal - checked both metadata mapping and cart item',
								{
									appointmentId,
									cartItemId: oldItem.id,
									cartId: invoice.cartId,
									oldItemName: oldItem.name,
									hasMetadataMapping:
										!!invoice.metadata
											?.cartToLabReportMapping
								}
							);
						}
					}
				}
			}
		}

		// Handle reminder impact (synchronous)
		if (invoice.cart?.appointmentId) {
			// Undo reminder completions that were completed by this appointment
			await this.patientRemindersService.undoCompletionsByAppointmentId(
				invoice.cart.appointmentId
			);
		}

		// Hard delete all reminders for this invoice (simpler approach)
		// New reminders will be created via SQS if needed
		await this.patientRemindersService.hardDeleteByInvoiceId(invoice.id);

		this.logger.log('Line item changes processed', {
			invoiceId: invoice.id,
			oldItemCount: oldDetails.length,
			newItemCount: newDetails.length
		});
	}

	/**
	 * Queue inventory adjustments for invoice updates
	 * @param invoice Original invoice
	 * @param newDetails New invoice details
	 * @param userId User making the change
	 */
	private async queueInventoryAdjustments(
		originalDetails: any,
		newDetails: any[],
		userId: string
	): Promise<void> {
		try {
			const oldDetails = (originalDetails.details as any[]) || [];
			const inventoryChanges = this.calculateInventoryChanges(
				oldDetails,
				newDetails
			);

			if (inventoryChanges.length > 0) {
				await this.sqsService.sendMessage({
					queueKey: 'NidanaInvoiceTasks',
					messageBody: {
						data: {
							taskType: 'updateInventoryForInvoiceUpdate',
							invoiceId: originalDetails.id,
							inventoryChanges,
							userId
						}
					},
					deduplicationId: `inventory-update-${originalDetails.id}-${Date.now()}`
				});

				this.logger.log('Inventory adjustments queued', {
					invoiceId: originalDetails.id,
					changesCount: inventoryChanges.length
				});
			}
		} catch (error) {
			this.logger.error('Failed to queue inventory adjustments', {
				error,
				invoiceId: originalDetails.id
			});
			// Don't throw error - inventory updates are async and shouldn't block invoice update
		}
	}

	/**
	 * Calculate inventory changes between old and new details
	 * @param oldDetails Original invoice details
	 * @param newDetails New invoice details
	 * @returns Array of inventory changes
	 */
	private calculateInventoryChanges(
		oldDetails: any[],
		newDetails: any[]
	): any[] {
		const changes: any[] = [];

		// Create maps for easier lookup with quantity aggregation
		const oldItemsMap = new Map();
		const newItemsMap = new Map();

		oldDetails.forEach(item => {
			const key = `${item.itemType}-${item.itemId}`;
			const existing = oldItemsMap.get(key);
			oldItemsMap.set(key, {
				...item,
				quantity: (existing?.quantity || 0) + item.quantity
			});
		});

		newDetails.forEach(item => {
			const key = `${item.itemType}-${item.itemId}`;
			const existing = newItemsMap.get(key);
			newItemsMap.set(key, {
				...item,
				quantity: (existing?.quantity || 0) + item.quantity
			});
		});

		// Check for removed or reduced items (need to revert stock)
		for (const [key, oldItem] of oldItemsMap) {
			const newItem = newItemsMap.get(key);

			if (!newItem) {
				// Item completely removed - revert full quantity
				changes.push({
					action: 'revert',
					itemType: oldItem.itemType,
					itemId: oldItem.itemId,
					quantity: oldItem.quantity
				});
			} else if (newItem.quantity < oldItem.quantity) {
				// Quantity reduced - revert difference
				changes.push({
					action: 'revert',
					itemType: oldItem.itemType,
					itemId: oldItem.itemId,
					quantity: oldItem.quantity - newItem.quantity
				});
			}
		}

		// Check for added or increased items (need to deduct stock)
		for (const [key, newItem] of newItemsMap) {
			const oldItem = oldItemsMap.get(key);

			if (!oldItem) {
				// Item completely added - deduct full quantity
				changes.push({
					action: 'deduct',
					itemType: newItem.itemType,
					itemId: newItem.itemId,
					quantity: newItem.quantity
				});
			} else if (newItem.quantity > oldItem.quantity) {
				// Quantity increased - deduct difference
				changes.push({
					action: 'deduct',
					itemType: newItem.itemType,
					itemId: newItem.itemId,
					quantity: newItem.quantity - oldItem.quantity
				});
			}
		}

		return changes;
	}

	/**
	 * Queue vaccination and reminder processing for newly added items
	 * @param invoice Original invoice
	 * @param newDetails New invoice details
	 */
	private async queueNewItemProcessing(
		originalDetails: any,
		newDetails: any[],
		invoice: InvoiceEntity
	): Promise<void> {
		try {
			const oldDetails = (originalDetails.details as any[]) || [];
			const newlyAddedItems = this.findNewlyAddedItems(
				oldDetails,
				newDetails
			);

			if (newlyAddedItems.length === 0) {
				this.logger.log('No newly added items found for processing', {
					invoiceId: originalDetails.id
				});
				return;
			}

			// Filter items that need vaccination generation
			const vaccinationItems = newlyAddedItems.filter(
				item => item.itemType === 'Vaccination'
			);

			// Filter items that might trigger global reminder rules
			const reminderTriggerItems = newlyAddedItems;

			this.logger.log('Found newly added items for processing', {
				invoiceId: originalDetails.id,
				totalNewItems: newlyAddedItems.length,
				vaccinationItems: vaccinationItems.length,
				reminderTriggerItems: reminderTriggerItems.length
			});

			// Queue vaccination generation for new vaccination items
			if (vaccinationItems.length > 0) {
				// Fetch doctor information if appointment exists
				let doctorName = '';
				let licenseNumber = '';
				let doctorId: string | null = null;

				if (originalDetails.cart?.appointmentId) {
					try {
						const appointmentDoctorResponse =
							await this.appointmentDoctorsEntity.findOne({
								where: {
									appointmentId:
										originalDetails.cart.appointmentId
								},
								relations: ['clinicUser', 'clinicUser.user']
							});

						if (appointmentDoctorResponse?.clinicUser?.user) {
							doctorName =
								`${appointmentDoctorResponse.clinicUser.user.firstName || ''} ${appointmentDoctorResponse.clinicUser.user.lastName || ''}`.trim();
							licenseNumber =
								appointmentDoctorResponse.clinicUser.user
									.licenseNumber || '';
							doctorId =
								appointmentDoctorResponse.clinicUser.user.id;
						}
					} catch (error) {
						this.logger.error(
							'Error fetching doctor information for vaccination generation',
							{
								error,
								appointmentId:
									originalDetails.cart.appointmentId,
								invoiceId: originalDetails.id
							}
						);
					}
				}

				await this.queueVaccinationGeneration(
					invoice,
					vaccinationItems,
					doctorName,
					licenseNumber,
					doctorId || undefined
				);
			}

			// Queue reminder processing for items that might trigger global rules
			if (reminderTriggerItems.length > 0) {
				await this.queueReminderProcessing(
					invoice,
					reminderTriggerItems
				);
			}
		} catch (error) {
			this.logger.error('Failed to queue new item processing', {
				error,
				invoiceId: invoice.id
			});
			// Don't throw error - this is async processing and shouldn't block invoice update
		}
	}

	/**
	 * Queue vaccination PDF generation for newly created vaccination records and reminder processing for newly added items
	 * This method is used for invoice updates where vaccination records are already created in the transaction
	 * @param originalDetails Original invoice details
	 * @param newDetails New invoice details
	 * @param invoice Invoice entity
	 * @param vaccinationRecords Already created vaccination records that need PDF generation
	 */
	private async queueNewItemProcessingWithVaccinationRecords(
		originalDetails: any[],
		newDetails: any[],
		invoice: InvoiceEntity,
		vaccinationRecords: PatientVaccination[]
	): Promise<void> {
		try {
			const newlyAddedItems = this.findNewlyAddedItems(
				originalDetails,
				newDetails
			);

			if (
				newlyAddedItems.length === 0 &&
				vaccinationRecords.length === 0
			) {
				this.logger.log(
					'No newly added items or vaccination records found for processing',
					{
						invoiceId: invoice.id
					}
				);
				return;
			}

			// Queue vaccination PDF generation for newly created vaccination records
			if (vaccinationRecords.length > 0) {
				// Get doctor information
				let doctorName = '';
				let licenseNumber = '';
				let doctorId: string | null = null;

				if (invoice.cart?.appointmentId) {
					try {
						const appointmentDoctorResponse =
							await this.appointmentDoctorsEntity.findOne({
								where: {
									appointmentId: invoice.cart.appointmentId,
									primary: true
								},
								relations: ['clinicUser', 'clinicUser.user']
							});

						if (appointmentDoctorResponse?.clinicUser?.user) {
							doctorName =
								`${appointmentDoctorResponse.clinicUser.user.firstName || ''} ${appointmentDoctorResponse.clinicUser.user.lastName || ''}`.trim();
							licenseNumber =
								appointmentDoctorResponse.clinicUser.user
									.licenseNumber || '';
							doctorId =
								appointmentDoctorResponse.clinicUser.user.id;
						}
					} catch (error) {
						this.logger.error(
							'Error fetching doctor information for vaccination PDF generation',
							{
								error,
								appointmentId: invoice.cart.appointmentId,
								invoiceId: invoice.id
							}
						);
					}
				}

				// Send vaccination record IDs to SQS for PDF generation
				const vaccinationRecordIds = vaccinationRecords.map(
					record => record.id
				);

				await this.sqsService.sendMessage({
					queueKey: 'NidanaInvoiceTasks',
					messageBody: {
						data: {
							taskType: 'generateVaccinationPdfs',
							patientId: invoice.patientId,
							invoiceId: invoice.id,
							vaccinationRecordIds, // Pass the created vaccination record IDs
							doctorName: doctorName || '',
							licenseNumber: licenseNumber || '',
							doctorId: doctorId || null
						}
					},
					deduplicationId: `vaccination-pdf-update-${invoice.id}-${Date.now()}`
				});

				this.logger.log(
					'Vaccination PDF generation queued for newly created records in update',
					{
						invoiceId: invoice.id,
						vaccinationRecordIds,
						recordCount: vaccinationRecords.length
					}
				);
			}

			// Queue reminder processing for items that might trigger global rules
			const reminderTriggerItems = newlyAddedItems;
			if (reminderTriggerItems.length > 0) {
				await this.queueReminderProcessing(
					invoice,
					reminderTriggerItems
				);
			}
		} catch (error) {
			this.logger.error(
				'Failed to queue new item processing with vaccination records',
				{
					error,
					invoiceId: invoice.id,
					vaccinationRecordCount: vaccinationRecords.length
				}
			);
			// Don't throw error - this is async processing and shouldn't block invoice update
		}
	}

	/**
	 * Find newly added items by comparing old and new details
	 * @param oldDetails Original invoice details
	 * @param newDetails New invoice details
	 * @returns Array of newly added items
	 */
	private findNewlyAddedItems(oldDetails: any[], newDetails: any[]): any[] {
		const newlyAdded: any[] = [];

		// Create map of old items for quick lookup with quantity aggregation
		const oldItemsMap = new Map();
		oldDetails.forEach(item => {
			// Use inventoryId as the primary identifier (itemId is legacy/fallback)
			const identifier = item.inventoryId || item.itemId;
			const key = `${item.itemType}-${identifier}`;
			const existing = oldItemsMap.get(key);
			oldItemsMap.set(key, {
				...item,
				quantity: (existing?.quantity || 0) + item.quantity
			});
		});

		// Create map of new items with quantity aggregation
		const newItemsMap = new Map();
		newDetails.forEach(item => {
			// Use inventoryId as the primary identifier (itemId is legacy/fallback)
			const identifier = item.inventoryId || item.itemId;
			const key = `${item.itemType}-${identifier}`;
			const existing = newItemsMap.get(key);
			newItemsMap.set(key, {
				...item,
				quantity: (existing?.quantity || 0) + item.quantity
			});
		});

		this.logger.log('Comparing items for newly added detection', {
			oldItemsCount: oldDetails.length,
			newItemsCount: newDetails.length,
			oldItemKeys: Array.from(oldItemsMap.keys()),
			newItemKeys: Array.from(newItemsMap.keys())
		});

		// Find items that exist in new details but not in old details
		for (const [key, newItem] of newItemsMap) {
			const oldItem = oldItemsMap.get(key);

			if (!oldItem) {
				// Completely new item
				this.logger.log('Found completely new item', {
					key,
					itemName: newItem.name,
					itemType: newItem.itemType,
					quantity: newItem.quantity
				});
				newlyAdded.push(newItem);
			} else if (newItem.quantity > oldItem.quantity) {
				// Quantity increased - treat as partial new item
				const additionalQuantity = newItem.quantity - oldItem.quantity;
				this.logger.log('Found item with increased quantity', {
					key,
					itemName: newItem.name,
					oldQuantity: oldItem.quantity,
					newQuantity: newItem.quantity,
					additionalQuantity
				});
				newlyAdded.push({
					...newItem,
					quantity: additionalQuantity
				});
			}
		}

		this.logger.log('Newly added items detection completed', {
			newlyAddedCount: newlyAdded.length,
			newlyAddedItems: newlyAdded.map(item => ({
				name: item.name,
				itemType: item.itemType,
				quantity: item.quantity,
				identifier: item.inventoryId || item.itemId
			}))
		});

		return newlyAdded;
	}

	/**
	 * Queue vaccination generation for new vaccination items
	 * @param invoice Invoice entity
	 * @param vaccinationItems New vaccination items
	 * @param doctorName Name of the doctor
	 * @param licenseNumber License number of the doctor
	 * @param doctorId ID of the doctor
	 */
	private async queueVaccinationGeneration(
		invoice: InvoiceEntity,
		vaccinationItems: any[],
		doctorName?: string,
		licenseNumber?: string,
		doctorId?: string
	): Promise<void> {
		try {
			await this.sqsService.sendMessage({
				queueKey: 'NidanaInvoiceTasks',
				messageBody: {
					data: {
						taskType: 'generateVaccination',
						patientId: invoice.patientId,
						invoiceId: invoice.id,
						doctorName: doctorName || '',
						licenseNumber: licenseNumber || '',
						doctorId: doctorId || null
					}
				},
				deduplicationId: `vaccination-gen-${invoice.id}-${Date.now()}`
			});

			this.logger.log('Vaccination generation queued for new items', {
				invoiceId: invoice.id,
				itemCount: vaccinationItems.length,
				doctorName,
				doctorId
			});
		} catch (error) {
			this.logger.error('Failed to queue vaccination generation', {
				error,
				invoiceId: invoice.id,
				itemCount: vaccinationItems.length
			});
		}
	}

	/**
	 * Queue reminder processing for new items that might trigger global rules
	 * @param invoice Invoice entity
	 * @param reminderTriggerItems New items that might trigger reminders
	 */
	private async queueReminderProcessing(
		invoice: InvoiceEntity,
		reminderTriggerItems: any[]
	): Promise<void> {
		try {
			await this.sqsService.sendMessage({
				queueKey: 'NidanaInvoiceTasks',
				messageBody: {
					data: {
						taskType: 'processReminders',
						patientId: invoice.patientId,
						clinicId: invoice.clinicId,
						brandId: invoice.brandId,
						invoiceDetails: reminderTriggerItems,
						reminderStatus: 'PENDING',
						invoiceId: invoice.id,
						appointmentId: invoice.cart?.appointmentId
					}
				},
				deduplicationId: `reminder-proc-${invoice.id}-${Date.now()}`
			});

			this.logger.log('Reminder processing queued for new items', {
				invoiceId: invoice.id,
				itemCount: reminderTriggerItems.length
			});
		} catch (error) {
			this.logger.error('Failed to queue reminder processing', {
				error,
				invoiceId: invoice.id,
				itemCount: reminderTriggerItems.length
			});
		}
	}

	/**
	 * Queue inventory reversion for invoice deletion
	 * Reverts stock for all items in the deleted invoice
	 * @param invoice Invoice entity being deleted
	 * @param userId User performing the deletion
	 */
	private async queueInventoryReversion(
		invoice: InvoiceEntity,
		userId: string
	): Promise<void> {
		try {
			const invoiceDetails = (invoice.details as any[]) || [];

			if (invoiceDetails.length === 0) {
				this.logger.log('No items to revert for invoice deletion', {
					invoiceId: invoice.id
				});
				return;
			}

			// Create reversion changes for all items (revert full quantities)
			const reversionChanges = invoiceDetails.map(item => ({
				action: 'revert',
				itemType: item.itemType,
				itemId: item.itemId,
				quantity: item.quantity
			}));

			await this.sqsService.sendMessage({
				queueKey: 'NidanaInvoiceTasks',
				messageBody: {
					data: {
						taskType: 'updateInventoryForInvoiceUpdate',
						invoiceId: invoice.id,
						inventoryChanges: reversionChanges,
						userId,
						operation: 'deletion' // Flag to indicate this is for deletion
					}
				},
				deduplicationId: `inventory-revert-${invoice.id}-${Date.now()}`
			});

			this.logger.log('Inventory reversion queued for invoice deletion', {
				invoiceId: invoice.id,
				itemCount: invoiceDetails.length,
				reversionCount: reversionChanges.length
			});
		} catch (error) {
			this.logger.error('Failed to queue inventory reversion', {
				error,
				invoiceId: invoice.id
			});
			// Don't throw error - inventory reversion is async and shouldn't block invoice deletion
		}
	}

	/**
	 * Write off an invoice - marks status as written off, sets balance to 0, and reverts inventory/vaccinations/lab reports
	 * @param invoiceId ID of the invoice to write off
	 * @param writeOffDto Write-off details including reason and notes
	 * @param userId ID of the user performing the write-off
	 * @returns Updated invoice entity
	 */
	async writeOffInvoice(
		invoiceId: string,
		writeOffDto: WriteOffInvoiceDto,
		userId: string
	): Promise<InvoiceEntity> {
		return await this.dataSource.transaction(
			async (entityManager: EntityManager) => {
				try {
					this.logger.log('Write-off invoice service called', {
						invoiceId,
						writeOffDto,
						userId
					});

					// 1. Find the invoice
					const invoice = await entityManager.findOne(InvoiceEntity, {
						where: { id: invoiceId },
						relations: ['cart']
					});

					if (!invoice) {
						throw new HttpException(
							'Invoice not found',
							HttpStatus.NOT_FOUND
						);
					}

					// 2. Validate that invoice can be written off (pending and partially paid invoices)
					if (
						invoice.status !== EnumInvoiceStatus.PENDING &&
						invoice.status !== EnumInvoiceStatus.PARTIALLY_PAID
					) {
						throw new HttpException(
							'Only pending and partially paid invoices can be written off',
							HttpStatus.FORBIDDEN
						);
					}

					// 3. Calculate writeoff amount (current balance due)
					const writeoffAmount = invoice.balanceDue || 0;

					// 4. Store the before state for audit logging
					const beforeState = {
						status: invoice.status,
						balanceDue: invoice.balanceDue,
						paidAmount: invoice.paidAmount,
						metadata: invoice.metadata
					};

					// 5. Get user information for writeoff metadata
					const writeoffUser = await entityManager.findOne(User, {
						where: { id: userId },
						select: ['id', 'firstName', 'lastName']
					});

					const writeoffUserName = writeoffUser
						? `${writeoffUser.firstName || ''} ${writeoffUser.lastName || ''}`.trim()
						: 'Unknown User';

					// 6. Update invoice metadata with writeoff information
					const currentMetadata = (invoice.metadata as any) || {};
					const updatedMetadata = {
						...currentMetadata,
						writeoff: {
							amount: writeoffAmount,
							date: new Date().toISOString(),
							reason: writeOffDto.reason || 'No reason provided',
							by: writeoffUserName,
							userId: userId
						}
					};

					// 6. Update invoice status, balance, and metadata
					await entityManager.update(
						InvoiceEntity,
						{ id: invoiceId },
						{
							status: EnumInvoiceStatus.WRITTEN_OFF,
							balanceDue: 0,
							metadata: updatedMetadata,
							updatedBy: userId,
							updatedAt: new Date()
						}
					);

					// 5. Get updated invoice for return and audit logging
					const updatedInvoice = await entityManager.findOne(
						InvoiceEntity,
						{
							where: { id: invoiceId },
							relations: ['cart']
						}
					);

					if (!updatedInvoice) {
						throw new HttpException(
							'Failed to retrieve updated invoice',
							HttpStatus.INTERNAL_SERVER_ERROR
						);
					}

					// 6. Store the after state for audit logging
					const afterState = {
						status: updatedInvoice.status,
						balanceDue: updatedInvoice.balanceDue,
						paidAmount: updatedInvoice.paidAmount,
						metadata: updatedInvoice.metadata
					};

					// 7. Log the write-off in audit trail with writeoff amount information
					const queryRunnerForAudit = {
						manager: entityManager
					} as QueryRunner;

					await this.logChange(
						invoiceId,
						userId,
						AuditLogOperationType.WRITE_OFF,
						{ before: beforeState, after: afterState },
						'Invoice written off',
						queryRunnerForAudit,
						writeOffDto.reason
					);

					try {
						// Generate a unique reference ID for this write-off entry
						const writeoffReferenceAlphaId = await generateUniqueCode(
							'referenceAlphaId',
							this.paymentDetailsRepository
						);

						// Build the payment-details entity
						const writeoffPayment = entityManager.create(
							PaymentDetailsEntity,
							{
								patientId: invoice.patientId,
								ownerId: invoice.ownerId,
								invoiceId: invoice.id,
								clinicId: invoice.clinicId,
								brandId: invoice.brandId,
								type: EnumAmountType.WriteOffInvoice,
								paymentType: EnumPaymentType.Cash, // Using Cash as placeholder for write-off entries
								amount: writeoffAmount,
								amountPayable: 0,
								mainBalance: 0,
								transactionAmount: writeoffAmount,
								previousBalance: beforeState.balanceDue || writeoffAmount,
								isCreditUsed: false,
								creditAmountUsed: 0,
								isCreditsAdded: false,
								creditAmountAdded: 0,
								referenceAlphaId: writeoffReferenceAlphaId,
								showInInvoice: false,
								showInLedger: true,
								paymentNotes: writeOffDto.reason || 'Invoice write-off',
								createdBy: userId,
								updatedBy: userId
							}
						);

						await entityManager.save(PaymentDetailsEntity, writeoffPayment);

						this.logger.log('Payment detail entry created for invoice write-off', {
							invoiceId,
							paymentDetailsId: writeoffPayment.id,
							writeoffAmount
						});
					} catch (pdError) {
						// Log but do not fail the transaction if payment-details creation fails
						this.logger.error('Failed to create payment-details record for write-off', {
							invoiceId,
							error: (pdError as Error).message
						});
					}

					this.logger.log(
						'Invoice write-off completed successfully',
						{
							invoiceId,
							previousStatus: beforeState.status,
							newStatus: afterState.status,
							writeoffAmount: writeoffAmount,
							previousBalance: beforeState.balanceDue,
							reason: writeOffDto.reason,
							userId
						}
					);

					return updatedInvoice;
				} catch (error: any) {
					this.logger.error('Error in write-off invoice service', {
						error: error.message,
						stack: error.stack,
						invoiceId,
						userId
					});

					if (error instanceof HttpException) {
						throw error;
					}

					throw new HttpException(
						`Failed to write off invoice: ${error.message}`,
						HttpStatus.INTERNAL_SERVER_ERROR
					);
				}
			}
		);
	}
	/**
	 * Fetch all invoice reference alpha IDs for an owner with applied filters
	 * Uses InvoiceEntity directly for ledger operations
	 */
	async fetchInvoiceIdsForOwner(
		ownerId: string,
		filters: {
			userId?: string;
			status?: string;
			startDate?: string;
			endDate?: string;
			searchTerm?: string;
		}
	): Promise<string[]> {
		const queryBuilder = this.invoiceRepository
			.createQueryBuilder('invoice')
			.select(['invoice.referenceAlphaId'])
			.where('invoice.ownerId = :ownerId', { ownerId })
			.andWhere('invoice.referenceAlphaId IS NOT NULL');

		// Apply status filter (comma-separated values supported)
		if (filters.status) {
			const statuses = filters.status.split(',').map(s => s.trim());
			queryBuilder.andWhere('invoice.status IN (:...statuses)', {
				statuses
			});
		}

		// Apply user filter (createdBy)
		if (filters.userId) {
			queryBuilder.andWhere('invoice.createdBy = :userId', {
				userId: filters.userId
			});
		}

		// Apply date range filters
		if (filters.startDate) {
			queryBuilder.andWhere('DATE(invoice.createdAt) >= :startDate', {
				startDate: filters.startDate
			});
		}

		if (filters.endDate) {
			queryBuilder.andWhere('DATE(invoice.createdAt) <= :endDate', {
				endDate: filters.endDate
			});
		}

		// Apply search term filter (search in referenceAlphaId)
		if (filters.searchTerm) {
			queryBuilder.andWhere(
				'invoice.referenceAlphaId ILIKE :searchTerm',
				{ searchTerm: `%${filters.searchTerm}%` }
			);
		}

		// Order by creation date (newest first)
		queryBuilder.orderBy('invoice.createdAt', 'DESC');

		try {
			const invoices = await queryBuilder.getMany();
			const referenceIds = invoices
				.map(invoice => invoice.referenceAlphaId)
				.filter(id => id) as string[];

			this.logger.log(
				'Fetched invoice reference IDs for owner from InvoiceEntity',
				{
					ownerId,
					totalCount: referenceIds.length,
					filters,
					firstFewIds: referenceIds.slice(0, 3)
				}
			);

			return referenceIds;
		} catch (error) {
			this.logger.error(
				'Error fetching invoice reference IDs for owner',
				{
					ownerId,
					filters,
					error
				}
			);
			throw new HttpException(
				'Failed to fetch invoice records for owner',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
export function calculateAge(dob: string): number {
	const birthDate = moment(dob, 'DD MMM YYYY');
	const today = moment();

	const age = today.diff(birthDate, 'years');
	return age;
}
