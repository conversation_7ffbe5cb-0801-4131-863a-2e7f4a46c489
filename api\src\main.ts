import 'newrelic';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import * as cookieParser from 'cookie-parser';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { ApiDocumentationBase } from './base/api-documentation-base';
import { HttpExceptionFilter } from './utils/http-exception.filter';
import { WinstonLogger } from './utils/logger/winston-logger.service';

export async function bootstrap() {
	try {
		// Set the SERVICE_TYPE environment variable to identify this as an API service
		process.env.SERVICE_TYPE = 'api';

		const app = await NestFactory.create(
			AppModule.register({ isSqsEnabled: false })
		);

		// const app = await NestFactory.create(AppModule);
		app.enableCors();
		app.setGlobalPrefix('api');
		app.useLogger(app.get(WinstonLogger));
		app.use(cookieParser());
		app.use(helmet());
		app.useGlobalFilters(new HttpExceptionFilter(app.get(WinstonLogger)));
		app.useGlobalPipes(new ValidationPipe());
		const configService = app.get(ConfigService);

		// Initialising Swagger
		const environment = configService.get<string>('app.nodeEnv') || 'development';
		if (!['prod', 'uat'].includes(environment)) {
			ApiDocumentationBase.initApiDocumentation(app);
		}
		// ApiDocumentationBase.initApiDocumentation(app);

		const port = configService?.get<number>('app.apiPort') ?? 8000;
		// console.log('Port is at', port);
		await app
			.listen(port)
			.then(() => console.log('Port is at', port))
			.catch(err => console.log('Error', err));
		return app;
	} catch (err) {
		console.log(err);
	}
	return null;
}
bootstrap();
