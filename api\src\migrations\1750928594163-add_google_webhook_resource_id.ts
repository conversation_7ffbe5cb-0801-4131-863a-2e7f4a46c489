import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGoogleWebhookResourceId1750928594163 implements MigrationInterface {
	name = 'AddGoogleWebhookResourceId1750928594163';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(
			`ALTER TABLE "users" ADD "google_webhook_resource_id" character varying`
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "google_webhook_resource_id"`);
	}
} 