import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGoogleCalendarEventsTable1752260349406 implements MigrationInterface {
  name = 'CreateGoogleCalendarEventsTable1752260349406';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "google_calendar_events" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "event_id" character varying NOT NULL,
        "calendar_id" character varying,
        "summary" character varying(1024),
        "description" text,
        "start_time" TIMESTAMPTZ NOT NULL,
        "end_time" TIMESTAMPTZ NOT NULL,
        "status" character varying(50),
        "raw" jsonb,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "pk_google_calendar_events" PRIMARY KEY ("id")
      );
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_gc_events_user_event" ON "google_calendar_events" ("user_id", "event_id");
    `);

    await queryRunner.query(`
      CREATE INDEX "idx_gc_events_user_start" ON "google_calendar_events" ("user_id", "start_time");
    `);

    await queryRunner.query(`
      ALTER TABLE "google_calendar_events"
      ADD CONSTRAINT "fk_gc_events_user" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "google_calendar_events" DROP CONSTRAINT "fk_gc_events_user"`);
    await queryRunner.query(`DROP INDEX "idx_gc_events_user_start"`);
    await queryRunner.query(`DROP INDEX "idx_gc_events_user_event"`);
    await queryRunner.query(`DROP TABLE "google_calendar_events"`);
  }
} 