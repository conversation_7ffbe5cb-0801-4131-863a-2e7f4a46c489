import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGoogleWebhookExpiresAt1752478824886 implements MigrationInterface {
    name = 'AddGoogleWebhookExpiresAt1752478824886';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "users" ADD "google_webhook_expires_at" TIMESTAMP WITH TIME ZONE`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "users" DROP COLUMN "google_webhook_expires_at"`
        );
    }
} 