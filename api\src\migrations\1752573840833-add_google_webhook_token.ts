import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGoogleWebhookToken1752573840833 implements MigrationInterface {
  name = 'AddGoogleWebhookToken1752573840833';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "google_webhook_token" varchar`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "google_webhook_token"`);
  }
} 