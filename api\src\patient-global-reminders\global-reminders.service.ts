import {
	BadRequestException,
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, DeepPartial, In } from 'typeorm';
import { GlobalReminderRule } from './entities/global-reminder-rule.entity';
import { CreateGlobalReminderRuleDto } from './dto/create-global-reminder-rule.dto';
import { UpdateGlobalReminderRuleDto } from './dto/update-global-reminder-rule.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ReminderTriggerType } from './enums/reminder-trigger.enum';
import {
	RecurrenceUnit,
	ReminderStatus
} from '../patient-reminders/enums/reminder.enum';
import { Patient } from '../patients/entities/patient.entity';
import { PatientReminder } from '../patient-reminders/entities/patient-reminder.entity';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class GlobalReminderService {
	constructor(
		@InjectRepository(GlobalReminderRule)
		private globalReminderRuleRepository: Repository<GlobalReminderRule>,
		@InjectRepository(Patient)
		private patientRepository: Repository<Patient>,
		@InjectRepository(PatientReminder)
		private patientReminderRepository: Repository<PatientReminder>,
		private readonly logger: WinstonLogger,
		private dataSource: DataSource
	) {}

	async createRule(
		createRuleDto: CreateGlobalReminderRuleDto,
		brandId: string
	): Promise<GlobalReminderRule> {
		try {
			await this.validateCreateRuleDto(createRuleDto);

			const ruleData: DeepPartial<GlobalReminderRule> = {
				clinicId: createRuleDto.clinicId,
				brandId: brandId,
				triggerType: createRuleDto.triggerType as ReminderTriggerType,
				condition: createRuleDto.condition,
				setReminderFor: createRuleDto.setReminderFor,
				recurrenceFrequency: createRuleDto.recurrenceFrequency,
				recurrenceUnit: createRuleDto.recurrenceUnit as RecurrenceUnit,
				recurrenceEndDate: createRuleDto.recurrenceEndDate
					? new Date(createRuleDto.recurrenceEndDate)
					: undefined,
				recurrenceEndType: createRuleDto.recurrenceEndType,
				startAfterDays: createRuleDto.startAfterDays,
				isActive: true
			};

			// Add type-specific conditions
			if (
				createRuleDto.triggerType ===
				ReminderTriggerType.SPECIES_BREED_AGE
			) {
				ruleData.speciesBreedAge = {
					gender: createRuleDto.speciesBreedAge?.gender
						? {
								value: createRuleDto.speciesBreedAge.gender,
								label: createRuleDto.speciesBreedAge.gender
							}
						: undefined,
					species: createRuleDto.speciesBreedAge?.species
						? {
								value: createRuleDto.speciesBreedAge.species,
								label: createRuleDto.speciesBreedAge.species
							}
						: undefined,
					breed: createRuleDto.speciesBreedAge?.breed
						? {
								value: createRuleDto.speciesBreedAge.breed,
								label: createRuleDto.speciesBreedAge.breed
							}
						: undefined,
					reproductiveStatus: undefined,
					ageRangeType: createRuleDto.speciesBreedAge?.ageRangeType,
					customYr: createRuleDto.speciesBreedAge?.customYr,
					customMths: createRuleDto.speciesBreedAge?.customMths,
					startCustomYr: createRuleDto.speciesBreedAge?.startCustomYr,
					startCustomMths:
						createRuleDto.speciesBreedAge?.startCustomMths,
					endCustomYr: createRuleDto.speciesBreedAge?.endCustomYr,
					endCustomMths: createRuleDto.speciesBreedAge?.endCustomMths
				};
				ruleData.isActive = false;
			} else {
				// For non-species/breed/age rules, store condition directly
				ruleData.condition = {
					value: createRuleDto.condition?.value,
					label: createRuleDto.condition?.label,
					type: createRuleDto.condition?.type
				};
			}

			const rule = this.globalReminderRuleRepository.create(ruleData);
			const savedRule =
				await this.globalReminderRuleRepository.save(rule);

			this.logger.log('Global reminder rule created successfully');

			// If this is a species/breed/age rule, process it for all patients in the background
			if (
				createRuleDto.triggerType ===
				ReminderTriggerType.SPECIES_BREED_AGE
			) {
				setImmediate(async () => {
					try {
						await this.processRuleForAllPatients(savedRule.id);
						this.logger.log(
							`setImmediate Successfully processed rule ${savedRule.id} for all patients`
						);
					} catch (processError) {
						this.logger.error(
							'setImmediate Error processing rule for all patients',
							{
								error:
									processError instanceof Error
										? processError.message
										: 'Unknown error',
								ruleId: savedRule.id
							}
						);
					}
				});
			}

			return savedRule;
		} catch (error) {
			console.log(error);
			this.logger.error('Error creating global reminder rule', { error });
			throw error instanceof BadRequestException
				? error
				: new InternalServerErrorException(
						'Failed to create global reminder rule'
					);
		}
	}

	async processAppointmentTriggers(
		appointmentId: string,
		patientId: string,
		clinicId: string,
		brandId: string,
		updatedPlans: any[],
		diagnoses: any[],
		prescriptions: any[],
		invoiceId?: string
	): Promise<void> {
		// Get all active global rules for the clinic
		const rules = await this.globalReminderRuleRepository.find({
			where: { clinicId, isActive: true }
		});

		for (const rule of rules) {
			switch (rule.triggerType) {
				case ReminderTriggerType.PLAN_UPDATED:
					await this.processPlanBasedRule(
						rule,
						updatedPlans,
						patientId,
						clinicId,
						brandId,
						invoiceId
					);
					break;
				case ReminderTriggerType.DIAGNOSED:
					await this.processDiagnosisBasedRule(
						rule,
						diagnoses,
						patientId,
						clinicId,
						brandId,
						invoiceId
					);
					break;
				case ReminderTriggerType.PRESCRIBED:
					await this.processPrescriptionBasedRule(
						rule,
						prescriptions,
						patientId,
						clinicId,
						brandId,
						invoiceId
					);
					break;
				case ReminderTriggerType.SPECIES_BREED_AGE:
					await this.processPatientAttributeBasedRule(
						rule,
						patientId,
						clinicId,
						brandId
					);
					break;
			}
		}
	}

	private async processPlanBasedRule(
		rule: GlobalReminderRule,
		plans: any[],
		patientId: string,
		clinicId: string,
		brandId: string,
		invoiceId?: string
	): Promise<void> {
		const matchingPlan = plans.find(
			plan => plan.planId === rule.condition.value
		);
		if (matchingPlan) {
			await this.createReminderFromRule(
				rule,
				patientId,
				clinicId,
				brandId,
				invoiceId
			);
		}
	}

	private async processDiagnosisBasedRule(
		rule: GlobalReminderRule,
		diagnoses: any[],
		patientId: string,
		clinicId: string,
		brandId: string,
		invoiceId?: string
	): Promise<void> {
		const matchingDiagnosis = diagnoses.find(
			diagnosis => diagnosis.value === rule.condition.value
		);
		if (matchingDiagnosis) {
			await this.createReminderFromRule(
				rule,
				patientId,
				clinicId,
				brandId,
				invoiceId
			);
		}
	}

	private async processPrescriptionBasedRule(
		rule: GlobalReminderRule,
		prescriptions: any[],
		patientId: string,
		clinicId: string,
		brandId: string,
		invoiceId?: string
	): Promise<void> {
		const matchingPrescription = prescriptions.find(
			prescription => prescription.prescriptionId === rule.condition.value
		);
		if (matchingPrescription) {
			await this.createReminderFromRule(
				rule,
				patientId,
				clinicId,
				brandId,
				invoiceId
			);
		}
	}

	private async processPatientAttributeBasedRule(
		rule: GlobalReminderRule,
		patientId: string,
		clinicId: string,
		brandId: string
	): Promise<void> {
		// const patient = await this.patientRepository.findOne({
		// 	where: { id: patientId }
		// });
		// if (!patient || !rule.speciesBreedAge) return;
		// const matchesAttributes = this.checkPatientAttributes(
		// 	patient,
		// 	rule.speciesBreedAge
		// );
		// if (matchesAttributes) {
		// 	await this.createReminderFromRule(rule, patientId, clinicId);
		// }
	}

	private checkPatientAttributes(patient: Patient, criteria: any): boolean {
		// Check species
		if (criteria.species && patient.species !== criteria.species.value) {
			return false;
		}

		// Check breed
		if (criteria.breed && patient.breed !== criteria.breed.value) {
			return false;
		}

		// Check gender
		if (criteria.gender && patient.gender !== criteria.gender.value) {
			return false;
		}

		// Check age range if specified
		if (criteria.ageRangeType && criteria.startCustomYr) {
			const patientAgeInMonths = this.calculateAge(patient.age) * 12;
			const minAgeInMonths = this.calculateAgeInMonths(
				criteria.startCustomYr,
				criteria.startCustomMths || '0'
			);
			const maxAgeInMonths = criteria.endCustomYr
				? this.calculateAgeInMonths(
						criteria.endCustomYr,
						criteria.endCustomMths || '0'
					)
				: 0;

			switch (criteria.ageRangeType) {
				case 'over':
					if (patientAgeInMonths < minAgeInMonths) return false;
					break;
				case 'under':
					if (patientAgeInMonths > minAgeInMonths) return false;
					break;
				case 'between':
					if (
						patientAgeInMonths < minAgeInMonths ||
						patientAgeInMonths > maxAgeInMonths
					)
						return false;
					break;
			}
		}

		return true;
	}

	private async createReminderFromRule(
		rule: GlobalReminderRule,
		patientId: string,
		clinicId: string,
		brandId: string,
		invoiceId?: string
	): Promise<void> {
		// Check for existing reminder to avoid duplicates
		const existingReminder = await this.patientReminderRepository.findOne({
			where: {
				patientId,
				createdFromRuleId: rule.id,
				status: ReminderStatus.PENDING
			}
		});

		if (existingReminder) {
			return;
		}

		try {
			// Create new reminder with all required fields
			const newReminder = new PatientReminder();
			newReminder.patientId = patientId;
			newReminder.clinicId = clinicId;
			newReminder.brandId = brandId;
			newReminder.title = rule.setReminderFor.label;
			newReminder.inventoryItemId = rule.setReminderFor.value;
			newReminder.inventoryType = rule.setReminderFor.type;
			newReminder.dueDate = new Date(
				Date.now() + (rule.startAfterDays || 0) * 24 * 60 * 60 * 1000
			);
			newReminder.status = ReminderStatus.PENDING;
			newReminder.isRecurring = !!rule.recurrenceFrequency;
			newReminder.recurrenceFrequency = rule.recurrenceFrequency;
			newReminder.recurrenceUnit = rule.recurrenceUnit;
			newReminder.recurrenceEndDate = rule.recurrenceEndDate;
			newReminder.recurrenceEndOccurrences =
				rule.recurrenceEndOccurrences;
			newReminder.createdFromRuleId = rule.id;
			// Set invoiceId if this reminder was created from an invoice flow
			if (invoiceId) {
				newReminder.invoiceId = invoiceId;
			}

			await this.patientReminderRepository.save(newReminder);
		} catch (error) {
			this.logger.error('Failed to create reminder from rule', {
				context: 'GlobalReminders',
				ruleId: rule.id,
				error: error
			});
			throw error;
		}
	}

	private calculateAgeInMonths(years?: string, months?: string): number {
		return Number(years || 0) * 12 + Number(months || 0);
	}

	private async validateCreateRuleDto(
		dto: CreateGlobalReminderRuleDto
	): Promise<void> {
		if (!dto.condition || !dto.setReminderFor) {
			throw new BadRequestException(
				'Condition and reminder settings are required'
			);
		}

		if (dto.triggerType === ReminderTriggerType.SPECIES_BREED_AGE) {
			if (
				!dto.speciesBreedAge ||
				(!dto.speciesBreedAge.species &&
					!dto.speciesBreedAge.breed &&
					!dto.speciesBreedAge.customYr)
			) {
				throw new BadRequestException(
					'Species, breed, or age criteria required for species/breed/age rules'
				);
			}
		}

		if (dto.recurrenceEndType === 'on' && !dto.recurrenceEndDate) {
			throw new BadRequestException(
				'End date required when recurrence end type is "on"'
			);
		}

		if (dto.recurrenceEndType === 'after') {
			throw new BadRequestException(
				'Number of occurrences required when recurrence end type is "after"'
			);
		}
	}

	async getRulesByClinic(clinicId: string): Promise<GlobalReminderRule[]> {
		try {
			return await this.globalReminderRuleRepository.find({
				where: { clinicId },
				order: { createdAt: 'DESC' }
			});
		} catch (error) {
			this.logger.error('Error fetching clinic rules', {
				error,
				clinicId
			});
			throw new InternalServerErrorException(
				'Failed to fetch clinic rules'
			);
		}
	}

	async updateRule(id: string, updateRuleDto: UpdateGlobalReminderRuleDto) {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const rule = await this.globalReminderRuleRepository.findOne({
				where: { id }
			});

			if (!rule) {
				throw new NotFoundException(
					`Global reminder rule with ID ${id} not found`
				);
			}

			// Format the data for update
			const formattedData: Partial<GlobalReminderRule> = {
				clinicId: updateRuleDto.clinicId,
				triggerType: updateRuleDto.triggerType as ReminderTriggerType,
				condition: updateRuleDto.condition
					? {
							value: updateRuleDto.condition.value,
							label: updateRuleDto.condition.label,
							type: updateRuleDto.condition.type,
							subList: updateRuleDto.condition.subList
						}
					: undefined,
				setReminderFor: updateRuleDto.setReminderFor
					? {
							value: updateRuleDto.setReminderFor.value,
							label: updateRuleDto.setReminderFor.label,
							type: updateRuleDto.setReminderFor.type,
							subList: updateRuleDto.setReminderFor.subList
						}
					: undefined,
				recurrenceFrequency: updateRuleDto.recurrenceFrequency,
				recurrenceUnit: updateRuleDto.recurrenceUnit as RecurrenceUnit,
				recurrenceEndDate: updateRuleDto.recurrenceEndDate
					? new Date(updateRuleDto.recurrenceEndDate)
					: undefined,
				recurrenceEndType: updateRuleDto.recurrenceEndType,
				startAfterDays: updateRuleDto.startAfterDays,
				isActive: false // Set to false while processing updates
			};

			// Handle species/breed/age data if present
			if (
				updateRuleDto.triggerType ===
					ReminderTriggerType.SPECIES_BREED_AGE &&
				updateRuleDto.speciesBreedAge
			) {
				formattedData.speciesBreedAge = {
					gender: updateRuleDto.speciesBreedAge.gender
						? {
								value: updateRuleDto.speciesBreedAge.gender,
								label: updateRuleDto.speciesBreedAge.gender
							}
						: undefined,
					species: updateRuleDto.speciesBreedAge.species
						? {
								value: updateRuleDto.speciesBreedAge.species,
								label: updateRuleDto.speciesBreedAge.species
							}
						: undefined,
					breed: updateRuleDto.speciesBreedAge.breed
						? {
								value: updateRuleDto.speciesBreedAge.breed,
								label: updateRuleDto.speciesBreedAge.breed
							}
						: undefined,
					ageRangeType: updateRuleDto.speciesBreedAge.ageRangeType,
					customYr: updateRuleDto.speciesBreedAge.customYr,
					customMths: updateRuleDto.speciesBreedAge.customMths,
					startCustomYr: updateRuleDto.speciesBreedAge.startCustomYr,
					startCustomMths:
						updateRuleDto.speciesBreedAge.startCustomMths,
					endCustomYr: updateRuleDto.speciesBreedAge.endCustomYr,
					endCustomMths: updateRuleDto.speciesBreedAge.endCustomMths
				};
			}

			// Update the rule
			Object.assign(rule, formattedData);
			const updatedRule = await queryRunner.manager.save(
				GlobalReminderRule,
				rule
			);

			await queryRunner.commitTransaction();

			// Process reminders update in background
			setImmediate(async () => {
				try {
					// If it's a species/breed/age rule, reprocess all patients
					if (
						updateRuleDto.triggerType ===
						ReminderTriggerType.SPECIES_BREED_AGE
					) {
						await this.processRuleForAllPatients(id);
					} else {
						// For other types, update the existing reminders with all the new rule settings
						const updatedReminders =
							await this.patientReminderRepository.find({
								where: {
									createdFromRuleId: id,
									status: ReminderStatus.PENDING
								}
							});

						// Calculate new due dates for each reminder
						const updates = updatedReminders.map(reminder => ({
							...reminder,
							title: updatedRule.setReminderFor.label,
							inventoryItemId: updatedRule.setReminderFor.value,
							inventoryType: updatedRule.setReminderFor.type,
							dueDate: new Date(
								Date.now() +
									(updatedRule.startAfterDays || 0) *
										24 *
										60 *
										60 *
										1000
							),
							isRecurring: !!updatedRule.recurrenceFrequency,
							recurrenceFrequency:
								updatedRule.recurrenceFrequency,
							recurrenceUnit: updatedRule.recurrenceUnit,
							recurrenceEndDate: updatedRule.recurrenceEndDate,
							recurrenceEndOccurrences:
								updatedRule.recurrenceEndOccurrences
						}));

						// Update reminders in chunks to avoid parameter limits
						const chunkSize = 100;
						for (let i = 0; i < updates.length; i += chunkSize) {
							const chunk = updates.slice(i, i + chunkSize);
							await this.patientReminderRepository.save(chunk);
						}

						this.logger.log(
							`Updated ${updates.length} reminders with new settings for rule ${id}`
						);
					}

					// Set the rule as active after successful update
					await this.globalReminderRuleRepository.update(
						{ id },
						{ isActive: true }
					);

					this.logger.log(
						`Successfully updated reminders for rule ${id}`
					);
				} catch (error) {
					this.logger.error(
						'Error updating reminders in background',
						{
							error:
								error instanceof Error
									? error.message
									: 'Unknown error',
							ruleId: id
						}
					);
				}
			});

			return updatedRule;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error updating global reminder rule', {
				error: error instanceof Error ? error.message : 'Unknown error',
				stack: error instanceof Error ? error.stack : undefined,
				ruleId: id,
				updateData: updateRuleDto
			});

			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async deleteRule(id: string): Promise<void> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// First check if rule exists
			const rule = await this.globalReminderRuleRepository.findOne({
				where: { id }
			});

			if (!rule) {
				throw new NotFoundException(
					`Global reminder rule with ID ${id} not found`
				);
			}

			// Delete all associated reminders first
			await queryRunner.manager
				.createQueryBuilder()
				.delete()
				.from(PatientReminder)
				.where('createdFromRuleId = :ruleId', { ruleId: id })
				.execute();

			// Then delete the rule
			await queryRunner.manager
				.createQueryBuilder()
				.delete()
				.from(GlobalReminderRule)
				.where('id = :id', { id })
				.execute();

			await queryRunner.commitTransaction();

			this.logger.log(
				`Successfully deleted rule ${id} and its associated reminders`
			);
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error(
				'Error deleting global reminder rule and reminders',
				{
					error:
						error instanceof Error
							? error.message
							: 'Unknown error',
					stack: error instanceof Error ? error.stack : undefined,
					ruleId: id
				}
			);
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	private calculateAge(
		dateString: string | undefined,
		unit: string = 'years'
	): number {
		if (!dateString || dateString === undefined) {
			throw new Error('Age date is null');
		}

		const today = new Date();
		const birthDate = new Date(dateString); // This will parse "09 Jan 2020" format correctly

		if (isNaN(birthDate.getTime())) {
			throw new Error(`Invalid date format: ${dateString}`);
		}

		const diffTime = Math.abs(today.getTime() - birthDate.getTime());

		switch (unit.toLowerCase()) {
			case 'days':
				return Math.floor(diffTime / (1000 * 60 * 60 * 24));
			case 'weeks':
				return Math.floor(diffTime / (1000 * 60 * 60 * 24 * 7));
			case 'months':
				return Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30.44));
			case 'years':
				return Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25));
			default:
				throw new Error(`Unsupported age unit: ${unit}`);
		}
	}

	async processRuleForAllPatients(ruleId: string): Promise<void> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const rule = await this.globalReminderRuleRepository.findOne({
				where: { id: ruleId }
			});

			if (
				!rule ||
				rule.triggerType !== ReminderTriggerType.SPECIES_BREED_AGE
			) {
				throw new BadRequestException('Invalid rule or rule type');
			}

			const whereClause: any = {
				clinicId: rule.clinicId,
				isDeceased: false
			};

			if (rule.speciesBreedAge?.species?.value) {
				whereClause.species = rule.speciesBreedAge.species.value;
			}
			if (rule.speciesBreedAge?.breed?.value) {
				whereClause.breed = rule.speciesBreedAge.breed.value;
			}
			if (rule.speciesBreedAge?.gender?.value) {
				whereClause.gender = rule.speciesBreedAge.gender.value;
			}

			const patients = await this.patientRepository.find({
				where: whereClause,
				select: ['id', 'clinicId', 'age']
			});

			let filteredPatients = patients;
			if (
				rule.speciesBreedAge?.ageRangeType &&
				rule.speciesBreedAge?.startCustomYr !== undefined
			) {
				filteredPatients = patients.filter(patient => {
					try {
						return this.checkPatientAge(
							patient.age,
							rule.speciesBreedAge!.ageRangeType!,
							rule.speciesBreedAge!.startCustomYr!,
							rule.speciesBreedAge!.startCustomMths,
							rule.speciesBreedAge!.endCustomYr,
							rule.speciesBreedAge!.endCustomMths
						);
					} catch (error) {
						this.logger.error(
							`Error checking age for patient ${patient.id}`,
							{ error }
						);
						return false;
					}
				});
			}

			const existingReminders = await queryRunner.manager.find(
				PatientReminder,
				{
					where: {
						createdFromRuleId: rule.id,
						status: In([
							ReminderStatus.PENDING,
							ReminderStatus.OVERRIDDEN
						])
					},
					select: ['id', 'patientId', 'status']
				}
			);

			const matchingPatientIds = new Set(filteredPatients.map(p => p.id));

			const remindersToDelete = existingReminders.filter(
				reminder =>
					!matchingPatientIds.has(reminder.patientId) &&
					reminder.status === ReminderStatus.PENDING
			);

			const remindersToUpdate = existingReminders.filter(
				reminder =>
					matchingPatientIds.has(reminder.patientId) &&
					reminder.status === ReminderStatus.PENDING
			);

			if (remindersToUpdate.length > 0) {
				const updates = remindersToUpdate.map(reminder => ({
					id: reminder.id,
					title: rule.setReminderFor.label,
					inventoryItemId: rule.setReminderFor.value,
					inventoryType: rule.setReminderFor.type,
					dueDate: new Date(
						Date.now() +
							(rule.startAfterDays || 0) * 24 * 60 * 60 * 1000
					),
					isRecurring: !!rule.recurrenceFrequency,
					recurrenceFrequency: rule.recurrenceFrequency,
					recurrenceUnit: rule.recurrenceUnit,
					recurrenceEndDate: rule.recurrenceEndDate,
					recurrenceEndOccurrences: rule.recurrenceEndOccurrences
				}));

				const updateChunkSize = 100;
				for (let i = 0; i < updates.length; i += updateChunkSize) {
					const chunk = updates.slice(i, i + updateChunkSize);
					await queryRunner.manager.save(PatientReminder, chunk);
				}
			}

			if (remindersToDelete.length > 0) {
				const chunkSize = 100;
				for (let i = 0; i < remindersToDelete.length; i += chunkSize) {
					const chunk = remindersToDelete.slice(i, i + chunkSize);
					await queryRunner.manager
						.createQueryBuilder()
						.delete()
						.from(PatientReminder)
						.where('id IN (:...ids)', {
							ids: chunk.map(r => r.id)
						})
						.execute();
				}
			}

			const existingPatientIds = new Set(
				existingReminders.map(r => r.patientId)
			);

			const newReminders = filteredPatients
				.filter(patient => !existingPatientIds.has(patient.id))
				.map(patient => ({
					patientId: patient.id,
					clinicId: patient.clinicId,
					brandId: rule.brandId,
					title: rule.setReminderFor.label,
					inventoryItemId: rule.setReminderFor.value,
					inventoryType: rule.setReminderFor.type,
					dueDate: new Date(
						Date.now() +
							(rule.startAfterDays || 0) * 24 * 60 * 60 * 1000
					),
					status: ReminderStatus.PENDING,
					isRecurring: !!rule.recurrenceFrequency,
					recurrenceFrequency: rule.recurrenceFrequency,
					recurrenceUnit: rule.recurrenceUnit,
					recurrenceEndDate: rule.recurrenceEndDate,
					recurrenceEndOccurrences: rule.recurrenceEndOccurrences,
					createdFromRuleId: rule.id
				}));

			if (newReminders.length > 0) {
				const chunkSize = 100;
				for (let i = 0; i < newReminders.length; i += chunkSize) {
					const chunk = newReminders.slice(i, i + chunkSize);
					await queryRunner.manager
						.createQueryBuilder()
						.insert()
						.into(PatientReminder)
						.values(chunk)
						.execute();
				}
			}

			rule.isActive = true;
			await queryRunner.manager.save(GlobalReminderRule, rule);
			await queryRunner.commitTransaction();

			this.logger.log(
				`Processed rule ${ruleId}: Updated ${remindersToUpdate.length}, Deleted ${remindersToDelete.length}, Created ${newReminders.length} reminders`
			);
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error in processRuleForAllPatients', {
				error: error instanceof Error ? error.message : 'Unknown error',
				ruleId
			});
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	private checkPatientAge(
		patientAge: string | undefined,
		ageRangeType: string,
		startCustomYr: string,
		startCustomMths?: string,
		endCustomYr?: string,
		endCustomMths?: string
	): boolean {
		if (!patientAge) return false;

		try {
			const ageInMonths = this.calculateAge(patientAge, 'months');
			const minAge = this.calculateAgeInMonths(
				startCustomYr,
				startCustomMths
			);
			const maxAge = this.calculateAgeInMonths(
				endCustomYr,
				endCustomMths
			);

			switch (ageRangeType) {
				case 'over':
					return ageInMonths >= minAge;
				case 'under':
					return ageInMonths <= minAge;
				case 'between':
					return ageInMonths >= minAge && ageInMonths <= maxAge;
				default:
					return false;
			}
		} catch (error) {
			this.logger.error('Error calculating age', { error, patientAge });
			return false;
		}
	}

	@Cron('0 0 * * *')
	async processSpeciesBreedAgeRules() {
		try {
			const rules = await this.globalReminderRuleRepository.find({
				where: {
					triggerType: ReminderTriggerType.SPECIES_BREED_AGE,
					isActive: true
				}
			});

			for (const rule of rules) {
				try {
					await this.processRuleForAllPatients(rule.id);
				} catch (error) {
					this.logger.error(`Error processing rule ${rule.id}`, {
						error:
							error instanceof Error
								? error.message
								: 'Unknown error',
						ruleId: rule.id
					});
					continue;
				}
			}

			this.logger.log(
				`Completed daily species/breed/age rules processing for ${rules.length} rules`
			);
		} catch (error) {
			this.logger.error(
				'Error in daily species/breed/age rules processing',
				{
					error:
						error instanceof Error ? error.message : 'Unknown error'
				}
			);
		}
	}

	async processInvoiceTriggers(
		patientId: string,
		clinicId: string,
		brandId: string,
		invoiceDetails: any[],
		invoiceId?: string
	): Promise<void> {
		try {
			const rules = await this.globalReminderRuleRepository.find({
				where: {
					clinicId,
					isActive: true,
					triggerType: ReminderTriggerType.PLAN_UPDATED
				}
			});

			if (rules.length === 0) return;

			for (const rule of rules) {
				const plans = invoiceDetails.map(detail => ({
					planId: detail.inventoryId,
					type: detail.itemType,
					name: detail.name
				}));

				await this.processPlanBasedRule(
					rule,
					plans,
					patientId,
					clinicId,
					brandId,
					invoiceId
				);
			}
		} catch (error) {
			this.logger.error('Error processing invoice triggers', {
				error: error instanceof Error ? error.message : 'Unknown error',
				patientId,
				clinicId
			});
			throw error;
		}
	}

	async processSpeciesBreedAgeRulesForPatient(
		patientId: string,
		clinicId: string,
		brandId: string
	): Promise<void> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const rules = await this.globalReminderRuleRepository.find({
				where: {
					clinicId,
					isActive: true,
					triggerType: ReminderTriggerType.SPECIES_BREED_AGE
				}
			});

			if (rules.length === 0) return;

			const patient = await this.patientRepository.findOne({
				where: { id: patientId }
			});

			if (!patient) {
				throw new NotFoundException(`Patient ${patientId} not found`);
			}

			// Get all existing reminders for this patient
			const existingReminders = await this.patientReminderRepository.find(
				{
					where: {
						patientId,
						status: ReminderStatus.PENDING,
						createdFromRuleId: In(rules.map(r => r.id))
					}
				}
			);

			// Create a set of rule IDs that still match the patient
			const matchingRuleIds = new Set<string>();

			for (const rule of rules) {
				try {
					const matchesAttributes = this.checkPatientAttributes(
						patient,
						rule.speciesBreedAge
					);

					if (matchesAttributes) {
						matchingRuleIds.add(rule.id);
						// Create reminder if it doesn't exist
						const existingReminder = existingReminders.find(
							r => r.createdFromRuleId === rule.id
						);

						if (!existingReminder) {
							await this.createReminderFromRule(
								rule,
								patientId,
								clinicId,
								brandId
							);
						}
					}
				} catch (error) {
					this.logger.error('Error processing rule for patient', {
						error:
							error instanceof Error
								? error.message
								: 'Unknown error',
						ruleId: rule.id,
						patientId
					});
					continue;
				}
			}

			// Delete reminders for rules that no longer match
			const remindersToDelete = existingReminders.filter(
				reminder =>
					reminder.createdFromRuleId &&
					!matchingRuleIds.has(reminder.createdFromRuleId)
			);

			if (remindersToDelete.length > 0) {
				await queryRunner.manager
					.createQueryBuilder()
					.delete()
					.from(PatientReminder)
					.where('id IN (:...ids)', {
						ids: remindersToDelete.map(r => r.id)
					})
					.execute();

				this.logger.log(
					`Deleted ${remindersToDelete.length} reminders that no longer match patient criteria`,
					{ patientId }
				);
			}

			await queryRunner.commitTransaction();
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error(
				'Error processing species/breed/age rules for patient',
				{
					error:
						error instanceof Error
							? error.message
							: 'Unknown error',
					patientId,
					clinicId
				}
			);
			throw error;
		} finally {
			await queryRunner.release();
		}
	}
}
