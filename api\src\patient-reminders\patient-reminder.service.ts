import {
	Injectable,
	NotFoundException,
	BadRequestException,
	forwardRef,
	Inject
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, DataSource, Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientReminder } from './entities/patient-reminder.entity';
import { PatientReminderHistory } from './entities/patient-reminder-history.entity';
import { CreateReminderDto } from './dto/create-reminder.dto';
import { UpdateReminderDto } from './dto/update-reminder.dto';
import { ReminderStatus, RecurrenceUnit } from './enums/reminder.enum';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { Patient } from '../patients/entities/patient.entity';
import { ReminderTriggerType } from '../patient-global-reminders/enums/reminder-trigger.enum';

@Injectable()
export class PatientRemindersService {
	constructor(
		@InjectRepository(PatientReminder)
		private readonly reminderRepository: Repository<PatientReminder>,
		@InjectRepository(PatientReminderHistory)
		private readonly historyRepository: Repository<PatientReminderHistory>,
		private readonly logger: WinstonLogger,
		private readonly dataSource: DataSource,
		private readonly globalReminderService: GlobalReminderService
	) {}

	async create(
		createReminderDto: CreateReminderDto,
		patientId: string,
		brandId: string
	): Promise<PatientReminder> {
		this.logger.log('Creating reminder', { dto: createReminderDto });

		// Check if patient is deceased
		const patient = await this.dataSource
			.getRepository(Patient)
			.findOne({ where: { id: patientId } });

		if (patient?.isDeceased) {
			throw new BadRequestException(
				'Cannot create reminders for deceased patients'
			);
		}

		const reminder = this.reminderRepository.create({
			...createReminderDto,
			patientId,
			brandId,
			status: ReminderStatus.PENDING
		});

		const savedReminder = await this.reminderRepository.save(reminder);

		// Create initial history entry
		await this.createHistoryEntry({
			reminder_id: savedReminder.id,
			previous_status: null,
			new_status: ReminderStatus.PENDING
		});

		return savedReminder;
	}

	async findAll(
		patientId: string,
		status?: ReminderStatus,
		page: number = 1,
		limit: number = 10
	): Promise<{ reminders: PatientReminder[]; total: number }> {
		// Check if patient is deceased
		const patient = await this.dataSource
			.getRepository(Patient)
			.findOne({ where: { id: patientId } });

		if (patient?.isDeceased) {
			return { reminders: [], total: 0 };
		}

		// First, check and create any applicable global reminders
		// await this.checkAndCreateGlobalReminders(patientId);

		const query = this.reminderRepository
			.createQueryBuilder('reminder')
			.where('reminder.patientId = :patientId', { patientId })
			.orderBy('reminder.dueDate', 'ASC');

		if (status) {
			query.andWhere('reminder.status = :status', { status });
		}

		const [reminders, total] = await query
			.skip((page - 1) * limit)
			.take(limit)
			.getManyAndCount();

		return { reminders, total };
	}

	async findOne(id: string, patientId: string): Promise<PatientReminder> {
		const reminder = await this.reminderRepository.findOne({
			where: { id, patientId },
			relations: ['history', 'history.changedByUser']
		});

		if (!reminder) {
			throw new NotFoundException(`Reminder with ID ${id} not found`);
		}

		return reminder;
	}

	async update(
		id: string,
		patientId: string,
		updateReminderDto: UpdateReminderDto
	): Promise<PatientReminder> {
		const reminder = await this.findOne(id, patientId);
		if (
			updateReminderDto.status &&
			updateReminderDto.status !== reminder.status
		) {
			await this.createHistoryEntry({
				reminder_id: id,
				previous_status: reminder.status,
				new_status: updateReminderDto.status
			});
		}

		Object.assign(reminder, updateReminderDto);
		return this.reminderRepository.save(reminder);
	}

	async remove(id: string, patientId: string): Promise<void> {
		const reminder = await this.findOne(id, patientId);
		await this.reminderRepository.remove(reminder);
	}

	async markComplete(
		id: string,
		patientId: string
	): Promise<PatientReminder> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Check if patient is deceased
			const patient = await queryRunner.manager.findOne(Patient, {
				where: { id: patientId }
			});

			if (patient?.isDeceased) {
				throw new BadRequestException(
					'Cannot complete reminders for deceased patients'
				);
			}

			const reminder = await queryRunner.manager.findOne(
				PatientReminder,
				{
					where: { id, patientId }
				}
			);

			if (!reminder) {
				throw new NotFoundException(`Reminder with ID ${id} not found`);
			}

			if (reminder.status === ReminderStatus.COMPLETED) {
				throw new BadRequestException('Reminder is already completed');
			}

			// Update only the current reminder
			const previousStatus = reminder.status;
			reminder.status = ReminderStatus.COMPLETED;
			reminder.completedAt = new Date();
			reminder.isManuallyCompleted = true;
			reminder.completedOccurrences =
				(reminder.completedOccurrences || 0) + 1;

			// Create history entry
			const historyEntry = new PatientReminderHistory();
			historyEntry.reminderId = id;
			historyEntry.previousStatus = previousStatus;
			historyEntry.newStatus = ReminderStatus.COMPLETED;
			historyEntry.changedAt = new Date();

			const updatedReminder = await queryRunner.manager.save(
				PatientReminder,
				reminder
			);
			await queryRunner.manager.save(
				PatientReminderHistory,
				historyEntry
			);

			// Only create next reminder if it's recurring
			if (reminder.isRecurring) {
				const nextDueDate = await this.calculateNextDueDate(
					reminder,
					reminder.completedAt
				);
				if (nextDueDate && reminder.recurrenceEndDate) {
					if (nextDueDate < reminder.recurrenceEndDate) {
						const nextReminder = this.createNextReminder(
							reminder,
							nextDueDate
						);
						await queryRunner.manager.save(
							PatientReminder,
							nextReminder
						);
					}
				} else {
					const nextReminder = this.createNextReminder(
						reminder,
						nextDueDate
					);
					await queryRunner.manager.save(
						PatientReminder,
						nextReminder
					);
				}
			}

			await queryRunner.commitTransaction();
			return updatedReminder;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error completing reminder', {
				error,
				reminderId: id
			});
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async markOverridden(
		id: string,
		patientId: string
	): Promise<PatientReminder> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Check if patient is deceased
			const patient = await queryRunner.manager.findOne(Patient, {
				where: { id: patientId }
			});

			if (patient?.isDeceased) {
				throw new BadRequestException(
					'Cannot modify reminders for deceased patients'
				);
			}

			const reminder = await queryRunner.manager.findOne(
				PatientReminder,
				{
					where: { id, patientId }
				}
			);

			if (!reminder) {
				throw new NotFoundException(`Reminder with ID ${id} not found`);
			}

			if (reminder.status === ReminderStatus.OVERRIDDEN) {
				throw new BadRequestException('Reminder is already completed');
			}

			// Update only the current reminder
			const previousStatus = reminder.status;
			reminder.status = ReminderStatus.OVERRIDDEN;

			// Create history entry
			const historyEntry = new PatientReminderHistory();
			historyEntry.reminderId = id;
			historyEntry.previousStatus = previousStatus;
			historyEntry.newStatus = ReminderStatus.OVERRIDDEN;
			historyEntry.changedAt = new Date();

			const updatedReminder = await queryRunner.manager.save(
				PatientReminder,
				reminder
			);
			await queryRunner.manager.save(
				PatientReminderHistory,
				historyEntry
			);

			await queryRunner.commitTransaction();
			return updatedReminder;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error completing reminder', {
				error,
				reminderId: id
			});
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async markIncomplete(
		id: string,
		patientId: string
	): Promise<PatientReminder> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Check if patient is deceased
			const patient = await queryRunner.manager.findOne(Patient, {
				where: { id: patientId }
			});

			if (patient?.isDeceased) {
				throw new BadRequestException(
					'Cannot modify reminders for deceased patients'
				);
			}

			const reminder = await queryRunner.manager.findOne(
				PatientReminder,
				{
					where: { id, patientId }
				}
			);

			if (!reminder) {
				throw new NotFoundException(`Reminder with ID ${id} not found`);
			}

			if (reminder.status !== ReminderStatus.COMPLETED) {
				throw new BadRequestException('Reminder is not completed');
			}

			if (!reminder.isManuallyCompleted) {
				throw new BadRequestException(
					'Only manually completed reminders can be marked as incomplete'
				);
			}

			const previousStatus = reminder.status;
			reminder.status = ReminderStatus.PENDING;
			reminder.completedAt = null;
			reminder.isManuallyCompleted = false;
			reminder.completedOccurrences = Math.max(
				0,
				(reminder.completedOccurrences || 1) - 1
			);

			// Create history entry
			const historyEntry = new PatientReminderHistory();
			historyEntry.reminderId = id;
			historyEntry.previousStatus = previousStatus;
			historyEntry.newStatus = ReminderStatus.PENDING;
			historyEntry.changedAt = new Date();

			const updatedReminder = await queryRunner.manager.save(
				PatientReminder,
				reminder
			);
			await queryRunner.manager.save(
				PatientReminderHistory,
				historyEntry
			);

			await queryRunner.commitTransaction();
			return updatedReminder;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async completeReminder(
		reminder: PatientReminder,
		quantity: number = 1,
		appointmentId?: string
	): Promise<void> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Check if patient is deceased
			const patient = await queryRunner.manager.findOne(Patient, {
				where: { id: reminder.patientId }
			});

			if (patient?.isDeceased) {
				throw new BadRequestException(
					'Cannot complete reminders for deceased patients'
				);
			}

			// Update current reminder
			reminder.status = ReminderStatus.COMPLETED;
			reminder.completedAt = new Date();
			reminder.completedAppointmentId = appointmentId;
			reminder.completedOccurrences =
				(reminder.completedOccurrences || 0) + 1;

			if (
				reminder.remainingQuantity !== undefined &&
				reminder.remainingQuantity !== null
			) {
				reminder.remainingQuantity = Math.max(
					0,
					reminder.remainingQuantity - quantity
				);
			}

			// Create completion history
			const history = new PatientReminderHistory();
			history.reminderId = reminder.id;
			history.previousStatus = ReminderStatus.PENDING;
			history.newStatus = ReminderStatus.COMPLETED;
			history.changedAt = new Date();

			// Handle recurring reminders with bulk completion
			if (reminder.isRecurring && quantity > 1) {
				const reminderDates = await this.generateReminderDates(
					reminder,
					quantity
				);

				for (const date of reminderDates.completedDates) {
					const nextReminder = this.createNextReminder(
						reminder,
						date,
						true
					);
					nextReminder.completedAt = new Date();
					nextReminder.completedAppointmentId = appointmentId;
					await queryRunner.manager.save(
						PatientReminder,
						nextReminder
					);
				}

				// Create next pending reminder if applicable
				if (reminderDates.nextPendingDate) {
					const pendingReminder = this.createNextReminder(
						reminder,
						reminderDates.nextPendingDate
					);
					await queryRunner.manager.save(
						PatientReminder,
						pendingReminder
					);
				}
			} else if (reminder.isRecurring) {
				// Single completion for recurring reminder
				const nextDueDate = await this.calculateNextDueDate(reminder);
				if (
					nextDueDate &&
					(!reminder.totalOccurrences ||
						(reminder.completedOccurrences || 0) <
							reminder.totalOccurrences)
				) {
					const nextReminder = this.createNextReminder(
						reminder,
						nextDueDate
					);
					await queryRunner.manager.save(
						PatientReminder,
						nextReminder
					);
				}
			}

			await queryRunner.manager.save(PatientReminder, reminder);
			await queryRunner.manager.save(PatientReminderHistory, history);
			await queryRunner.commitTransaction();
		} catch (error) {
			await queryRunner.rollbackTransaction();
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async getReminderStatus(reminder: PatientReminder): Promise<{
		status: ReminderStatus;
		nextDueDate?: Date;
		isOverdue: boolean;
	}> {
		const now = new Date();
		const dueDate = new Date(reminder.dueDate);

		if (reminder.status === ReminderStatus.COMPLETED) {
			const nextReminder = await this.reminderRepository.findOne({
				where: {
					completionSeriesId:
						reminder.completionSeriesId || reminder.id,
					status: ReminderStatus.PENDING
				},
				order: { dueDate: 'ASC' }
			});

			return {
				status: ReminderStatus.COMPLETED,
				nextDueDate: nextReminder?.dueDate,
				isOverdue: false
			};
		}

		if (dueDate < now) {
			return {
				status: reminder.isRecurring
					? ReminderStatus.MISSED
					: ReminderStatus.OVERDUE,
				isOverdue: true
			};
		}

		return {
			status: ReminderStatus.PENDING,
			isOverdue: false
		};
	}

	private createNextReminder(
		reminder: PatientReminder,
		nextDueDate: Date,
		isCompleted: boolean = false
	): PatientReminder {
		const nextReminder = new PatientReminder();
		Object.assign(nextReminder, {
			...reminder,
			id: undefined, // Let the DB generate a new ID
			status: isCompleted
				? ReminderStatus.COMPLETED
				: ReminderStatus.PENDING,
			dueDate: nextDueDate,
			completedAt: isCompleted ? new Date() : null,
			completedAppointmentId: isCompleted
				? reminder.completedAppointmentId
				: null,
			completionSeriesId: reminder.completionSeriesId || reminder.id
		});
		return nextReminder;
	}

	private async calculateNextDueDate(
		reminder: PatientReminder,
		fromDate: Date = new Date()
	): Promise<Date> {
		if (
			!reminder.isRecurring ||
			!reminder.recurrenceFrequency ||
			!reminder.recurrenceUnit
		) {
			return fromDate;
		}

		// If we've reached total occurrences or end date, no next date
		// if ((reminder.totalOccurrences && reminder.completedOccurrences !== undefined &&
		//      reminder.completedOccurrences >= reminder.totalOccurrences) ||
		//     (reminder.recurrenceEndDate && fromDate > reminder.recurrenceEndDate)) {
		//     return fromDate;
		// }

		const nextDate = new Date(fromDate);

		switch (reminder.recurrenceUnit) {
			case RecurrenceUnit.DAY:
				nextDate.setDate(
					nextDate.getDate() + reminder.recurrenceFrequency
				);
				break;
			case RecurrenceUnit.WEEK:
				nextDate.setDate(
					nextDate.getDate() + 7 * reminder.recurrenceFrequency
				);
				break;
			case RecurrenceUnit.MONTH:
				nextDate.setMonth(
					nextDate.getMonth() + reminder.recurrenceFrequency
				);
				break;
			case RecurrenceUnit.YEAR:
				nextDate.setFullYear(
					nextDate.getFullYear() + reminder.recurrenceFrequency
				);
				break;
		}

		return nextDate;
	}

	private async generateReminderDates(
		reminder: PatientReminder,
		quantity: number
	): Promise<{
		completedDates: Date[];
		nextPendingDate: Date;
	}> {
		const completedDates: Date[] = [];
		let currentDate = new Date(reminder.dueDate); // Create a new Date to avoid mutation
		const maxOccurrences = reminder.totalOccurrences
			? Math.min(
					quantity,
					reminder.totalOccurrences -
						(reminder.completedOccurrences || 0)
				)
			: quantity;

		for (let i = 0; i < maxOccurrences - 1; i++) {
			currentDate = await this.calculateNextDueDate(
				reminder,
				currentDate
			);

			if (
				reminder.recurrenceEndDate &&
				currentDate > reminder.recurrenceEndDate
			) {
				break;
			}

			completedDates.push(new Date(currentDate));
		}

		// Calculate next pending date
		let nextPendingDate = await this.calculateNextDueDate(
			reminder,
			currentDate
		);

		// If there's an end date and the next date would exceed it,
		// or if we've reached total occurrences, use the last valid date
		if (
			(reminder.recurrenceEndDate &&
				nextPendingDate > reminder.recurrenceEndDate) ||
			(reminder.totalOccurrences &&
				(reminder.completedOccurrences || 0) + completedDates.length >=
					reminder.totalOccurrences)
		) {
			nextPendingDate = currentDate;
		}

		return {
			completedDates,
			nextPendingDate
		};
	}

	private async createHistoryEntry(params: {
		reminder_id: string;
		previous_status: ReminderStatus | null;
		new_status: ReminderStatus;
	}): Promise<PatientReminderHistory> {
		// Create without using repository.create
		const history = new PatientReminderHistory();
		history.reminderId = params.reminder_id;
		history.previousStatus = params.previous_status as ReminderStatus; // Type assertion since we know null is acceptable
		history.newStatus = params.new_status;
		history.changedAt = new Date();

		return this.historyRepository.save(history);
	}

	// @Cron(CronExpression.EVERY_HOUR)
	// async checkAndUpdateReminderStatuses(): Promise<void> {
	//     try {
	//         const now = new Date();
	//         this.logger.log('Running reminder status update cron');

	//         // Update one-time reminders
	//         await this.reminderRepository
	//             .createQueryBuilder()
	//             .update(PatientReminder)
	//             .set({ status: ReminderStatus.OVERDUE })
	//             .where('isRecurring = :isRecurring', { isRecurring: false })
	//             .andWhere('status = :status', { status: ReminderStatus.PENDING })
	//             .andWhere('dueDate < :now', { now })
	//             .execute();

	//         // Handle recurring reminders
	//         const missedRecurringReminders = await this.reminderRepository.find({
	//             where: {
	//                 isRecurring: true,
	//                 status: ReminderStatus.PENDING,
	//                 dueDate: new Date(now.getTime() - 24 * 60 * 60 * 1000) // 24 hours ago
	//             }
	//         });

	//         for (const reminder of missedRecurringReminders) {
	//             reminder.status = ReminderStatus.MISSED;
	//             await this.reminderRepository.save(reminder);

	//             await this.createHistoryEntry({
	//                 reminder_id: reminder.id,
	//                 previous_status: ReminderStatus.PENDING,
	//                 new_status: ReminderStatus.MISSED
	//             });

	//             await this.createNextRecurringReminder(reminder);
	//         }

	//         this.logger.log('Reminder status update completed', {
	//             updatedCount: missedRecurringReminders.length
	//         });
	//     } catch (error) {
	//         this.logger.error('Error in reminder status update cron', { error });
	//     }
	// }

	// private async createNextRecurringReminder(
	//     completedReminder: PatientReminder
	// ): Promise<PatientReminder | null> {
	//     if (!completedReminder.recurrenceFrequency || !completedReminder.recurrenceUnit) {
	//         return null;
	//     }

	//     const nextDueDate = this.calculateNextDueDate(
	//         completedReminder.dueDate,
	//         completedReminder.recurrenceFrequency,
	//         completedReminder.recurrenceUnit
	//     );

	//     if (completedReminder.recurrenceEndDate && nextDueDate > completedReminder.recurrenceEndDate) {
	//         return null;
	//     }

	//     // Create new instance without using repository.create
	//     const newReminder = new PatientReminder();
	//     // Copy only the fields we want to duplicate
	//     newReminder.patientId = completedReminder.patientId;
	//     newReminder.clinicId = completedReminder.clinicId;
	//     newReminder.title = completedReminder.title;
	//     newReminder.inventoryItemId = completedReminder.inventoryItemId;
	//     newReminder.inventoryType = completedReminder.inventoryType;
	//     newReminder.dueDate = nextDueDate;
	//     newReminder.status = ReminderStatus.PENDING;
	//     newReminder.isRecurring = completedReminder.isRecurring;
	//     newReminder.recurrenceFrequency = completedReminder.recurrenceFrequency;
	//     newReminder.recurrenceUnit = completedReminder.recurrenceUnit;
	//     newReminder.recurrenceEndDate = completedReminder.recurrenceEndDate;
	//     newReminder.recurrenceEndOccurrences = completedReminder.recurrenceEndOccurrences;
	//     newReminder.parentReminderId = completedReminder.id;

	//     return this.reminderRepository.save(newReminder);
	// }

	// private async checkAndCreateGlobalReminders(
	// 	patientId: string
	// ): Promise<void> {
	// 	try {
	// 		// Get the patient details
	// 		const patient = await this.dataSource
	// 			.getRepository(Patient)
	// 			.findOne({
	// 				where: { id: patientId }
	// 			});

	// 		if (!patient) {
	// 			throw new NotFoundException(
	// 				`Patient with ID ${patientId} not found`
	// 			);
	// 		}

	// 		// Get all active global rules for the patient's clinic
	// 		const rules = await this.globalReminderService.getRulesByClinic(
	// 			patient.clinicId
	// 		);

	// 		for (const rule of rules) {
	// 			// Check if a reminder already exists for this rule and patient
	// 			const existingReminder = await this.reminderRepository.findOne({
	// 				where: {
	// 					patientId,
	// 					createdFromRuleId: rule.id,
	// 					status: ReminderStatus.PENDING
	// 				}
	// 			});

	// 			if (
	// 				rule.triggerType === ReminderTriggerType.SPECIES_BREED_AGE
	// 			) {
	// 				// Check if patient still matches the criteria
	// 				const stillMatches = await this.checkPatientAttributes(
	// 					patient,
	// 					rule.speciesBreedAge
	// 				);

	// 				if (existingReminder) {
	// 					if (!stillMatches) {
	// 						// Delete the reminder if it no longer matches
	// 						await this.reminderRepository.remove(
	// 							existingReminder
	// 						);
	// 						this.logger.log(
	// 							`Deleted reminder ${existingReminder.id} as it no longer matches the criteria`
	// 						);
	// 					}
	// 					continue;
	// 				}

	// 				// Create new reminder if criteria matches and no reminder exists
	// 				if (stillMatches) {
	// 					await this.createReminderFromRule(rule, patient);
	// 				}
	// 			} else {
	// 				// For other trigger types
	// 				if (existingReminder) {
	// 					continue; // Skip if reminder already exists
	// 				}
	// 				// For other trigger types, we'll create the reminder when the trigger occurs
	// 				continue;
	// 			}
	// 		}
	// 	} catch (error) {
	// 		this.logger.error('Error checking global reminders', {
	// 			error,
	// 			patientId
	// 		});
	// 		// Don't throw the error to prevent breaking the main findAll flow
	// 	}
	// }

	// private async checkPatientAttributes(
	// 	patient: Patient,
	// 	criteria: any
	// ): Promise<boolean> {
	// 	if (!criteria) return false;

	// 	// Check species
	// 	if (criteria.species && patient.species !== criteria.species.value) {
	// 		return false;
	// 	}

	// 	// Check breed
	// 	if (criteria.breed && patient.breed !== criteria.breed.value) {
	// 		return false;
	// 	}

	// 	// Check gender
	// 	if (criteria.gender && patient.gender !== criteria.gender.value) {
	// 		return false;
	// 	}

	// 	// Check age range if specified
	// 	if (criteria.ageRangeType && patient.age) {
	// 		const patientAge = this.calculateAgeInMonths(patient.age);
	// 		const minAge = this.calculateAgeInMonths(
	// 			criteria.startCustomYr?.toString(),
	// 			criteria.startCustomMths?.toString()
	// 		);
	// 		const maxAge = this.calculateAgeInMonths(
	// 			criteria.endCustomYr?.toString(),
	// 			criteria.endCustomMths?.toString()
	// 		);

	// 		switch (criteria.ageRangeType) {
	// 			case 'over':
	// 				if (patientAge < minAge) return false;
	// 				break;
	// 			case 'under':
	// 				if (patientAge > minAge) return false;
	// 				break;
	// 			case 'between':
	// 				if (patientAge < minAge || patientAge > maxAge)
	// 					return false;
	// 				break;
	// 		}
	// 	}

	// 	return true;
	// }

	// private async createReminderFromRule(
	// 	rule: any,
	// 	patient: Patient
	// ): Promise<void> {
	// 	const reminder = new PatientReminder();
	// 	reminder.patientId = patient.id;
	// 	reminder.clinicId = patient.clinicId;
	// 	reminder.title = rule.setReminderFor.label;
	// 	reminder.inventoryItemId = rule.setReminderFor.value;
	// 	reminder.inventoryType = rule.setReminderFor.type;
	// 	reminder.dueDate = new Date(
	// 		Date.now() + (rule.startAfterDays || 0) * 24 * 60 * 60 * 1000
	// 	);
	// 	reminder.status = ReminderStatus.PENDING;
	// 	reminder.isRecurring = !!rule.recurrenceFrequency;
	// 	reminder.recurrenceFrequency = rule.recurrenceFrequency;
	// 	reminder.recurrenceUnit = rule.recurrenceUnit;
	// 	reminder.recurrenceEndDate = rule.recurrenceEndDate;
	// 	reminder.recurrenceEndOccurrences = rule.recurrenceEndOccurrences;
	// 	reminder.createdFromRuleId = rule.id;

	// 	await this.reminderRepository.save(reminder);

	// 	// Create initial history entry
	// 	await this.createHistoryEntry({
	// 		reminder_id: reminder.id,
	// 		previous_status: null,
	// 		new_status: ReminderStatus.PENDING
	// 	});
	// }

	// private calculateAgeInMonths(years?: string, months?: string): number {
	// 	const totalMonths = (Number(years) || 0) * 12 + (Number(months) || 0);
	// 	return totalMonths;
	// }

	/**
	 * Hard delete patient reminders by invoice ID
	 * Used when an invoice is deleted or updated
	 */
	async hardDeleteByInvoiceId(
		invoiceId: string,
		entityManager?: any
	): Promise<void> {
		try {
			const manager = entityManager || this.dataSource.manager;

			// Delete reminders that were created from this specific invoice
			// Uses the new invoice_id column added to patient_reminders table

			const result = await manager
				.createQueryBuilder()
				.delete()
				.from(PatientReminder)
				.where('invoice_id = :invoiceId', { invoiceId })
				.execute();

			this.logger.log('Hard deleted patient reminders by invoice ID', {
				invoiceId,
				deletedCount: result.affected
			});
		} catch (error) {
			this.logger.error(
				'Failed to hard delete patient reminders by invoice ID',
				{
					error,
					invoiceId
				}
			);
			throw error;
		}
	}

	/**
	 * Undo reminder completions that were completed by a specific appointment
	 * Used when impromptu invoices are updated/deleted
	 */
	async undoCompletionsByAppointmentId(
		appointmentId: string,
		entityManager?: any
	): Promise<void> {
		try {
			const manager = entityManager || this.dataSource.manager;

			// Find all reminders that were completed by this appointment
			const completedReminders = await manager.find(PatientReminder, {
				where: {
					completedAppointmentId: appointmentId,
					status: ReminderStatus.COMPLETED
				}
			});

			if (completedReminders.length === 0) {
				this.logger.log('No reminders found to undo for appointment', {
					appointmentId
				});
				return;
			}

			this.logger.log('Undoing reminder completions for appointment', {
				appointmentId,
				reminderCount: completedReminders.length
			});

			// Undo completion for each reminder
			for (const reminder of completedReminders) {
				const previousStatus = reminder.status;

				// Reset completion fields
				reminder.status = ReminderStatus.PENDING;
				reminder.completedAt = null;
				reminder.completedAppointmentId = null;
				reminder.completedOccurrences = Math.max(
					0,
					(reminder.completedOccurrences || 1) - 1
				);

				// Save changes
				await manager.save(PatientReminder, reminder);

				this.logger.log('Undid reminder completion', {
					reminderId: reminder.id,
					appointmentId,
					previousStatus,
					newStatus: ReminderStatus.PENDING
				});
			}

			this.logger.log(
				'Successfully undid all reminder completions for appointment',
				{
					appointmentId,
					undoneCount: completedReminders.length
				}
			);
		} catch (error) {
			this.logger.error(
				'Error undoing reminder completions by appointment',
				{
					error:
						error instanceof Error ? error.message : String(error),
					appointmentId
				}
			);
			throw error;
		}
	}
}
