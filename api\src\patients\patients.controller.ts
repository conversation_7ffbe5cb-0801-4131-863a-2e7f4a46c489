import {
	Controller,
	Get,
	Post,
	Body,
	Patch,
	Param,
	Query,
	// UseGuards,
	HttpException,
	HttpStatus,
	NotFoundException,
	Put,
	UseGuards,
	Req
} from '@nestjs/common';
import { PatientsService } from './patients.service';
import { CreatePatientDto } from './dto/create-patient.dto';
import { UpdatePatientDTO } from './dto/update-patient.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import {
	ApiTags,
	ApiResponse,
	ApiQuery,
	ApiOperation,
	ApiParam,
	ApiBearerAuth
	// ApiBearerAuth
} from '@nestjs/swagger';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Patients')
@ApiBearerAuth()
@Controller('patients')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PatientsController {
	constructor(
		private readonly patientsService: PatientsService,
		private readonly logger: WinstonLogger
	) {}

	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create a new patient' })
	@ApiResponse({
		status: 201,
		description: 'The patient has been successfully created.',
		type: CreatePatientDto
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('createPatient')
	async create(
		@Body() createPatientDto: CreatePatientDto,
		@Req() req: { user: { brandId: string } }
	) {
		try {
			console.log(createPatientDto);
			this.logger.log('Creating new patient', { dto: createPatientDto });
			const patient = await this.patientsService.create(
				createPatientDto,
				req.user.brandId
			);
			this.logger.log('Patient created successfully', {
				patientId: patient.id
			});
			return patient;
		} catch (error) {
			this.logger.error('Error creating patient', { error });
			throw new HttpException(
				'Failed to create patient',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get all patients' })
	@ApiQuery({
		name: 'page',
		required: false,
		type: Number,
		description: 'Page number for pagination'
	})
	@ApiQuery({
		name: 'limit',
		required: false,
		type: Number,
		description: 'Number of items per page'
	})
	@ApiResponse({
		status: 200,
		description: 'List of patients with owner information'
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('getAllPatients')
	async getAllPatients(
		@Query('page') page: number = 1,
		@Query('limit') limit: number = 10,
		@Query('searchTerm') searchTerm: string = '',
		@Query('withBalance') withBalance: string,
		@Req() req: { user: { clinicId: string } }
	) {
		try {
			const withBalanceFlag = withBalance === 'true';
			this.logger.log('Fetching all patients', {
				page,
				limit,
				searchTerm
			});
			const result = await this.patientsService.getAllPatients(
				page,
				limit,
				searchTerm,
				withBalanceFlag,
				req.user.clinicId
			);
			this.logger.log('Patients fetched successfully');
			return result;
		} catch (error) {
			this.logger.error('Error fetching patients', { error });
			throw new HttpException(
				'Failed to fetch patients',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get a patient by id' })
	@ApiParam({ name: 'id', type: 'string', description: 'Patient ID' })
	@ApiResponse({
		status: 200,
		description: 'The patient details',
		type: CreatePatientDto
	})
	@ApiResponse({ status: 404, description: 'Patient not found' })
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('getPatientById')
	async getPatient(@Param('id') id: string) {
		try {
			this.logger.log('Fetching patient details', { patientId: id });
			const patient = await this.patientsService.getPatientDetails(id);
			this.logger.log('Patient details fetched successfully', {
				patientId: id
			});
			return patient;
		} catch (error) {
			this.logger.error('Error fetching patient details', {
				error,
				patientId: id
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new HttpException(
				'Failed to fetch patient details',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Put(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update a patient' })
	@ApiParam({ name: 'id', type: 'string', description: 'Patient ID' })
	@ApiResponse({
		status: 200,
		description: 'The patient has been successfully updated.',
		type: UpdatePatientDTO
	})
	@ApiResponse({ status: 404, description: 'Patient not found' })
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('updatePatient')
	async updatePatient(
		@Param('id') id: string,
		@Body() updatePatientDto: UpdatePatientDTO
	) {
		try {
			this.logger.log('Updating patient', {
				patientId: id,
				dto: updatePatientDto
			});
			const result = await this.patientsService.updatePatient(
				id,
				updatePatientDto
			);

			if ('error' in result) {
				throw new HttpException(
					result.error,
					result.statusCode || HttpStatus.INTERNAL_SERVER_ERROR
				);
			}

			this.logger.log('Patient updated successfully', { patientId: id });
			return result;
		} catch (error) {
			this.logger.error('Error updating patient', {
				error,
				patientId: id
			});
			if (error instanceof HttpException) {
				throw error;
			}
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new HttpException(
				'Failed to update patient',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('clinic/:clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get all patients for a specific clinic' })
	@ApiParam({
		name: 'clinicId',
		type: 'string',
		description: 'ID of the clinic'
	})
	@ApiQuery({
		name: 'page',
		required: false,
		type: Number,
		description: 'Page number for pagination'
	})
	@ApiQuery({
		name: 'limit',
		required: false,
		type: Number,
		description: 'Number of items per page'
	})
	@ApiQuery({
		name: 'search',
		required: false,
		type: String,
		description: 'Search patient by name, or owner info'
	})
	@ApiResponse({
		status: 200,
		description: 'List of patients for the specified clinic'
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@ApiResponse({ status: 404, description: 'Clinic not found.' })
	@TrackMethod('getClinicPatients')
	async getClinicPatients(
		@Param('clinicId') clinicId: string,
		@Query('page') page: number = 1,
		@Query('limit') limit: number = 10,
		@Query('search') search: string
	) {
		try {
			this.logger.log('Fetching patients for clinic', {
				clinicId,
				page,
				limit,
				search
			});
			const result = await this.patientsService.getClinicPatients(
				clinicId,
				page,
				limit,
				search
			);
			this.logger.log('Patients fetched successfully for clinic', {
				clinicId
			});
			return result;
		} catch (error) {
			this.logger.error('Error fetching patients for clinic', {
				error,
				clinicId
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new HttpException(
				'Failed to fetch patients for clinic',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
