import {
	Injectable,
	NotFoundException,
	BadRequestException,
	InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
	Repository,
	DataSource,
	UpdateResult,
	Brackets,
	QueryRunner,
	In
} from 'typeorm';
import { Patient } from './entities/patient.entity';
import { PatientOwner } from './entities/patient-owner.entity';
import { CreatePatientDto } from './dto/create-patient.dto';
import { UpdatePatientDTO } from './dto/update-patient.dto';
import { OwnersService } from '../owners/owners.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import moment = require('moment');
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { Not } from 'typeorm';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { EnumInvoiceStatus } from '../invoice/enums/enum-invoice-status';

@Injectable()
export class PatientsService {
	constructor(
		@InjectRepository(Patient)
		private patientsRepository: Repository<Patient>,
		@InjectRepository(PatientOwner)
		private patientOwnersRepository: Repository<PatientOwner>,
		private readonly ownersService: OwnersService,
		private dataSource: DataSource,
		private readonly logger: WinstonLogger,
		private readonly globalReminderService: GlobalReminderService,
		@InjectRepository(GlobalOwner)
		private globalOwnersRepository: Repository<GlobalOwner>,
		@InjectRepository(OwnerBrand)
		private ownerBrandsRepository: Repository<OwnerBrand>,
		@InjectRepository(InvoiceEntity)
		private invoiceRepository: Repository<InvoiceEntity>
	) {}

	async create(
		createPatientDto: CreatePatientDto,
		brandId: string
	): Promise<Patient> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Starting patient creation process', { brandId });
			const { ownersData, ...patientData } = createPatientDto;

			// Validate owners data
			if (!ownersData?.length) {
				this.logger.warn(
					'Patient creation failed - No owners provided'
				);
				throw new BadRequestException(
					'At least one owner must be provided'
				);
			}

			// Create patient record
			const patient = this.patientsRepository.create({
				...patientData,
				brandId
			});

			const savedPatient = await queryRunner.manager.save(
				Patient,
				patient
			);
			this.logger.log('Patient record created successfully', {
				patientId: savedPatient.id,
				brandId
			});

			// Process each owner
			for (const ownerData of ownersData) {
				this.logger.log('Processing owner data', {
					phoneNumber: ownerData.phoneNumber,
					patientId: savedPatient.id
				});

				try {
					const owner =
						await this.ownersService.findOrCreateOwnerBrand(
							{
								phoneNumber: ownerData.phoneNumber,
								countryCode: ownerData.countryCode || '',
								firstName: ownerData.firstName,
								lastName: ownerData.lastName,
								email: ownerData.email,
								address: ownerData.address
							},
							brandId,
							queryRunner
						);

					this.logger.log('Owner brand processed successfully', {
						ownerId: owner.id,
						patientId: savedPatient.id
					});

					// Create patient-owner relationship
					const patientOwner = this.patientOwnersRepository.create({
						patientId: savedPatient.id,
						ownerId: owner.id,
						clinicId: patient.clinicId,
						brandId,
						isPrimary: ownerData.isPrimary,
						globalOwnerId: owner.globalOwnerId
					});

					await queryRunner.manager.save(PatientOwner, patientOwner);
					this.logger.log('Patient-owner relationship created', {
						patientId: savedPatient.id,
						ownerId: owner.id,
						globalOwnerId: owner.globalOwnerId
					});
				} catch (ownerError) {
					this.logger.error('Error processing owner', {
						error: ownerError,
						phoneNumber: ownerData.phoneNumber,
						patientId: savedPatient.id
					});
					throw ownerError; // Re-throw to trigger rollback
				}
			}

			await queryRunner.commitTransaction();
			this.logger.log('Patient creation completed successfully', {
				patientId: savedPatient.id
			});

			return this.findOne(savedPatient.id);
		} catch (error) {
			await queryRunner.rollbackTransaction();

			this.logger.error('Patient creation failed', {
				error: error,
				brandId,
				dto: createPatientDto
			});

			if (error instanceof BadRequestException) {
				throw error; // Re-throw validation errors
			}

			throw new InternalServerErrorException(
				'Failed to create patient. Please try again or contact support.'
			);
		} finally {
			await queryRunner.release();
		}
	}

	// Helper methods first
	private async isPhoneNumberInUse(
		phoneNumber: string,
		excludeGlobalOwnerId?: string,
		queryRunner?: QueryRunner
	): Promise<boolean> {
		const repository = queryRunner
			? queryRunner.manager.getRepository(GlobalOwner)
			: this.globalOwnersRepository;

		const existingOwner = await repository.findOne({
			where: {
				phoneNumber,
				...(excludeGlobalOwnerId && { id: Not(excludeGlobalOwnerId) })
			}
		});

		return !!existingOwner;
	}

	private async updateOwnerDetails(
		ownerBrand: OwnerBrand,
		ownerData: {
			firstName: string;
			lastName: string;
			email?: string;
			address?: string;
			phoneNumber: string;
			countryCode: string;
		},
		queryRunner: QueryRunner
	): Promise<{ success: boolean; error?: string }> {
		try {
			// Check if phone number is being changed
			const isPhoneChanged =
				ownerBrand.globalOwner.phoneNumber !== ownerData.phoneNumber;

			if (isPhoneChanged) {
				// Check if new phone number is already in use
				const isPhoneInUse = await this.isPhoneNumberInUse(
					ownerData.phoneNumber,
					ownerBrand.globalOwnerId,
					queryRunner
				);

				if (isPhoneInUse) {
					return {
						success: false,
						error: 'This phone number is already associated with another user'
					};
				}

				// Update global owner phone details
				await queryRunner.manager.update(
					GlobalOwner,
					{ id: ownerBrand.globalOwnerId },
					{
						phoneNumber: ownerData.phoneNumber,
						countryCode: ownerData.countryCode
					}
				);
			}

			// Update owner brand details
			await queryRunner.manager.update(
				OwnerBrand,
				{ id: ownerBrand.id },
				{
					firstName: ownerData.firstName,
					lastName: ownerData.lastName,
					email: ownerData.email,
					address: ownerData.address
				}
			);

			return { success: true };
		} catch (error) {
			this.logger.error('Error updating owner details', error);
			return {
				success: false,
				error: 'Failed to update owner details'
			};
		}
	}

	// async updatePatient(
	// 	id: string,
	// 	updatePatientDto: UpdatePatientDTO
	// ): Promise<Patient> {
	// 	const queryRunner = this.dataSource.createQueryRunner();

	// 	await queryRunner.connect();
	// 	await queryRunner.startTransaction();

	// 	try {
	// 		this.logger.log('Updating patient', { patientId: id });

	// 		const patient = await this.patientsRepository.findOne({
	// 			where: { id },
	// 			relations: ['patientOwners', 'patientOwners.owner']
	// 		});

	// 		if (!patient) {
	// 			throw new NotFoundException(
	// 				`Patient with ID "${id}" not found`
	// 			);
	// 		}

	// 		// Save old owner IDs before any updates
	// 		const oldOwnerIds = new Set(
	// 			patient.patientOwners.map(po => po.owner.id)
	// 		);

	// 		this.logger.log('Saved existing owner IDs before update', {
	// 			patientId: id,
	// 			oldOwnerIds: [...oldOwnerIds]
	// 		});

	// 		Object.assign(patient, updatePatientDto);

	// 		await queryRunner.manager.remove(patient.patientOwners);

	// 		const newPatientOwners: PatientOwner[] = [];

	// 		for (const ownerData of updatePatientDto.ownersData) {
	// 			let owner = await this.ownersRepository.findOne({
	// 				where: [{ phoneNumber: ownerData.phoneNumber }]
	// 			});

	// 			if (owner) {
	// 				Object.assign(owner, {
	// 					firstName: ownerData.firstName,
	// 					lastName: ownerData.lastName,
	// 					phoneNumber: ownerData.phoneNumber,
	// 					countryCode: ownerData.countryCode,
	// 					email: ownerData.email,
	// 					address: ownerData.address
	// 				});
	// 			} else {
	// 				owner = this.ownersRepository.create({
	// 					firstName: ownerData.firstName,
	// 					lastName: ownerData.lastName,
	// 					phoneNumber: ownerData.phoneNumber,
	// 					countryCode: ownerData.countryCode,
	// 					email: ownerData.email,
	// 					address: ownerData.address
	// 				});
	// 			}

	// 			owner = await queryRunner.manager.save(Owner, owner);

	// 			const newPatientOwner = this.patientOwnersRepository.create({
	// 				patient: patient,
	// 				owner: owner,
	// 				isPrimary: ownerData.isPrimary
	// 			});

	// 			newPatientOwners.push(
	// 				await queryRunner.manager.save(newPatientOwner)
	// 			);
	// 		}

	// 		patient.patientOwners = newPatientOwners;

	// 		await queryRunner.manager.save(patient);

	// 		// Handle payment details for owner changes
	// 		this.logger.log(
	// 			'Starting payment details update for owner changes',
	// 			{
	// 				patientId: id,
	// 				oldOwnerCount: oldOwnerIds.size,
	// 				newOwnerCount: newPatientOwners.length
	// 			}
	// 		);

	// 		const newOwnerIds = new Set(
	// 			newPatientOwners.map(po => po.owner.id)
	// 		);

	// 		// Find owners that were removed
	// 		const removedOwnerIds = [...oldOwnerIds].filter(
	// 			id => !newOwnerIds.has(id)
	// 		);
	// 		// Find owners that were added
	// 		const addedOwnerIds = [...newOwnerIds].filter(
	// 			id => !oldOwnerIds.has(id)
	// 		);

	// 		this.logger.log('Owner changes detected', {
	// 			patientId: id,
	// 			removedOwnerCount: removedOwnerIds.length,
	// 			addedOwnerCount: addedOwnerIds.length,
	// 			removedOwnerIds,
	// 			addedOwnerIds
	// 		});

	// 		if (removedOwnerIds.length > 0 && addedOwnerIds.length > 0) {
	// 			// First get all existing payment details for removed owners
	// 			this.logger.log(
	// 				'Fetching existing payment details for removed owners',
	// 				{
	// 					patientId: id,
	// 					removedOwnerIds
	// 				}
	// 			);

	// 			const existingPaymentDetails = await queryRunner.manager.find(
	// 				PaymentDetailsEntity,
	// 				{
	// 					where: {
	// 						patientId: id,
	// 						ownerId: In(removedOwnerIds)
	// 					}
	// 				}
	// 			);

	// 			// Group entries by referenceAlphaId
	// 			const entriesByReference = new Map();
	// 			existingPaymentDetails.forEach(entry => {
	// 				if (entry.referenceAlphaId) {
	// 					if (!entriesByReference.has(entry.referenceAlphaId)) {
	// 						entriesByReference.set(entry.referenceAlphaId, []);
	// 					}
	// 					entriesByReference
	// 						.get(entry.referenceAlphaId)
	// 						.push(entry);
	// 				}
	// 			});

	// 			// Hide entries for removed owners
	// 			this.logger.log(
	// 				'Updating visibility flags for removed owners',
	// 				{
	// 					patientId: id,
	// 					removedOwnerIds
	// 				}
	// 			);

	// 			await queryRunner.manager.update(
	// 				PaymentDetailsEntity,
	// 				{
	// 					patientId: id,
	// 					ownerId: In(removedOwnerIds)
	// 				},
	// 				{
	// 					showInInvoice: false,
	// 					showInLedger: true
	// 				}
	// 			);

	// 			// For each added owner, check if they already have entries with these referenceAlphaIds
	// 			for (const newOwnerId of addedOwnerIds) {
	// 				this.logger.log('Processing added owner', {
	// 					patientId: id,
	// 					ownerId: newOwnerId
	// 				});

	// 				// Get existing entries for this owner with matching referenceAlphaIds
	// 				const existingOwnerEntries = await queryRunner.manager.find(
	// 					PaymentDetailsEntity,
	// 					{
	// 						where: {
	// 							patientId: id,
	// 							ownerId: newOwnerId,
	// 							referenceAlphaId: In([
	// 								...entriesByReference.keys()
	// 							])
	// 						}
	// 					}
	// 				);

	// 				// Create a set of referenceAlphaIds that this owner already has
	// 				const existingReferenceIds = new Set(
	// 					existingOwnerEntries.map(
	// 						entry => entry.referenceAlphaId
	// 					)
	// 				);

	// 				// Update visibility for existing entries
	// 				if (existingOwnerEntries.length > 0) {
	// 					this.logger.log(
	// 						'Updating visibility for existing entries',
	// 						{
	// 							patientId: id,
	// 							ownerId: newOwnerId,
	// 							count: existingOwnerEntries.length
	// 						}
	// 					);

	// 					await queryRunner.manager.update(
	// 						PaymentDetailsEntity,
	// 						{
	// 							patientId: id,
	// 							ownerId: newOwnerId,
	// 							referenceAlphaId: In([...existingReferenceIds])
	// 						},
	// 						{
	// 							showInInvoice: true,
	// 							showInLedger: true
	// 						}
	// 					);
	// 				}

	// 				// Create new entries only for referenceAlphaIds that don't exist for this owner
	// 				const entriesToCreate: Partial<PaymentDetailsEntity>[] = [];
	// 				entriesByReference.forEach((entries, referenceId) => {
	// 					if (!existingReferenceIds.has(referenceId)) {
	// 						// Use the first entry as template
	// 						const templateEntry = entries[0];
	// 						entriesToCreate.push({
	// 							...templateEntry,
	// 							id: undefined,
	// 							ownerId: newOwnerId,
	// 							referenceAlphaId: referenceId,
	// 							showInInvoice: true,
	// 							showInLedger: false
	// 						});
	// 					}
	// 				});

	// 				if (entriesToCreate.length > 0) {
	// 					this.logger.log('Creating new payment details', {
	// 						patientId: id,
	// 						ownerId: newOwnerId,
	// 						count: entriesToCreate.length
	// 					});

	// 					await queryRunner.manager.insert(
	// 						PaymentDetailsEntity,
	// 						entriesToCreate
	// 					);
	// 				}
	// 			}
	// 		}

	// 		await queryRunner.commitTransaction();

	// 		this.logger.log('Successfully completed payment details update', {
	// 			patientId: id
	// 		});

	// 		this.logger.log('Patient updated successfully', { patientId: id });

	// 		const updatedPatient = await this.patientsRepository.findOne({
	// 			where: { id },
	// 			relations: ['patientOwners', 'patientOwners.owner']
	// 		});

	// 		const updatedResponse = {
	// 			...updatedPatient,
	// 			patientOwners: updatedPatient?.patientOwners.map(po => ({
	// 				id: po.id,
	// 				owner: {
	// 					id: po.owner.id,
	// 					firstName: po.owner.firstName,
	// 					lastName: po.owner.lastName,
	// 					email: po.owner.email,
	// 					phoneNumber: po.owner.phoneNumber,
	// 					address: po.owner.address
	// 				}
	// 			}))
	// 		};

	// 		// After successful patient update, check for applicable global reminder rules
	// 		try {
	// 			// Process species/breed/age rules for the new patient
	// 			await this.globalReminderService.processSpeciesBreedAgeRulesForPatient(
	// 				patient.id,
	// 				patient.clinicId,
	// 				patient.brandId
	// 			);

	// 			this.logger.log(
	// 				'Successfully processed global reminder rules',
	// 				{
	// 					patientId: patient.id
	// 				}
	// 			);
	// 		} catch (reminderError) {
	// 			this.logger.error('Error processing global reminder rules', {
	// 				error: reminderError,
	// 				patientId: patient.id
	// 			});
	// 			// Don't throw the error as we don't want to fail patient creation
	// 		}

	// 		return updatedResponse as Patient;
	// 	} catch (error) {
	// 		await queryRunner.rollbackTransaction();
	// 		this.logger.error('Error updating patient', {
	// 			error,
	// 			patientId: id
	// 		});
	// 		throw new InternalServerErrorException('Failed to update patient');
	// 	} finally {
	// 		await queryRunner.release();
	// 	}
	// }

	// async getPatientDetails(id: string): Promise<Patient> {
	// 	this.logger.log('Fetching patient details', { patientId: id });

	// 	const patient = await this.patientsRepository.findOne({
	// 		where: { id },
	// 		relations: [
	// 			'patientOwners',
	// 			'patientOwners.owner',
	// 			'clinic',
	// 			'clinic.brand'
	// 		]
	// 	});

	// 	if (!patient) {
	// 		this.logger.error('Patient not found', { patientId: id });
	// 		throw new NotFoundException(`Patient with ID "${id}" not found`);
	// 	}

	// 	return patient;
	// }

	// Main update method
	async updatePatient(
		id: string,
		updatePatientDto: UpdatePatientDTO
	): Promise<Patient | { error: string; statusCode: number }> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Get patient with current owners
			const patient = await this.patientsRepository.findOne({
				where: { id },
				relations: [
					'patientOwners',
					'patientOwners.ownerBrand',
					'patientOwners.ownerBrand.globalOwner'
				]
			});

			if (!patient) {
				throw new NotFoundException(
					`Patient with ID "${id}" not found`
				);
			}

			// Save old owner IDs before any updates for payment details handling
			const oldOwnerIds = new Set(
				patient.patientOwners.map(po => po.ownerBrand.id)
			);

			// Update basic patient info
			const { ownersData, ...patientData } = updatePatientDto;
			Object.assign(patient, patientData);
			await queryRunner.manager.save(Patient, patient);

			// Validate owners data
			if (!ownersData?.length) {
				throw new BadRequestException(
					'At least one owner must be provided'
				);
			}

			// Process each owner
			const newPatientOwners: PatientOwner[] = [];
			const newOwnerBrands: OwnerBrand[] = [];

			// Validate that exactly one owner is primary
			const primaryOwnerCount = ownersData.filter(
				owner => owner.isPrimary
			).length;
			if (primaryOwnerCount !== 1) {
				throw new BadRequestException(
					'Exactly one owner must be marked as primary'
				);
			}

			for (const ownerData of ownersData) {
				// Check if global owner exists with this phone number
				let globalOwner = await this.globalOwnersRepository.findOne({
					where: { phoneNumber: ownerData.phoneNumber }
				});

				if (!globalOwner) {
					// Create new global owner
					globalOwner = this.globalOwnersRepository.create({
						phoneNumber: ownerData.phoneNumber,
						countryCode: ownerData.countryCode || ''
					});
					await queryRunner.manager.save(GlobalOwner, globalOwner);
				}

				// Check if owner brand exists for this global owner in this brand
				let ownerBrand = await this.ownerBrandsRepository.findOne({
					where: {
						globalOwnerId: globalOwner.id,
						brandId: patient.brandId
					}
				});

				if (ownerBrand) {
					// Update existing owner brand
					ownerBrand.firstName = ownerData.firstName;
					ownerBrand.lastName = ownerData.lastName;
					ownerBrand.email = ownerData.email;
					ownerBrand.address = ownerData.address;
				} else {
					// Create new owner brand
					ownerBrand = this.ownerBrandsRepository.create({
						globalOwnerId: globalOwner.id,
						brandId: patient.brandId,
						firstName: ownerData.firstName,
						lastName: ownerData.lastName,
						email: ownerData.email,
						address: ownerData.address
					});
				}

				ownerBrand = await queryRunner.manager.save(
					OwnerBrand,
					ownerBrand
				);
				newOwnerBrands.push(ownerBrand);

				// Create patient-owner relationship with isPrimary flag
				const patientOwner = this.patientOwnersRepository.create({
					patientId: patient.id,
					ownerId: ownerBrand.id,
					clinicId: patient.clinicId,
					brandId: patient.brandId,
					globalOwnerId: globalOwner.id,
					isPrimary: ownerData.isPrimary || false // Explicitly set isPrimary
				});

				newPatientOwners.push(patientOwner);
			}

			// Remove old patient-owner relationships
			await queryRunner.manager.remove(patient.patientOwners);

			// Save new patient-owner relationships
			await queryRunner.manager.save(PatientOwner, newPatientOwners);

			// Handle payment details visibility
			const newOwnerIds = new Set(newOwnerBrands.map(ob => ob.id));

			// Find owners that were removed
			const removedOwnerIds = [...oldOwnerIds].filter(
				id => !newOwnerIds.has(id)
			);

			// Find owners that were added
			const addedOwnerBrands = newOwnerBrands.filter(
				ob => !oldOwnerIds.has(ob.id)
			);

			if (removedOwnerIds.length > 0) {
				// Get all existing payment details for removed owners
				const existingPaymentDetails = await queryRunner.manager.find(
					PaymentDetailsEntity,
					{
						where: {
							patientId: id,
							ownerId: In(removedOwnerIds)
						}
					}
				);

				// Group entries by referenceAlphaId
				const entriesByReference = new Map();
				existingPaymentDetails.forEach(entry => {
					if (entry.referenceAlphaId) {
						if (!entriesByReference.has(entry.referenceAlphaId)) {
							entriesByReference.set(entry.referenceAlphaId, []);
						}
						entriesByReference
							.get(entry.referenceAlphaId)
							.push(entry);
					}
				});

				// Hide entries for removed owners
				await queryRunner.manager.update(
					PaymentDetailsEntity,
					{
						patientId: id,
						ownerId: In(removedOwnerIds)
					},
					{
						showInInvoice: false,
						showInLedger: true
					}
				);

				// Handle payment details for new owners
				for (const newOwnerBrand of addedOwnerBrands) {
					// Get existing entries for this owner with matching referenceAlphaIds
					const existingOwnerEntries = await queryRunner.manager.find(
						PaymentDetailsEntity,
						{
							where: {
								patientId: id,
								ownerId: newOwnerBrand.id,
								referenceAlphaId: In([
									...entriesByReference.keys()
								])
							}
						}
					);

					// Create a set of referenceAlphaIds that this owner already has
					const existingReferenceIds = new Set(
						existingOwnerEntries.map(
							entry => entry.referenceAlphaId
						)
					);

					// Update visibility for existing entries
					if (existingOwnerEntries.length > 0) {
						await queryRunner.manager.update(
							PaymentDetailsEntity,
							{
								patientId: id,
								ownerId: newOwnerBrand.id,
								referenceAlphaId: In([...existingReferenceIds])
							},
							{
								showInInvoice: true,
								showInLedger: true
							}
						);
					}

					// Create new entries for referenceAlphaIds that don't exist for new owner
					const entriesToCreate: Partial<PaymentDetailsEntity>[] = [];
					entriesByReference.forEach((entries, referenceId) => {
						if (!existingReferenceIds.has(referenceId)) {
							const templateEntry = entries[0];
							entriesToCreate.push({
								...templateEntry,
								id: undefined,
								ownerId: newOwnerBrand.id,
								referenceAlphaId: referenceId,
								showInInvoice: true,
								showInLedger: false
							});
						}
					});

					if (entriesToCreate.length > 0) {
						await queryRunner.manager.insert(
							PaymentDetailsEntity,
							entriesToCreate
						);
					}
				}
			}

			await queryRunner.commitTransaction();

			// Process global reminder rules after successful update
			try {
				await this.globalReminderService.processSpeciesBreedAgeRulesForPatient(
					patient.id,
					patient.clinicId,
					patient.brandId
				);
			} catch (reminderError) {
				this.logger.error('Error processing global reminder rules', {
					error: reminderError,
					patientId: patient.id
				});
				// Don't throw the error as we don't want to fail patient update
			}

			this.logger.log('Patient updated successfully', { patientId: id });
			return this.findOne(id);
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error updating patient', {
				error,
				patientId: id
			});

			if (
				error instanceof NotFoundException ||
				error instanceof BadRequestException
			) {
				throw error;
			}

			throw new InternalServerErrorException('Failed to update patient');
		} finally {
			await queryRunner.release();
		}
	}

	async getPatientDetails(id: string): Promise<Patient> {
		this.logger.log('Fetching patient details', { patientId: id });

		const patient = await this.patientsRepository.findOne({
			where: { id },
			relations: [
				'patientOwners',
				'patientOwners.ownerBrand',
				'patientOwners.ownerBrand.globalOwner',
				'clinic',
				'clinic.brand',
				'appointments',
				'appointments.appointmentDetails'
			]
		});

		// console.log(patient, 'patient');

		if (!patient) {
			this.logger.error('Patient not found', { patientId: id });
			throw new NotFoundException(`Patient with ID "${id}" not found`);
		}

		// Calculate and populate weight if it's null
		if (
			patient.weight === null &&
			patient.appointments &&
			patient.appointments.length > 0
		) {
			// Sort appointments by updatedAt in descending order (newest first)
			const sortedAppointments = patient.appointments
				.slice()
				.sort((a, b) => moment(b.updatedAt).diff(moment(a.updatedAt)));

			let latestWeight: string | number | null = null;
			let latestTimestamp: Date | null = null;

			// Loop through appointments to find the latest weight
			for (const appointment of sortedAppointments) {
				const { weight, updatedAt, appointmentDetails } = appointment;

				// Check for weight in vitals (higher priority)
				// Safely access nested properties
				const details = appointmentDetails?.details as any; // Type assertion
				const objective = details?.objective as any; // Type assertion
				const vitals = objective?.vitals;

				// Get the weight and timestamp from vitals if they exist
				const vitalsWeight =
					vitals?.length > 0
						? vitals[vitals.length - 1]?.weight
						: null;
				const vitalsTimestamp =
					vitals?.length > 0
						? appointmentDetails?.updatedAt || null
						: null;

				// Case 1: Vitals weight present (overrides regular weight)
				if (
					vitalsWeight !== null &&
					vitalsWeight !== undefined &&
					String(vitalsWeight).trim() !== ''
				) {
					if (
						!latestTimestamp ||
						(vitalsTimestamp &&
							moment(vitalsTimestamp).isAfter(
								moment(latestTimestamp)
							))
					) {
						latestWeight = vitalsWeight;
						latestTimestamp = vitalsTimestamp;
						break; // Found weight in vitals, exit loop
					}
				}
				// Case 2: Appointment weight present
				else if (
					weight !== null &&
					weight !== undefined &&
					String(weight).trim() !== ''
				) {
					if (
						!latestTimestamp ||
						(updatedAt &&
							moment(updatedAt).isAfter(moment(latestTimestamp)))
					) {
						latestWeight = weight;
						latestTimestamp = updatedAt || null;
						break; // Found weight in appointment, exit loop
					}
				}
			}

			// Update patient weight if found
			if (latestWeight !== null) {
				patient.weight = latestWeight.toString();
			}
		}

		if (patient.patientOwners) {
			patient.patientOwners.sort((a, b) => {
				return Number(b.isPrimary) - Number(a.isPrimary);
			});

			// Directly update each owner's balance and the patient's overall balance
			let primaryOwnerBalance = 0;

			// Process balances sequentially to avoid too many DB calls at once
			for (const po of patient.patientOwners) {
				try {
					const ownerId = po.ownerBrand.id;
					const balance = await this.computeOwnerBalance(ownerId);

					// Update the ownerBalance directly in the ownerBrand object
					po.ownerBrand.ownerBalance = balance;

					// If this is the primary owner, update the patient's balance
					if (po.isPrimary) {
						primaryOwnerBalance = balance;
					}
				} catch (error) {
					this.logger.error('Error computing owner balance', {
						error
					});
					// Continue with other owners if one fails
				}
			}

			// Update the patient's overall balance with the primary owner's balance
			patient.balance = primaryOwnerBalance;
		}

		return patient;
	}

	async getAllPatients(
		page: number = 1,
		limit: number = 10,
		searchTerm: string = '',
		withBalance: boolean = false,
		clinicId: string = ''
	): Promise<{ patients: any[]; total: number }> {
		this.logger.log(
			`Fetching patients: page ${page}, limit ${limit}, searchTerm ${searchTerm}`
		);

		// const [patients, total] = await this.patientsRepository
		// 	.createQueryBuilder('patient')
		// 	.leftJoinAndSelect('patient.patientOwners', 'patientOwners')
		// 	.leftJoinAndSelect('patientOwners.owner', 'owner')
		// 	.leftJoinAndSelect('patient.appointments', 'appointments')
		// 	.leftJoinAndSelect(
		// 		'appointments.appointmentDoctors',
		// 		'appointmentDoctors'
		// 	)
		// 	.leftJoinAndSelect('appointmentDoctors.doctor', 'doctor')
		// 	.where('patient.patientName ILIKE :searchTerm', {
		// 		searchTerm: `%${searchTerm}%`
		// 	})
		// 	.orWhere('owner.firstName ILIKE :searchTerm', {
		// 		searchTerm: `%${searchTerm}%`
		// 	})
		// 	.orWhere('owner.lastName ILIKE :searchTerm', {
		// 		searchTerm: `%${searchTerm}%`
		// 	})
		// 	.orWhere('owner.email ILIKE :searchTerm', {
		// 		searchTerm: `%${searchTerm}%`
		// 	})
		// 	.orWhere('owner.phoneNumber ILIKE :searchTerm', {
		// 		searchTerm: `%${searchTerm}%`
		// 	})

		// 	.skip((page - 1) * limit)
		// 	.take(limit)
		// 	.orderBy('patient.createdAt', 'DESC')
		// 	.getManyAndCount();

		// const [patients, total] =
		// 	await this.patientsRepository.findAndCount({
		// 		select: [
		// 			'id',
		// 			'patientName',
		// 			'breed',
		// 			'createdAt',
		// 			'updatedAt',
		// 			'balance'
		// 		],
		// 		relations: [
		// 			'patientOwners',
		// 			'patientOwners.owner',
		// 			'appointments',
		// 			'appointments.appointmentDoctors',
		// 			'appointments.appointmentDoctors.clinicUser',
		// 			'appointments.appointmentDoctors.clinicUser.user'
		// 		],
		// 		skip: (page - 1) * limit,
		// 		take: limit,
		// 		order: { createdAt: 'DESC' },
		// 		where: withBalance
		// 			? [{ balance: MoreThan(0) }, { balance: LessThan(0) }]
		// 				: {}
		// 	});

		// this.logger.log(`Fetching patients: page ${page}, limit ${limit}`);

		//const [patients, total] = await this.patientsRepository
		// First get unique patient IDs matching search criteria
		// 1) Build a query to get just the matching patient IDs (for pagination + total count)
		try {
			const trimmedTerm = searchTerm.trim();
			const searchWords = trimmedTerm
				? trimmedTerm.toLowerCase().split(/\s+/) // e.g. ["leo","shah"]
				: [];

			// -- 1) Build the base query
			const qb = this.patientsRepository
				.createQueryBuilder('patient')
				.leftJoinAndSelect('patient.patientOwners', 'patientOwners')
				.leftJoinAndSelect('patientOwners.ownerBrand', 'ownerBrand')
				.leftJoinAndSelect('ownerBrand.globalOwner', 'globalOwner')
				.leftJoinAndSelect(
					'patient.appointments',
					'appointments',
					'appointments.status = :status',
					{ status: EnumAppointmentStatus.Completed }
				)
				.leftJoinAndSelect(
					'appointments.appointmentDoctors',
					'appointmentDoctors'
				)
				.leftJoinAndSelect(
					'appointmentDoctors.clinicUser',
					'clinicUser'
				)
				.leftJoinAndSelect('clinicUser.user', 'doctor')
				.where('patient.clinicId = :clinicId', { clinicId });

			// -- 2) Multi-word partial matching in WHERE clause
			if (searchWords.length > 0) {
				// For each word, we require that it matches at least one column
				qb.andWhere(
					new Brackets(outerQb => {
						searchWords.forEach((word, i) => {
							outerQb.andWhere(
								new Brackets(subQb => {
									subQb
										.where(
											`LOWER(patient.patientName) ILIKE :w${i}`,
											{
												[`w${i}`]: `%${word}%`
											}
										)
										.orWhere(
											`LOWER(ownerBrand.firstName) ILIKE :w${i}`,
											{
												[`w${i}`]: `%${word}%`
											}
										)
										.orWhere(
											`LOWER(ownerBrand.lastName) ILIKE :w${i}`,
											{
												[`w${i}`]: `%${word}%`
											}
										)
										.orWhere(
											`LOWER(globalOwner.phoneNumber) ILIKE :w${i}`,
											{
												[`w${i}`]: `%${word}%`
											}
										);
								})
							);
						});
					})
				);
			}

			// -- 4) Add ranking for exact vs partial
			// We create a CASE for each word:
			//    CASE WHEN anyColumn = word THEN 0 ELSE 1 END
			// and sum them. Fewer points = better match (means more exact hits).
			//
			// Example for 2 words ("leo","shah"):
			//    ( CASE WHEN col=leo THEN 0 ELSE 1 END + CASE WHEN col=shah THEN 0 ELSE 1 END ) AS match_rank
			//

			if (searchWords.length > 0) {
				// Build sub-CASE for each word, then sum them
				const caseExpressions: string[] = searchWords.map(
					(word, idx) => {
						// each "word" has param `:exactX`
						return `
						CASE
							WHEN (
							LOWER(patient.patientName) = :exact${idx}
							OR LOWER(ownerBrand.firstName) = :exact${idx}
							OR LOWER(ownerBrand.lastName) = :exact${idx}
							OR LOWER(globalOwner.phoneNumber) = :exact${idx}
							)
							THEN 0
							ELSE 1
						END
						`;
					}
				);

				// sum them up: e.g. "CASE... + CASE..."
				const rankSql = caseExpressions.join(' + ');

				qb.addSelect(`(${rankSql})`, 'match_rank');

				// Set all exact match params
				searchWords.forEach((word, i) => {
					qb.setParameter(`exact${i}`, word); // e.g. "leo" or "shah"
				});

				// Order by match_rank ascending (exact hits first), then updatedAt
				qb.orderBy('match_rank', 'ASC').addOrderBy(
					'patient.updatedAt',
					'DESC'
				);
			} else {
				// If no search term, just sort by updatedAt
				qb.orderBy('patient.updatedAt', 'DESC');
			}

			// -- 5) Pagination
			qb.skip((page - 1) * limit).take(limit);

			// -- 6) Execute and get total
			const [patients, total] = await qb.getManyAndCount();

			// -- 7) Transform the data
			const patientsWithOwnerInfo = patients.map(patient => {
				const owners = patient.patientOwners.map(po => ({
					id: po.ownerBrand.id,
					name: `${po.ownerBrand.firstName} ${po.ownerBrand.lastName}`,
					phoneNumber: po.ownerBrand.globalOwner.phoneNumber,
					countryCode: po.ownerBrand.globalOwner.countryCode,
					email: po.ownerBrand.email,
					isPrimary: po.isPrimary,
					ownerBalance: 0 // Initialize with 0 instead of undefined
				}));
				owners.sort(
					(a, b) => Number(b.isPrimary) - Number(a.isPrimary)
				);

				const latestAppointment = patient.appointments
					?.filter(
						app => app.status === EnumAppointmentStatus.Completed
					)
					.sort((a1, a2) => moment(a2.date).diff(moment(a1.date)))[0];

				const primaryDoctor =
					latestAppointment?.appointmentDoctors?.find(
						ad => ad.primary
					)?.clinicUser?.user;

				return {
					balance: 0, // Initialize with 0 instead of undefined
					id: patient.id,
					patientName: patient.patientName,
					species: patient.species,
					breed: patient.breed,
					owners,
					lastVisit: patient.updatedAt,
					primaryDoctor: primaryDoctor
						? `${primaryDoctor.firstName} ${primaryDoctor.lastName}`
						: '',
					dummyData: patient.dummyData,
					appointment: latestAppointment
						? {
								id: latestAppointment.id,
								reason: latestAppointment.reason,
								date: latestAppointment.date,
								primaryDoctor: primaryDoctor
									? `${primaryDoctor.firstName} ${primaryDoctor.lastName}`
									: ''
							}
						: null,
					symptom: '' // dummy
				};
			});

			// If we need balance info, compute it dynamically without storing
			if (withBalance) {
				// Process in parallel for better performance
				await Promise.all(
					patientsWithOwnerInfo.flatMap(patient =>
						patient.owners.map(async owner => {
							const currentBalance =
								await this.computeOwnerBalance(owner.id);
							owner.ownerBalance = currentBalance;

							// Also update the main balance field if this is the primary owner
							if (owner.isPrimary) {
								patient.balance = currentBalance;
							}
						})
					)
				);
			}

			return { patients: patientsWithOwnerInfo, total };
		} catch (error) {
			this.logger.error(`Error fetching patients: ${error}`);
			throw error;
		}
	}

	async getClinicPatients(
		clinicId: string,
		page: number = 1,
		limit: number = 10,
		search?: string
	): Promise<{ patients: any[]; total: number }> {
		const qb = this.patientsRepository.createQueryBuilder('patient');
		qb.leftJoinAndSelect('patient.patientOwners', 'patientOwner')
			.leftJoinAndSelect('patientOwner.ownerBrand', 'ownerBrand')
			.leftJoinAndSelect('ownerBrand.globalOwner', 'globalOwner')
			.where('patient.clinicId = :clinicId', { clinicId });

		// 1) Split input into tokens if search string is provided
		if (search && search.trim().length > 0) {
			const tokens = search.split(/\s+/);

			tokens.forEach((token, index) => {
				const paramName = `token_${index}`;

				qb.andWhere(
					new Brackets(qb2 => {
						qb2.where(`patient.patientName ILIKE :${paramName}`, {
							[paramName]: `%${token}%`
						})
							.orWhere(
								`ownerBrand.firstName ILIKE :${paramName}`,
								{
									[paramName]: `%${token}%`
								}
							)
							.orWhere(
								`ownerBrand.lastName ILIKE :${paramName}`,
								{
									[paramName]: `%${token}%`
								}
							)
							.orWhere(
								`globalOwner.phoneNumber ILIKE :${paramName}`,
								{
									[paramName]: `%${token}%`
								}
							);
					})
				);
			});
		}

		// Pagination + ordering
		qb.skip((page - 1) * limit)
			.take(limit)
			.orderBy('patient.createdAt', 'DESC');

		// Execute the query
		const [patients, total] = await qb.getManyAndCount();

		// Return them in desired format...
		const patientsWithOwnerInfo = patients.map(patient => {
			const owners = patient.patientOwners.map(po => ({
				id: po.ownerBrand.id,
				name: `${po.ownerBrand.firstName} ${po.ownerBrand.lastName}`,
				phoneNumber: po.ownerBrand.globalOwner.phoneNumber,
				countryCode: po.ownerBrand.globalOwner.countryCode,
				email: po.ownerBrand.email
			}));

			return {
				id: patient.id,
				patientName: patient.patientName,
				breed: patient.breed,
				species: patient.species,
				owners,
				lastVisit: patient.updatedAt,
				primaryDoctor: 'Dr Dummy',
				symptom: 'DummySymp'
			};
		});

		return { patients: patientsWithOwnerInfo, total };
	}

	async findOne(id: string): Promise<Patient> {
		try {
			this.logger.log('Fetching patient by ID', { patientId: id });
			const patient = await this.patientsRepository.findOne({
				where: { id },
				relations: [
					'patientOwners',
					'patientOwners.ownerBrand',
					'patientOwners.ownerBrand.globalOwner'
				]
			});
			if (!patient) {
				this.logger.error('Patient not found', { patientId: id });
				throw new NotFoundException(
					`Patient with ID "${id}" not found`
				);
			}

			// Directly update each owner's balance and the patient's overall balance
			if (patient.patientOwners && patient.patientOwners.length > 0) {
				let primaryOwnerBalance = 0;

				for (const po of patient.patientOwners) {
					try {
						const ownerId = po.ownerBrand.id;
						const balance = await this.computeOwnerBalance(ownerId);

						// Update the ownerBalance directly in the ownerBrand object
						po.ownerBrand.ownerBalance = balance;

						// If this is the primary owner, update the patient's balance
						if (po.isPrimary) {
							primaryOwnerBalance = balance;
						}
					} catch (error) {
						this.logger.error('Error computing owner balance', {
							error
						});
						// Continue with other owners if one fails
					}
				}

				// Update the patient's overall balance with the primary owner's balance
				patient.balance = primaryOwnerBalance;
			}

			this.logger.log('Patient fetched successfully', { patientId: id });
			return patient;
		} catch (error) {
			this.logger.error('Error fetching patient', {
				error,
				patientId: id
			});
			throw error;
		}
	}

	async updatePatienttBalance(
		id: string,
		balance: number
	): Promise<{ status: boolean } | Patient> {
		const patient = await this.patientsRepository.findOne({
			where: { id }
		});

		if (!patient) {
			throw new NotFoundException(
				`This patient with ${id} doesn't exist`
			);
		}

		const updateResult: UpdateResult = await this.patientsRepository.update(
			id,
			{ balance: balance }
		);

		if (updateResult.affected === 0) {
			return { status: false };
		}

		const updatedPatient = await this.patientsRepository.findOne({
			where: { id }
		});

		return updatedPatient ? updatedPatient : { status: false };
	}

	/**
	 * Compute owner balance by summing all pending balances from invoices
	 * @param ownerId - The owner ID to calculate balance for
	 * @returns The total balance due for the owner (negative value indicates money owed)
	 */
	async computeOwnerBalance(ownerId: string): Promise<number> {
		try {
			this.logger.log('Computing owner balance', { ownerId });

			// Find all invoices associated with the owner that have pending or partially paid balances
			const invoices = await this.invoiceRepository.find({
				where: [
					{
						ownerId,
						status: EnumInvoiceStatus.PENDING,
						balanceDue: Not(0)
					},
					{
						ownerId,
						status: EnumInvoiceStatus.PARTIALLY_PAID,
						balanceDue: Not(0)
					}
				],
				select: ['id', 'balanceDue', 'status'] // Added status for better logging
			});

			// Sum up the balance due from all invoices
			// Negate the sum since pending balance represents money owed by the owner (negative balance)
			const totalBalance =
				-1 *
				invoices.reduce(
					(sum, invoice) => sum + Number(invoice.balanceDue),
					0
				);

			this.logger.log('Owner balance computed successfully', {
				ownerId,
				totalBalance,
				invoiceCount: invoices.length,
				pendingCount: invoices.filter(
					i => i.status === EnumInvoiceStatus.PENDING
				).length,
				partiallyPaidCount: invoices.filter(
					i => i.status === EnumInvoiceStatus.PARTIALLY_PAID
				).length
			});

			return totalBalance;
		} catch (error) {
			this.logger.error('Error computing owner balance', {
				error,
				ownerId
			});
			throw new InternalServerErrorException(
				'Failed to compute owner balance'
			);
		}
	}
}
