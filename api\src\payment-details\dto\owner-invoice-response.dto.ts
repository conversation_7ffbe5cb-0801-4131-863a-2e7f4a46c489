export interface OwnerInvoiceResponse {
	ownerDetails: {
		id: string;
		name: string;
		balance: number;
		credits: number;
	};
	invoices: OwnerInvoiceDetails[];
	uniqueUsers: { id: string; name: string }[];
	pagination?: {
		total: number;
		page: number;
		limit: number;
		hasMore: boolean;
	};
}

export interface CreditNoteInfo {
	id: string;
	referenceAlphaId: string;
	amount: number;
	createdAt: Date;
}

// Interface for structured audit operations
export interface AuditOperation {
	type:
		| 'ADD_ITEM'
		| 'REMOVE_ITEM'
		| 'CHANGE_QUANTITY'
		| 'APPLY_DISCOUNT'
		| 'CHANGE_DISCOUNT'
		| 'REMOVE_DISCOUNT'
		| 'WRITE_OFF_INVOICE'
		| 'CANCEL_INVOICE';
	item?: {
		id: string;
		name: string;
	};
	before?: any;
	after?: any;
	description: string;
	reason?: string; // Add optional reason field
	writeoffAmount?: number; // Add optional writeoff amount field
	cancellationAmount?: number; // Add optional cancellation amount field
}

// Add interface for invoice audit logs
export interface InvoiceAuditLogInfo {
	id: string;
	operationType: 'UPDATE' | 'DELETE' | 'CREATE' | 'WRITE_OFF';
	operations?: AuditOperation[] | null; // Changed from changedFieldsSummary to operations
	timestamp: Date;
	userId: string | null;
	userName: string;
}

export interface OwnerInvoiceDetails {
	id: string;
	referenceAlphaId: string;
	invoiceAmount: number;
	totalCollected: number;
	totalprice: number;
	discountAmount: number;
	details: any;
	balanceDue: number;
	status: string;
	createdAt: Date;
	createdBy: string;
	createdByName: string;
	patientId: string;
	patientName: string;
	comment: string;
	metadata: any;
	payments: PaymentDetail[];
	originalInvoiceReferenceAlphaId?: string | null;
	cartId?: string;
	appointmentId?: string; // Add appointmentId from cart for lab report creation
	discount?: number; // Discount percentage
	creditNotes?: CreditNoteInfo[]; // Array of credit notes associated with this invoice
	auditLogs?: InvoiceAuditLogInfo[]; // Array of audit logs for this invoice
}

export interface PaymentDetail {
	id: string;
	amount: number;
	paymentType: string;
	paymentMode: string;
	createdAt: Date;
	createdBy: string;
	createdByName: string;
	isCreditUsed: boolean;
	creditAmountUsed: number;
	referenceAlphaId: string;
}

export interface OwnerInvoiceFilters {
	startDate?: string;
	endDate?: string;
	petName?: string;
	status?: string; // Can be comma-separated list of statuses
	paymentMode?: string;
	searchTerm?: string;
	userId?: string; // Added to filter by invoice creator
	invoiceType?: string; // Type of invoice: 'Invoice' or 'CreditNote'
}
