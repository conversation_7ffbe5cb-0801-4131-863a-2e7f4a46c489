import {
	Body,
	Controller,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Post,
	Put,
	Req,
	UseGuards,
	Query
} from '@nestjs/common';
import { PaymentDetailsService } from './payment-details.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiOkResponse,
	ApiQuery,
	ApiParam,
	ApiOperation
} from '@nestjs/swagger';
import { PaymentDetailsEntity } from './entities/payment-details.entity';
import { PaymentDetailsDto } from './dto/payment-details.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { RolesGuard } from '../auth/guards/roles.guard';
import { OwnerInvoiceFilters } from './dto/owner-invoice-response.dto';
import { EnumInvoiceStatus } from '../invoice/enums/enum-invoice-status';
import { BulkPaymentDetailsDto } from './dto/bulk-payment-details.dto';
import { BulkPaymentResponse } from './dto/bulk-payment-response.dto';
import { TabActivitiesService } from '../tab-activity/tab-activity.service';
import { TabName, ActionType } from '../tab-activity/enums/tab-activity.enums';
import { OwnerPaymentFiltersDto } from './dto/owner-payment-filters.dto';
import { LedgerItem } from './interfaces/ledger-item.interface';

@Controller('payment-details')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PaymentDetailsController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly paymentDetailsService: PaymentDetailsService,
		private readonly tabActivitiesService: TabActivitiesService
	) {}

	@ApiOkResponse({
		description: 'Creates a payment detail entry ',
		type: PaymentDetailsEntity
	})
	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createPaymentDetails-payment-details')
	createPaymentDetails(
		@Body() paymentDetailsDto: PaymentDetailsDto,
		@Req()
		req: { user: { clinicId: string; brandId: string; userId: string } }
	) {
		try {
			this.logger.log('Creating new payment detail', {
				dto: paymentDetailsDto
			});

			return this.paymentDetailsService.createPaymentDetails(
				paymentDetailsDto,
				req.user.clinicId,
				req.user.brandId,
				req.user.userId
			);
		} catch (error) {
			this.logger.error('Error creating new payment detail', {
				error,
				paymentDetailsDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':patientId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getPaymentDetailsForPatientsOwner-payment-details')
	getPaymentDetailsForPatientsOwner(@Param('patientId') patientId: string) {
		try {
			this.logger.log('Fetching payment details by patient ID', {
				patientId
			});

			return this.paymentDetailsService.getPaymentDetailsForPatientsOwner(
				patientId
			);
		} catch (error) {
			this.logger.error('Error fetching payment details by patient ID', {
				error
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Get('patient-receipts/:patientId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getPaymentDetailsForPatient-payment-details')
	async getPaymentDetailsForPatient(
		@Param('patientId') patientId: string,
		@Query('search') search?: string,
		@Query('page') page?: string,
		@Query('limit') limit?: string
	) {
		try {
			// Validate patientId exists
			if (!patientId) {
				throw new HttpException(
					'Patient ID is required',
					HttpStatus.BAD_REQUEST
				);
			}

			this.logger.log('Fetching payment details for patient', {
				patientId,
				search,
				page,
				limit
			});

			// Parse page and limit to numbers with defaults if provided
			const pageNumber = 1;
			const limitNumber = 10000;

			const result =
				await this.paymentDetailsService.getPaymentDetailsForPatient(
					patientId,
					search,
					pageNumber,
					limitNumber
				);

			// Return result even if empty (don't throw error for empty results)
			return result;
		} catch (error) {
			this.logger.error('Error fetching payment details for patient', {
				error,
				patientId,
				search
			});

			// Handle specific error cases
			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				'Failed to fetch payment details for patient',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('patient-invoices/:patientId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getInvoicesForPatient-payment-details')
	async getInvoicesForPatient(
		@Param('patientId') patientId: string,
		@Query('search') search?: string,
		@Query('page') page?: string,
		@Query('limit') limit?: string,
		@Query('invoiceType') invoiceType?: string
	) {
		try {
			// Validate patientId exists
			if (!patientId) {
				throw new HttpException(
					'Patient ID is required',
					HttpStatus.BAD_REQUEST
				);
			}

			this.logger.log('Fetching invoices for patient', {
				patientId,
				search,
				page,
				limit,
				invoiceType
			});

			// Parse page and limit to numbers with defaults if provided
			const pageNumber = 1;
			const limitNumber = 10000;

			const result =
				await this.paymentDetailsService.getInvoicesForPatient(
					patientId,
					search,
					pageNumber,
					limitNumber,
					invoiceType
				);

			// Return result even if empty (don't throw error for empty results)
			return result;
		} catch (error) {
			this.logger.error('Error fetching invoices for patient', {
				error,
				patientId,
				search
			});

			// Handle specific error cases
			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				'Failed to fetch invoices for patient',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('owner-receipts/:ownerId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getPaymentDetailsForOwner-payment-details')
	async getPaymentDetailsForOwner(
		@Param('ownerId') ownerId: string,
		@Query('startDate') startDate?: string,
		@Query('endDate') endDate?: string,
		@Query('petName') petName?: string,
		@Query('paymentMode') paymentMode?: string,
		@Query('paymentType') paymentType?: string,
		@Query('userId') userId?: string,
		@Query('searchTerm') searchTerm?: string,
		@Query('page') page?: string,
		@Query('limit') limit?: string
	) {
		try {
			// Validate ownerId exists
			if (!ownerId) {
				throw new HttpException(
					'Owner ID is required',
					HttpStatus.BAD_REQUEST
				);
			}

			this.logger.log('Fetching payment details by owner ID', {
				ownerId,
				filters: {
					startDate,
					endDate,
					petName,
					paymentMode,
					paymentType,
					userId,
					searchTerm,
					page,
					limit
				}
			});

			// Parse page and limit to numbers with defaults if provided
			const pageNumber = page ? parseInt(page, 10) : 1;
			const limitNumber = limit ? parseInt(limit, 10) : 20;

			const filters: OwnerPaymentFiltersDto = {
				startDate,
				endDate,
				petName,
				paymentMode,
				paymentType,
				userId,
				searchTerm,
				page: pageNumber,
				limit: limitNumber
			};

			const result =
				await this.paymentDetailsService.getPaymentDetailsForOwner(
					ownerId,
					filters
				);

			// Return result even if empty (don't throw error for empty results)
			return result;
		} catch (error) {
			this.logger.error('Error fetching payment details by owner ID', {
				error,
				ownerId
			});

			// Handle specific error cases
			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				'Failed to fetch payment details',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('findOne/:id')
	@TrackMethod('getIndividualPaymentDetail')
	getIndividualPaymentDetail(@Param('id') id: string) {
		try {
			this.logger.log('Fetching payment details for ID', {
				id
			});

			return this.paymentDetailsService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching payment details for ID', {
				error
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Put('delete-ledger/:id')
	@TrackMethod('delete-laser-fileKey')
	deleteLegderFileKeyForPayment(@Param('id') id: string) {
		try {
			this.logger.log('updating payment details for ID', {
				id
			});

			return this.paymentDetailsService.deleteLedgerFileKey(id);
		} catch (error) {
			this.logger.error('Error updating payment details for ID', {
				error
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Get('owner-invoices/:ownerId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getOwnerInvoicesWithPayments-payment-details')
	getOwnerInvoicesWithPayments(
		@Param('ownerId') ownerId: string,
		@Req()
		req: { user: { userId: string; brandId: string; clinicId: string } },
		@Query('page') page?: string,
		@Query('limit') limit?: string,
		@Query('startDate') startDate?: string,
		@Query('endDate') endDate?: string,
		@Query('petName') petName?: string,
		@Query('status') status?: string,
		@Query('paymentMode') paymentMode?: string,
		@Query('searchTerm') searchTerm?: string,
		@Query('userId') userId?: string,
		@Query('invoiceType') invoiceType?: string
	) {
		try {
			this.logger.log('Fetching owner invoices with payments', {
				ownerId,
				page,
				limit,
				filters: {
					startDate,
					endDate,
					petName,
					status,
					paymentMode,
					searchTerm,
					userId,
					invoiceType
				},
				userId: req.user.userId
			});

			const filters: OwnerInvoiceFilters = {
				startDate,
				endDate,
				petName,
				status,
				paymentMode,
				searchTerm,
				userId,
				invoiceType
			};

			// Parse pagination parameters
			const pageNumber = page ? parseInt(page, 10) : 1;
			const limitNumber = limit ? parseInt(limit, 10) : 10;

			return this.paymentDetailsService.getOwnerInvoicesWithPayments(
				ownerId,
				filters,
				req.user.userId,
				req.user.brandId,
				req.user.clinicId,
				pageNumber,
				limitNumber
			);
		} catch (error) {
			this.logger.error('Error fetching owner invoices with payments', {
				error,
				ownerId
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get('pending-invoices/:ownerId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getOwnerPendingInvoices-payment-details')
	getOwnerPendingInvoices(
		@Param('ownerId') ownerId: string,
		@Req()
		req: { user: { userId: string; brandId: string; clinicId: string } },
		@Query('startDate') startDate?: string,
		@Query('endDate') endDate?: string,
		@Query('petName') petName?: string,
		@Query('searchTerm') searchTerm?: string
	) {
		try {
			this.logger.log('Fetching pending invoices for owner', {
				ownerId,
				userId: req.user.userId,
				filters: {
					startDate,
					endDate,
					petName,
					searchTerm
				}
			});

			const filters: OwnerInvoiceFilters = {
				startDate,
				endDate,
				petName,
				status: [
					EnumInvoiceStatus.PENDING,
					EnumInvoiceStatus.PARTIALLY_PAID
				].join(','),
				searchTerm
			};

			return this.paymentDetailsService.getOwnerPendingInvoices(
				ownerId,
				req.user.userId,
				filters,
				req.user.brandId,
				req.user.clinicId
			);
		} catch (error) {
			this.logger.error('Error fetching pending invoices for owner', {
				error,
				ownerId
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get('owner-ledger/:ownerId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getOwnerLedger-payment-details')
	getOwnerLedger(
		@Param('ownerId') ownerId: string,
		@Req()
		req: { user: { userId: string; brandId: string; clinicId: string } }
	): Promise<{
		ownerDetails: {
			id: string;
			name: string;
			balance: number;
			credits: number;
			createdAt: Date;
			openingBalance: number;
		};
		ledgerItems: LedgerItem[];
		uniqueUsers: { id: string; name: string }[];
		summary: {
			totalMonetaryDebits: number;
			totalMonetaryCredits: number;
			finalRunningBalance: number;
			totalProfileCreditsAdded: number;
			totalProfileCreditsUsed: number;
			finalRunningCredits: number;
		};
	}> {
		try {
			this.logger.log('Fetching ledger for owner', {
				ownerId,
				userId: req.user.userId
			});

			return this.paymentDetailsService.getOwnerLedger(
				ownerId,
				req.user.userId,
				req.user.brandId,
				req.user.clinicId
			);
		} catch (error) {
			this.logger.error('Error fetching ledger for owner', {
				error,
				ownerId
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description:
			'Creates multiple payment detail entries for bulk invoice settlement. If no invoiceIds are provided, all pending invoices for the owner will be processed.',
		type: BulkPaymentResponse
	})
	@Post('bulk')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createBulkPaymentDetails-payment-details')
	createBulkPaymentDetails(
		@Body() bulkPaymentDetailsDto: BulkPaymentDetailsDto,
		@Req()
		req: { user: { clinicId: string; brandId: string; userId: string } }
	) {
		try {
			this.logger.log('Creating bulk payment details', {
				dto: bulkPaymentDetailsDto
			});

			return this.paymentDetailsService.createBulkPaymentDetails(
				bulkPaymentDetailsDto,
				req.user.clinicId,
				req.user.brandId,
				req.user.userId
			);
		} catch (error) {
			this.logger.error('Error creating bulk payment details', {
				error,
				bulkPaymentDetailsDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get('document-status/:referenceAlphaId')
	@ApiOperation({
		summary: 'Check the status of a payment document generation request',
		description: 'Polls for completion of a payment receipt document'
	})
	@ApiParam({
		name: 'referenceAlphaId',
		required: true,
		description: 'Reference Alpha ID of the payment to check'
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('checkPaymentDocumentStatus-payment-details')
	async checkPaymentDocumentStatus(
		@Param('referenceAlphaId') referenceAlphaId: string,
		@Req()
		req: { user: { clinicId: string; brandId: string; userId: string } }
	) {
		try {
			this.logger.log('Checking payment document status', {
				referenceAlphaId,
				userId: req.user.userId
			});

			// Check if required parameter is provided
			if (!referenceAlphaId) {
				throw new HttpException(
					'Missing required parameter: referenceAlphaId',
					HttpStatus.BAD_REQUEST
				);
			}

			const result =
				await this.paymentDetailsService.checkPaymentDocumentStatus(
					referenceAlphaId
				);

			// If no result found, throw 404 error
			if (!result) {
				throw new HttpException(
					'Payment document not found',
					HttpStatus.NOT_FOUND
				);
			}

			return result;
		} catch (error) {
			this.logger.error('Error checking payment document status', {
				error,
				referenceAlphaId
			});

			// If error is already an HttpException, rethrow it
			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				(error as Error).message ||
					'Error checking payment document status',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('documents/:referenceAlphaId')
	@ApiParam({ name: 'referenceAlphaId', type: 'string' })
	@ApiQuery({
		name: 'documentType',
		enum: ['creditnote', 'payment-details'],
		description: 'Type of payment document'
	})
	@ApiQuery({
		name: 'action',
		enum: ['share', 'download'],
		description: 'Whether to share or download the document'
	})
	@ApiQuery({
		name: 'shareMethod',
		enum: ['email', 'whatsapp', 'both'],
		required: false,
		description: 'Method to use for sharing'
	})
	@ApiQuery({
		name: 'recipient',
		enum: ['client', 'other'],
		required: false,
		description: 'Recipient of the document (client or custom recipient)'
	})
	@ApiQuery({
		name: 'email',
		type: 'string',
		required: false,
		description: 'Custom email when recipient is "other"'
	})
	@ApiQuery({
		name: 'phoneNumber',
		type: 'string',
		required: false,
		description: 'Custom phone number when recipient is "other"'
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('handlePaymentDocument-payment-details')
	async handlePaymentDocument(
		@Param('referenceAlphaId') referenceAlphaId: string,
		@Query('documentType') documentType: 'payment-details',
		@Query('action') action: 'share' | 'download',
		@Query('shareMethod')
		shareMethod: 'email' | 'whatsapp' | 'both' | undefined,
		@Query('recipient') recipient: 'client' | 'other' | undefined,
		@Query('email') email: string | undefined,
		@Query('phoneNumber') phoneNumber: string | undefined,
		@Req()
		req: { user: { clinicId: string; brandId: string; userId: string } }
	) {
		this.logger.log('Handling payment document request', {
			referenceAlphaId,
			documentType,
			action,
			shareMethod,
			recipient,
			email,
			phoneNumber,
			userId: req.user.userId
		});

		try {
			this.logger.log('Handling payment document operation', {
				referenceAlphaId,
				documentType,
				action,
				shareMethod,
				recipient,
				userId: req.user.userId
			});

			// Check if required parameters are provided
			if (!referenceAlphaId || !documentType || !action) {
				throw new HttpException(
					'Missing required parameters',
					HttpStatus.BAD_REQUEST
				);
			}

			// If action is share, shareMethod is required
			if (action === 'share' && !shareMethod) {
				throw new HttpException(
					'Share method is required for share action',
					HttpStatus.BAD_REQUEST
				);
			}

			// Validate recipient-related fields
			if (
				action === 'share' &&
				recipient === 'other' &&
				!email &&
				!phoneNumber
			) {
				throw new HttpException(
					'Email or phone number is required when recipient is "other"',
					HttpStatus.BAD_REQUEST
				);
			}

			const result =
				await this.paymentDetailsService.handlePaymentDocument(
					referenceAlphaId,
					documentType,
					action,
					shareMethod,
					req.user.brandId,
					req.user.userId,
					recipient,
					email,
					phoneNumber
				);

			// If no result found, throw 404 error
			if (!result) {
				throw new HttpException(
					'Payment document not found',
					HttpStatus.NOT_FOUND
				);
			}

			// Create tab activity for the document action
			try {
				this.logger.log('Creating tab activity for payment document', {
					referenceAlphaId,
					action,
					userId: req.user.userId
				});

				// Get payment details directly from the service
				const paymentDetails =
					await this.paymentDetailsService.findPaymentsByReferenceAlphaId(
						referenceAlphaId,
						req.user.brandId
					);

				this.logger.log('Retrieved payment details', {
					paymentDetailsCount: paymentDetails?.length,
					hasPaymentDetails: !!paymentDetails
				});

				if (paymentDetails && paymentDetails.length > 0) {
					// Find the first payment with a patient ID

					this.logger.log('Processing payment activity', {
						referenceAlphaId,
						clinicId: req.user.clinicId,
						brandId: req.user.brandId
					});

					const tabActivity = await this.tabActivitiesService.create(
						{
							clinicId: req.user.clinicId,
							brandId: req.user.brandId,
							tabName: TabName.INVOICES,
							actionType:
								action === 'download'
									? ActionType.DOWNLOAD
									: ActionType.SHARE,
							referenceId: referenceAlphaId
						},
						req.user.userId
					);

					this.logger.log('Created tab activity', {
						tabActivityId: tabActivity.id,
						actionType: tabActivity.actionType,
						tabName: tabActivity.tabName
					});

					// Add tab activity to the result
					if (result && typeof result === 'object') {
						(result as any).tabActivity = tabActivity;
					}
				} else {
					this.logger.warn('No payment details found', {
						referenceAlphaId
					});
				}
			} catch (error: any) {
				// Just log the error but don't fail the entire request if tab activity creation fails
				this.logger.error('Error creating tab activity', {
					error: error.message,
					stack: error.stack,
					referenceAlphaId
				});
			}

			return result;
		} catch (error) {
			this.logger.error('Error handling payment document', {
				error,
				referenceAlphaId,
				documentType,
				action,
				shareMethod,
				recipient,
				email,
				phoneNumber
			});

			// If error is already an HttpException, rethrow it
			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				(error as Error).message || 'Error handling payment document',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
