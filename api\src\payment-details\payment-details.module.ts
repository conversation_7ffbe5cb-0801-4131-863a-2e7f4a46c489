import { Module, forwardRef } from '@nestjs/common';
import { PaymentDetailsService } from './payment-details.service';
import { PaymentDetailsController } from './payment-details.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentDetailsEntity } from './entities/payment-details.entity';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsService } from '../patients/patients.service';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { OwnersModule } from '../owners/owners.module';
import { RoleModule } from '../roles/role.module';
import { OwnersService } from '../owners/owners.service';
import { SESModule } from '../utils/aws/ses/ses.module';
import { S3Service } from '../utils/aws/s3/s3.service';
import { WhatsappModule } from '../utils/whatsapp-integration/whatsapp.module';
import { GlobalReminderModule } from '../patient-global-reminders/global-reminders.module';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { InvoiceAuditLogEntity } from '../invoice/entities/invoice-audit-log.entity';
import { Brand } from '../brands/entities/brand.entity';
import { PetTransferHistory } from '../owners/entities/pet-transfer-history.entity';
import { CreditTransactionEntity } from '../credits/entities/credit-transaction.entity';
import { CreditsModule } from '../credits/credits.module';
import { SqsModule } from '../utils/aws/sqs/sqs.module';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { TabActivityModule } from '../tab-activity/tab-activity.module';
import { TabActivityEntity } from '../tab-activity/entities/tab-activity.entity';
import { MergedPaymentReceiptDocumentEntity } from './entities/merged-payment-receipt-document.entity';
import { LoggerModule } from '../utils/logger/logger-module';
import { S3Module } from '../utils/aws/s3/s3.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			PaymentDetailsEntity,
			Patient,
			PatientOwner,
			ClinicEntity,
			GlobalOwner,
			OwnerBrand,
			InvoiceEntity,
			InvoiceAuditLogEntity,
			Brand,
			PetTransferHistory,
			CreditTransactionEntity,
			TabActivityEntity,
			MergedPaymentReceiptDocumentEntity
		]),
		OwnersModule,
		SESModule,
		WhatsappModule,
		GlobalReminderModule,
		RoleModule,
		CreditsModule,
		forwardRef(() => SqsModule),
		TabActivityModule,
		LoggerModule,
		S3Module
	],
	controllers: [PaymentDetailsController],
	providers: [
		PaymentDetailsService,
		PatientsService,
		OwnersService,
		S3Service,
		SqsService
	],
	exports: [PaymentDetailsService]
})
export class PaymentDetailsModule {}
