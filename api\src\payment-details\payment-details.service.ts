import {
	Injectable,
	Logger,
	NotFoundException,
	InternalServerErrorException,
	HttpException,
	HttpStatus,
	Inject,
	forwardRef
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentDetailsEntity } from './entities/payment-details.entity';
import { Repository, In, Connection, QueryRunner, Not } from 'typeorm';
import { PaymentDetailsDto } from './dto/payment-details.dto';
import { PatientsService } from '../patients/patients.service';
import { generateReceipt } from '../utils/pdfs/receipt';
import { generatePDF } from '../utils/generatePdf';
import { EnumAmountType } from './enums/enum-credit-types';
import { S3Service } from '../utils/aws/s3/s3.service';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { uuidv4 } from 'uuidv7';
import { generateUniqueCode } from '../utils/common/generate_alpha-numeric_code';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { EnumInvoiceStatus } from '../invoice/enums/enum-invoice-status';
import { LedgerItem } from './interfaces/ledger-item.interface';
import {
	CreditNoteInfo,
	OwnerInvoiceResponse,
	OwnerInvoiceFilters,
	OwnerInvoiceDetails
} from './dto/owner-invoice-response.dto';
import { CreditTransactionEntity } from '../credits/entities/credit-transaction.entity';
import { CreditTransactionType } from '../credits/enums/enum-credit-transaction-type';
import { BulkPaymentDetailsDto } from './dto/bulk-payment-details.dto';
import { BulkPaymentResponse } from './dto/bulk-payment-response.dto';
import { EnumPaymentType } from '../invoice/enums/enum-payment-types';
import { EnumInvoiceType } from '../invoice/enums/enum-invoice-types';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { OwnerPaymentFiltersDto } from './dto/owner-payment-filters.dto';
import { DerivedCreditTransactionType } from '../credits/enums/enum-derived-credit-transaction-type';
import { InvoiceAuditLogEntity } from '../invoice/entities/invoice-audit-log.entity';
import { InvoiceAuditLogInfo } from './dto/owner-invoice-response.dto';

// New interface for patient invoices response (simplified)
export interface PatientInvoicesResponse {
	invoices: PatientInvoiceDetails[];
	total: number;
	pagination: {
		total: number;
		page: number;
		limit: number;
		hasMore: boolean;
	};
	summary: {
		totalAmount: number;
		totalPaidAmount: number;
		totalBalanceAmount: number;
		totalCreditAmount: number;
	};
}

export interface PatientInvoiceDetails {
	id: string;
	referenceAlphaId: string;
	invoiceAmount: number;
	totalCollected: number;
	totalprice: number;
	discountAmount: number;
	details: any;
	balanceDue: number;
	status: string;
	createdAt: Date;
	createdBy: string;
	createdByName: string;
	patientId: string;
	comment: string;
	metadata: any;
	payments: PaymentDetail[];
	cartId?: string;
	discount?: number;
	auditLogs?: InvoiceAuditLogInfo[]; // Add audit logs information
	appointmentId?: string; // Add appointment ID information
	creditNotes?: CreditNoteInfo[]; // Add credit notes information for refund cards
}

// Add this interface near the top of the file, before the PaymentDetailsService class
interface InvoiceDetailItem {
	id: string;
	quantity: number;
	isAddedToCart?: boolean; // Add this property for refund logic
	[key: string]: any; // Allow other properties
}

export interface EnhancedPaymentDetail extends PaymentDetailsEntity {
	payments?: any[]; // Define a specific interface for payment objects if possible
	createdByName?: string;
	relatedPayments?: any[];
	collectPayments?: any[];
	hasCollectPayment?: boolean;
	collectAmount?: number;
	isReconcileEntry?: boolean;
}

interface RelatedPayment {
	id: string;
	amount: number;
	paymentType: string;
	paymentMode: string;
	createdAt: Date;
	createdBy: string;
	createdByName: string;
	isCreditUsed: boolean;
	creditAmountUsed: number;
	referenceAlphaId: string;
	totalAmount?: number;
	totalCreditAmount?: number;
	individualPayments?: RelatedPayment[];
	isBulkPayment?: boolean;
}

// Add the PaymentDetail interface at the top of the file with other interfaces
interface PaymentDetail {
	id: string;
	amount: number;
	paymentType: string;
	paymentMode: string;
	createdAt: Date;
	createdBy: string;
	createdByName: string;
	isCreditUsed: boolean;
	creditAmountUsed: number;
	referenceAlphaId: string;
	[key: string]: any; // For any additional properties
}

@Injectable()
export class PaymentDetailsService {
	private readonly logger = new Logger(PaymentDetailsService.name);

	constructor(
		@InjectRepository(PaymentDetailsEntity)
		private paymentDetailsRepository: Repository<PaymentDetailsEntity>,
		private readonly patientService: PatientsService,
		private s3Service: S3Service,
		private readonly mailService: SESMailService,
		private readonly whatsappService: WhatsappService,
		private readonly loggerService: WinstonLogger,
		@InjectRepository(ClinicEntity)
		private clinicRepository: Repository<ClinicEntity>,
		@InjectRepository(PatientOwner)
		private patientOwnerRepository: Repository<PatientOwner>,
		@InjectRepository(InvoiceEntity)
		private invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(InvoiceAuditLogEntity)
		private invoiceAuditLogRepository: Repository<InvoiceAuditLogEntity>,
		@InjectRepository(OwnerBrand)
		private ownerBrandRepository: Repository<OwnerBrand>,
		@InjectRepository(CreditTransactionEntity)
		private creditTransactionRepository: Repository<CreditTransactionEntity>,
		private connection: Connection,
		@Inject(forwardRef(() => SqsService))
		private readonly sqsService: SqsService
	) {}

	async createPaymentDetails(
		paymentDetailsDto: PaymentDetailsDto,
		clinicId: string,
		brandId: string,
		userId: string
	): Promise<PaymentDetailsEntity[]> {
		const queryRunner = this.connection.createQueryRunner();
		const logContext = {
			clinicId,
			brandId,
			userId,
			ownerId: paymentDetailsDto.ownerId,
			invoiceId: paymentDetailsDto.invoiceId,
			patientId: paymentDetailsDto.patientId,
			requestId: uuidv4()
		};

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Start creating payment detail',
				{ paymentDetailsDto }
			);

			// Fetch owner data
			const ownerData = await this.fetchOwnerData(
				queryRunner,
				paymentDetailsDto.ownerId,
				brandId
			);

			// Validate invoice
			const invoice = paymentDetailsDto.invoiceId
				? await this.validateInvoice(
						queryRunner,
						paymentDetailsDto.invoiceId,
						paymentDetailsDto
					)
				: null;

			// Calculate credits and excess
			const {
				newCredits,
				amountToApply,
				creditAmountUsed,
				excessAmount
			} = this.calculateNewBalances(
				paymentDetailsDto,
				ownerData,
				invoice
			);

			const createdPayments: PaymentDetailsEntity[] = [];

			// Generate separate reference IDs for different payment types
			const cashReferenceAlphaId = await generateUniqueCode(
				'referenceAlphaId',
				this.paymentDetailsRepository
			);

			// For credit notes, we may need separate reference IDs
			const isCreditNoteWithExcess =
				paymentDetailsDto.type === EnumAmountType.CreditNote &&
				excessAmount > 0;

			// Create cash payment entity if amount > 0 or it's a special case
			if (
				(Number(paymentDetailsDto.amount) > 0 ||
					// Allow zero-amount payments for invoice types with transaction amount
					(paymentDetailsDto.type === EnumAmountType.Invoice &&
						Number(paymentDetailsDto.transactionAmount) > 0) ||
					// Allow zero-amount payments for Credit Notes
					paymentDetailsDto.type === EnumAmountType.CreditNote) &&
				// We want to create a cash payment in all cases EXCEPT when:
				// 1. It's a credit-to-cash conversion (Return type with isCreditUsed=true)
				// 2. The amount to apply is zero and it's not an Invoice payment or Credit Note
				(amountToApply > 0 ||
					paymentDetailsDto.type === EnumAmountType.Invoice ||
					paymentDetailsDto.type === EnumAmountType.CreditNote ||
					// Special case for credit-to-cash conversion
					(paymentDetailsDto.type === EnumAmountType.Return &&
						paymentDetailsDto.isCreditUsed))
			) {
				const cashPayment = await this.createCashPaymentEntity(
					queryRunner,
					paymentDetailsDto,
					paymentDetailsDto.type === EnumAmountType.Return &&
						paymentDetailsDto.isCreditUsed
						? paymentDetailsDto.amount // For credit-to-cash, use full amount
						: amountToApply, // For other cases, use amountToApply
					cashReferenceAlphaId,
					clinicId,
					brandId,
					userId
				);
				createdPayments.push(cashPayment);

				// Create credit transaction for Collect payment
				if (paymentDetailsDto.type === EnumAmountType.Collect) {
					await this.createCreditTransaction(
						queryRunner,
						ownerData.id,
						paymentDetailsDto.amount,
						CreditTransactionType.ADD,
						`Amount ${paymentDetailsDto.amount} added to credits via Collect payment`,
						{
							paymentId: cashPayment.id,
							invoiceId: null
						},
						clinicId,
						brandId,
						userId
					);
				}
			}

			// Create credit payment entity if credits are used
			if (
				paymentDetailsDto.isCreditUsed &&
				creditAmountUsed > 0 &&
				paymentDetailsDto.type !== EnumAmountType.Return // Don't create credit payment for credit-to-cash conversion
			) {
				const creditReferenceAlphaId = await generateUniqueCode(
					'referenceAlphaId',
					this.paymentDetailsRepository
				);

				const creditPayment = await this.createCreditPaymentEntity(
					queryRunner,
					paymentDetailsDto,
					creditAmountUsed,
					creditReferenceAlphaId,
					clinicId,
					brandId,
					userId
				);
				createdPayments.push(creditPayment);

				// Create credit transaction for credit usage
				await this.createCreditTransaction(
					queryRunner,
					ownerData.id,
					creditAmountUsed,
					CreditTransactionType.USE,
					`Credits used for ${paymentDetailsDto.invoiceId ? 'invoice ' + paymentDetailsDto.invoiceId : 'payment'}`,
					{
						paymentId: creditPayment.id,
						invoiceId: paymentDetailsDto.invoiceId
					},
					clinicId,
					brandId,
					userId
				);
			}

			// For credit-to-cash conversion, create a credit transaction to track credit usage
			if (
				paymentDetailsDto.type === EnumAmountType.Return &&
				paymentDetailsDto.isCreditUsed &&
				creditAmountUsed > 0
			) {
				await this.createCreditTransaction(
					queryRunner,
					ownerData.id,
					creditAmountUsed,
					CreditTransactionType.USE,
					`Credits converted to cash return`,
					{
						paymentId: createdPayments[0].id, // Reference the cash payment
						invoiceId: null
					},
					clinicId,
					brandId,
					userId
				);
			}

			// Update invoice status if applicable
			if (invoice) {
				await this.updateInvoiceStatus(
					queryRunner,
					invoice,
					paymentDetailsDto,
					amountToApply,
					creditAmountUsed
				);
			}

			// Handle excess amount from credit notes specially
			if (
				excessAmount > 0 &&
				paymentDetailsDto.type !== EnumAmountType.Collect
			) {
				// Generate a new reference ID for excess payment in credit notes
				const excessReferenceAlphaId = isCreditNoteWithExcess
					? await generateUniqueCode(
							'referenceAlphaId',
							this.paymentDetailsRepository
						)
					: cashReferenceAlphaId;

				// Create the excess payment
				const excessPayment = await this.handleExcessAmountAsCredits(
					queryRunner,
					paymentDetailsDto,
					excessAmount,
					excessReferenceAlphaId,
					clinicId,
					brandId,
					userId,
					logContext,
					// Only for credit notes, include invoice ID and patient ID
					isCreditNoteWithExcess
						? {
								includeInvoiceId: true,
								includePatientId: true
							}
						: undefined
				);
				createdPayments.push(excessPayment);
			}

			// Update owner credits
			ownerData.ownerCredits = newCredits;
			await queryRunner.manager.save(ownerData);

			await queryRunner.commitTransaction();
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Payment processing completed',
				{ paymentIds: createdPayments.map(p => p.id), newCredits }
			);

			return createdPayments;
		} catch (error: any) {
			await queryRunner.rollbackTransaction();
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Error in creating payment',
				{ errorMessage: error.message },
				'error'
			);
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	// New method for creating cash payment entity
	async createCashPaymentEntity(
		queryRunner: QueryRunner,
		paymentDetailsDto: PaymentDetailsDto,
		cashAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string
	): Promise<PaymentDetailsEntity> {
		const payment = new PaymentDetailsEntity();
		payment.ownerId = paymentDetailsDto.ownerId;
		payment.invoiceId = paymentDetailsDto.invoiceId;
		if (paymentDetailsDto.patientId) {
			payment.patientId = paymentDetailsDto.patientId;
		}
		payment.type = paymentDetailsDto.type;
		payment.amount = cashAmount;
		payment.paymentType = paymentDetailsDto.paymentType || 'Cash';
		payment.transactionAmount = paymentDetailsDto.transactionAmount || 0;
		payment.amountPayable = paymentDetailsDto.amountPayable || cashAmount;

		// Handle credit usage for different payment types
		if (
			paymentDetailsDto.type === EnumAmountType.Return &&
			paymentDetailsDto.isCreditUsed
		) {
			// For refunds with credit use, set isCreditUsed=true and creditAmountUsed
			payment.isCreditUsed = true;
			payment.creditAmountUsed =
				paymentDetailsDto.creditAmountUsed || cashAmount;
		} else {
			payment.isCreditUsed = false;
			payment.creditAmountUsed = 0;
		}

		// Set credits added flag and amount correctly for Collect type payments
		if (paymentDetailsDto.type === EnumAmountType.Collect) {
			payment.isCreditsAdded = true;
			payment.creditAmountAdded = cashAmount;
		} else {
			payment.isCreditsAdded = false;
			payment.creditAmountAdded = 0;
		}

		payment.paymentNotes = paymentDetailsDto.paymentNotes;
		payment.showInInvoice = true;
		payment.showInLedger = true;
		payment.brandId = brandId;
		payment.clinicId = clinicId;
		payment.previousBalance = 0;
		payment.mainBalance = 0;
		payment.createdBy = userId;
		payment.updatedBy = userId;
		payment.referenceAlphaId = referenceAlphaId;
		payment.receiptDetail = {};

		return await queryRunner.manager.save(payment);
	}

	// New method for creating credit payment entity
	async createCreditPaymentEntity(
		queryRunner: QueryRunner,
		paymentDetailsDto: PaymentDetailsDto,
		creditAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string
	): Promise<PaymentDetailsEntity> {
		const payment = new PaymentDetailsEntity();
		payment.ownerId = paymentDetailsDto.ownerId;
		payment.invoiceId = paymentDetailsDto.invoiceId;
		if (paymentDetailsDto.patientId) {
			payment.patientId = paymentDetailsDto.patientId;
		}
		payment.type = paymentDetailsDto.invoiceId
			? EnumAmountType.ReconcileInvoice
			: paymentDetailsDto.type;
		payment.amount = 0; // Cash amount is 0 for credit payment
		payment.paymentType = EnumPaymentType.Credits;
		payment.transactionAmount = 0;
		payment.amountPayable = 0;
		payment.isCreditUsed = true;
		payment.creditAmountUsed = creditAmount;
		payment.isCreditsAdded = false;
		payment.creditAmountAdded = 0;
		payment.paymentNotes = paymentDetailsDto.paymentNotes;
		payment.showInInvoice = true;
		payment.showInLedger = true;
		payment.brandId = brandId;
		payment.clinicId = clinicId;
		payment.previousBalance = 0;
		payment.mainBalance = 0;
		payment.createdBy = userId;
		payment.updatedBy = userId;
		payment.referenceAlphaId = referenceAlphaId;
		payment.receiptDetail = {};

		return await queryRunner.manager.save(payment);
	}

	async handleExcessAmountAsCredits(
		queryRunner: QueryRunner,
		paymentDetailsDto: PaymentDetailsDto,
		excessAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string,
		logContext: any,
		options?: {
			includeInvoiceId?: boolean;
			includePatientId?: boolean;
		}
	): Promise<PaymentDetailsEntity> {
		const excessPayment = new PaymentDetailsEntity();
		excessPayment.ownerId = paymentDetailsDto.ownerId;
		excessPayment.type = EnumAmountType.Collect;
		excessPayment.amount = excessAmount;
		excessPayment.paymentType = options?.includeInvoiceId
			? EnumPaymentType.Credits
			: paymentDetailsDto.paymentType || 'Cash';
		excessPayment.transactionAmount = 0;
		excessPayment.amountPayable = 0;
		excessPayment.isCreditUsed = false;
		excessPayment.creditAmountUsed = 0;
		excessPayment.isCreditsAdded = true;
		excessPayment.creditAmountAdded = excessAmount;
		excessPayment.paymentNotes = paymentDetailsDto.description;
		excessPayment.showInInvoice = true;
		excessPayment.showInLedger = true;
		excessPayment.brandId = brandId;
		excessPayment.clinicId = clinicId;

		// Include invoice ID and patient ID if requested (for credit notes)
		if (options?.includeInvoiceId && paymentDetailsDto.invoiceId) {
			excessPayment.invoiceId = paymentDetailsDto.invoiceId;
		}

		if (options?.includePatientId && paymentDetailsDto.patientId) {
			excessPayment.patientId = paymentDetailsDto.patientId;
		}

		excessPayment.previousBalance = 0; // No balance tracking
		excessPayment.mainBalance = 0; // No balance tracking
		excessPayment.createdBy = userId;
		excessPayment.updatedBy = userId;
		excessPayment.referenceAlphaId = referenceAlphaId;
		excessPayment.receiptDetail = {};

		const savedExcessPayment =
			await queryRunner.manager.save(excessPayment);

		await this.createCreditTransaction(
			queryRunner,
			paymentDetailsDto.ownerId,
			excessAmount,
			CreditTransactionType.ADD,
			`Excess payment of ${excessAmount} added to credits`,
			{
				paymentId: savedExcessPayment.id,
				invoiceId: excessPayment.invoiceId // Use the assigned invoice ID if available
			},
			clinicId,
			brandId,
			userId
		);

		this.logPaymentEvent(
			this.logger,
			logContext,
			'Handled excess amount as credits',
			{ excessAmount, paymentId: savedExcessPayment.id }
		);

		return savedExcessPayment;
	}

	// Helper Function Implementations
	async fetchOwnerData(
		queryRunner: QueryRunner,
		ownerId: string,
		brandId: string
	) {
		const ownerData = await queryRunner.manager.findOne(OwnerBrand, {
			where: { id: ownerId, brandId },
			lock: { mode: 'pessimistic_write' }
		});
		if (!ownerData) throw new Error('Owner not found');

		if (ownerData.globalOwnerId) {
			const globalOwnerData = await queryRunner.manager.findOne(
				GlobalOwner,
				{
					where: { id: ownerData.globalOwnerId }
				}
			);
			(ownerData as any).globalOwner = globalOwnerData || null;
		}
		return ownerData;
	}

	async validateInvoice(
		queryRunner: QueryRunner,
		invoiceId: string,
		paymentDetailsDto: PaymentDetailsDto
	) {
		const invoice = await queryRunner.manager.findOne(InvoiceEntity, {
			where: { id: invoiceId }
		});
		if (invoice && !invoice.details) {
			paymentDetailsDto.type = EnumAmountType.Collect;
		}
		return invoice || null;
	}

	calculateNewBalances(paymentDetailsDto: any, ownerData: any, invoice: any) {
		const amount = Number(paymentDetailsDto.amount);
		const transactionAmount = Number(
			paymentDetailsDto.transactionAmount || 0
		);
		let newCredits = Number(ownerData.ownerCredits || 0);
		let creditAmountUsed = 0;
		let amountToApply = amount;
		let excessAmount = 0;

		switch (paymentDetailsDto.type) {
			case EnumAmountType.Collect:
				// For Collect payments, entire amount goes to owner credits
				// isCreditsAdded and creditAmountAdded are set in createCashPaymentEntity
				// Credit transaction is created after creating the cash payment
				newCredits += amount;
				excessAmount = amount; // Entire amount goes to credits
				paymentDetailsDto.isCreditAdded = true;
				paymentDetailsDto.creditAmountAdded = amount;
				break;
			case EnumAmountType.Return:
				// Check if this is a credit-to-cash conversion
				if (paymentDetailsDto.isCreditUsed && amount > 0) {
					creditAmountUsed = amount; // Amount of credits to convert to cash
					if (newCredits < creditAmountUsed) {
						throw new Error('Insufficient credits for return');
					}
					newCredits -= creditAmountUsed;
					paymentDetailsDto.isCreditUsed = true;
					paymentDetailsDto.creditAmountUsed = creditAmountUsed;
					// For credit-to-cash conversion, we don't need a separate cash payment
					amountToApply = 0;
				} else {
					// Regular cash return
					amountToApply = amount;
					creditAmountUsed = 0;
					paymentDetailsDto.isCreditUsed = false;
					paymentDetailsDto.creditAmountUsed = 0;
				}
				break;
			case EnumAmountType.Invoice:
			case EnumAmountType.ReconcileInvoice:
				if (invoice) {
					const pendingAmount =
						Number(invoice.invoiceAmount) -
						Number(invoice.paidAmount || 0);

					// Check if cash amount exceeds pending amount
					if (amount > pendingAmount) {
						excessAmount = amount - pendingAmount;
						amountToApply = pendingAmount;
						paymentDetailsDto.amount = amountToApply;
						newCredits += excessAmount;
						// No credit needed since cash alone covers the invoice
						creditAmountUsed = 0;
					} else {
						// Cash amount is less than or equal to pending amount
						amountToApply = amount;

						// If credits are to be used, calculate credit amount needed
						if (paymentDetailsDto.isCreditUsed) {
							const remainingAfterCash = pendingAmount - amount;
							const requestedCreditAmount = Number(
								paymentDetailsDto.creditAmountUsed || 0
							);

							// Use the minimum of: requested credit, available credit, remaining amount needed
							creditAmountUsed = Math.min(
								requestedCreditAmount,
								newCredits,
								remainingAfterCash
							);

							newCredits -= creditAmountUsed;

							// Check if there's still excess after using both cash and credit
							const totalPayment = amount + creditAmountUsed;
							if (totalPayment > pendingAmount) {
								// This shouldn't happen with the logic above, but safety check
								const excess = totalPayment - pendingAmount;
								excessAmount = excess;
								newCredits += excess;
								creditAmountUsed -= excess; // Adjust credit used
							}
						} else {
							creditAmountUsed = 0;
						}
					}
				}
				break;
			case EnumAmountType.CreditNote:
				if (transactionAmount > amount) {
					excessAmount = transactionAmount - amount;
					newCredits += excessAmount;
				} else if (amount > transactionAmount) {
					excessAmount = amount - transactionAmount;
					newCredits += excessAmount;
				}
				break;
		}

		return {
			newCredits,
			amountToApply,
			creditAmountUsed,
			excessAmount
		};
	}

	async updateInvoiceStatus(
		queryRunner: QueryRunner,
		invoice: any,
		paymentDetailsDto: PaymentDetailsDto,
		amountToApply: number,
		creditAmountUsed: number
	) {
		// Calculate total payment amount including cash and credits
		const totalPayment = Number(amountToApply) + Number(creditAmountUsed);
		const newPaidAmount = Number(invoice.paidAmount || 0) + totalPayment;
		const newBalanceDue = Math.max(
			0,
			Number(invoice.invoiceAmount) - newPaidAmount
		);

		// Determine status based on balance
		const newStatus =
			newBalanceDue <= 0.01 // Allow for small rounding differences
				? EnumInvoiceStatus.FULLY_PAID
				: newPaidAmount > 0
					? EnumInvoiceStatus.PARTIALLY_PAID
					: invoice.status;

		await queryRunner.manager.update(
			InvoiceEntity,
			{ id: invoice.id },
			{
				status: newStatus,
				paidAmount: newPaidAmount,
				balanceDue: newBalanceDue,
				updatedAt: new Date()
			}
		);

		if (
			paymentDetailsDto.type === EnumAmountType.CreditNote &&
			invoice.invoiceType === EnumInvoiceType.Refund
		) {
			await queryRunner.manager.update(
				InvoiceEntity,
				{ id: invoice.id },
				{
					status: EnumInvoiceStatus.FULLY_PAID,
					balanceDue: 0,
					updatedAt: new Date()
				}
			);
		}
	}

	async createCreditTransaction(
		queryRunner: QueryRunner,
		ownerId: string,
		amount: number,
		transactionType: CreditTransactionType,
		description: string,
		relatedIds: any,
		clinicId: string,
		brandId: string,
		userId: string
	) {
		let derivedTransactionType: DerivedCreditTransactionType =
			DerivedCreditTransactionType.UNKNOWN;
		let paymentDetail: PaymentDetailsEntity | null = null;

		if (relatedIds.paymentId) {
			// Fetch the paymentDetail within the same transaction
			paymentDetail = await queryRunner.manager.findOne(
				PaymentDetailsEntity,
				{
					where: { id: relatedIds.paymentId }
				}
			);
		}

		// --- Logic to determine derived_transaction_type based on migration rules ---

		if (transactionType === CreditTransactionType.USE) {
			if (paymentDetail) {
				// Rule 1: CREDITS_RETURNED
				if (paymentDetail.type === EnumAmountType.Return) {
					derivedTransactionType =
						DerivedCreditTransactionType.CREDITS_RETURNED;
				}
				// Rule 2: CREDITS_USED (with specific payment types)
				else if (
					paymentDetail.type === EnumAmountType.ReconcileInvoice ||
					paymentDetail.type === EnumAmountType.BulkReconcileInvoice
				) {
					derivedTransactionType =
						DerivedCreditTransactionType.CREDITS_USED;
				}
				// If paymentDetail exists but type doesn't match above for USE, it might remain UNKNOWN
				// or you might decide a default. Migration default for USE is CREDITS_USED if no specific conditions met.
				// However, migration rule 7 (USE without payment_detail_id) is handled below.
				// If paymentDetail IS present, and type is not Return/Reconcile/BulkReconcile, it will be UNKNOWN by default here.
				// This seems fine as migration does not specify other types for USE.
			} else {
				// Rule 7: Default case for USE transactions without payment details
				derivedTransactionType =
					DerivedCreditTransactionType.CREDITS_USED;
			}
		} else if (transactionType === CreditTransactionType.ADD) {
			if (paymentDetail) {
				// Rule 3: EXCESS_PAYMENT
				if (paymentDetail.type === EnumAmountType.Collect) {
					if (
						paymentDetail.amountPayable === 0 &&
						(paymentDetail.creditAmountAdded > 0 || amount > 0) // `amount` is ct.amount
					) {
						derivedTransactionType =
							DerivedCreditTransactionType.EXCESS_PAYMENT;
					}
					// Rule 4a: CREDITS_ADDED (Direct Purchase/Grant)
					else if (paymentDetail.amountPayable > 0) {
						derivedTransactionType =
							DerivedCreditTransactionType.CREDITS_ADDED;
					}
				}
				// Rule 5: CREDITS_ADDED (Refund to Credits)
				else if (paymentDetail.type === EnumAmountType.Return) {
					derivedTransactionType =
						DerivedCreditTransactionType.CREDITS_ADDED;
				}
				// If paymentDetail exists but type doesn't match above for ADD, it might remain UNKNOWN.
				// Migration rule 8 (ADD without payment_detail_id) is handled below.
			} else {
				// Rule 6: Special case "Credit added from previous positive balance"
				if (
					description ===
					'Credit added from previous positive balance'
				) {
					derivedTransactionType =
						DerivedCreditTransactionType.CREDITS_ADDED;
				}
				// Rule 8: Default case for ADD transactions without payment details (and not matching special description)
				else {
					derivedTransactionType =
						DerivedCreditTransactionType.CREDITS_ADDED;
				}
			}
		}
		const creditTransaction = queryRunner.manager.create(
			CreditTransactionEntity,
			{
				ownerId,
				amount,
				transactionType,
				derivedTransactionType,
				description,
				invoiceId: relatedIds.invoiceId,
				paymentDetailId: relatedIds.paymentId,
				clinicId,
				brandId,
				createdBy: userId,
				updatedBy: userId
			}
		);
		await queryRunner.manager.save(creditTransaction);
	}

	async verifyCreditAvailability(
		queryRunner: QueryRunner,
		bulkPaymentDto: BulkPaymentDetailsDto,
		ownerData: OwnerBrand,
		logContext: any
	): Promise<{ creditAmountToUse: number }> {
		const availableOwnerCredits = Number(ownerData.ownerCredits || 0);
		let creditAmountToUse = 0;

		if (
			bulkPaymentDto.isCreditUsed &&
			bulkPaymentDto.creditAmountUsed &&
			bulkPaymentDto.creditAmountUsed > 0
		) {
			if (availableOwnerCredits < bulkPaymentDto.creditAmountUsed) {
				this.logPaymentEvent(
					this.logger,
					logContext,
					'Insufficient credits for bulk payment',
					{
						availableCredits: availableOwnerCredits,
						requestedCredits: bulkPaymentDto.creditAmountUsed
					},
					'error'
				);
				throw new Error('Insufficient credits available');
			}
			creditAmountToUse = bulkPaymentDto.creditAmountUsed;
		}
		return { creditAmountToUse };
	}

	logPaymentEvent(
		logger: Logger,
		logContext: any,
		message: string,
		additionalData: any,
		level: 'log' | 'error' = 'log'
	) {
		logger[level](message, { ...logContext, ...additionalData });
	}

	/**
	 * Get payment details for a patient's owner, including soft deleted, cancelled, and written off invoices
	 * @param patientId - The ID of the patient
	 * @returns Payment details with total count and owner details
	 */
	async getPaymentDetailsForPatientsOwner(patientId: string): Promise<{
		paymentDetails: PaymentDetailsEntity[];
		total: number;
		ownerDetails: { createdAt: Date; openingBalance: number } | null;
	}> {
		// Step 1: Fetch the primary owner
		const primaryOwner = await this.getPrimaryOwner(patientId);
		if (!primaryOwner) {
			return { paymentDetails: [], total: 0, ownerDetails: null };
		}

		// Step 2: Build and execute the payment details query
		const paymentDetailsQuery = this.paymentDetailsRepository
			.createQueryBuilder('payment')
			.leftJoinAndSelect('payment.invoice', 'invoice')
			.leftJoinAndSelect('payment.patient', 'patient')
			.leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
			.leftJoinAndMapOne(
				'payment.creator',
				'users',
				'user',
				'payment.created_by = user.id'
			)
			// Include soft deleted, cancelled, and written off invoices
			.withDeleted()
			.where(
				// For Invoice and Credit Note - show all for the owner and patient
				'(payment.type IN (:...invoiceTypes) AND payment.patient_id = :patientId) OR ' +
					// For Bulk Reconcile Invoice - include all for this owner regardless of patient
					'(payment.type = :bulkReconcileType AND payment.owner_id = :ownerId) OR ' +
					// For other payment types - require both owner and patient match
					'(payment.type NOT IN (:...invoiceTypes) AND payment.type != :bulkReconcileType AND payment.owner_id = :ownerId AND payment.patient_id = :patientId)',
				{
					ownerId: primaryOwner.ownerBrand.id,
					patientId,
					invoiceTypes: ['Invoice', 'Credit Note'],
					bulkReconcileType: 'Bulk Reconcile Invoice'
				}
			);

		// Add query for related payments
		paymentDetailsQuery.orWhere(
			'payment.reference_alpha_id IN ' +
				'(SELECT DISTINCT p.reference_alpha_id FROM payment_details p WHERE p.patient_id IS NULL AND p.owner_id = :ownerId2)',
			{ ownerId2: primaryOwner.ownerBrand.id }
		);

		paymentDetailsQuery.orderBy('payment.created_at', 'DESC');

		const [paymentDetailsResults] =
			await paymentDetailsQuery.getManyAndCount();

		// Step 3: Map results and add createdByName
		const paymentDetails = paymentDetailsResults.map(payment => {
			const result = payment as EnhancedPaymentDetail;
			const creator = (payment as any).creator;
			result.createdByName =
				creator && (creator.firstName || creator.lastName)
					? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
					: result.createdBy
						? 'Staff Member'
						: 'System';
			return result;
		});

		// Step 4: Filter out invalid invoices
		const initialFilteredPaymentDetails = paymentDetails.filter(pd =>
			pd.type === 'Invoice'
				? (pd.invoice?.details?.filter(
						item => (item as any).isAddedToCart === true
					)?.length || 0) > 0
				: true
		);

		// Step 5: Build refunded items map for credit notes
		const refundedItemsMap = this.buildRefundedItemsMap(
			initialFilteredPaymentDetails
		);

		// Step 6: Consolidate payments with same referenceAlphaId
		const consolidatedPayments = this.consolidatePayments(
			initialFilteredPaymentDetails
		);

		// Step 7: Fetch and group related payments for all invoices in one query
		const invoiceIds = consolidatedPayments
			.filter(
				pd =>
					(pd.type === 'Invoice' || pd.type === 'Credit Note') &&
					pd.invoiceId
			)
			.map(pd => pd.invoiceId);
		const relatedPaymentsMap = await this.fetchRelatedPayments(
			invoiceIds as string[]
		);

		// Step 8: Enhance payment details with related payments and refund info
		const enhancedPaymentDetails = consolidatedPayments.map(pd => {
			const enhancedPd = pd as EnhancedPaymentDetail;

			// For Invoice and Credit Note types, attach payments by invoiceId
			if (
				(enhancedPd.type === 'Invoice' ||
					enhancedPd.type === 'Credit Note') &&
				enhancedPd.invoiceId
			) {
				const relatedPayments =
					relatedPaymentsMap.get(enhancedPd.invoiceId) || [];
				enhancedPd.payments =
					this.processRelatedPayments(relatedPayments);
				if (enhancedPd.invoice?.cartId && enhancedPd.invoice?.details) {
					enhancedPd.invoice.details =
						this.addRefundInfoToInvoiceDetails(
							enhancedPd.invoice.details,
							refundedItemsMap.get(enhancedPd.invoice.cartId) ||
								new Map()
						);
				}
			} else {
				enhancedPd.payments = [];
			}

			// Only transform "Collect" type payments to "Reconcile Invoice" when not processed by consolidation
			if (
				enhancedPd.type === 'Collect' &&
				enhancedPd.relatedPayments &&
				enhancedPd.hasCollectPayment &&
				enhancedPd.relatedPayments.length > 1
			) {
				enhancedPd.type = EnumAmountType.ReconcileInvoice;
			}

			return enhancedPd;
		});

		// Step 9: Add 'Reconcile Invoice' duplicates
		const finalPaymentDetails = this.addReconcileInvoices(
			enhancedPaymentDetails
		);

		// Step 10: Filter payment details based on patient and owner
		const patientFilteredPaymentDetails = finalPaymentDetails.filter(
			paymentDetail => {
				// For Invoice and Credit Note types, check both patient and owner
				if (
					paymentDetail.type === 'Invoice' ||
					paymentDetail.type === 'Credit Note'
				) {
					return paymentDetail.patientId === patientId;
				}

				// Special case for Bulk Reconcile Invoice - include all entries for this owner
				if (paymentDetail.type === 'Bulk Reconcile Invoice') {
					return paymentDetail.ownerId === primaryOwner.ownerBrand.id;
				}

				// For other types (Collect, Return, ReconcileInvoice) filter by both patientId and ownerId
				return paymentDetail.ownerId === primaryOwner.ownerBrand.id;
			}
		);

		// Step 11: Return the result
		return {
			paymentDetails: patientFilteredPaymentDetails,
			total: patientFilteredPaymentDetails.length,
			ownerDetails: primaryOwner.ownerBrand
				? {
						createdAt: primaryOwner.ownerBrand.createdAt,
						openingBalance:
							primaryOwner.ownerBrand.openingBalance || 0
					}
				: null
		};
	}

	/**
	 * Get payment details for a specific patient with optional search on reference_alpha_id
	 * @param patientId - The ID of the patient
	 * @param search - Optional search term for reference_alpha_id
	 * @param page - Page number for pagination
	 * @param limit - Number of items per page
	 * @returns Payment details with pagination info
	 */
	async getPaymentDetailsForPatient(
		patientId: string,
		search?: string,
		page: number = 1,
		limit: number = 20
	): Promise<{
		paymentDetails: PaymentDetailsEntity[];
		total: number;
		ownerDetails: { createdAt: Date; openingBalance: number } | null;
		totalPages: number;
		currentPage: number;
		uniqueUsers: { id: string; name: string }[];
	}> {
		try {
			// Step 1: Get the primary owner of the patient to filter related payments
			const primaryOwner = await this.getPrimaryOwner(patientId);
			if (!primaryOwner) {
				return {
					paymentDetails: [],
					total: 0,
					ownerDetails: null,
					totalPages: 0,
					currentPage: page,
					uniqueUsers: []
				};
			}

			// Step 2: Build comprehensive query with complex business logic
			const queryParams: any = {
				patientId,
				ownerId: primaryOwner.ownerBrand.id
			};

			// Build the base query with advanced joins and soft deleted records
			let query = this.paymentDetailsRepository
				.createQueryBuilder('payment')
				.leftJoinAndSelect('payment.invoice', 'invoice')
				.leftJoin('payment.patient', 'patient')
				.leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
				.leftJoinAndMapOne(
					'payment.creator',
					'users',
					'user',
					'payment.created_by = user.id'
				)
				// Include soft deleted, cancelled, and written off invoices for comprehensive reporting
				.withDeleted()
				.where(
					// For Invoice and Credit Note - show all for the patient
					'(payment.type IN (:...invoiceTypes) AND payment.patient_id = :patientId) OR ' +
						// For Bulk Reconcile Invoice - include only if patientId matches
						'(payment.type = :bulkReconcileType AND payment.owner_id = :ownerId AND payment.patient_id = :patientId) OR ' +
						// For other payment types - require both owner and patient match
						'(payment.type NOT IN (:...invoiceTypes) AND payment.type != :bulkReconcileType AND payment.owner_id = :ownerId AND payment.patient_id = :patientId) OR ' +
						// Include entries with null patientId that belong to this owner
						'(payment.patient_id IS NULL AND payment.owner_id = :ownerId)',
					{
						...queryParams,
						invoiceTypes: ['Invoice', 'Credit Note'],
						bulkReconcileType: 'Bulk Reconcile Invoice'
					}
				);

			// Add query for related payments (bulk reconcile entries without patient ID)
			query.orWhere(
				'payment.reference_alpha_id IN ' +
					'(SELECT DISTINCT p.reference_alpha_id FROM payment_details p WHERE p.patient_id = :patientId2 AND p.owner_id = :ownerId2)',
				{ patientId2: patientId, ownerId2: primaryOwner.ownerBrand.id }
			);

			// Add search filter for reference_alpha_id if provided
			if (search && search.trim() !== '') {
				query = query.andWhere(
					'payment.referenceAlphaId ILIKE :search',
					{
						search: `%${search.trim()}%`
					}
				);
			}

			// Filter out empty invoices - critical for financial accuracy
			query = query.andWhere(`(
				payment.type != 'Invoice' OR
				(invoice.details IS NOT NULL AND jsonb_array_length(
					jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
				) > 0)
			)`);

			// Filter out entries with no financial impact - essential for reporting
			query = query.andWhere(`(
				payment.amount > 0 OR
				payment.credit_amount_used > 0 OR
				payment.credit_amount_added > 0
			)`);

			// Apply ordering
			query = query.orderBy('payment.created_at', 'DESC');

			// Execute the query to get ALL matching results for comprehensive processing
			const allPaymentDetailsResults = await query.getMany();

			// Step 3: Fetch unique users who created payment details for comprehensive reporting
			const uniqueUsers: { id: string; name: string }[] = [];

			// Map results and add createdByName
			const allPaymentDetailsMapped = allPaymentDetailsResults.map(
				payment => {
					const result = payment as EnhancedPaymentDetail;
					const creator = (payment as any).creator;
					result.createdByName =
						creator && (creator.firstName || creator.lastName)
							? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
							: result.createdBy
								? 'Staff Member'
								: 'System';
					return result;
				}
			);

			// Consolidate payments with same referenceAlphaId
			const consolidatedPayments = this.consolidatePayments(
				allPaymentDetailsMapped
			);

			// Filter out consolidated payments with no financial impact
			// This is necessary because consolidation might result in zero amounts
			const allConsolidatedPayments = consolidatedPayments.filter(
				payment =>
					(payment.amount && payment.amount > 0) ||
					(payment.creditAmountUsed &&
						payment.creditAmountUsed > 0) ||
					(payment.creditAmountAdded && payment.creditAmountAdded > 0)
			);

			// Now, total is the length of the filtered consolidated array
			const total = allConsolidatedPayments.length;

			// Apply pagination in memory
			const startIndex = (page - 1) * limit;
			const endIndex = startIndex + limit;
			const paginatedPaymentDetails = allConsolidatedPayments.slice(
				startIndex,
				endIndex
			);

			// Get owner details
			const ownerDetails = primaryOwner.ownerBrand
				? {
						createdAt: primaryOwner.ownerBrand.createdAt,
						openingBalance:
							primaryOwner.ownerBrand.openingBalance || 0
					}
				: null;

			const totalPages = Math.ceil(total / limit);

			return {
				paymentDetails: paginatedPaymentDetails,
				total: total,
				ownerDetails,
				totalPages,
				currentPage: page,
				uniqueUsers
			};
		} catch (error) {
			console.error(`ERROR in getPaymentDetailsForPatient:`, error);
			throw error;
		}
	}

	/**
	 * Get invoices for a specific patient with comprehensive processing
	 * @param patientId - The ID of the patient
	 * @param search - Optional search term for invoice.reference_alpha_id
	 * @param page - Page number for pagination
	 * @param limit - Number of items per page
	 * @returns Invoices with comprehensive financial reporting
	 */
	async getInvoicesForPatient(
		patientId: string,
		search?: string,
		page: number = 1,
		limit: number = 20,
		invoiceType?: string
	): Promise<PatientInvoicesResponse> {
		this.logger.log('Fetching comprehensive invoices for patient', {
			patientId,
			search,
			page,
			limit,
			invoiceType
		});

		try {
			// Step 1: Build base invoice query for comprehensive reporting
			// Remove owner restriction to fetch invoices from all owners for this patient
			let invoiceQuery = this.invoiceRepository
				.createQueryBuilder('invoice')
				.leftJoinAndMapOne(
					'invoice.creator',
					'users',
					'user',
					'invoice.created_by = user.id'
				)
				.withDeleted() // Include soft deleted for comprehensive reporting
				.where('invoice.patientId = :patientId', { patientId })
				.andWhere('invoice.invoiceType = :invoiceType', {
					invoiceType: invoiceType || EnumInvoiceType.Invoice
				}).andWhere(`(
					CASE
						WHEN '${invoiceType}' = '${EnumInvoiceType.Invoice}' THEN
							JSONB_EXTRACT_PATH_TEXT(invoice.metadata::jsonb, 'isOldBalanceInvoice') = 'true'
							OR
							(invoice.details IS NOT NULL AND jsonb_array_length(
								jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
							) > 0)
						ELSE true
					END
				)`);

			// Add search filter for invoice.reference_alpha_id if provided
			if (search && search.trim() !== '') {
				invoiceQuery = invoiceQuery.andWhere(
					'invoice.referenceAlphaId ILIKE :search',
					{
						search: `%${search.trim()}%`
					}
				);
			}

			// Get total count for pagination
			const totalCount = await invoiceQuery.getCount();

			// Apply pagination and ordering
			const invoiceResults = await invoiceQuery
				.orderBy('invoice.createdAt', 'DESC')
				.skip((page - 1) * limit)
				.take(limit)
				.getMany();

			// Step 2: Get all invoice IDs for comprehensive payment processing
			const invoiceIds = invoiceResults.map(invoice => invoice.id);

			// Step 3: Fetch comprehensive payment details for all invoices
			const paymentDetailsMap =
				await this.fetchRelatedPayments(invoiceIds);

			// Step 4: Get appointmentIds for invoices via their cart relationships
			if (invoiceResults.length > 0) {
				const cartIds = invoiceResults
					.map((invoice: any) => invoice.cartId)
					.filter(Boolean);

				if (cartIds.length > 0) {
					const cartResults = await this.connection.manager
						.createQueryBuilder()
						.select([
							'cart.id as cartId',
							'cart.appointment_id as appointmentId'
						])
						.from('carts', 'cart')
						.where('cart.id IN (:...cartIds)', { cartIds })
						.withDeleted()
						.getRawMany();

					// Create cart-to-appointment mapping
					const cartToAppointmentMap = new Map<string, string>();
					cartResults.forEach(item => {
						const cartId = item.cartid || item.cartId;
						const appointmentId =
							item.appointmentid || item.appointmentId;

						if (cartId && appointmentId) {
							cartToAppointmentMap.set(cartId, appointmentId);
						}
					});

					// Assign appointment IDs to invoices
					invoiceResults.forEach((invoice: any) => {
						const appointmentId =
							cartToAppointmentMap.get(invoice.cartId) || null;
						invoice.appointmentId = appointmentId;
					});
				}
			}

			// Step 5: Fetch comprehensive audit logs
			const auditLogsMap = await this.fetchInvoiceAuditLogs(invoiceIds);

			// Step 6: Build refunded items map from ALL credit notes for this patient
			const refundedItemsMap = new Map<string, Map<string, number>>();

			// Fetch ALL credit notes for this patient to build a complete refund map
			let allCreditNotes: InvoiceEntity[] = [];
			try {
				allCreditNotes = await this.invoiceRepository.find({
					where: {
						patientId,
						invoiceType: EnumInvoiceType.Refund
					}
				});

				this.logger.log(
					'Fetched all credit notes for refund calculation',
					{
						patientId,
						count: allCreditNotes.length
					}
				);
			} catch (error) {
				this.logger.error(
					'Error fetching all credit notes for refund calculation',
					{
						error,
						patientId
					}
				);
				allCreditNotes = [];
			}

			// Step 6.1: Identify which invoices are credit notes and create original invoice reference mapping
			const creditNotes = invoiceResults.filter(
				invoice => invoice.invoiceType === EnumInvoiceType.Refund
			);

			// Step 6.2: Create a mapping of original invoice references if we have credit notes
			const originalInvoiceReferences = new Map<string, string>();

			if (creditNotes.length > 0) {
				// Get unique cartIds from credit notes
				const cartIds = [
					...new Set(
						creditNotes
							.map(creditNote => creditNote.cartId)
							.filter(Boolean)
					)
				];

				if (cartIds.length > 0) {
					try {
						// Step 6.2a: Find original invoices that match these cartIds
						const originalInvoices =
							await this.invoiceRepository.find({
								where: {
									cartId: In(cartIds),
									invoiceType: EnumInvoiceType.Invoice,
									patientId // Ensure we only get invoices for this patient
								},
								select: [
									'id',
									'cartId',
									'referenceAlphaId',
									'referenceId'
								]
							});

						this.logger.log(
							'Found original invoices for credit note references',
							{
								patientId,
								cartIds,
								originalInvoicesCount: originalInvoices.length
							}
						);

						// Step 6.2b: Create mapping from cartId to original invoice reference
						originalInvoices.forEach(invoice => {
							const referenceId =
								invoice.referenceAlphaId ||
								(invoice.referenceId
									? String(invoice.referenceId)
									: '');

							if (invoice.cartId && referenceId) {
								originalInvoiceReferences.set(
									invoice.cartId,
									referenceId
								);
							}
						});
					} catch (error) {
						this.logger.error(
							'Error fetching original invoices for credit notes',
							{
								error,
								cartIds,
								patientId
							}
						);
						// Continue without original references if this fails
					}
				}
			}

			// Process all credit notes to build the refunded items map
			// and create a map of original invoice IDs to their credit notes
			const invoiceToCreditNotesMap = new Map<string, CreditNoteInfo[]>();

			allCreditNotes.forEach(creditNote => {
				if (creditNote.cartId && creditNote.details) {
					const cartId = creditNote.cartId;
					const itemMap =
						refundedItemsMap.get(cartId) ||
						new Map<string, number>();

					const details = Array.isArray(creditNote.details)
						? creditNote.details
						: [];
					details.forEach(item => {
						const detailItem = item as InvoiceDetailItem;
						if (detailItem && detailItem.id) {
							const refundedQty = Number(
								detailItem.quantity || 0
							);
							itemMap.set(
								detailItem.id,
								(itemMap.get(detailItem.id) || 0) + refundedQty
							);
						}
					});
					refundedItemsMap.set(cartId, itemMap);
				}

				// Build credit notes mapping for refund cards
				if (creditNote.cartId) {
					// Find the original invoice that matches this credit note's cartId
					const originalInvoice = invoiceResults.find(
						invoice =>
							invoice.cartId === creditNote.cartId &&
							invoice.invoiceType !== EnumInvoiceType.Refund
					);

					if (originalInvoice) {
						const creditNoteInfo: CreditNoteInfo = {
							id: creditNote.id,
							referenceAlphaId: creditNote.referenceAlphaId || '',
							amount: Number(creditNote.invoiceAmount || 0),
							createdAt: creditNote.createdAt
						};

						const existingCreditNotes =
							invoiceToCreditNotesMap.get(originalInvoice.id) ||
							[];
						existingCreditNotes.push(creditNoteInfo);
						invoiceToCreditNotesMap.set(
							originalInvoice.id,
							existingCreditNotes
						);
					}
				}
			});

			// Step 7: Process invoices with comprehensive financial data
			const processedInvoices: PatientInvoiceDetails[] =
				invoiceResults.map(invoice => {
					const creator = (invoice as any).creator;
					const createdByName =
						creator && (creator.firstName || creator.lastName)
							? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
							: 'System';

					// Get payments for this invoice
					const relatedPayments =
						paymentDetailsMap.get(invoice.id) || [];
					const processedPayments =
						this.processRelatedPayments(relatedPayments);

					// Calculate comprehensive financial totals
					const totalPaidAmount = processedPayments.reduce(
						(sum, payment) => sum + (payment.amount || 0),
						0
					);
					const invoiceAmount = invoice.invoiceAmount || 0;
					const totalPrice = invoice.totalPrice || 0;

					// Convert processed payments to PaymentDetail format
					const paymentDetails: PaymentDetail[] =
						processedPayments.map(payment => ({
							id: payment.id,
							amount: payment.amount,
							paymentType: payment.paymentType,
							paymentMode: payment.paymentMode,
							createdAt: payment.createdAt,
							createdBy: payment.createdBy,
							createdByName: payment.createdByName,
							isCreditUsed: payment.isCreditUsed,
							creditAmountUsed: payment.creditAmountUsed,
							referenceAlphaId: payment.referenceAlphaId
						}));

					// Get audit logs for this invoice
					const auditLogs = auditLogsMap.get(invoice.id) || [];

					// Get credit notes for this invoice (for refund cards)
					const creditNotes =
						invoiceToCreditNotesMap.get(invoice.id) || [];

					// Determine if this is a credit note and find its original invoice reference
					let originalInvoiceReferenceAlphaId: string | null = null;

					if (invoice.invoiceType === EnumInvoiceType.Refund) {
						// Check if we have a mapping for this cartId
						if (
							invoice.cartId &&
							originalInvoiceReferences.has(invoice.cartId)
						) {
							originalInvoiceReferenceAlphaId =
								originalInvoiceReferences.get(invoice.cartId) ||
								null;
						}
					}

					// Add refund information to invoice details
					let processedDetails = invoice.details || [];
					if (
						invoice.cartId &&
						refundedItemsMap.has(invoice.cartId)
					) {
						const refundedItems = refundedItemsMap.get(
							invoice.cartId
						)!;
						processedDetails = processedDetails.map(item => {
							const detailItem = item as InvoiceDetailItem;
							// Only items that are added to cart can be refunded
							if (detailItem.isAddedToCart) {
								const refundedQty = detailItem.id
									? refundedItems.get(detailItem.id) || 0
									: 0;
								const originalQty = Number(
									detailItem.quantity || 0
								);
								const remainingQty = Math.max(
									0,
									originalQty - refundedQty
								);

								return {
									...item,
									refundedQuantity: refundedQty,
									remainingRefundQuantity: remainingQty,
									canBeRefunded: remainingQty > 0
								};
							} else {
								// For items not added to cart, set refund fields to 0 and canBeRefunded to false
								return {
									...item,
									refundedQuantity: 0,
									remainingRefundQuantity: 0,
									canBeRefunded: false
								};
							}
						});
					} else {
						// No refunds for this invoice, set default refund values
						processedDetails = processedDetails.map(item => {
							const detailItem = item as InvoiceDetailItem;
							const originalQty = Number(
								detailItem.quantity || 0
							);
							return {
								...item,
								refundedQuantity: 0,
								remainingRefundQuantity:
									detailItem.isAddedToCart ? originalQty : 0,
								canBeRefunded:
									detailItem.isAddedToCart && originalQty > 0
							};
						});
					}

					return {
						id: invoice.id,
						referenceAlphaId: invoice.referenceAlphaId || '',
						invoiceAmount,
						totalCollected: totalPaidAmount,
						totalprice: totalPrice,
						discountAmount: invoice.totalDiscount || 0,
						details: processedDetails, // Use processed details with refund info
						balanceDue: invoice.balanceDue || 0,
						status: invoice.status,
						createdAt: invoice.createdAt,
						createdBy: invoice.createdBy || '',
						createdByName,
						patientId: invoice.patientId,
						comment: '',
						metadata: invoice.metadata || {},
						payments: paymentDetails,
						cartId: invoice.cartId,
						discount: invoice.discount,
						auditLogs: auditLogs, // Add audit logs information
						appointmentId: (invoice as any).appointmentId || null, // Add appointment ID information
						creditNotes: creditNotes, // Add credit notes information for refund cards
						originalInvoiceReferenceAlphaId // Add the original invoice reference for credit notes
					};
				});

			// Step 8: Calculate comprehensive summary
			const summary = {
				totalAmount: processedInvoices.reduce(
					(sum, inv) => sum + inv.invoiceAmount,
					0
				),
				totalPaidAmount: processedInvoices.reduce(
					(sum, inv) => sum + inv.totalCollected,
					0
				),
				totalBalanceAmount: processedInvoices.reduce(
					(sum, inv) => sum + inv.balanceDue,
					0
				),
				totalCreditAmount: processedInvoices.reduce(
					(sum, inv) =>
						sum +
						inv.payments.reduce(
							(creditSum, payment) =>
								creditSum + payment.creditAmountUsed,
							0
						),
					0
				)
			};

			const totalPages = Math.ceil(totalCount / limit);

			this.logger.log(
				'Processed comprehensive invoices for patient successfully',
				{
					patientId,
					totalCount,
					processedCount: processedInvoices.length,
					totalPages,
					currentPage: page,
					summary
				}
			);

			return {
				invoices: processedInvoices,
				total: totalCount,
				pagination: {
					total: totalCount,
					page,
					limit,
					hasMore: page < totalPages
				},
				summary
			};
		} catch (error: any) {
			this.logger.error(
				'Error fetching comprehensive invoices for patient',
				{
					error: error.message,
					stack: error.stack,
					patientId,
					search
				}
			);

			// Return empty response instead of throwing error
			// This ensures the API doesn't fail when no data is found
			return {
				invoices: [],
				total: 0,
				pagination: {
					total: 0,
					page,
					limit,
					hasMore: false
				},
				summary: {
					totalAmount: 0,
					totalPaidAmount: 0,
					totalBalanceAmount: 0,
					totalCreditAmount: 0
				}
			};
		}
	}

	async getPaymentDetailsForOwner(
		ownerId: string,
		filters?: OwnerPaymentFiltersDto
	): Promise<{
		paymentDetails: PaymentDetailsEntity[];
		total: number;
		ownerDetails: { createdAt: Date; openingBalance: number } | null;
		uniqueUsers: { id: string; name: string }[];
	}> {
		// Step 1: Validate the owner exists
		const owner = await this.ownerBrandRepository.findOne({
			where: { id: ownerId }
		});

		if (!owner) {
			return {
				paymentDetails: [],
				total: 0,
				ownerDetails: null,
				uniqueUsers: []
			};
		}

		try {
			// Basic where conditions
			const whereConditions: any = { ownerId };
			const queryParams: any = { ownerId };

			// Create an array to hold additional where clauses for complex conditions
			const additionalWhereConditions: string[] = [];

			// Apply date range filters
			if (filters?.startDate) {
				additionalWhereConditions.push(
					'payment.created_at >= :startDate'
				);
				queryParams.startDate = new Date(filters.startDate);
			}

			if (filters?.endDate) {
				additionalWhereConditions.push(
					'payment.created_at <= :endDate'
				);
				queryParams.endDate = new Date(filters.endDate);
			}

			// Apply payment mode/type filters
			if (filters?.paymentMode) {
				// Check if it's a comma-separated list of payment modes
				if (filters.paymentMode.includes(',')) {
					// Split the comma-separated string into an array of payment modes
					const paymentModes = filters.paymentMode
						.split(',')
						.map(mode => mode.trim());

					// Create OR conditions for each payment mode
					const paymentModeConditions = paymentModes.map(
						(_, index) =>
							`payment.payment_type = :paymentMode${index}`
					);

					// Add the OR conditions to the query
					additionalWhereConditions.push(
						`(${paymentModeConditions.join(' OR ')})`
					);

					// Add each payment mode as a separate parameter
					paymentModes.forEach((mode, index) => {
						queryParams[`paymentMode${index}`] = mode;
					});
				} else {
					// Single payment mode
					whereConditions.paymentType = filters.paymentMode;
				}
			}

			// Apply payment type filters
			if (filters?.paymentType) {
				whereConditions.type = filters.paymentType;
			}

			// Apply creator/user ID filter
			// The DTO uses `userId` (singular) which can now accept a comma-separated string of IDs.
			if (filters?.userId && filters.userId.trim() !== '') {
				const userIdString = filters.userId.trim(); // Changed from userIds to userId to match DTO
				if (userIdString.includes(',')) {
					// Split the comma-separated string into an array of user IDs
					const userIdsArray = userIdString
						.split(',')
						.map((id: string) => id.trim())
						.filter((id: string) => id !== '');
					if (userIdsArray.length > 0) {
						// Use TypeORM's In operator for multiple user IDs
						whereConditions.createdBy = In(userIdsArray);
					}
				} else {
					// Single user ID
					whereConditions.createdBy = userIdString;
				}
			}

			// Get the base query with the simple where conditions
			let query = this.paymentDetailsRepository
				.createQueryBuilder('payment')
				.leftJoinAndSelect('payment.invoice', 'invoice')
				.leftJoinAndSelect('payment.patient', 'patient')
				.leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
				.leftJoinAndMapOne(
					'payment.creator',
					'users',
					'user',
					'payment.created_by = user.id'
				)
				.where(whereConditions);

			// Add complex conditions
			if (additionalWhereConditions.length > 0) {
				query = query.andWhere(
					additionalWhereConditions.join(' AND '),
					queryParams
				);
			}

			// Apply pet name filter
			if (filters?.petName) {
				query = query.andWhere('patient.patient_name LIKE :petName', {
					petName: `%${filters.petName}%`
				});
			}

			// Apply search filter
			if (filters?.searchTerm && filters.searchTerm.trim() !== '') {
				const searchTerm = `%${filters.searchTerm.toLowerCase()}%`;

				// Try to parse as date
				let dateParam: Date | null = null;
				try {
					const possibleDate = new Date(filters.searchTerm);
					if (!isNaN(possibleDate.getTime())) {
						dateParam = possibleDate;
					}
				} catch {
					// Not a date, continue with text search
				}

				if (dateParam) {
					// Date search
					const startOfDay = new Date(dateParam);
					startOfDay.setHours(0, 0, 0, 0);

					const endOfDay = new Date(dateParam);
					endOfDay.setHours(23, 59, 59, 999);

					query = query.andWhere(
						'(payment.created_at BETWEEN :startDate AND :endDate)',
						{
							startDate: startOfDay,
							endDate: endOfDay
						}
					);
				} else {
					// Text search
					query = query.andWhere(
						`(
						LOWER(payment.reference_alpha_id) LIKE :searchTerm OR
						LOWER(COALESCE(patient.patient_name, '')) LIKE :searchTerm OR
						LOWER(payment.payment_type) LIKE :searchTerm OR
						LOWER(payment.type) LIKE :searchTerm OR
						LOWER(COALESCE(user.first_name, '')) LIKE :searchTerm OR
						LOWER(COALESCE(user.last_name, '')) LIKE :searchTerm OR
						LOWER(COALESCE(invoice.reference_alpha_id, '')) LIKE :searchTerm
					)`,
						{ searchTerm }
					);
				}
			}

			// Filter out empty invoices
			query = query.andWhere(`(
				payment.type != 'Invoice' OR
				(invoice.details IS NOT NULL AND jsonb_array_length(
					jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
				) > 0)
			)`);

			// Filter out entries with no financial impact
			query = query.andWhere(`(
				payment.amount > 0 OR
				payment.credit_amount_used > 0 OR
				payment.credit_amount_added > 0
			)`);

			// Explicitly select the created_at column with an alias for sorting
			// This helps TypeORM resolve the column correctly in complex queries.
			query = query.addSelect(
				'payment.created_at',
				'payment_order_by_created_at'
			);

			// Apply order by with explicit column name
			query = query.orderBy('payment_order_by_created_at', 'DESC');

			// Execute the query to get ALL matching results (no DB pagination yet)
			const allPaymentDetailsResults = await query.getMany();

			// Fetch unique users who created payment details for this owner
			let uniqueUsers: { id: string; name: string }[] = [];
			try {
				// Get the base query with only owner filter - no other filters applied
				const usersQuery = this.paymentDetailsRepository
					.createQueryBuilder('payment')
					.select([
						'DISTINCT user.id AS id',
						'user.first_name AS firstname',
						'user.last_name AS lastname'
					])
					.leftJoin('users', 'user', 'payment.created_by = user.id')
					.where('payment.owner_id = :ownerId', { ownerId })
					.andWhere(`(
						payment.amount > 0 OR
						payment.credit_amount_used > 0 OR
						payment.credit_amount_added > 0
					)`);

				const users = await usersQuery.getRawMany();

				// Log the raw user data to debug
				this.logger.log('Raw user data from query', {
					users: users.slice(0, 3), // Log first 3 users for debugging
					count: users.length
				});

				// Format user data for frontend - using lowercase column names from raw query
				uniqueUsers = users
					.map(user => ({
						id: user.id,
						name:
							`${user.firstname || ''} ${user.lastname || ''}`.trim() ||
							'Staff Member'
					}))
					// Filter out users named 'Staff Member'
					.filter(user => user.name !== 'Staff Member');
			} catch (error) {
				this.logger.error(
					'Error fetching unique users for payment details',
					{
						error,
						ownerId
					}
				);
				uniqueUsers = []; // Use empty array on error
			}

			if (allPaymentDetailsResults.length === 0) {
				return {
					paymentDetails: [],
					total: 0,
					ownerDetails: {
						createdAt: owner.createdAt,
						openingBalance: owner.openingBalance || 0
					},
					uniqueUsers // This will be an empty array if no payments matched, which is correct.
				};
			}

			// Map results and add createdByName
			const allPaymentDetailsMapped = allPaymentDetailsResults.map(
				payment => {
					const result = payment as EnhancedPaymentDetail;
					const creator = (payment as any).creator;
					result.createdByName =
						creator && (creator.firstName || creator.lastName)
							? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
							: result.createdBy
								? 'Staff Member'
								: 'System';
					return result;
				}
			);

			// Consolidate payments with same referenceAlphaId
			const allConsolidatedPayments = this.consolidatePayments(
				allPaymentDetailsMapped
			);

			// Now, total is the length of the consolidated array
			const total = allConsolidatedPayments.length;

			// Apply pagination in memory
			let paginatedPayments = allConsolidatedPayments;
			if (filters?.page && filters?.limit) {
				const skip = (filters.page - 1) * filters.limit;
				paginatedPayments = allConsolidatedPayments.slice(
					skip,
					skip + filters.limit
				);
			}

			// Return the result
			return {
				paymentDetails: paginatedPayments, // PAGINATED CONSOLIDATED ITEMS
				total: total, // CORRECTED POST-CONSOLIDATION COUNT
				ownerDetails: {
					createdAt: owner.createdAt,
					openingBalance: owner.openingBalance || 0
				},
				uniqueUsers // This now correctly reflects users from filtered results.
			};
		} catch (error) {
			console.error(`ERROR in getPaymentDetailsForOwner:`, error);
			throw error;
		}
	}

	// Helper Functions

	/** Fetch the primary owner for a patient */
	private async getPrimaryOwner(
		patientId: string
	): Promise<PatientOwner | null> {
		const primaryOwner = await this.patientOwnerRepository.findOne({
			where: { patientId, isPrimary: true },
			relations: ['ownerBrand']
		});
		if (!primaryOwner) {
			this.logger.warn(
				`No primary owner found for patient ID: ${patientId}`
			);
		}
		return primaryOwner;
	}

	/** Build a map of refunded quantities per item by cartId from credit notes */
	private buildRefundedItemsMap(
		paymentDetails: PaymentDetailsEntity[]
	): Map<string, Map<string, number>> {
		const refundedItemsMap = new Map<string, Map<string, number>>();
		paymentDetails.forEach(pd => {
			if (pd.type === 'Credit Note' && pd.invoice?.cartId) {
				const cartId = pd.invoice.cartId;
				const itemMap =
					refundedItemsMap.get(cartId) || new Map<string, number>();
				(pd.invoice.details || []).forEach(item => {
					const detailItem = item as InvoiceDetailItem;
					if (detailItem.id) {
						const refundedQty = Number(detailItem.quantity || 0);
						itemMap.set(
							detailItem.id,
							(itemMap.get(detailItem.id) || 0) + refundedQty
						);
					}
				});
				refundedItemsMap.set(cartId, itemMap);
			}
		});
		return refundedItemsMap;
	}

	/** Consolidate payments with the same referenceAlphaId */
	private consolidatePayments(
		paymentDetails: PaymentDetailsEntity[]
	): EnhancedPaymentDetail[] {
		const paymentsByRef = new Map<string, PaymentDetailsEntity[]>();
		const nonConsolidatedPayments: PaymentDetailsEntity[] = [];

		// Group payments by referenceAlphaId
		paymentDetails.forEach(pd => {
			if (pd.referenceAlphaId) {
				const group = paymentsByRef.get(pd.referenceAlphaId) || [];
				group.push(pd);
				paymentsByRef.set(pd.referenceAlphaId, group);
			} else {
				nonConsolidatedPayments.push(pd);
			}
		});

		// Consolidate each group
		const consolidatedPayments = Array.from(paymentsByRef.values())
			.map(group => {
				// Sort group by creation date to ensure consistent ordering
				const sortedGroup = [...group].sort((a, b) => {
					// Primary sort by patientId presence
					if (a.patientId && !b.patientId) return -1;
					if (!a.patientId && b.patientId) return 1;

					// Secondary sort by type to put Reconcile Invoice and Collect in a predictable order
					const typeOrder: Record<string, number> = {
						'Reconcile Invoice': 0,
						Collect: 1,
						Invoice: 2,
						'Credit Note': 3,
						Return: 4
					};

					const aTypeOrder = typeOrder[a.type] ?? 999;
					const bTypeOrder = typeOrder[b.type] ?? 999;
					if (aTypeOrder !== bTypeOrder) {
						return aTypeOrder - bTypeOrder;
					}

					// Finally sort by creation date (oldest first to maintain consistent order)
					return (
						new Date(a.createdAt).getTime() -
						new Date(b.createdAt).getTime()
					);
				});

				const basePayment = {
					...sortedGroup[0]
				} as EnhancedPaymentDetail;

				// If base payment is of type 'Collect', change it to 'ReconcileInvoice'
				if (basePayment.type === 'Collect' && sortedGroup.length > 1) {
					basePayment.type = EnumAmountType.ReconcileInvoice;
				}

				basePayment.amount = group.reduce(
					(sum, p) => sum + Number(p.amount || 0),
					0
				);
				basePayment.creditAmountUsed = group.reduce(
					(sum, p) => sum + Number(p.creditAmountUsed || 0),
					0
				);

				// Handle credits added across collect payments
				const creditsAddedPayments = group.filter(
					p => p.isCreditsAdded
				);
				if (creditsAddedPayments.length > 0) {
					basePayment.isCreditsAdded = true;
					basePayment.creditAmountAdded = creditsAddedPayments.reduce(
						(sum, p) => sum + Number(p.creditAmountAdded || 0),
						0
					);
				}

				// Always add relatedPayments, even for single items
				basePayment.relatedPayments = sortedGroup
					.filter(p => p.type !== 'Collect') // Filter out Collect entries
					.map(p => ({
						...p,
						invoiceReferenceAlphaId: p.referenceAlphaId
					}));

				// Handle specific fields for 'Collect' payments if present
				const collectPayments = group.filter(p => p.type === 'Collect');
				if (collectPayments.length > 0) {
					basePayment.collectPayments = collectPayments.map(p => ({
						id: p.id,
						amount: p.amount,
						createdAt: p.createdAt,
						ledgerDocumentFilekey: p.ledgerDocumentFilekey
					}));
					basePayment.hasCollectPayment = true;
					basePayment.collectAmount = collectPayments.reduce(
						(sum, p) => sum + Number(p.amount || 0),
						0
					);
				}
				return [basePayment];
			})
			.flat();

		// For non-consolidated payments, add them with their own relatedPayments
		const enhancedNonConsolidatedPayments = nonConsolidatedPayments.map(
			(payment: PaymentDetailsEntity) => {
				const enhancedPayment = {
					...payment,
					relatedPayments: [
						{
							...payment,
							invoiceReferenceAlphaId: payment.referenceAlphaId
						}
					]
				} as EnhancedPaymentDetail;
				return enhancedPayment;
			}
		);

		// Combine all payments
		const allPayments = [
			...enhancedNonConsolidatedPayments,
			...consolidatedPayments
		];

		// Group by invoiceId for special ordering
		const paymentsByInvoiceId = new Map<string, PaymentDetailsEntity[]>();
		const paymentsWithoutInvoice: PaymentDetailsEntity[] = [];

		allPayments.forEach(payment => {
			if (payment.invoiceId) {
				const invoiceGroup =
					paymentsByInvoiceId.get(payment.invoiceId) || [];
				invoiceGroup.push(payment);
				paymentsByInvoiceId.set(payment.invoiceId, invoiceGroup);
			} else {
				paymentsWithoutInvoice.push(payment);
			}
		});

		// Process each invoice group to ensure Reconcile Invoice comes before Invoice
		const processedPayments: PaymentDetailsEntity[] = [];

		paymentsByInvoiceId.forEach(group => {
			// Sort the group so that "Reconcile Invoice" comes before "Invoice"
			const sortedGroup = [...group].sort((a, b) => {
				if (a.type === 'Reconcile Invoice' && b.type === 'Invoice')
					return -1;
				if (a.type === 'Invoice' && b.type === 'Reconcile Invoice')
					return 1;
				// If neither case applies, maintain original ordering by timestamp
				return (
					new Date(b.createdAt).getTime() -
					new Date(a.createdAt).getTime()
				);
			});
			processedPayments.push(...sortedGroup);
		});

		// Add payments without invoice
		processedPayments.push(...paymentsWithoutInvoice);

		// Final sort by timestamp for payments that aren't part of the same invoice
		return processedPayments.sort((a, b) => {
			// If they have the same invoiceId, the ordering has already been handled above
			if (a.invoiceId && b.invoiceId && a.invoiceId === b.invoiceId) {
				return 0; // Preserve the order established earlier
			}

			// If they have the same referenceAlphaId, keep the earlier grouped order
			if (
				a.referenceAlphaId &&
				b.referenceAlphaId &&
				a.referenceAlphaId === b.referenceAlphaId
			) {
				return 0;
			}

			// Otherwise sort by timestamp
			return (
				new Date(b.createdAt).getTime() -
				new Date(a.createdAt).getTime()
			);
		}) as EnhancedPaymentDetail[];
	}

	/** Fetch all related payments for given invoice IDs in one query */
	private async fetchRelatedPayments(
		invoiceIds: string[]
	): Promise<Map<string, any[]>> {
		if (!invoiceIds.length) return new Map();
		const query = this.paymentDetailsRepository
			.createQueryBuilder('payment')
			.leftJoin('users', 'user', 'payment.created_by = user.id')
			.leftJoin('patients', 'patient', 'payment.patient_id = patient.id')
			.select([
				'payment.id AS payment_id',
				'payment.amount AS payment_amount',
				'payment.type AS type',
				'payment.payment_type AS payment_type',
				'payment.created_at AS payment_created_at',
				'payment.created_by AS payment_created_by',
				'payment.is_credit_used AS payment_is_credit_used',
				'payment.credit_amount_used AS payment_credit_amount_used',
				'payment.reference_alpha_id AS payment_reference_alpha_id',
				'payment.reference_id AS payment_reference_id',
				'payment.invoice_id AS invoice_id',
				'payment.patient_id AS patient_id',
				'user.first_name AS user_first_name',
				'user.last_name AS user_last_name',
				'patient.patient_name AS patient_name'
			])
			.where('payment.invoice_id IN (:...invoiceIds)', { invoiceIds })
			.orderBy('payment.created_at', 'ASC');
		const payments = await query.getRawMany();

		const grouped = new Map<string, any[]>();
		payments.forEach(p => {
			const invoiceId = p.invoice_id;
			const group = grouped.get(invoiceId) || [];
			group.push(p);
			grouped.set(invoiceId, group);
		});
		return grouped;
	}

	/** Process related payments, consolidating bulk payments within them */
	private processRelatedPayments(payments: any[]): RelatedPayment[] {
		const paymentObjs = payments.map(p => ({
			id: p.payment_id,
			amount: Number(p.payment_amount || 0),
			paymentType: p.type || '',
			paymentMode: p.payment_type || '',
			createdAt: p.payment_created_at,
			createdBy: p.payment_created_by || '',
			createdByName:
				p.user_first_name && p.user_last_name
					? `${p.user_first_name} ${p.user_last_name}`.trim()
					: p.payment_created_by
						? 'Staff Member'
						: 'System',
			isCreditUsed: !!p.payment_is_credit_used,
			creditAmountUsed: Number(p.payment_credit_amount_used || 0),
			referenceAlphaId:
				p.payment_reference_alpha_id ||
				(p.payment_reference_id ? String(p.payment_reference_id) : ''),
			patientId: p.patient_id || null,
			patientName: p.patient_name || null
		}));

		const groupedByRef = new Map<string, RelatedPayment[]>();
		const nonBulkPayments: RelatedPayment[] = [];
		paymentObjs.forEach(p => {
			if (
				p.paymentType === EnumAmountType.BulkReconcileInvoice &&
				p.referenceAlphaId
			) {
				const group = groupedByRef.get(p.referenceAlphaId) || [];
				group.push(p);
				groupedByRef.set(p.referenceAlphaId, group);
			} else {
				nonBulkPayments.push(p);
			}
		});

		const consolidatedBulk = Array.from(groupedByRef.values()).map(
			group => {
				const bulkPayment = { ...group[0] };
				bulkPayment.totalAmount = group.reduce(
					(sum, p) => sum + p.amount,
					0
				);
				bulkPayment.totalCreditAmount = group.reduce(
					(sum, p) => sum + p.creditAmountUsed,
					0
				);
				bulkPayment.individualPayments = group;
				bulkPayment.isBulkPayment = true;
				return bulkPayment;
			}
		);

		return [...nonBulkPayments, ...consolidatedBulk].sort(
			(a, b) =>
				new Date(a.createdAt).getTime() -
				new Date(b.createdAt).getTime()
		);
	}

	/** Add refund information to invoice details */
	private addRefundInfoToInvoiceDetails(
		details: any[],
		refundedItems: Map<string, number>
	): any[] {
		return details.map(item => {
			const detailItem = item as InvoiceDetailItem;
			const refundedQty = detailItem.id
				? refundedItems.get(detailItem.id) || 0
				: 0;
			const originalQty = Number(detailItem.quantity || 0);
			const remainingQty = Math.max(0, originalQty - refundedQty);
			return {
				...item,
				refundedQuantity: refundedQty,
				remainingRefundQuantity: remainingQty,
				canBeRefunded: remainingQty > 0
			};
		});
	}

	/** Duplicate invoices and credit notes as 'Reconcile Invoice' */
	private addReconcileInvoices(
		paymentDetails: EnhancedPaymentDetail[]
	): EnhancedPaymentDetail[] {
		const result: EnhancedPaymentDetail[] = [];
		paymentDetails.forEach(pd => {
			if (
				(pd.type === 'Invoice' || pd.type === 'Credit Note') &&
				(pd.amount > 0 || pd.creditAmountUsed > 0)
			) {
				const reconcilePd = {
					...pd,
					type: EnumAmountType.ReconcileInvoice
				} as EnhancedPaymentDetail;
				delete reconcilePd.payments;
				result.push(reconcilePd);
			}
			result.push(pd);
		});
		return result;
	}

	async generatePdfForReceipt(receiptData: any, fileKey: string) {
		try {
			const receiptHTML = generateReceipt(receiptData);

			const receiptBuffer = await generatePDF(receiptHTML);
			await this.s3Service.uploadPdfToS3(receiptBuffer, fileKey);
			this.loggerService.log('successfully generated pdf for payment');
			return { receiptBuffer };
		} catch (error) {
			this.loggerService.log('error during generating pdf', error);
			return { receiptBuffer: null };
		}
	}

	async findOne(id: string) {
		const response = await this.paymentDetailsRepository.findOne({
			where: { id }
		});

		if (!response) {
			throw new NotFoundException(
				`Not found entity for requested id: ${id}`
			);
		}

		return response;
	}

	async deleteLedgerFileKey(id: string) {
		const paymentDetail = await this.paymentDetailsRepository.findOne({
			where: { id }
		});

		if (!paymentDetail) {
			throw new NotFoundException(
				`Not found entity for requested id: ${id}`
			);
		}

		if (paymentDetail.ledgerDocumentFilekey) {
			await this.s3Service.deleteFile(
				paymentDetail.ledgerDocumentFilekey
			);
			await this.paymentDetailsRepository.update(id, {
				ledgerDocumentFilekey: ''
			});
		}
	}

	async getOwnerInvoicesWithPayments(
		ownerId: string,
		filters: OwnerInvoiceFilters,
		userId: string,
		brandId: string,
		clinicId: string,
		page: number = 1,
		limit: number = 10
	): Promise<OwnerInvoiceResponse> {
		try {
			// 1. Get owner details
			const owner = await this.ownerBrandRepository.findOne({
				where: { id: ownerId }
			});

			if (!owner) {
				throw new NotFoundException(
					`Owner not found with ID: ${ownerId}`
				);
			}

			// Add the computeOwnerBalance method first
			const ownerBalance = await this.computeOwnerBalance(owner.id);
			const ownerDetails = {
				id: owner.id,
				name: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
				balance: ownerBalance, // Use computed balance instead of stored value
				credits: Number(owner.ownerCredits || 0)
			};

			let uniqueUsers: { id: string; name: string }[] = [];
			try {
				const usersQuery = this.invoiceRepository
					.createQueryBuilder('invoice')
					.withDeleted() // Include soft-deleted invoices
					.select([
						'DISTINCT user.id AS id',
						'user.first_name AS firstName',
						'user.last_name AS lastName'
					])
					.innerJoin('users', 'user', 'invoice.created_by = user.id')
					.where('invoice.ownerId = :ownerId', { ownerId })
					.andWhere('invoice.brandId = :brandId', { brandId })
					.andWhere('invoice.clinicId = :clinicId', { clinicId })
					.andWhere('invoice.invoiceType = :invoiceType', {
						invoiceType:
							filters.invoiceType || EnumInvoiceType.Invoice
					});

				const users = await usersQuery.getRawMany();
				// Format user data for frontend
				uniqueUsers = users.map(user => ({
					id: user.id,
					name:
						`${user.firstname || ''} ${user.lastname || ''}`.trim() ||
						'Staff Member'
				}));
			} catch (error) {
				this.logger.error('Error fetching unique users', {
					error,
					ownerId
				});
				uniqueUsers = []; // Use empty array on error
			}

			// Extract and sanitize search term
			const searchTerm = filters.searchTerm || '';
			const hasSearchTerm = searchTerm.trim() !== '';

			// 2. Find invoices for this owner directly
			let invoices = [];
			let totalInvoices = 0;
			try {
				// Create a single unified query that searches everything at once
				const combinedQuery = this.invoiceRepository
					.createQueryBuilder('invoice')
					.withDeleted() // Include soft-deleted invoices
					.leftJoin(
						'patients',
						'patient',
						'invoice.patientId = patient.id'
					)
					.leftJoin('users', 'user', 'invoice.created_by = user.id')
					.where('invoice.ownerId = :ownerId', { ownerId })
					.andWhere('invoice.brandId = :brandId', { brandId })
					.andWhere('invoice.clinicId = :clinicId', { clinicId })
					.andWhere('invoice.invoiceType = :invoiceType', {
						invoiceType:
							filters.invoiceType || EnumInvoiceType.Invoice
					}).andWhere(`(
						CASE
							WHEN '${filters.invoiceType}' = '${EnumInvoiceType.Invoice}' THEN
								JSONB_EXTRACT_PATH_TEXT(invoice.metadata::jsonb, 'isOldBalanceInvoice') = 'true'
								OR
								(invoice.details IS NOT NULL AND jsonb_array_length(
									jsonb_path_query_array(invoice.details, '$[*] ? (@.isAddedToCart == true)')
								) > 0)
							ELSE true
						END
					)`);

				// Apply filters
				if (filters.startDate) {
					combinedQuery.andWhere('invoice.createdAt >= :startDate', {
						startDate: new Date(filters.startDate)
					});
				}

				if (filters.endDate) {
					combinedQuery.andWhere('invoice.createdAt <= :endDate', {
						endDate: new Date(filters.endDate)
					});
				}

				if (filters.status) {
					// Check if status is comma-separated list of statuses
					const statuses = filters.status
						.split(',')
						.map(s => s.trim())
						.filter(Boolean);
					if (statuses.length > 1) {
						combinedQuery.andWhere(
							'invoice.status IN (:...statuses)',
							{ statuses }
						);
					} else {
						combinedQuery.andWhere('invoice.status = :status', {
							status: filters.status
						});
					}
				}

				// Filter by pet name if provided (exact match)
				if (filters.petName) {
					combinedQuery.andWhere('patient.patient_name = :petName', {
						petName: filters.petName
					});
				}

				// Filter by creator/user ID if provided
				if (filters.userId) {
					const userIds = filters.userId
						.split(',')
						.map(id => id.trim())
						.filter(Boolean);
					if (userIds.length > 0) {
						combinedQuery.andWhere(
							'invoice.created_by IN (:...userIds)',
							{ userIds }
						);
					}
				}

				// Apply search term with explicit debugging
				if (hasSearchTerm) {
					// Convert searchTerm to lowercase and add % wildcards for LIKE
					const formattedTerm = `%${searchTerm.toLowerCase()}%`;

					combinedQuery.andWhere(
						`(
							LOWER(invoice.reference_alpha_id) LIKE :searchTerm OR
							LOWER(COALESCE(patient.patient_name, '')) LIKE :searchTerm OR
							LOWER(invoice.status::text) LIKE :searchTerm OR
							LOWER(COALESCE(user.first_name, '')) LIKE :searchTerm OR
							LOWER(COALESCE(user.last_name, '')) LIKE :searchTerm
						)`,
						{ searchTerm: formattedTerm }
					);
				}
				// Get total count before pagination
				totalInvoices = await combinedQuery.getCount();

				// Add pagination - get normal invoice entities first
				invoices = await combinedQuery
					.orderBy('invoice.createdAt', 'DESC')
					.skip((page - 1) * limit)
					.take(limit)
					.getMany();

				// Get appointmentIds for invoices via their cart relationships
				if (invoices.length > 0) {
					const cartIds = invoices
						.map(invoice => (invoice as any).cartId)
						.filter(Boolean);

					if (cartIds.length > 0) {
						const cartResults = await this.connection.manager
							.createQueryBuilder()
							.select([
								'cart.id as cartId',
								'cart.appointment_id as appointmentId'
							])
							.from('carts', 'cart')
							.where('cart.id IN (:...cartIds)', { cartIds })
							.withDeleted()
							.getRawMany();

						// Create cart-to-appointment mapping
						const cartToAppointmentMap = new Map<string, string>();
						cartResults.forEach(item => {
							const cartId = item.cartid || item.cartId;
							const appointmentId =
								item.appointmentid || item.appointmentId;

							if (cartId && appointmentId) {
								cartToAppointmentMap.set(cartId, appointmentId);
							}
						});

						// Assign appointment IDs to invoices
						invoices = invoices.map(invoice => {
							const appointmentId =
								cartToAppointmentMap.get(
									(invoice as any).cartId
								) || null;
							(invoice as any).appointmentId = appointmentId;
							return invoice;
						});
					}
				}
			} catch (error) {
				this.logger.error('Error fetching invoices', {
					error,
					ownerId,
					searchTerm
				});
				// Return empty results instead of failing completely
				return {
					ownerDetails,
					invoices: [],
					uniqueUsers,
					pagination: {
						total: 0,
						page,
						limit,
						hasMore: false
					}
				};
			}

			if (invoices.length === 0) {
				return {
					ownerDetails,
					invoices: [],
					uniqueUsers,
					pagination: {
						total: totalInvoices,
						page,
						limit,
						hasMore: false
					}
				};
			}

			// Fetch audit logs for all invoices in a single query for efficiency
			const invoiceIds = invoices.map(invoice => invoice.id);
			const auditLogsMap = await this.fetchInvoiceAuditLogs(invoiceIds);

			// Step 1: Identify which invoices are credit notes
			const creditNotes = invoices.filter(
				invoice => invoice.invoiceType === EnumInvoiceType.Refund
			);

			// Step 2: Create a mapping of original invoice references if we have credit notes
			const originalInvoiceReferences = new Map<string, string>();

			if (creditNotes.length > 0) {
				// Step 2a: Collect unique cartIds from credit notes that have them
				const cartIds = creditNotes
					.filter(creditNote => creditNote.cartId) // Only include credit notes with a cartId
					.map(creditNote => creditNote.cartId as string); // Ensure cartId is treated as string

				if (cartIds.length > 0) {
					try {
						// Step 2b: Find all original invoices that match these cartIds
						const originalInvoicesQuery = this.invoiceRepository
							.createQueryBuilder('invoice')
							.select([
								'invoice.id',
								'invoice.cartId',
								'invoice.referenceAlphaId',
								'invoice.referenceId'
							])
							.where('invoice.cartId IN (:...cartIds)', {
								cartIds
							})
							.andWhere('invoice.invoiceType = :invoiceType', {
								invoiceType: EnumInvoiceType.Invoice
							}) // Original invoice type
							.andWhere('invoice.ownerId = :ownerId', { ownerId })
							.andWhere('invoice.brandId = :brandId', { brandId })
							.andWhere('invoice.clinicId = :clinicId', {
								clinicId
							});

						const originalInvoices =
							await originalInvoicesQuery.getMany();

						// Step 2c: Create mapping from cartId to original invoice reference
						originalInvoices.forEach(invoice => {
							const referenceId =
								invoice.referenceAlphaId ||
								(invoice.referenceId
									? String(invoice.referenceId)
									: '');

							if (invoice.cartId && referenceId) {
								originalInvoiceReferences.set(
									invoice.cartId,
									referenceId
								);
							}
						});
					} catch (error) {
						this.logger.error(
							'Error fetching original invoices for credit notes',
							{
								error,
								cartIds
							}
						);
						// Continue without original references if this fails
					}
				}
			}

			// 3. Process invoices
			const invoiceResults: OwnerInvoiceDetails[] = [];
			// If we have a search term and no direct matches were found initially,
			// we'll need to check each invoice for pet name match

			// Build refunded items map from ALL credit notes for this owner, not just the ones in the current page
			const refundedItemsMap = new Map<string, Map<string, number>>();

			// Fetch ALL credit notes for this owner to build a complete refund map
			let allCreditNotes: InvoiceEntity[] = [];
			try {
				allCreditNotes = await this.invoiceRepository.find({
					where: {
						ownerId,
						brandId,
						clinicId,
						invoiceType: EnumInvoiceType.Refund
					}
				});

				this.logger.log(
					'Fetched all credit notes for refund calculation',
					{
						ownerId,
						count: allCreditNotes.length
					}
				);
			} catch (error) {
				this.logger.error(
					'Error fetching all credit notes for refund calculation',
					{
						error,
						ownerId
					}
				);
				// Continue with the credit notes we have from the current page
				allCreditNotes = creditNotes;
			}

			// Create a map of original invoice IDs to their credit notes
			const invoiceToCreditNotesMap = new Map<string, CreditNoteInfo[]>();

			// Process all credit notes to build the refunded items map
			// and create a map of original invoice IDs to their credit notes
			allCreditNotes.forEach(creditNote => {
				if (creditNote.cartId) {
					// Logic to build refundedItemsMap
					if (creditNote.details) {
						const cartId = creditNote.cartId;
						const itemMap =
							refundedItemsMap.get(cartId) ||
							new Map<string, number>();

						const details = Array.isArray(creditNote.details)
							? creditNote.details
							: [];
						details.forEach(item => {
							const detailItem = item as InvoiceDetailItem;
							if (detailItem && detailItem.id) {
								const refundedQty = Number(
									detailItem.quantity || 0
								);
								itemMap.set(
									detailItem.id,
									(itemMap.get(detailItem.id) || 0) +
										refundedQty
								);
							}
						});
						refundedItemsMap.set(cartId, itemMap);
					}

					// Logic to build invoiceToCreditNotesMap
					const originalInvoice = invoices.find(
						invoice =>
							invoice.invoiceType === EnumInvoiceType.Invoice &&
							invoice.cartId === creditNote.cartId
					);

					if (originalInvoice) {
						const creditNoteInfo: CreditNoteInfo = {
							id: creditNote.id,
							referenceAlphaId: creditNote.referenceAlphaId || '',
							amount: Number(creditNote.invoiceAmount || 0),
							createdAt: creditNote.createdAt
						};

						const existingCreditNotes =
							invoiceToCreditNotesMap.get(originalInvoice.id) ||
							[];
						existingCreditNotes.push(creditNoteInfo);
						invoiceToCreditNotesMap.set(
							originalInvoice.id,
							existingCreditNotes
						);
					}
				}
			});

			for (const invoice of invoices) {
				try {
					// Check if this is an old balance invoice
					// Commenting out unused variable
					// const isOldBalanceInvoice = invoice?.metadata?.isOldBalanceInvoice === true;

					// We've already filtered empty details arrays at the database level
					// Get payments for this invoice
					let payments: any[] = [];
					try {
						// TypeORM requires proper entity relations
						const paymentQuery = this.paymentDetailsRepository
							.createQueryBuilder('payment')
							.leftJoinAndSelect('payment.invoice', 'invoice')
							.leftJoinAndSelect('payment.patient', 'patient')
							.leftJoinAndSelect(
								'payment.ownerBrand',
								'ownerBrand'
							)
							.leftJoinAndMapOne(
								'payment.creator',
								'users',
								'user',
								'payment.created_by = user.id'
							)
							.select([
								'payment.id AS payment_id',
								'payment.amount AS payment_amount',
								'payment.type AS type',
								'payment.payment_type AS payment_type',
								'payment.created_at AS payment_created_at',
								'payment.created_by AS payment_created_by',
								'payment.main_balance AS payment_main_balance',
								'payment.is_credit_used AS payment_is_credit_used',
								'payment.credit_amount_used AS payment_credit_amount_used',
								'payment.reference_alpha_id AS payment_reference_alpha_id',
								'payment.reference_id AS payment_reference_id',
								'user.id AS user_id',
								'user.first_name AS user_first_name',
								'user.last_name AS user_last_name'
							])
							.where('payment.invoice_id = :invoiceId', {
								invoiceId: invoice.id
							});

						if (filters.paymentMode) {
							paymentQuery.andWhere(
								'payment.payment_type = :paymentMode',
								{ paymentMode: filters.paymentMode }
							);
						}

						// Use getRawMany to get the raw database results with aliases
						payments = await paymentQuery
							.orderBy('payment.created_at', 'ASC')
							.getRawMany();
					} catch (error) {
						// Just log the error and continue with empty payments array
						this.logger.error('Error fetching payments', {
							error,
							invoiceId: invoice.id
						});
						payments = []; // Use empty array on error
					}

					// Skip invoices with no payments if payment mode filter is applied
					if (filters.paymentMode && payments.length === 0) {
						continue;
					}

					// Transform payment data
					const paymentDetails: PaymentDetail[] = payments.map(
						(payment: any) => ({
							id: payment.payment_id,
							amount: Number(payment.payment_amount || 0),
							paymentType: payment.type || '',
							paymentMode: payment.payment_type || '',
							createdAt: payment.payment_created_at,
							createdBy: payment.payment_created_by || '',
							createdByName:
								payment.user_first_name &&
								payment.user_last_name
									? `${payment.user_first_name} ${payment.user_last_name}`.trim()
									: payment.payment_created_by
										? 'Staff Member'
										: 'System',
							isCreditUsed: !!payment.payment_is_credit_used,
							creditAmountUsed: Number(
								payment.payment_credit_amount_used || 0
							),
							referenceAlphaId:
								payment.payment_reference_alpha_id ||
								(payment.payment_reference_id
									? String(payment.payment_reference_id)
									: '')
						})
					);

					// Group payments by referenceAlphaId for bulk payments
					const groupedPayments = new Map<string, any[]>();

					paymentDetails.forEach(payment => {
						if (
							payment.paymentType ===
								EnumAmountType.BulkReconcileInvoice &&
							payment.referenceAlphaId
						) {
							if (
								!groupedPayments.has(payment.referenceAlphaId)
							) {
								groupedPayments.set(
									payment.referenceAlphaId,
									[]
								);
							}
							const paymentGroup = groupedPayments.get(
								payment.referenceAlphaId
							);
							if (paymentGroup) {
								paymentGroup.push(payment);
							}
						}
					});

					// Process the grouped payments
					const combinedPaymentDetails: any[] = [];

					// First add non-bulk payments directly
					paymentDetails.forEach(payment => {
						if (
							payment.paymentType !==
							EnumAmountType.BulkReconcileInvoice
						) {
							combinedPaymentDetails.push(payment);
						}
					});

					// Then add combined bulk payments
					groupedPayments.forEach(payments => {
						if (payments.length > 0) {
							// Use the first payment as the base
							const baseBulkPayment = { ...payments[0] };

							// Combine the amounts from all payments with this referenceAlphaId
							baseBulkPayment.totalAmount = payments.reduce(
								(sum, p) => sum + p.amount,
								0
							);
							baseBulkPayment.totalCreditAmount = payments.reduce(
								(sum, p) => sum + p.creditAmountUsed,
								0
							);

							// Add related payments with invoice reference IDs
							baseBulkPayment.relatedPayments = payments.map(
								p => {
									// Get invoice reference alpha ID or look it up if needed
									let invoiceRefAlphaId = p.referenceAlphaId;

									// If we don't have the reference alpha ID, try to extract it from related data
									if (
										!invoiceRefAlphaId &&
										p.invoice &&
										p.invoice.referenceAlphaId
									) {
										invoiceRefAlphaId =
											p.invoice.referenceAlphaId;
									}

									return {
										...p,
										invoiceReferenceAlphaId:
											invoiceRefAlphaId
									};
								}
							);

							// Update description to indicate bulk payment
							baseBulkPayment.bulkPayment = true;

							combinedPaymentDetails.push(baseBulkPayment);
						}
					});

					// Get invoice creator info
					let invoiceCreatorName = 'System';
					try {
						if (invoice.createdBy) {
							// Try to get user information by ID
							const userQuery = this.connection.manager
								.createQueryBuilder()
								.select([
									'user.id',
									'user.first_name',
									'user.last_name'
								])
								.from('users', 'user')
								.where('user.id = :userId', {
									userId: invoice.createdBy
								});

							const creator = await userQuery.getRawOne();

							if (
								creator &&
								(creator.first_name || creator.last_name)
							) {
								invoiceCreatorName =
									`${creator.first_name || ''} ${creator.last_name || ''}`.trim();
							} else {
								invoiceCreatorName = 'Staff Member';
							}
						}
					} catch (error) {
						this.logger.error('Error fetching invoice creator', {
							error,
							invoiceId: invoice.id
						});
					}

					// Get patient name
					let patientName = 'Unknown';
					try {
						if (invoice.patientId) {
							const patient = await this.patientService.findOne(
								invoice.patientId
							);
							if (patient && patient.patientName) {
								patientName = patient.patientName;
							}
						}
					} catch (error) {
						this.logger.error('Error fetching patient', {
							error,
							patientId: invoice.patientId
						});
					}

					// Determine if this is a credit note and find its original invoice reference
					let originalInvoiceReferenceAlphaId: string | null = null;

					if (invoice.invoiceType === EnumInvoiceType.Refund) {
						// Check if we have a mapping for this cartId
						if (
							invoice.cartId &&
							originalInvoiceReferences.has(invoice.cartId)
						) {
							originalInvoiceReferenceAlphaId =
								originalInvoiceReferences.get(invoice.cartId) ||
								null;
						}
					}

					// Process invoice details to add refund information if this is a regular invoice
					let processedDetails = invoice.details;

					// Only process details for regular invoices, not credit notes
					if (
						invoice.invoiceType === EnumInvoiceType.Invoice &&
						invoice.cartId &&
						Array.isArray(invoice.details)
					) {
						// Get refunded items for this cart if they exist
						const refundedItems =
							refundedItemsMap.get(invoice.cartId) ||
							new Map<string, number>();

						// Always add refund information to each item in the invoice details
						// even if there are no refunds yet
						processedDetails = invoice.details.map(item => {
							const detailItem = item as InvoiceDetailItem;

							// Only items that are added to cart can be refunded
							if (detailItem.isAddedToCart) {
								const refundedQty = detailItem.id
									? refundedItems.get(detailItem.id) || 0
									: 0;
								const originalQty = Number(
									detailItem.quantity || 0
								);
								const remainingQty = Math.max(
									0,
									originalQty - refundedQty
								);

								return {
									...item,
									refundedQuantity: refundedQty,
									remainingRefundQuantity: remainingQty,
									canBeRefunded: remainingQty > 0
								};
							} else {
								// For items not added to cart, set refund fields to 0 and canBeRefunded to false
								return {
									...item,
									refundedQuantity: 0,
									remainingRefundQuantity: 0,
									canBeRefunded: false
								};
							}
						});
					}

					// Get credit notes for this invoice if it's a regular invoice
					let creditNotes: CreditNoteInfo[] = [];
					if (invoice.invoiceType === EnumInvoiceType.Invoice) {
						creditNotes =
							invoiceToCreditNotesMap.get(invoice.id) || [];
					}

					// Get audit logs for this invoice
					const auditLogs: InvoiceAuditLogInfo[] =
						auditLogsMap.get(invoice.id) || [];

					// Add invoice to results
					invoiceResults.push({
						id: invoice.id,
						referenceAlphaId:
							invoice.referenceAlphaId ||
							(invoice.referenceId
								? String(invoice.referenceId)
								: ''),
						invoiceAmount: Number(invoice.invoiceAmount || 0),
						totalCollected: Number(invoice.paidAmount || 0),
						totalprice: Number(invoice.totalPrice || 0),
						discountAmount: Number(invoice.totalDiscount || 0),
						cartId: invoice.cartId || '',
						appointmentId: (invoice as any).appointmentId || '', // Add appointmentId from cart
						details: processedDetails || '',
						balanceDue: Number(invoice.balanceDue || 0),
						status: invoice.status || 'PENDING',
						createdAt: invoice.createdAt,
						createdBy: invoice.createdBy || '',
						createdByName: invoiceCreatorName,
						patientId: invoice.patientId || '',
						patientName,
						comment: '', // Added placeholder comment field
						metadata: invoice.metadata || {},
						originalInvoiceReferenceAlphaId, // Add the new field
						discount: Number(invoice.discount || 0), // Add discount percentage
						payments: combinedPaymentDetails,
						creditNotes: creditNotes, // Add credit notes information
						auditLogs: auditLogs // Add audit logs information
					});
				} catch (error) {
					this.logger.error('Error processing invoice', {
						error,
						invoiceId: invoice.id
					});
				}
			}

			// Calculate if there are more results
			const hasMore = totalInvoices > page * limit;

			return {
				ownerDetails,
				invoices: invoiceResults,
				uniqueUsers,
				pagination: {
					total: totalInvoices,
					page,
					limit,
					hasMore
				}
			};
		} catch (error) {
			this.logger.error('Error in getOwnerInvoicesWithPayments', {
				error,
				ownerId
			});
			throw error;
		}
	}

	async getOwnerLedger(
		ownerId: string,
		userId: string,
		brandId: string,
		clinicId: string
	): Promise<{
		ownerDetails: {
			id: string;
			name: string;
			balance: number; // Final monetary running balance for the period
			credits: number; // Overall current credits of the owner
			createdAt: Date;
			openingBalance: number; // Monetary opening balance
		};
		ledgerItems: LedgerItem[];
		uniqueUsers: { id: string; name: string }[];
		summary: {
			totalMonetaryDebits: number;
			totalMonetaryCredits: number;
			finalRunningBalance: number;
			totalProfileCreditsAdded: number;
			totalProfileCreditsUsed: number;
			finalRunningCredits: number;
		};
	}> {
		try {
			this.logger.log('Getting combined ledger for owner', {
				ownerId,
				userId
			});

			// 1. Get owner details
			const owner = await this.ownerBrandRepository.findOne({
				where: { id: ownerId }
			});

			if (!owner) {
				throw new NotFoundException(
					`Owner not found with ID: ${ownerId}`
				);
			}

			// IMPORTANT NEW STEP: Check for the existence of an "old balance invoice" that consolidates previous entries
			let consolidationDate: Date | null = null;
			let hasConsolidationInvoice = false;

			try {
				// Find the earliest invoice with isOldBalanceInvoice=true for this owner
				const oldBalanceInvoice = await this.invoiceRepository
					.createQueryBuilder('invoice')
					.where('invoice.ownerId = :ownerId', { ownerId })
					.andWhere('invoice.brandId = :brandId', { brandId })
					.andWhere('invoice.clinicId = :clinicId', { clinicId })
					.andWhere(
						"JSONB_EXTRACT_PATH_TEXT(invoice.metadata::jsonb, 'isOldBalanceInvoice') = 'true'"
					)
					.orderBy('invoice.createdAt', 'ASC')
					.getOne();

				if (oldBalanceInvoice) {
					hasConsolidationInvoice = true;
					consolidationDate = oldBalanceInvoice.createdAt;
					this.logger.log(
						'Found consolidation invoice with isOldBalanceInvoice=true',
						{
							ownerId,
							invoiceId: oldBalanceInvoice.id,
							consolidationDate: consolidationDate,
							invoiceCreatedAt: oldBalanceInvoice.createdAt
						}
					);
				}
			} catch (error) {
				this.logger.error('Error checking for consolidation invoice', {
					error,
					ownerId
				});
				// Continue without filtering if there's an error in this step
			}

			// 2. Get payment details
			const paymentDetailsResult =
				await this.getPaymentDetailsForOwner(ownerId);

			// 3. Get invoices
			const invoicesResult = await this.getOwnerInvoicesWithPayments(
				ownerId,
				{ invoiceType: EnumInvoiceType.Invoice }, // Regular Invoices
				userId,
				brandId,
				clinicId,
				1,
				10000 // Assuming a large enough limit to fetch all for ledger
			);

			// 4. Get credit notes (Refund Invoices)
			const creditnoteResult = await this.getOwnerInvoicesWithPayments(
				ownerId,
				{ invoiceType: EnumInvoiceType.Refund }, // Refund Invoices (Credit Notes)
				userId,
				brandId,
				clinicId,
				1,
				10000 // Assuming a large enough limit
			);

			// 5. Combine and transform into LedgerItems
			let ledgerItems: LedgerItem[] = [];

			if (
				paymentDetailsResult.paymentDetails &&
				paymentDetailsResult.paymentDetails.length > 0
			) {
				const paymentItems = paymentDetailsResult.paymentDetails.map(
					payment => {
						const enhancedPayment =
							payment as EnhancedPaymentDetail;
						let monetaryCredit = 0;
						let monetaryDebit = 0;
						let itemDisplayType: string = payment.type || '';
						let currentCreditChange = 0;

						const paymentAmountNumber = Number(payment.amount) || 0;
						const creditAmountUsedNumber =
							Number(payment.creditAmountUsed) || 0;
						const creditAmountAddedNumber =
							Number(payment.creditAmountAdded) || 0;

						if (payment.type === EnumAmountType.Collect) {
							if (
								payment.isCreditsAdded &&
								creditAmountAddedNumber > 0
							) {
								if (payment.invoiceId) {
									monetaryDebit = creditAmountAddedNumber;
									monetaryCredit = 0;
									currentCreditChange =
										creditAmountAddedNumber;
									itemDisplayType = 'Credits Added';
								} else {
									monetaryDebit = 0;
									monetaryCredit = 0;
									currentCreditChange =
										creditAmountAddedNumber;
									itemDisplayType = 'Credits Added';
								}
								// THIS IS THE KEY CHANGE:
								// Money collected/allocated specifically to add to profile credits.
								// This transaction line itself is neutral to the monetary running balance.
								// The cash inflow (if new money) is for the credit pool, not general balance.
								// Or, if from excess (e.g. CN), the monetary impact was handled by the CN itself.
							} else {
								// A 'Collect' type not adding to credits is unusual.
								// If it happens, treat as a general monetary collection.
								monetaryCredit = paymentAmountNumber;
								currentCreditChange = paymentAmountNumber;
								itemDisplayType = 'Credits Added'; // Clarify display type
							}
						} else if (payment.type === EnumAmountType.Return) {
							// Cash is going OUT of the business to the customer
							itemDisplayType = 'Cash Refund'; // Default type

							if (
								payment.isCreditUsed &&
								creditAmountUsedNumber > 0
							) {
								// Cash returned by cashing out existing profile credits
								currentCreditChange = -creditAmountUsedNumber; // Profile credits deducted
								itemDisplayType = 'Credits Returned';
								// monetaryDebit still stands as cash is physically returned.
							}
						} else if (
							payment.type === EnumAmountType.ReconcileInvoice ||
							payment.type ===
								EnumAmountType.BulkReconcileInvoice ||
							payment.type === EnumAmountType.Invoice // Payment against an invoice
						) {
							itemDisplayType = 'Invoice Cleared';
							// Cash/Card/etc. coming IN against an invoice
							if (
								paymentAmountNumber > 0 &&
								!(
									payment.isCreditUsed &&
									payment.paymentType ===
										EnumPaymentType.Credits
								)
							) {
								monetaryCredit = paymentAmountNumber;
							}
							// If profile credits are used to pay part of the invoice:
							if (
								payment.isCreditUsed &&
								creditAmountUsedNumber > 0
							) {
								currentCreditChange = -creditAmountUsedNumber; // Profile credits used
								monetaryCredit = +creditAmountUsedNumber;
								// No direct monetaryCredit for the creditAmountUsed part on this line
							}
							if (
								payment.isCreditsAdded &&
								creditAmountAddedNumber > 0
							) {
								currentCreditChange = creditAmountAddedNumber;
								monetaryCredit =
									paymentAmountNumber -
									creditAmountAddedNumber;
								itemDisplayType += ' & Credits Added';
							}
						} else if (payment.type === EnumAmountType.CreditNote) {
							// This is a PaymentDetailsEntity with type 'CreditNote',
							// representing cash paid out for a refund related to a Credit Note invoice.
							monetaryDebit = paymentAmountNumber; // Cash paid out
							itemDisplayType = 'Amount Returned';
						} else if (payment.type === EnumAmountType.WriteOffInvoice) {
							// Write-off adjustments reduce the outstanding balance without affecting profile credits
							itemDisplayType = 'Invoice Write-off';
							monetaryCredit = paymentAmountNumber; // Credit entry to offset the debit from original invoice
							currentCreditChange = 0;
							monetaryDebit = 0;
						}

						return {
							id: payment.id,
							type: itemDisplayType,
							referenceAlphaId: payment.referenceAlphaId,
							amount: paymentAmountNumber, // This is the 'amount' field of the payment_details record
							debit: monetaryDebit,
							credit: monetaryCredit,
							runningBalance: null,
							creditChange: currentCreditChange,
							runningCredits: null,
							createdAt: payment.createdAt,
							createdBy: payment.createdBy,
							createdByName:
								enhancedPayment.createdByName || 'System',
							paymentType: payment.paymentType,
							paymentMode: payment.paymentType,
							patientId: payment.patientId,
							patientName: payment.patient
								? `${payment.patient.patientName || ''}`.trim()
								: '',
							notes: payment.paymentNotes || '',
							isCreditUsed: payment.isCreditUsed,
							creditAmountUsed: creditAmountUsedNumber,
							source: 'payment',
							// Populate clearedInvoices for payment entries
							clearedInvoices: (() => {
								const cleared: {
									invoiceId: string;
									invoiceReferenceAlphaId?: string;
									amountApplied: number;
								}[] = [];

								// Check if relatedPayments exists AND is not an empty array
								if (
									enhancedPayment.relatedPayments &&
									enhancedPayment.relatedPayments.length > 0
								) {
									// Iterate through the individual payments that were consolidated
									enhancedPayment.relatedPayments.forEach(
										// Optional chaining ?. is not strictly needed here due to the improved if-condition, but doesn't harm
										relatedPayment => {
											if (relatedPayment.invoice) {
												cleared.push({
													invoiceId:
														relatedPayment.invoice
															.id, // Assuming id is always present if invoice object exists
													invoiceReferenceAlphaId:
														relatedPayment.invoice
															.referenceAlphaId,
													amountApplied:
														Number(
															relatedPayment.amount ||
																0
														) +
														Number(
															relatedPayment.creditAmountUsed ||
																0
														) // Amount applied by this specific related payment
												});
											}
										}
									);
								} else {
									// This block will now also run if enhancedPayment.relatedPayments is an empty array
									// or if enhancedPayment.invoice exists (even if it's null, the properties will be handled)
									if (
										enhancedPayment.invoice ||
										enhancedPayment.type === 'Collect' ||
										enhancedPayment.type === 'Return'
									) {
										// Added a check to ensure we only push if there's an invoice OR it's a type that might not have one but still needs an entry
										cleared.push({
											invoiceId:
												enhancedPayment.invoice?.id ||
												'', // Fallback to empty string if invoice or id is null/undefined
											invoiceReferenceAlphaId:
												enhancedPayment.invoice
													?.referenceAlphaId,
											amountApplied:
												Number(
													enhancedPayment.amount || 0
												) +
												Number(
													enhancedPayment.creditAmountUsed ||
														0
												) // Amount applied by the main enhancedPayment
										});
									}
								}

								return cleared.length > 0 ? cleared : undefined;
							})()
						} as LedgerItem;
					}
				);
				ledgerItems.push(...paymentItems);
			}

			if (invoicesResult.invoices && invoicesResult.invoices.length > 0) {
				const invoiceItems: LedgerItem[] = [];

				invoicesResult.invoices.forEach(invoice => {
					const invoiceAmountNumber =
						Number(invoice.invoiceAmount) || 0;

					// Check if this is a balance consolidation invoice
					const isOldBalanceInvoice =
						invoice.metadata &&
						typeof invoice.metadata === 'object' &&
						invoice.metadata.isOldBalanceInvoice === true;

					// Check if this invoice is written off or cancelled
					const isWriteoffInvoice =
						invoice.status === EnumInvoiceStatus.WRITTEN_OFF;
					const isCancelledInvoice =
						invoice.status === EnumInvoiceStatus.CANCELLED;

					// Check for writeoff information in metadata for partial write-offs
					let writeoffInfo = null;
					let isPartialWriteoff = false;
					let isCompleteWriteoff = false;

					if (
						invoice.metadata &&
						typeof invoice.metadata === 'object'
					) {
						// Check for writeoff metadata (for both complete and partial writeoffs)
						if (
							invoice.metadata.writeoff &&
							typeof invoice.metadata.writeoff === 'object'
						) {
							writeoffInfo = invoice.metadata.writeoff;
							const writeoffAmount =
								Number(writeoffInfo.amount) || 0;
							const paidAmount =
								Number(invoice.totalCollected) || 0;

							// Determine if it's a partial or complete writeoff
							// Partial writeoff: some amount was paid before writeoff
							// Complete writeoff: no payments made, entire amount written off
							if (paidAmount > 0 && writeoffAmount > 0) {
								isPartialWriteoff = true;
								isCompleteWriteoff = false;
							} else if (paidAmount === 0 && writeoffAmount > 0) {
								isPartialWriteoff = false;
								isCompleteWriteoff = true;
							}
						}
					}

					// Set the appropriate display type
					let displayType: string;
					if (isOldBalanceInvoice) {
						displayType = 'Previous Balance Consolidated';
					} else {
						displayType = 'Invoice Generated';
					}

					// For balance consolidation invoices, add a comment if not already present
					const invoiceComment = isOldBalanceInvoice
						? invoice.comment ||
							'Consolidation of previous negative balance'
						: invoice.comment || '';

					// Create the main invoice ledger item
					const invoiceItem: LedgerItem = {
						id: invoice.id,
						type: displayType,
						referenceAlphaId: invoice.referenceAlphaId,
						amount: invoiceAmountNumber,
						// Complete writeoff and cancelled invoices should not appear as debits
						// Partial writeoff invoices should show full debit (writeoff will offset separately)
						debit: invoiceAmountNumber,
						credit: 0,
						runningBalance: null,
						creditChange: 0, // Invoices themselves don't change credits; payments do
						runningCredits: null,
						balanceDue: Number(invoice.balanceDue) || 0,
						totalCollected: Number(invoice.totalCollected) || 0,
						createdAt: invoice.createdAt,
						createdBy: invoice.createdBy,
						createdByName: invoice.createdByName || 'System',
						patientId: invoice.patientId,
						patientName: invoice.patientName || '',
						comment: invoiceComment,
						status: invoice.status,
						source: 'invoice',
						isConsolidationInvoice: isOldBalanceInvoice,
						isWriteoffInvoice:
							isWriteoffInvoice ||
							isCompleteWriteoff ||
							isPartialWriteoff, // Add flag to identify any writeoff invoices
						isCancelledInvoice: isCancelledInvoice // Add flag to identify cancelled invoices
					};

					invoiceItems.push(invoiceItem);

					// Create separate ledger entries for writeoffs and cancellations
					if (isPartialWriteoff && writeoffInfo) {
						// Partial writeoff entry
						const writeoffAmount = Number(writeoffInfo.amount) || 0;
						const writeoffDate = writeoffInfo.date
							? new Date(writeoffInfo.date)
							: invoice.createdAt;
						const writeoffBy = writeoffInfo.by || 'System';
						const writeoffReason =
							writeoffInfo.reason || 'Partial writeoff';

						const writeoffLedgerItem: LedgerItem = {
							id: `writeoff-${invoice.id}`, // Unique ID for writeoff entry
							type: 'Partial Writeoff',
							referenceAlphaId: `WO-${invoice.referenceAlphaId}`, // Prefix with WO for writeoff
							amount: writeoffAmount,
							debit: 0, // Writeoff reduces the amount owed (credit to customer)
							credit: writeoffAmount, // Credit entry to balance the partial writeoff
							runningBalance: null,
							creditChange: 0, // Writeoffs don't affect profile credits
							runningCredits: null,
							balanceDue: 0, // Not applicable for writeoff entries
							totalCollected: 0, // Not applicable for writeoff entries
							createdAt: writeoffDate,
							createdBy: writeoffInfo.userId || invoice.createdBy,
							createdByName: writeoffBy,
							patientId: invoice.patientId,
							patientName: invoice.patientName || '',
							comment: writeoffReason,
							status: 'WRITTEN_OFF',
							source: 'invoice', // Still related to invoice
							notes: `Partial writeoff of ${writeoffAmount} from invoice ${invoice.referenceAlphaId}`
						};

						invoiceItems.push(writeoffLedgerItem);

						this.logger.log(
							'Created partial writeoff ledger entry',
							{
								invoiceId: invoice.id,
								invoiceReferenceAlphaId:
									invoice.referenceAlphaId,
								writeoffAmount,
								writeoffDate,
								writeoffBy,
								writeoffReason
							}
						);
					} else if (isCompleteWriteoff && writeoffInfo) {
						// Complete writeoff entry
						const writeoffAmount = Number(writeoffInfo.amount) || 0;
						const writeoffDate = writeoffInfo.date
							? new Date(writeoffInfo.date)
							: invoice.createdAt;
						const writeoffBy = writeoffInfo.by || 'System';
						const writeoffReason =
							writeoffInfo.reason || 'Complete writeoff';

						const writeoffLedgerItem: LedgerItem = {
							id: `writeoff-${invoice.id}`, // Unique ID for writeoff entry
							type: 'Complete Writeoff',
							referenceAlphaId: `WO-${invoice.referenceAlphaId}`, // Prefix with WO for writeoff
							amount: writeoffAmount,
							debit: 0, // Writeoff reduces the amount owed (credit to customer)
							credit: writeoffAmount, // Credit entry to balance the complete writeoff
							runningBalance: null,
							creditChange: 0, // Writeoffs don't affect profile credits
							runningCredits: null,
							balanceDue: 0, // Not applicable for writeoff entries
							totalCollected: 0, // Not applicable for writeoff entries
							createdAt: writeoffDate,
							createdBy: writeoffInfo.userId || invoice.createdBy,
							createdByName: writeoffBy,
							patientId: invoice.patientId,
							patientName: invoice.patientName || '',
							comment: writeoffReason,
							status: 'WRITTEN_OFF',
							source: 'invoice', // Still related to invoice
							notes: `Complete writeoff of ${writeoffAmount} from invoice ${invoice.referenceAlphaId}`
						};

						invoiceItems.push(writeoffLedgerItem);

						this.logger.log(
							'Created complete writeoff ledger entry',
							{
								invoiceId: invoice.id,
								invoiceReferenceAlphaId:
									invoice.referenceAlphaId,
								writeoffAmount,
								writeoffDate,
								writeoffBy,
								writeoffReason
							}
						);
					} else if (isCancelledInvoice) {
						// Invoice cancellation entry
						const cancellationDate = invoice.createdAt; // Use created date as base

						// Try to get cancellation info from metadata if available
						let cancellationBy = 'System';
						let cancellationReason = 'Invoice cancelled';
						let cancellationActualDate = cancellationDate;

						if (
							invoice.metadata &&
							typeof invoice.metadata === 'object'
						) {
							if (invoice.metadata.cancellation) {
								cancellationBy =
									invoice.metadata.cancellation.by ||
									'System';
								cancellationReason =
									invoice.metadata.cancellation.reason ||
									'Invoice cancelled';
								// Use cancellation date from metadata if available
								if (invoice.metadata.cancellation.date) {
									cancellationActualDate = new Date(
										invoice.metadata.cancellation.date
									);
								}
							}
						}

						const cancellationLedgerItem: LedgerItem = {
							id: `cancellation-${invoice.id}`, // Unique ID for cancellation entry
							type: 'Invoice Cancellation',
							referenceAlphaId: `IC-${invoice.referenceAlphaId}`, // Prefix with CN for cancellation
							amount: invoiceAmountNumber,
							debit: 0, // Cancellation removes the debt obligation
							credit: invoiceAmountNumber, // Credit entry to balance the cancellation
							runningBalance: null,
							creditChange: 0, // Cancellations don't affect profile credits
							runningCredits: null,
							balanceDue: 0, // Not applicable for cancellation entries
							totalCollected: 0, // Not applicable for cancellation entries
							createdAt: cancellationActualDate,
							createdBy:
								invoice.metadata?.cancellation?.userId ||
								invoice.createdBy,
							createdByName: cancellationBy,
							patientId: invoice.patientId,
							patientName: invoice.patientName || '',
							comment: cancellationReason,
							status: 'CANCELLED',
							source: 'invoice', // Still related to invoice
							notes: `Invoice cancellation of ${invoiceAmountNumber} for invoice ${invoice.referenceAlphaId}`
						};

						invoiceItems.push(cancellationLedgerItem);

						this.logger.log(
							'Created invoice cancellation ledger entry',
							{
								invoiceId: invoice.id,
								invoiceReferenceAlphaId:
									invoice.referenceAlphaId,
								cancellationAmount: invoiceAmountNumber,
								cancellationDate: cancellationActualDate,
								cancellationBy,
								cancellationReason
							}
						);
					}
				});

				ledgerItems.push(...invoiceItems);
			}

			if (
				creditnoteResult.invoices &&
				creditnoteResult.invoices.length > 0
			) {
				// These are InvoiceEntities with invoiceType: EnumInvoiceType.Refund
				const creditNoteInvoiceItems = creditnoteResult.invoices.map(
					cnInvoice => {
						const cnAmountNumber =
							Number(cnInvoice.invoiceAmount) || 0;
						return {
							id: cnInvoice.id,
							type: 'Credit Note Generated', // Indicates a credit note was created
							referenceAlphaId: cnInvoice.referenceAlphaId,
							amount: cnAmountNumber,
							debit: 0,
							credit: cnAmountNumber, // A credit note issued increases owner's balance (reduces liability or customer debt)
							runningBalance: null,
							creditChange: 0, // The CN itself doesn't change profile credits; associated payments do
							runningCredits: null,
							balanceDue: Number(cnInvoice.balanceDue) || 0,
							totalCollected:
								Number(cnInvoice.totalCollected) || 0, // How much of this CN was "paid out" or applied
							createdAt: cnInvoice.createdAt,
							createdBy: cnInvoice.createdBy,
							createdByName: cnInvoice.createdByName || 'System',
							patientId: cnInvoice.patientId,
							patientName: cnInvoice.patientName || '',
							comment: cnInvoice.comment || '',
							status: cnInvoice.status,
							source: 'creditnote'
						} as LedgerItem;
					}
				);
				ledgerItems.push(...creditNoteInvoiceItems);
			}

			// NEW STEP: Filter out entries created before the consolidation invoice if it exists
			if (hasConsolidationInvoice && consolidationDate) {
				const originalItemCount = ledgerItems.length;

				ledgerItems = ledgerItems.filter(item => {
					const itemDate = new Date(item.createdAt);
					return itemDate >= consolidationDate!;
				});

				this.logger.log(
					'Filtered ledger items before consolidation date',
					{
						ownerId,
						consolidationDate,
						originalItemCount,
						filteredItemCount: ledgerItems.length,
						removedItems: originalItemCount - ledgerItems.length
					}
				);
			}

			// REVISED APPROACH:
			// For consolidation invoices, the opening balance was already migrated to either:
			// 1. Credits (for positive balances) - handled via credit transactions
			// 2. Consolidation invoices (for negative balances) - shown as invoices with isOldBalanceInvoice=true

			// Only add an explicit opening balance entry if:
			// - No consolidation invoice exists (i.e., this is a new owner created after the migration)
			// - The owner has a non-zero opening balance value
			const openingBalanceAmount = Number(owner.openingBalance) || 0;

			if (!hasConsolidationInvoice && openingBalanceAmount !== 0) {
				const openingBalanceDate = new Date(
					owner.createdAt.getTime() - 1000
				); // Just before owner creation

				const openingBalanceEntry: LedgerItem = {
					id: 'opening-balance-' + ownerId,
					type: 'Opening Balance',
					referenceAlphaId: '',
					amount: Math.abs(openingBalanceAmount),
					// Depending on whether opening balance is positive or negative:
					debit:
						openingBalanceAmount < 0
							? Math.abs(openingBalanceAmount)
							: 0,
					credit: openingBalanceAmount > 0 ? openingBalanceAmount : 0,
					runningBalance: openingBalanceAmount, // Initial running balance
					creditChange: 0, // Opening balance doesn't affect credits
					runningCredits: 0, // Credits start at 0
					createdAt: openingBalanceDate,
					createdBy: 'system',
					createdByName: 'System',
					source: 'payment', // Using 'payment' as the source type to match existing enum
					patientName: ''
				};

				// Add opening balance as first ledger entry
				ledgerItems.push(openingBalanceEntry);

				this.logger.log(
					'Added opening balance entry to ledger for new owner',
					{
						ownerId,
						openingBalance: openingBalanceAmount,
						entryDate: openingBalanceDate
					}
				);
			} else if (hasConsolidationInvoice) {
				this.logger.log(
					'Skipping explicit opening balance entry due to consolidation invoice',
					{
						ownerId,
						openingBalance: openingBalanceAmount,
						hasConsolidationInvoice
					}
				);
			}

			// 6. Sort all ledger items chronologically for calculation
			ledgerItems.sort((a, b) => {
				const timeA = new Date(a.createdAt).getTime();
				const timeB = new Date(b.createdAt).getTime();

				if (timeA !== timeB) {
					return timeA - timeB; // Ascending for calculation
				}

				// Secondary sort for items at the exact same millisecond to ensure consistency
				// Prefer 'Invoice' and 'Credit Note (Issued)' before 'Payment' if same time
				const typeOrderValue = (item: LedgerItem): number => {
					if (item.type === 'Opening Balance') return 0; // Always first
					if (item.source === 'invoice') return 1;
					if (item.source === 'creditnote') return 2;
					if (item.source === 'payment') {
						// For payments, "Add to Credits" might come before "Credit Note Refund (Cash)"
						// if they are part of the same refund operation.
						if (item.type === 'Add to Credits') return 3;
						if (item.type === 'Credit Note Refund (Cash)') return 4;
						return 5; // Other payments
					}
					return 6;
				};

				const orderA = typeOrderValue(a);
				const orderB = typeOrderValue(b);

				if (orderA !== orderB) {
					return orderA - orderB;
				}

				return a.id.localeCompare(b.id); // Fallback to ID for stable sort
			});

			// 7. Calculate running balances and running credits
			let currentRunningBalance = 0; // Start at 0 instead of opening balance since we're adding it as an entry
			let currentRunningCredits = 0; // Start credits from 0 for the ledger period

			ledgerItems.forEach(item => {
				currentRunningBalance =
					currentRunningBalance - item.debit + item.credit;
				item.runningBalance = currentRunningBalance;

				currentRunningCredits =
					currentRunningCredits + item.creditChange;
				item.runningCredits = currentRunningCredits;
			});

			// 8. Sort for display (typically newest first)
			ledgerItems.sort((a, b) => {
				const timeA = new Date(a.createdAt).getTime();
				const timeB = new Date(b.createdAt).getTime();

				if (timeA !== timeB) {
					return timeB - timeA; // Descending for display
				}
				// Secondary sort for items at the exact same millisecond (reversed from calculation sort)
				const typeOrderValue = (item: LedgerItem): number => {
					if (item.source === 'invoice') return 5; // Later in display if same time as payment
					if (item.source === 'creditnote') return 4;
					if (item.source === 'payment') {
						if (item.type === 'Credit Note Refund (Cash)') return 1; // Show cash part of refund first
						if (item.type === 'Add to Credits') return 2; // Then credit part
						return 3;
					}
					return 6;
				};
				const orderA = typeOrderValue(a);
				const orderB = typeOrderValue(b);

				if (orderA !== orderB) {
					return orderA - orderB;
				}
				return b.id.localeCompare(a.id); // Fallback to ID for stable sort
			});

			// 9. Prepare owner details
			const ownerDetails = {
				id: owner.id,
				name: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
				balance: currentRunningBalance, // Final monetary running balance from ledger calculation
				credits: Number(owner.ownerCredits) || 0, // Overall current credits from owner profile
				createdAt: owner.createdAt,
				openingBalance: Number(owner.openingBalance) || 0
			};

			// 10. Calculate summary data
			const totalMonetaryDebits = ledgerItems.reduce(
				(sum, item) => sum + (item.debit || 0),
				0
			);
			const totalMonetaryCredits = ledgerItems.reduce(
				(sum, item) => sum + (item.credit || 0),
				0
			);
			const totalProfileCreditsAdded = ledgerItems.reduce(
				(sum, item) =>
					sum + (item.creditChange > 0 ? item.creditChange : 0),
				0
			);
			const totalProfileCreditsUsed = ledgerItems.reduce(
				(sum, item) =>
					sum + (item.creditChange < 0 ? -item.creditChange : 0), // sum of absolute values of used credits
				0
			);

			// 11. Return combined result
			return {
				ownerDetails,
				ledgerItems,
				uniqueUsers: [
					...(paymentDetailsResult.uniqueUsers || []),
					...(invoicesResult.uniqueUsers || []),
					...(creditnoteResult.uniqueUsers || [])
				].filter(
					(user, index, self) =>
						index === self.findIndex(u => u.id === user.id) &&
						user.name !== 'Staff Member'
				),
				summary: {
					totalMonetaryDebits,
					totalMonetaryCredits,
					finalRunningBalance: currentRunningBalance,
					totalProfileCreditsAdded,
					totalProfileCreditsUsed,
					finalRunningCredits: currentRunningCredits
				}
			};
		} catch (error) {
			this.logger.error('Error in getOwnerLedger', {
				error,
				ownerId
			});
			throw error;
		}
	}
	async getOwnerPendingInvoices(
		ownerId: string,
		userId: string,
		filters: OwnerInvoiceFilters,
		brandId: string,
		clinicId: string
	) {
		try {
			this.logger.log('Getting pending invoices for owner', {
				ownerId,
				userId,
				filters
			});

			// 1. Get owner details
			const owner = await this.ownerBrandRepository.findOne({
				where: { id: ownerId }
			});

			if (!owner) {
				throw new NotFoundException(
					`Owner not found with ID: ${ownerId}`
				);
			}

			// Add the computeOwnerBalance method first
			const ownerBalance = await this.computeOwnerBalance(owner.id);
			const ownerDetails = {
				id: owner.id,
				name: `${owner.firstName || ''} ${owner.lastName || ''}`.trim(),
				balance: ownerBalance, // Use computed balance instead of stored value
				credits: Number(owner.ownerCredits || 0)
			};

			// 2. Create a query builder for finding invoices
			const query = this.invoiceRepository
				.createQueryBuilder('invoice')
				.leftJoin(
					'patients',
					'patient',
					'invoice.patientId = patient.id'
				)
				.leftJoin('users', 'user', 'invoice.created_by = user.id')
				.where('invoice.ownerId = :ownerId', { ownerId })
				.andWhere('invoice.brandId = :brandId', { brandId })
				.andWhere('invoice.clinicId = :clinicId', { clinicId })
				.andWhere('invoice.status != :cancelledStatus', {
					cancelledStatus: EnumInvoiceStatus.CANCELLED
				})
				.andWhere('invoice.invoiceType = :invoiceType', {
					invoiceType: EnumInvoiceType.Invoice
				});

			// Handle status filtering - if a comma-separated string is provided, split and use IN operator
			if (filters.status) {
				if (filters.status.includes(',')) {
					// Multiple statuses provided as comma-separated string
					const statuses = filters.status
						.split(',')
						.map(status => status.trim());
					query.andWhere('invoice.status IN (:...statuses)', {
						statuses
					});
				} else {
					// Single status
					query.andWhere('invoice.status = :status', {
						status: filters.status
					});
				}
			} else {
				// Default to only PENDING if no status is provided
				query.andWhere('invoice.status = :status', {
					status: EnumInvoiceStatus.PENDING
				});
			}

			// Apply filters
			if (filters.startDate) {
				query.andWhere('invoice.createdAt >= :startDate', {
					startDate: new Date(filters.startDate)
				});
			}

			if (filters.endDate) {
				query.andWhere('invoice.createdAt <= :endDate', {
					endDate: new Date(filters.endDate)
				});
			}

			// Filter by creator/user ID if provided
			if (filters.userId) {
				query.andWhere('invoice.created_by = :userId', {
					userId: filters.userId
				});
			}

			// Apply search term if provided
			if (filters.searchTerm && filters.searchTerm.trim() !== '') {
				const searchTerm = `%${filters.searchTerm.toLowerCase()}%`;
				query.andWhere(
					`(
						LOWER(invoice.reference_alpha_id) LIKE LOWER(:searchTerm) OR
						LOWER(patient.patient_name) LIKE LOWER(:searchTerm) OR
						LOWER(invoice.status) LIKE LOWER(:searchTerm) OR
						LOWER(user.first_name) LIKE LOWER(:searchTerm) OR
						LOWER(user.last_name) LIKE LOWER(:searchTerm)
					)`,
					{ searchTerm }
				);
			}

			// Apply pet name filter if provided
			if (filters.petName && filters.petName.trim() !== '') {
				query.andWhere(
					'LOWER(patient.patient_name) LIKE LOWER(:petName)',
					{
						petName: `%${filters.petName.toLowerCase()}%`
					}
				);
			}

			// Get all invoices, ordered by createdAt in descending order
			const pendingInvoices = await query
				.orderBy('invoice.createdAt', 'DESC')
				.getMany();

			if (pendingInvoices.length === 0) {
				return {
					ownerDetails,
					pendingInvoices: []
				};
			}

			// 3. Process invoices to get only required information
			const processedInvoices = await Promise.all(
				pendingInvoices.map(async invoice => {
					// Get patient name
					let patientName = 'Unknown';
					try {
						if (invoice.patientId) {
							const patient = await this.patientService.findOne(
								invoice.patientId
							);
							if (patient && patient.patientName) {
								patientName = patient.patientName;
							}
						}
					} catch (error) {
						this.logger.error('Error fetching patient', {
							error,
							patientId: invoice.patientId
						});
					}

					// Return only the required fields
					return {
						id: invoice.id,
						invoiceDate: invoice.createdAt,
						metadata: invoice.metadata || {},
						patientId: invoice.patientId,
						patientName,
						invoiceReference:
							invoice.referenceAlphaId ||
							String(invoice.referenceId || ''),
						balanceDue: Number(invoice.balanceDue || 0),
						invoiceAmount: Number(invoice.invoiceAmount || 0),
						totalCollected: Number(invoice.paidAmount || 0),
						comment: 'Comment', // Added placeholder comment field
						status: invoice.status // Include status in the response
					};
				})
			);

			return {
				ownerDetails,
				pendingInvoices: processedInvoices
			};
		} catch (error) {
			this.logger.error('Error in getOwnerPendingInvoices', {
				error,
				ownerId
			});
			throw error;
		}
	}

	async createBulkPaymentDetails(
		bulkPaymentDto: BulkPaymentDetailsDto,
		clinicId: string,
		brandId: string,
		userId: string
	): Promise<BulkPaymentResponse> {
		const queryRunner = this.connection.createQueryRunner();
		const requestId = uuidv4();
		const logContext = {
			clinicId,
			brandId,
			userId,
			ownerId: bulkPaymentDto.ownerId,
			invoiceIds: bulkPaymentDto.invoiceIds,
			patientId: bulkPaymentDto.patientId,
			requestId
		};

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Starting bulk payment processing',
				{ bulkPaymentDto }
			);

			// Fetch owner data
			const ownerData = await this.fetchOwnerData(
				queryRunner,
				bulkPaymentDto.ownerId,
				brandId
			);

			// Verify credit availability
			const { creditAmountToUse } = await this.verifyCreditAvailability(
				queryRunner,
				bulkPaymentDto,
				ownerData,
				logContext
			);

			// Calculate total payment corpus
			const totalPaymentCorpus =
				bulkPaymentDto.cashAmount + creditAmountToUse;
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Calculated payment corpus',
				{
					cashAmount: bulkPaymentDto.cashAmount,
					creditAmount: creditAmountToUse,
					totalPaymentCorpus
				}
			);

			// Fetch invoices
			const invoices = await this.fetchInvoicesForBulkPayment(
				queryRunner,
				bulkPaymentDto,
				clinicId,
				brandId,
				logContext
			);

			// Generate unique reference IDs
			const cashReferenceAlphaId = await generateUniqueCode(
				'referenceAlphaId',
				this.paymentDetailsRepository
			);
			const creditReferenceAlphaId = await generateUniqueCode(
				'referenceAlphaId',
				this.paymentDetailsRepository
			);

			// Process payments
			let remainingCashAmount = bulkPaymentDto.cashAmount;
			let remainingCreditAmount = creditAmountToUse;
			let totalCashApplied = 0;
			let totalCreditApplied = 0;
			const createdPayments: PaymentDetailsEntity[] = [];

			for (const invoice of invoices) {
				const { cashApplied, creditApplied, payments } =
					await this.processInvoicePayment(
						queryRunner,
						invoice,
						bulkPaymentDto,
						remainingCashAmount,
						remainingCreditAmount,
						cashReferenceAlphaId,
						creditReferenceAlphaId,
						clinicId,
						brandId,
						userId,
						logContext
					);

				remainingCashAmount -= cashApplied;
				remainingCreditAmount -= creditApplied;
				totalCashApplied += cashApplied;
				totalCreditApplied += creditApplied;
				createdPayments.push(...payments);
			}

			// Handle excess cash as credits
			if (remainingCashAmount > 0) {
				const excessPayment = await this.handleExcessCashAsCredits(
					queryRunner,
					bulkPaymentDto,
					remainingCashAmount,
					cashReferenceAlphaId,
					clinicId,
					brandId,
					userId,
					logContext
				);
				createdPayments.push(excessPayment);
			}

			// Update owner credits
			const newCredits =
				Number(ownerData.ownerCredits || 0) -
				totalCreditApplied +
				remainingCashAmount;
			ownerData.ownerCredits = newCredits;
			await queryRunner.manager.save(ownerData);

			this.logPaymentEvent(
				this.logger,
				logContext,
				'Updated owner credits after bulk payment',
				{
					previousCredits: Number(ownerData.ownerCredits || 0),
					newCredits,
					totalCashApplied,
					totalCreditApplied,
					excessToCredits: remainingCashAmount
				}
			);

			await queryRunner.commitTransaction();

			return {
				payments: createdPayments,
				totalCashApplied,
				totalCreditApplied,
				amountAddedToCredits: remainingCashAmount,
				message: `Successfully processed payment for ${invoices.length} invoices`
			};
		} catch (error: any) {
			await queryRunner.rollbackTransaction();
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Error in bulk payment processing',
				{ error },
				'error'
			);
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	async fetchInvoicesForBulkPayment(
		queryRunner: QueryRunner,
		bulkPaymentDto: BulkPaymentDetailsDto,
		clinicId: string,
		brandId: string,
		logContext: any
	): Promise<InvoiceEntity[]> {
		let invoices: InvoiceEntity[] = [];
		const whereClause =
			!bulkPaymentDto.invoiceIds || bulkPaymentDto.invoiceIds.length === 0
				? {
						ownerId: bulkPaymentDto.ownerId,
						status: In([
							EnumInvoiceStatus.PENDING,
							EnumInvoiceStatus.PARTIALLY_PAID
						]),
						brandId,
						clinicId,
						invoiceType: EnumInvoiceType.Invoice
					}
				: {
						id: In(bulkPaymentDto.invoiceIds || []),
						ownerId: bulkPaymentDto.ownerId
					};

		if (
			!bulkPaymentDto.invoiceIds ||
			bulkPaymentDto.invoiceIds.length === 0
		) {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'No specific invoices selected, fetching all pending invoices',
				{ ownerId: bulkPaymentDto.ownerId }
			);
		}

		invoices = await queryRunner.manager.find(InvoiceEntity, {
			where: whereClause,
			order: { createdAt: 'ASC' }
		});

		if (invoices.length === 0) {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'No valid invoices found for bulk payment',
				{
					requestedInvoiceIds:
						bulkPaymentDto.invoiceIds || 'All pending invoices'
				},
				'error'
			);
			throw new Error('No valid invoices found for the requested owner');
		}
		return invoices;
	}

	async processInvoicePayment(
		queryRunner: QueryRunner,
		invoice: InvoiceEntity,
		bulkPaymentDto: BulkPaymentDetailsDto,
		remainingCashAmount: number,
		remainingCreditAmount: number,
		cashReferenceAlphaId: string,
		creditReferenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string,
		logContext: any
	): Promise<{
		cashApplied: number;
		creditApplied: number;
		payments: PaymentDetailsEntity[];
	}> {
		const pendingAmount =
			Number(invoice.invoiceAmount) - Number(invoice.paidAmount || 0);
		if (pendingAmount <= 0) {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Skipping already paid invoice',
				{ invoiceId: invoice.id, status: invoice.status }
			);
			return { cashApplied: 0, creditApplied: 0, payments: [] };
		}

		const cashApplied = Math.min(remainingCashAmount, pendingAmount);
		const remainingAfterCash = pendingAmount - cashApplied;
		const creditApplied =
			remainingAfterCash > 0 && remainingCreditAmount > 0
				? Math.min(remainingCreditAmount, remainingAfterCash)
				: 0;

		const payments: PaymentDetailsEntity[] = [];

		if (cashApplied > 0) {
			const cashPayment = await this.createPaymentEntityForBulk(
				queryRunner,
				bulkPaymentDto,
				invoice,
				cashApplied,
				0,
				cashReferenceAlphaId,
				clinicId,
				brandId,
				userId,
				'cash'
			);
			payments.push(cashPayment);
		}

		if (creditApplied > 0) {
			const creditPayment = await this.createPaymentEntityForBulk(
				queryRunner,
				bulkPaymentDto,
				invoice,
				0,
				creditApplied,
				creditReferenceAlphaId,
				clinicId,
				brandId,
				userId,
				'credit'
			);
			payments.push(creditPayment);

			await this.createCreditTransaction(
				queryRunner,
				bulkPaymentDto.ownerId,
				creditApplied,
				CreditTransactionType.USE,
				`Credits used for invoice payment`,
				{ paymentId: creditPayment.id, invoiceId: invoice.id },
				clinicId,
				brandId,
				userId
			);
		}

		const newPaidAmount =
			Number(invoice.paidAmount || 0) + cashApplied + creditApplied;
		const newBalanceDue = Math.max(
			0,
			Number(invoice.invoiceAmount) - newPaidAmount
		);
		const newStatus =
			newBalanceDue <= 0
				? EnumInvoiceStatus.FULLY_PAID
				: newPaidAmount > 0
					? EnumInvoiceStatus.PARTIALLY_PAID
					: invoice.status;

		await queryRunner.manager.update(
			InvoiceEntity,
			{ id: invoice.id },
			{
				status: newStatus,
				paidAmount: newPaidAmount,
				balanceDue: newBalanceDue,
				updatedAt: new Date()
			}
		);

		this.logPaymentEvent(
			this.logger,
			logContext,
			'Updated invoice after payment',
			{
				invoiceId: invoice.id,
				newStatus,
				newPaidAmount,
				cashApplied,
				creditsApplied: creditApplied
			}
		);

		return { cashApplied, creditApplied, payments };
	}
	async createPaymentEntityForBulk(
		queryRunner: QueryRunner,
		bulkPaymentDto: BulkPaymentDetailsDto,
		invoice: InvoiceEntity,
		cashAmount: number,
		creditAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string,
		paymentType: 'cash' | 'credit'
	): Promise<PaymentDetailsEntity> {
		const payment = new PaymentDetailsEntity();
		payment.ownerId = bulkPaymentDto.ownerId;
		payment.invoiceId = invoice.id;

		// Use the patient ID from the invoice if available, otherwise fallback to the one from the DTO
		if (invoice.patientId) {
			payment.patientId = invoice.patientId;
		} else if (bulkPaymentDto.patientId) {
			payment.patientId = bulkPaymentDto.patientId;
		}

		// If there's only one invoice in the bulk payment, use ReconcileInvoice instead of BulkReconcileInvoice
		const isSingleInvoiceReconciliation =
			bulkPaymentDto.invoiceIds && bulkPaymentDto.invoiceIds.length === 1;
		payment.type = isSingleInvoiceReconciliation
			? EnumAmountType.ReconcileInvoice
			: EnumAmountType.BulkReconcileInvoice;

		payment.amount = cashAmount;
		payment.paymentType =
			paymentType === 'credit'
				? EnumPaymentType.Credits
				: ((bulkPaymentDto.paymentType ||
						EnumPaymentType.Cash) as EnumPaymentType);
		payment.transactionAmount = cashAmount;
		payment.amountPayable = cashAmount;
		payment.isCreditUsed = paymentType === 'credit';
		payment.creditAmountUsed = creditAmount;
		payment.isCreditsAdded = false;
		payment.creditAmountAdded = 0;
		payment.paymentNotes = bulkPaymentDto.description;
		payment.showInInvoice = true;
		payment.showInLedger = true;
		payment.brandId = brandId;
		payment.clinicId = clinicId;
		payment.previousBalance = 0; // No balance tracking
		payment.mainBalance = 0; // No balance tracking
		payment.createdBy = userId;
		payment.updatedBy = userId;
		payment.referenceAlphaId = referenceAlphaId;
		payment.receiptDetail = {};

		return await queryRunner.manager.save(payment);
	}

	async handleExcessCashAsCredits(
		queryRunner: QueryRunner,
		bulkPaymentDto: BulkPaymentDetailsDto,
		remainingCashAmount: number,
		cashReferenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string,
		logContext: any
	): Promise<PaymentDetailsEntity> {
		const excessPayment = new PaymentDetailsEntity();
		excessPayment.ownerId = bulkPaymentDto.ownerId;
		excessPayment.type = EnumAmountType.Collect;
		excessPayment.amount = remainingCashAmount;
		excessPayment.paymentType = (bulkPaymentDto.paymentType ||
			'Cash') as EnumPaymentType;
		excessPayment.transactionAmount = 0;
		excessPayment.amountPayable = 0;
		excessPayment.isCreditUsed = false;
		excessPayment.creditAmountUsed = 0;
		excessPayment.isCreditsAdded = true;
		excessPayment.creditAmountAdded = remainingCashAmount;
		excessPayment.paymentNotes = bulkPaymentDto.description;
		excessPayment.showInInvoice = true;
		excessPayment.showInLedger = true;
		excessPayment.brandId = brandId;
		excessPayment.clinicId = clinicId;
		excessPayment.previousBalance = 0; // No balance tracking
		excessPayment.mainBalance = 0; // No balance tracking
		excessPayment.createdBy = userId;
		excessPayment.updatedBy = userId;
		excessPayment.referenceAlphaId = cashReferenceAlphaId;
		excessPayment.receiptDetail = {};

		const savedExcessPayment =
			await queryRunner.manager.save(excessPayment);

		await this.createCreditTransaction(
			queryRunner,
			bulkPaymentDto.ownerId,
			remainingCashAmount,
			CreditTransactionType.ADD,
			`Excess payment added as credits from bulk payment`,
			{ paymentId: savedExcessPayment.id, invoiceId: null },
			clinicId,
			brandId,
			userId
		);

		this.logPaymentEvent(
			this.logger,
			logContext,
			'Handled excess cash as credits',
			{
				excessAmount: remainingCashAmount,
				paymentId: savedExcessPayment.id
			}
		);

		return savedExcessPayment;
	}

	// Add the computeOwnerBalance method first
	/**
	 * Fetch audit logs for multiple invoices efficiently
	 * @param invoiceIds Array of invoice IDs to fetch audit logs for
	 * @returns Map of invoice ID to audit logs
	 */
	private async fetchInvoiceAuditLogs(
		invoiceIds: string[]
	): Promise<Map<string, InvoiceAuditLogInfo[]>> {
		try {
			if (invoiceIds.length === 0) {
				return new Map();
			}

			// Fetch audit logs for all invoices in a single query
			const auditLogs = await this.invoiceAuditLogRepository
				.createQueryBuilder('auditLog')
				.leftJoinAndSelect('auditLog.user', 'user')
				.where('auditLog.invoiceId IN (:...invoiceIds)', {
					invoiceIds
				})
				.orderBy('auditLog.timestamp', 'DESC')
				.getMany();

			// Group audit logs by invoice ID
			const auditLogMap = new Map<string, InvoiceAuditLogInfo[]>();

			auditLogs.forEach(auditLog => {
				const auditLogInfo: InvoiceAuditLogInfo = {
					id: auditLog.id,
					operationType: auditLog.operationType,
					operations: auditLog.changedFieldsSummary, // Updated field name
					timestamp: auditLog.timestamp,
					userId: auditLog.userId,
					userName:
						auditLog.userId === null
							? 'SYSTEM'
							: auditLog.user
								? `${auditLog.user.firstName || ''} ${auditLog.user.lastName || ''}`.trim() ||
									'Staff Member'
								: 'Unknown User'
				};

				const existingLogs = auditLogMap.get(auditLog.invoiceId) || [];
				existingLogs.push(auditLogInfo);
				auditLogMap.set(auditLog.invoiceId, existingLogs);
			});

			this.logger.log('Fetched audit logs for invoices', {
				invoiceCount: invoiceIds.length,
				auditLogCount: auditLogs.length
			});

			return auditLogMap;
		} catch (error) {
			this.logger.error('Error fetching invoice audit logs', {
				error,
				invoiceIds
			});
			// Return empty map on error to not break the main functionality
			return new Map();
		}
	}

	private async computeOwnerBalance(ownerId: string): Promise<number> {
		try {
			this.logger.log('Computing owner balance', { ownerId });

			// Find all invoices associated with the owner that have pending or partially paid balances
			const invoices = await this.invoiceRepository.find({
				where: [
					{
						ownerId,
						status: EnumInvoiceStatus.PENDING,
						balanceDue: Not(0)
					},
					{
						ownerId,
						status: EnumInvoiceStatus.PARTIALLY_PAID,
						balanceDue: Not(0)
					}
				],
				select: ['id', 'balanceDue', 'status']
			});

			// Sum up the balance due from all invoices
			// Negate the sum since pending balance represents money owed by the owner (negative balance)
			const totalBalance =
				-1 *
				invoices.reduce(
					(sum, invoice) => sum + Number(invoice.balanceDue),
					0
				);

			this.logger.log('Owner balance computed successfully', {
				ownerId,
				totalBalance,
				invoiceCount: invoices.length,
				pendingCount: invoices.filter(
					i => i.status === EnumInvoiceStatus.PENDING
				).length,
				partiallyPaidCount: invoices.filter(
					i => i.status === EnumInvoiceStatus.PARTIALLY_PAID
				).length
			});

			return totalBalance;
		} catch (error) {
			this.logger.error('Error computing owner balance', {
				error,
				ownerId
			});
			throw new InternalServerErrorException(
				'Failed to compute owner balance'
			);
		}
	}

	/**
	 * Handle payment document operations (share or download)
	 * @param referenceAlphaId Reference alpha ID for the document
	 * @param documentType Type of document (payment-details)
	 * @param action Whether to share or download
	 * @param shareMethod Method to use for sharing (if applicable)
	 * @param brandId Brand ID
	 * @param userId User ID
	 * @returns Response with status and data
	 */
	async handlePaymentDocument(
		referenceAlphaId: string,
		documentType: 'payment-details',
		action: 'share' | 'download',
		shareMethod: 'email' | 'whatsapp' | 'both' | undefined,
		brandId: string,
		userId: string,
		recipient?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	) {
		this.loggerService.log(
			'Payment service: handlePaymentDocument called',
			{
				referenceAlphaId,
				documentType,
				action,
				shareMethod,
				brandId,
				userId,
				recipient,
				email,
				phoneNumber
			}
		);

		try {
			// Fetch all payment details entries with this referenceAlphaId and brandId
			const paymentDetails = await this.paymentDetailsRepository.find({
				where: {
					referenceAlphaId,
					brandId
				}
			});

			// Log the entries
			this.loggerService.log(
				'Found payment details for document request',
				{
					count: paymentDetails.length,
					referenceAlphaId,
					brandId
				}
			);

			// Get the first payment detail to extract common information
			const firstPayment = paymentDetails[0];
			if (!firstPayment) {
				throw new NotFoundException('No payment details found');
			}

			// Get patient and owner details for validation and file naming
			const patientId = firstPayment.patientId;
			let patientDetails = null;
			let ownerDetails = null;

			if (patientId) {
				try {
					patientDetails =
						await this.patientService.getPatientDetails(patientId);
					if (firstPayment.ownerId) {
						ownerDetails = await this.ownerBrandRepository.findOne({
							where: {
								id: firstPayment.ownerId,
								brandId
							}
						});
					}
				} catch (error) {
					this.loggerService.error(
						'Error fetching patient or owner details',
						{
							error,
							patientId,
							ownerId: firstPayment.ownerId
						}
					);
					// Continue without patient/owner details
				}
			}

			// Find permanent and temporary file keys across all payment details
			let existingPermanentFileKey: string | undefined;
			let permanentFileName: string | undefined;
			const temporaryFileKeys: string[] = [];

			// Check all payment details for file keys
			for (const payment of paymentDetails) {
				const receiptDetail = payment.receiptDetail as Record<
					string,
					string
				>;

				// Look for permanent file keys (starts with 'receipt/')
				if (
					receiptDetail?.fileKey &&
					receiptDetail.fileKey.startsWith('receipt/') &&
					!existingPermanentFileKey
				) {
					existingPermanentFileKey = receiptDetail.fileKey;
					permanentFileName = receiptDetail.fileName;
				}

				// Collect temporary file keys (starts with 'receipt_temp/')
				else if (
					receiptDetail?.fileKey &&
					receiptDetail.fileKey.startsWith('receipt_temp/') &&
					!temporaryFileKeys.includes(receiptDetail.fileKey)
				) {
					temporaryFileKeys.push(receiptDetail.fileKey);
				}
			}

			const hasPermanentFile = !!existingPermanentFileKey;

			this.loggerService.log('Payment document file status', {
				referenceAlphaId,
				hasPermanentFile,
				existingPermanentFileKey,
				hasTemporaryFiles: temporaryFileKeys.length > 0,
				temporaryFileKeys
			});

			// If a permanent file exists, make sure all payment details have the same file info
			if (hasPermanentFile) {
				const receiptDetail = {
					fileKey: existingPermanentFileKey,
					fileName:
						permanentFileName ||
						`payment_receipt_${referenceAlphaId}.pdf`,
					fileType: 'PDF'
				};

				// Update all payment records that don't already have this permanent file key
				const updatePromises = paymentDetails
					.filter(payment => {
						const detail = payment.receiptDetail as Record<
							string,
							string
						>;
						return (
							!detail?.fileKey ||
							detail.fileKey !== existingPermanentFileKey
						);
					})
					.map(payment => {
						payment.receiptDetail = receiptDetail;
						return this.paymentDetailsRepository.save(payment);
					});

				if (updatePromises.length > 0) {
					await Promise.all(updatePromises);
					this.loggerService.log(
						'Synced permanent file key to all payment records',
						{
							referenceAlphaId,
							fileKey: existingPermanentFileKey,
							updatedCount: updatePromises.length
						}
					);
				}
			}

			// If temporary files exist but no permanent file, clean them up
			if (temporaryFileKeys.length > 0 && !hasPermanentFile) {
				// Delete all temporary files from S3
				for (const tempFileKey of temporaryFileKeys) {
					try {
						await this.s3Service.deleteFile(tempFileKey);
						this.loggerService.log(
							'Deleted temporary receipt file',
							{
								tempFileKey,
								referenceAlphaId
							}
						);
					} catch (deleteError) {
						// Just log the error but continue with the process
						this.loggerService.error(
							'Failed to delete temporary receipt file',
							{
								error: deleteError,
								tempFileKey,
								referenceAlphaId
							}
						);
					}
				}

				// Clear file keys from all payment records with temporary files and set isGenerating=true
				const clearPromises = paymentDetails
					.filter(payment => {
						const detail = payment.receiptDetail as Record<
							string,
							string
						>;
						return (
							detail?.fileKey &&
							detail.fileKey.startsWith('receipt_temp/')
						);
					})
					.map(payment => {
						// Instead of empty object, set isGenerating flag
						payment.receiptDetail = { isGenerating: 'true' };
						return this.paymentDetailsRepository.save(payment);
					});

				if (clearPromises.length > 0) {
					await Promise.all(clearPromises);
					this.loggerService.log(
						'Cleared temporary file keys and set generation flag on payment records',
						{
							referenceAlphaId,
							clearedCount: clearPromises.length
						}
					);
				}
			}

			// For download requests with existing permanent file, return URL immediately
			if (action === 'download' && hasPermanentFile) {
				this.loggerService.log(
					'Using existing permanent file for download',
					{
						fileKey: existingPermanentFileKey,
						referenceAlphaId
					}
				);

				try {
					// Generate a formatted filename
					let fileName =
						permanentFileName ||
						`payment_receipt_${referenceAlphaId}.pdf`;

					// If we have patient/owner details, create a more descriptive filename
					if (patientDetails && ownerDetails) {
						const petName = patientDetails.patientName || 'pet';
						const ownerName = ownerDetails.lastName || 'owner';

						const formattedPetName = petName
							.replace(/[^a-zA-Z0-9]/g, '_')
							.toLowerCase();
						const formattedOwnerName = ownerName
							.replace(/[^a-zA-Z0-9]/g, '_')
							.toLowerCase();

						fileName = `${formattedPetName}_${formattedOwnerName}_receipt_${referenceAlphaId}.pdf`;
					}

					// Get download URL from S3
					const downloadUrl =
						await this.s3Service.getDownloadPreSignedUrl(
							existingPermanentFileKey as string,
							fileName
						);

					return {
						status: 'success',
						data: {
							downloadUrl,
							fileName
						}
					};
				} catch (error) {
					this.loggerService.error(
						'Error generating download URL for permanent file',
						{
							error,
							fileKey: existingPermanentFileKey,
							referenceAlphaId
						}
					);

					throw new InternalServerErrorException(
						'Failed to generate download URL'
					);
				}
			}

			// Queue the task for asynchronous processing
			await this.queuePaymentDocumentTask(
				referenceAlphaId,
				action,
				shareMethod,
				brandId,
				userId,
				existingPermanentFileKey, // Only pass the permanent file key if available
				recipient,
				email,
				phoneNumber
			);

			// Return immediate response with status
			return {
				status: 'processing',
				data: {
					referenceAlphaId,
					message: `Document ${action} request is being processed`
				}
			};
		} catch (error) {
			this.loggerService.error('Error in handlePaymentDocument', {
				error,
				referenceAlphaId,
				action,
				shareMethod,
				recipient,
				email,
				phoneNumber
			});

			// Pass through HttpExceptions
			if (error instanceof HttpException) {
				throw error;
			}

			// For other errors, throw a generic internal server error
			throw new InternalServerErrorException(
				'Failed to process document request'
			);
		}
	}

	/**
	 * Check if a payment receipt PDF has been generated and is ready
	 * @param referenceAlphaId Reference alpha ID of the payment to check
	 * @returns Status and URL if ready
	 */
	async checkPaymentDocumentStatus(referenceAlphaId: string) {
		this.logger.log('Checking payment document status', {
			referenceAlphaId
		});

		try {
			// Find all payment details with this reference ID
			const paymentDetails = await this.paymentDetailsRepository.find({
				where: { referenceAlphaId }
			});

			if (!paymentDetails || paymentDetails.length === 0) {
				throw new NotFoundException('Payment details not found');
			}

			// First check if any record has the isGenerating flag set
			for (const payment of paymentDetails) {
				const receiptDetail = payment.receiptDetail as Record<
					string,
					string
				>;
				if (receiptDetail?.isGenerating === 'true') {
					this.logger.log('Document generation in progress', {
						referenceAlphaId
					});

					return {
						status: true,
						data: {
							isReady: false
						},
						message: 'Document is still being generated'
					};
				}
			}

			// Check if any of the payment details has a file key - either permanent or temporary
			let fileKey: string | undefined;
			let fileName: string | undefined;
			let isPermanent: boolean = false;

			// First priority: Check for permanent files (receipt/)
			for (const payment of paymentDetails) {
				const receiptDetail = payment.receiptDetail as Record<
					string,
					string
				>;
				if (
					receiptDetail?.fileKey &&
					receiptDetail.fileKey.startsWith('receipt/')
				) {
					fileKey = receiptDetail.fileKey;
					fileName =
						receiptDetail.fileName ||
						`payment_receipt_${referenceAlphaId}.pdf`;
					isPermanent = true;
					break;
				}
			}

			// Second priority: Check for temporary files (receipt_temp/)
			if (!fileKey) {
				for (const payment of paymentDetails) {
					const receiptDetail = payment.receiptDetail as Record<
						string,
						string
					>;
					if (
						receiptDetail?.fileKey &&
						receiptDetail.fileKey.startsWith('receipt_temp/')
					) {
						fileKey = receiptDetail.fileKey;
						fileName =
							receiptDetail.fileName ||
							`payment_receipt_${referenceAlphaId}.pdf`;
						isPermanent = false;
						break;
					}
				}
			}

			if (!fileKey) {
				// No document has been generated yet
				return {
					status: true,
					data: {
						isReady: false
					},
					message: 'Document is still being generated'
				};
			}

			// Document is ready, generate a download URL
			const url = await this.s3Service.getDownloadPreSignedUrl(
				fileKey,
				fileName || `payment_receipt_${referenceAlphaId}.pdf`
			);

			// Log additional info about the document found
			this.logger.log('Found payment document file', {
				referenceAlphaId,
				fileKey,
				isPermanent,
				fileName
			});

			return {
				status: true,
				data: {
					isReady: true,
					url,
					fileName,
					isPermanent
				},
				message: 'Document is ready for download'
			};
		} catch (error) {
			this.logger.error('Error checking payment document status', {
				error,
				referenceAlphaId
			});

			throw error;
		}
	}

	/**
	 * Queue a task for payment document generation and/or sharing
	 * @param referenceAlphaId Reference alpha ID of the payment
	 * @param action Whether to share or download
	 * @param shareMethod Method to use for sharing (if applicable)
	 * @param brandId Brand ID
	 * @param userId User ID
	 * @param fileKey Optional file key to use for the document
	 */
	private async queuePaymentDocumentTask(
		referenceAlphaId: string,
		action: 'share' | 'download',
		shareMethod: 'email' | 'whatsapp' | 'both' | undefined,
		brandId: string,
		userId: string,
		fileKey?: string,
		recipient?: 'client' | 'other',
		email?: string,
		phoneNumber?: string
	): Promise<void> {
		try {
			// Validate fileKey if provided
			if (
				fileKey &&
				fileKey.startsWith('receipt/') &&
				!fileKey.includes(referenceAlphaId)
			) {
				this.logger.warn(
					'Permanent fileKey does not contain referenceAlphaId',
					{
						referenceAlphaId,
						fileKey
					}
				);
			}

			// If no permanent file exists or we're not using it, set isGenerating flag on all records
			if (!fileKey || action === 'share') {
				// Find all payment details with this reference ID
				const payments = await this.paymentDetailsRepository.find({
					where: { referenceAlphaId }
				});

				if (payments && payments.length > 0) {
					// Update all records with the isGenerating flag
					const updatePromises = payments.map(payment => {
						const receiptDetail =
							(payment.receiptDetail as Record<string, string>) ||
							{};
						receiptDetail.isGenerating = 'true';
						payment.receiptDetail = receiptDetail;
						return this.paymentDetailsRepository.save(payment);
					});

					await Promise.all(updatePromises);

					this.logger.log('Set generation flag on payment records', {
						referenceAlphaId,
						updatedCount: payments.length
					});
				}
			}

			// Log detailed info about the request
			this.logger.log('Queueing payment document task', {
				referenceAlphaId,
				action,
				shareMethod,
				recipient,
				email,
				phoneNumber,
				fileKey,
				isPermanentFile: fileKey && fileKey.startsWith('receipt/'),
				hasFileKey: !!fileKey,
				fileKeyLength: fileKey?.length || 0
			});

			await this.sqsService.sendMessage({
				queueKey: 'NidanaInvoiceTasks',
				messageBody: {
					data: {
						taskType: 'processPaymentDocument',
						referenceAlphaId,
						action,
						shareMethod,
						brandId,
						userId,
						fileKey,
						recipient,
						email,
						phoneNumber,
						// Include a flag to clear the generation flag when complete
						clearGeneratingFlag: true
					}
				},
				deduplicationId: `payment-document-${referenceAlphaId}-${Date.now()}`
			});

			this.logger.log('Payment document task queued successfully', {
				referenceAlphaId,
				action,
				fileKey,
				recipient
			});
		} catch (error) {
			this.logger.error('Failed to queue payment document task', {
				error,
				referenceAlphaId,
				action,
				recipient
			});

			throw new HttpException(
				'Failed to process document request',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	/**
	 * Find payment details by reference alpha ID
	 * @param referenceAlphaId The reference alpha ID to search for
	 * @param brandId Optional brand ID to filter by
	 * @returns Array of payment details matching the reference alpha ID
	 */
	async findPaymentsByReferenceAlphaId(
		referenceAlphaId: string,
		brandId?: string
	): Promise<PaymentDetailsEntity[]> {
		const query: any = {
			where: { referenceAlphaId }
		};

		if (brandId) {
			query.where.brandId = brandId;
		}

		return this.paymentDetailsRepository.find(query);
	}

	/**
	 * Create payment details within an existing transaction
	 * This method allows payment creation to be part of a larger atomic operation
	 */
	async createPaymentDetailsWithTransaction(
		paymentDetailsDto: PaymentDetailsDto,
		clinicId: string,
		brandId: string,
		userId: string,
		queryRunner: QueryRunner
	): Promise<PaymentDetailsEntity[]> {
		const logContext = {
			clinicId,
			brandId,
			userId,
			ownerId: paymentDetailsDto.ownerId,
			invoiceId: paymentDetailsDto.invoiceId,
			patientId: paymentDetailsDto.patientId,
			requestId: uuidv4(),
			transactionMode: 'external'
		};

		try {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Start creating payment detail within existing transaction',
				{ paymentDetailsDto }
			);

			// Fetch owner data using the external transaction
			const ownerData = await this.fetchOwnerDataWithTransaction(
				queryRunner,
				paymentDetailsDto.ownerId,
				brandId
			);

			// Validate invoice using the external transaction
			const invoice = paymentDetailsDto.invoiceId
				? await this.validateInvoiceWithTransaction(
						queryRunner,
						paymentDetailsDto.invoiceId,
						paymentDetailsDto
					)
				: null;

			// Additional validation for atomic operations
			if (paymentDetailsDto.invoiceId && !invoice) {
				this.logPaymentEvent(
					this.logger,
					logContext,
					'ERROR: Invoice not found in transaction',
					{ invoiceId: paymentDetailsDto.invoiceId },
					'error'
				);
				throw new Error(
					`Invoice with ID ${paymentDetailsDto.invoiceId} not found in current transaction`
				);
			}

			if (invoice) {
				this.logPaymentEvent(
					this.logger,
					logContext,
					'Invoice validated successfully in transaction',
					{
						invoiceId: invoice.id,
						invoiceStatus: invoice.status,
						invoiceAmount: invoice.invoiceAmount
					}
				);
			}

			// Calculate credits and excess
			const originalCredits = Number(ownerData.ownerCredits || 0);
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Starting balance calculation in transaction',
				{
					originalAmount: paymentDetailsDto.amount,
					originalCredits,
					isCreditUsed: paymentDetailsDto.isCreditUsed,
					creditAmountRequested: paymentDetailsDto.creditAmountUsed,
					invoiceAmount: invoice?.invoiceAmount,
					invoicePaidAmount: invoice?.paidAmount,
					pendingAmount: invoice
						? Number(invoice.invoiceAmount) -
							Number(invoice.paidAmount || 0)
						: 0
				}
			);

			const {
				newCredits,
				amountToApply,
				creditAmountUsed,
				excessAmount
			} = this.calculateNewBalances(
				paymentDetailsDto,
				ownerData,
				invoice
			);

			this.logPaymentEvent(
				this.logger,
				logContext,
				'Balance calculation completed in transaction',
				{
					newCredits,
					amountToApply,
					creditAmountUsed,
					excessAmount,
					creditsChange: newCredits - originalCredits,
					totalPayment:
						amountToApply +
						(paymentDetailsDto.isCreditUsed ? creditAmountUsed : 0)
				}
			);

			const createdPayments: PaymentDetailsEntity[] = [];

			// Generate separate reference IDs for different payment types
			const cashReferenceAlphaId = await generateUniqueCode(
				'referenceAlphaId',
				this.paymentDetailsRepository
			);

			// For credit notes, we may need separate reference IDs
			const isCreditNoteWithExcess =
				paymentDetailsDto.type === EnumAmountType.CreditNote &&
				excessAmount > 0;

			// Create cash payment entity if amount > 0 or it's a special case
			if (
				(Number(paymentDetailsDto.amount) > 0 ||
					// Allow zero-amount payments for invoice types with transaction amount
					(paymentDetailsDto.type === EnumAmountType.Invoice &&
						Number(paymentDetailsDto.transactionAmount) > 0) ||
					// Allow zero-amount payments for Credit Notes
					paymentDetailsDto.type === EnumAmountType.CreditNote) &&
				// We want to create a cash payment in all cases EXCEPT when:
				// 1. It's a credit-to-cash conversion (Return type with isCreditUsed=true)
				// 2. The amount to apply is zero and it's not an Invoice payment or Credit Note
				(amountToApply > 0 ||
					paymentDetailsDto.type === EnumAmountType.Invoice ||
					paymentDetailsDto.type === EnumAmountType.CreditNote ||
					// Special case for credit-to-cash conversion
					(paymentDetailsDto.type === EnumAmountType.Return &&
						paymentDetailsDto.isCreditUsed))
			) {
				const cashPayment =
					await this.createCashPaymentEntityWithTransaction(
						queryRunner,
						paymentDetailsDto,
						paymentDetailsDto.type === EnumAmountType.Return &&
							paymentDetailsDto.isCreditUsed
							? paymentDetailsDto.amount // For credit-to-cash, use full amount
							: amountToApply, // For other cases, use amountToApply
						cashReferenceAlphaId,
						clinicId,
						brandId,
						userId
					);
				createdPayments.push(cashPayment);

				// Create credit transaction for Collect payment
				if (paymentDetailsDto.type === EnumAmountType.Collect) {
					await this.createCreditTransactionWithTransaction(
						queryRunner,
						ownerData.id,
						paymentDetailsDto.amount,
						CreditTransactionType.ADD,
						`Amount ${paymentDetailsDto.amount} added to credits via Collect payment`,
						{
							paymentId: cashPayment.id,
							invoiceId: null
						},
						clinicId,
						brandId,
						userId
					);
				}
			}

			// Create credit payment entity if credits are used
			if (
				paymentDetailsDto.isCreditUsed &&
				creditAmountUsed > 0 &&
				paymentDetailsDto.type !== EnumAmountType.Return // Don't create credit payment for credit-to-cash conversion
			) {
				const creditReferenceAlphaId = await generateUniqueCode(
					'referenceAlphaId',
					this.paymentDetailsRepository
				);

				const creditPayment =
					await this.createCreditPaymentEntityWithTransaction(
						queryRunner,
						paymentDetailsDto,
						creditAmountUsed,
						creditReferenceAlphaId,
						clinicId,
						brandId,
						userId
					);
				createdPayments.push(creditPayment);

				// Create credit transaction for credit usage
				await this.createCreditTransactionWithTransaction(
					queryRunner,
					ownerData.id,
					creditAmountUsed,
					CreditTransactionType.USE,
					`Credits used for ${paymentDetailsDto.invoiceId ? 'invoice ' + paymentDetailsDto.invoiceId : 'payment'}`,
					{
						paymentId: creditPayment.id,
						invoiceId: paymentDetailsDto.invoiceId
					},
					clinicId,
					brandId,
					userId
				);
			}

			// For credit-to-cash conversion, create a credit transaction to track credit usage
			if (
				paymentDetailsDto.type === EnumAmountType.Return &&
				paymentDetailsDto.isCreditUsed &&
				creditAmountUsed > 0
			) {
				await this.createCreditTransactionWithTransaction(
					queryRunner,
					ownerData.id,
					creditAmountUsed,
					CreditTransactionType.USE,
					`Credits converted to cash return`,
					{
						paymentId: createdPayments[0].id, // Reference the cash payment
						invoiceId: null
					},
					clinicId,
					brandId,
					userId
				);
			}

			// Update invoice status if applicable
			if (invoice) {
				await this.updateInvoiceStatusWithTransaction(
					queryRunner,
					invoice,
					paymentDetailsDto,
					amountToApply,
					creditAmountUsed
				);
			}

			// Handle excess amount from credit notes specially
			if (
				excessAmount > 0 &&
				paymentDetailsDto.type !== EnumAmountType.Collect
			) {
				// Generate a new reference ID for excess payment in credit notes
				const excessReferenceAlphaId = isCreditNoteWithExcess
					? await generateUniqueCode(
							'referenceAlphaId',
							this.paymentDetailsRepository
						)
					: cashReferenceAlphaId;

				// Create the excess payment
				const excessPayment =
					await this.handleExcessAmountAsCreditsWithTransaction(
						queryRunner,
						paymentDetailsDto,
						excessAmount,
						excessReferenceAlphaId,
						clinicId,
						brandId,
						userId,
						logContext,
						// Only for credit notes, include invoice ID and patient ID
						isCreditNoteWithExcess
							? {
									includeInvoiceId: true,
									includePatientId: true
								}
							: undefined
					);
				createdPayments.push(excessPayment);
			}

			// Update owner credits
			ownerData.ownerCredits = newCredits;
			await queryRunner.manager.save(ownerData);

			this.logPaymentEvent(
				this.logger,
				logContext,
				'Payment processing completed within external transaction',
				{ paymentIds: createdPayments.map(p => p.id), newCredits }
			);

			return createdPayments;
		} catch (error: any) {
			this.logPaymentEvent(
				this.logger,
				logContext,
				'Error in creating payment within external transaction',
				{ errorMessage: error.message },
				'error'
			);
			throw error;
		}
	}

	// Helper methods for transaction-aware operations
	private async fetchOwnerDataWithTransaction(
		queryRunner: QueryRunner,
		ownerId: string,
		brandId: string
	) {
		// Implementation similar to fetchOwnerData but using queryRunner
		// You'll need to implement this based on your existing fetchOwnerData method
		// For now, delegating to existing method which already accepts queryRunner
		return this.fetchOwnerData(queryRunner, ownerId, brandId);
	}

	private async validateInvoiceWithTransaction(
		queryRunner: QueryRunner,
		invoiceId: string,
		paymentDetailsDto: PaymentDetailsDto
	) {
		// Implementation similar to validateInvoice but using queryRunner
		return this.validateInvoice(queryRunner, invoiceId, paymentDetailsDto);
	}

	private async createCashPaymentEntityWithTransaction(
		queryRunner: QueryRunner,
		paymentDetailsDto: PaymentDetailsDto,
		cashAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string
	): Promise<PaymentDetailsEntity> {
		// Implementation identical to createCashPaymentEntity but using queryRunner
		return this.createCashPaymentEntity(
			queryRunner,
			paymentDetailsDto,
			cashAmount,
			referenceAlphaId,
			clinicId,
			brandId,
			userId
		);
	}

	private async createCreditPaymentEntityWithTransaction(
		queryRunner: QueryRunner,
		paymentDetailsDto: PaymentDetailsDto,
		creditAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string
	): Promise<PaymentDetailsEntity> {
		// Implementation identical to createCreditPaymentEntity but using queryRunner
		return this.createCreditPaymentEntity(
			queryRunner,
			paymentDetailsDto,
			creditAmount,
			referenceAlphaId,
			clinicId,
			brandId,
			userId
		);
	}

	private async createCreditTransactionWithTransaction(
		queryRunner: QueryRunner,
		ownerId: string,
		amount: number,
		transactionType: CreditTransactionType,
		description: string,
		relatedIds: any,
		clinicId: string,
		brandId: string,
		userId: string
	) {
		// Implementation identical to createCreditTransaction but using queryRunner
		return this.createCreditTransaction(
			queryRunner,
			ownerId,
			amount,
			transactionType,
			description,
			relatedIds,
			clinicId,
			brandId,
			userId
		);
	}

	private async updateInvoiceStatusWithTransaction(
		queryRunner: QueryRunner,
		invoice: any,
		paymentDetailsDto: PaymentDetailsDto,
		amountToApply: number,
		creditAmountUsed: number
	): Promise<void> {
		// Calculate total payment amount including cash and credits
		const totalPayment = Number(amountToApply) + Number(creditAmountUsed);
		const newPaidAmount = Number(invoice.paidAmount || 0) + totalPayment;
		const newBalanceDue = Math.max(
			0,
			Number(invoice.invoiceAmount) - newPaidAmount
		);

		// Determine status based on balance
		const newStatus =
			newBalanceDue <= 0.01 // Allow for small rounding differences
				? EnumInvoiceStatus.FULLY_PAID
				: newPaidAmount > 0
					? EnumInvoiceStatus.PARTIALLY_PAID
					: invoice.status;

		await queryRunner.manager.update(
			InvoiceEntity,
			{ id: invoice.id },
			{
				status: newStatus,
				paidAmount: newPaidAmount,
				balanceDue: newBalanceDue,
				updatedAt: new Date()
			}
		);

		// Special handling for credit notes and refunds
		if (
			paymentDetailsDto.type === EnumAmountType.CreditNote &&
			invoice.invoiceType === EnumInvoiceType.Refund
		) {
			await queryRunner.manager.update(
				InvoiceEntity,
				{ id: invoice.id },
				{
					status: EnumInvoiceStatus.FULLY_PAID,
					balanceDue: 0,
					updatedAt: new Date()
				}
			);
		}
	}

	private async handleExcessAmountAsCreditsWithTransaction(
		queryRunner: QueryRunner,
		paymentDetailsDto: PaymentDetailsDto,
		excessAmount: number,
		referenceAlphaId: string,
		clinicId: string,
		brandId: string,
		userId: string,
		logContext: any,
		options?: {
			includeInvoiceId?: boolean;
			includePatientId?: boolean;
		}
	): Promise<PaymentDetailsEntity> {
		// Implementation identical to handleExcessAmountAsCredits but using queryRunner
		// Delegate to existing method
		return this.handleExcessAmountAsCredits(
			queryRunner,
			paymentDetailsDto,
			excessAmount,
			referenceAlphaId,
			clinicId,
			brandId,
			userId,
			logContext,
			options
		);
	}
}
