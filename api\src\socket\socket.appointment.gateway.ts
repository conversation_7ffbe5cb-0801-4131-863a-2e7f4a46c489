import { Logger } from '@nestjs/common';
import { Socket, Server } from 'socket.io';
import {
	WebSocketGateway,
	WebSocketServer,
	OnGatewayConnection,
	OnGatewayDisconnect,
	SubscribeMessage,
	MessageBody,
	ConnectedSocket
} from '@nestjs/websockets';
import { RedisClientType } from 'redis';
import { AppointmentSessionsService } from './appointment-sessions.service';
import { AppointmentSessionChange } from './appointment-session-changes.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisService } from '../utils/redis/redis.service';

interface AccordionPayload {
	appointmentId: string;
	type: string;
	action: string;
	[key: string]: any;
}

interface UserResponse {
	id: number;
	name: string;
}

// New interface to track user sessions across multiple rooms
interface UserSession {
	userId: string;
	userInitials: string;
	socketId: string;
	appointmentId: string;
	joinedAt: Date;
}

// Interface to track room membership
interface RoomMembership {
	userId: string;
	userInitials: string;
	socketIds: Set<string>; // Multiple sockets for same user in same room
	lastActivity: Date;
}

@WebSocketGateway({
	cors: { origin: '*' },
	namespace: 'events/appointments/',
	pingInterval: 5000, // 5 seconds - server sends ping every 5s
	pingTimeout: 10000 // 10 seconds - wait max 10s for client pong response
})
export class AppointmentGateway
	implements OnGatewayConnection, OnGatewayDisconnect
{
	@WebSocketServer() server!: Server;
	private readonly logger = new Logger('AppointmentGateway');
	protected redisSubClient: RedisClientType;
	protected redisPubClient: RedisClientType;

	// Enhanced tracking data structures
	private clientInfoMap: Map<
		string,
		{ appointmentId: string; userInitials: string; userId: string }
	> = new Map();

	// Track all user sessions across all rooms
	private userSessions: Map<string, UserSession[]> = new Map(); // userId -> sessions

	// Track room memberships with support for multiple sockets per user
	private roomMemberships: Map<string, Map<string, RoomMembership>> =
		new Map(); // roomId -> userId -> membership

	// Legacy room users map (keeping for backward compatibility)
	private roomUsers: Map<string, Array<[number, string]>> = new Map();

	constructor(
		private readonly sessionService: AppointmentSessionsService,
		@InjectRepository(AppointmentSessionChange)
		private readonly sessionChangeRepository: Repository<AppointmentSessionChange>,
		private readonly redisService: RedisService
	) {
		try {
			this.logger.log(
				'SocketAppointemnt Initializing Appointment Gateway'
			);

			// Get Redis clients from centralized service
			this.redisSubClient = this.redisService.getSubClient();
			this.redisPubClient = this.redisService.getPubClient();

			this.logger.log(
				'SocketAppointemnt Redis subscriber client connected successfully'
			);
			this.logger.log(
				'SocketAppointemnt Redis publisher client connected successfully'
			);

			this.redisSubClient.on('error', err => {
				this.logger.log(
					'SocketAppointemnt Redis subscriber client error:',
					err
				);
			});

			this.redisSubClient.subscribe('appointment-updates', message => {
				try {
					this.handleRedisMessage('appointment-updates', message);
				} catch (error) {
					this.logger.error('Failed to handle Redis message', {
						message,
						error:
							error instanceof Error
								? error.message
								: String(error)
					});
				}
			});

			// Subscribe to calendar updates
			this.redisSubClient.subscribe('calendar-updates', message => {
				try {
					this.handleRedisMessage('calendar-updates', message);
				} catch (error) {
					this.logger.error('Failed to handle calendar Redis message', {
						message,
						error
					});
				}
			});
		} catch (error) {
			this.logger.log(
				'SocketAppointemnt Failed to initialize Redis clients',
				error
			);
			throw error;
		}
	}

	async onModuleInit(): Promise<void> {
		this.logger.log('SocketAppointemnt AppointmentGateway initialized');
		await this.sessionService.deleteAll();

		// Clear all tracking maps on module init
		this.clientInfoMap.clear();
		this.userSessions.clear();
		this.roomMemberships.clear();
		this.roomUsers.clear();
	}

	async handleConnection(socket: Socket): Promise<void> {
		try {
			this.logger.log(
				`SocketAppointment Connection attempt started: ${socket.id}`
			);
		} catch (error) {
			this.logger.log(
				'SocketAppointment Connection handler failed',
				error
			);
		}
	}

	/**
	 * Add a user session to tracking
	 */
	private addUserSession(userId: string, session: UserSession): void {
		if (!this.userSessions.has(userId)) {
			this.userSessions.set(userId, []);
		}
		this.userSessions.get(userId)!.push(session);
	}

	/**
	 * Remove a user session from tracking
	 */
	private removeUserSession(userId: string, socketId: string): void {
		const sessions = this.userSessions.get(userId);
		if (sessions) {
			const updatedSessions = sessions.filter(
				s => s.socketId !== socketId
			);
			if (updatedSessions.length === 0) {
				this.userSessions.delete(userId);
			} else {
				this.userSessions.set(userId, updatedSessions);
			}
		}
	}

	/**
	 * Add or update room membership
	 */
	private addRoomMembership(
		roomId: string,
		userId: string,
		userInitials: string,
		socketId: string
	): void {
		if (!this.roomMemberships.has(roomId)) {
			this.roomMemberships.set(roomId, new Map());
		}

		const roomMembers = this.roomMemberships.get(roomId)!;

		if (roomMembers.has(userId)) {
			// User already in room, add socket to existing membership
			const membership = roomMembers.get(userId)!;
			membership.socketIds.add(socketId);
			membership.lastActivity = new Date();
			this.logger.log(
				'SocketAppointment User added additional socket to room',
				{
					roomId,
					userId,
					socketId,
					totalSockets: membership.socketIds.size
				}
			);
		} else {
			// New user in room
			roomMembers.set(userId, {
				userId,
				userInitials,
				socketIds: new Set([socketId]),
				lastActivity: new Date()
			});
			this.logger.log('SocketAppointment New user joined room', {
				roomId,
				userId,
				socketId
			});
		}
	}

	/**
	 * Remove socket from room membership
	 */
	private removeRoomMembership(
		roomId: string,
		userId: string,
		socketId: string
	): boolean {
		const roomMembers = this.roomMemberships.get(roomId);
		if (!roomMembers || !roomMembers.has(userId)) {
			return false;
		}

		const membership = roomMembers.get(userId)!;
		membership.socketIds.delete(socketId);

		if (membership.socketIds.size === 0) {
			// No more sockets for this user in this room
			roomMembers.delete(userId);
			this.logger.log('SocketAppointment User completely left room', {
				roomId,
				userId,
				socketId
			});

			// Clean up empty room
			if (roomMembers.size === 0) {
				this.roomMemberships.delete(roomId);
			}
			return true; // User completely left room
		} else {
			this.logger.log(
				'SocketAppointment User removed one socket from room',
				{
					roomId,
					userId,
					socketId,
					remainingSockets: membership.socketIds.size
				}
			);
			return false; // User still has other sockets in room
		}
	}

	/**
	 * Get unique users in a room (for UI display)
	 */
	private getRoomUsers(roomId: string): UserResponse[] {
		const roomMembers = this.roomMemberships.get(roomId);
		if (!roomMembers) {
			return [];
		}

		const users: UserResponse[] = [];
		let id = 1;

		for (const membership of roomMembers.values()) {
			users.push({
				id: id++,
				name: membership.userInitials
			});
		}

		return users;
	}

	/**
	 * Check if user is already in room
	 */
	private isUserInRoom(roomId: string, userId: string): boolean {
		const roomMembers = this.roomMemberships.get(roomId);
		return roomMembers ? roomMembers.has(userId) : false;
	}

	/**
	 * Get all sessions for a user
	 */
	private getUserSessions(userId: string): UserSession[] {
		return this.userSessions.get(userId) || [];
	}

	/**
	 * Get room membership info for a user
	 */
	private getUserRoomMembership(
		roomId: string,
		userId: string
	): RoomMembership | undefined {
		const roomMembers = this.roomMemberships.get(roomId);
		return roomMembers ? roomMembers.get(userId) : undefined;
	}

	/**
	 * Get all active rooms for a user
	 */
	private getUserActiveRooms(userId: string): string[] {
		const sessions = this.getUserSessions(userId);
		return [
			...new Set(sessions.map(s => `appointment:${s.appointmentId}`))
		];
	}

	/**
	 * Get debugging information about current gateway state
	 */
	private getGatewayState(): any {
		const state = {
			totalClients: this.clientInfoMap.size,
			totalUsers: this.userSessions.size,
			totalRooms: this.roomMemberships.size,
			rooms: {} as any,
			users: {} as any
		};

		// Room information
		for (const [roomId, members] of this.roomMemberships.entries()) {
			state.rooms[roomId] = {
				memberCount: members.size,
				members: Array.from(members.values()).map(m => ({
					userId: m.userId,
					userInitials: m.userInitials,
					socketCount: m.socketIds.size,
					lastActivity: m.lastActivity
				}))
			};
		}

		// User information
		for (const [userId, sessions] of this.userSessions.entries()) {
			state.users[userId] = {
				sessionCount: sessions.length,
				sessions: sessions.map(s => ({
					socketId: s.socketId,
					appointmentId: s.appointmentId,
					joinedAt: s.joinedAt
				}))
			};
		}

		return state;
	}

	@SubscribeMessage('joinAppointment')
	async handleJoinAppointment(
		@ConnectedSocket() client: Socket,
		@MessageBody()
		data: { appointmentId: string; userId: string; userInitials: string }
	): Promise<void> {
		try {
			const { appointmentId, userId, userInitials } = data;
			this.logger.log(
				'SocketAppointment joinAppointment triggered',
				data
			);

			if (!appointmentId || !userId) {
				this.logger.log(
					'SocketAppointment Missing required parameters',
					{
						appointmentId,
						userId
					}
				);
				return;
			}

			const roomId = `appointment:${appointmentId}`;
			this.logger.log(`SocketAppointment Room ID generated: ${roomId}`);

			// Check if this socket is already connected to any room
			const existingClientInfo = this.clientInfoMap.get(client.id);
			if (existingClientInfo) {
				this.logger.log(
					'SocketAppointment Socket already connected to appointment',
					{
						socketId: client.id,
						existingAppointment: existingClientInfo.appointmentId,
						newAppointment: appointmentId
					}
				);

				// If trying to join the same room, just acknowledge
				if (existingClientInfo.appointmentId === appointmentId) {
					client.emit('joinedAppointment', appointmentId);
					return;
				}

				// If joining different room, handle the transition
				await this.handleRoomTransition(
					client,
					existingClientInfo,
					appointmentId,
					userId,
					userInitials
				);
				return;
			}

			// Join the socket room
			await client.join(roomId);

			// Store client info
			this.clientInfoMap.set(client.id, {
				appointmentId,
				userInitials,
				userId
			});

			// Add to user sessions tracking
			const session: UserSession = {
				userId,
				userInitials,
				socketId: client.id,
				appointmentId,
				joinedAt: new Date()
			};
			this.addUserSession(userId, session);

			// Add to room membership tracking
			this.addRoomMembership(roomId, userId, userInitials, client.id);

			this.logger.log('SocketAppointment Client joined room', {
				roomId,
				userId,
				socketId: client.id
			});

			// Create session record in database
			await this.sessionService.create(userId, client.id, userInitials);

			// Update legacy room users for backward compatibility
			this.updateLegacyRoomUsers(roomId);

			// Emit the updated user list to all clients in room
			const usersList = this.getRoomUsers(roomId);
			this.server.to(roomId).emit('roomUsers', usersList);

			// Log status
			const roomClients = await this.server.in(roomId).allSockets();
			this.logger.log('SocketAppointment Room status after join', {
				socketId: client.id,
				appointmentId,
				activeClients: roomClients.size,
				uniqueUsers: usersList.length,
				usersList
			});

			// Fetch existing session changes from DB
			const existingChanges = await this.sessionChangeRepository.find({
				where: { roomId }
			});
			this.logger.log('SocketAppointment Found existing changes', {
				changesCount: existingChanges.length
			});

			// Send existing changes to the newly joined client
			this.logger.log(
				`SocketAppointment Emitting sessionState to ${client.id} in room ${roomId}`,
				{
					clientId: client.id,
					roomId,
					sessionChangesCount: existingChanges.length,
					sessionChanges: existingChanges // Log the actual changes
				}
			);
			client.emit('sessionState', existingChanges);

			// Notify the client that they've joined the appointment
			client.emit('joinedAppointment', appointmentId);

			this.logger.log(
				'SocketAppointment joinAppointment handler completed successfully'
			);
		} catch (error) {
			this.logger.log('SocketAppointment joinAppointment failed', error);
		}
	}

	/**
	 * Handle user transitioning from one room to another
	 */
	private async handleRoomTransition(
		client: Socket,
		existingInfo: {
			appointmentId: string;
			userInitials: string;
			userId: string;
		},
		newAppointmentId: string,
		userId: string,
		userInitials: string
	): Promise<void> {
		const oldRoomId = `appointment:${existingInfo.appointmentId}`;
		const newRoomId = `appointment:${newAppointmentId}`;

		this.logger.log('SocketAppointment Handling room transition', {
			socketId: client.id,
			userId,
			oldRoom: oldRoomId,
			newRoom: newRoomId
		});

		// Leave old room
		await client.leave(oldRoomId);

		// Remove from old room membership
		const userLeftOldRoom = this.removeRoomMembership(
			oldRoomId,
			userId,
			client.id
		);

		// Update old room users list if user completely left
		if (userLeftOldRoom) {
			this.updateLegacyRoomUsers(oldRoomId);
			const oldRoomUsers = this.getRoomUsers(oldRoomId);
			this.server.to(oldRoomId).emit('roomUsers', oldRoomUsers);
		}

		// Join new room
		await client.join(newRoomId);

		// Update client info
		this.clientInfoMap.set(client.id, {
			appointmentId: newAppointmentId,
			userInitials,
			userId
		});

		// Update user session
		this.removeUserSession(userId, client.id);
		const newSession: UserSession = {
			userId,
			userInitials,
			socketId: client.id,
			appointmentId: newAppointmentId,
			joinedAt: new Date()
		};
		this.addUserSession(userId, newSession);

		// Add to new room membership
		this.addRoomMembership(newRoomId, userId, userInitials, client.id);

		// Update legacy room users and emit updates
		this.updateLegacyRoomUsers(newRoomId);
		const newRoomUsers = this.getRoomUsers(newRoomId);
		this.server.to(newRoomId).emit('roomUsers', newRoomUsers);

		// Send existing changes for new room
		const existingChanges = await this.sessionChangeRepository.find({
			where: { roomId: newRoomId }
		});
		client.emit('sessionState', existingChanges);
		client.emit('joinedAppointment', newAppointmentId);
	}

	/**
	 * Update legacy room users map for backward compatibility
	 */
	private updateLegacyRoomUsers(roomId: string): void {
		const roomMembers = this.roomMemberships.get(roomId);
		if (!roomMembers) {
			this.roomUsers.delete(roomId);
			return;
		}

		const legacyUsers: Array<[number, string]> = [];
		let id = 1;

		for (const membership of roomMembers.values()) {
			legacyUsers.push([id++, membership.userInitials]);
		}

		this.roomUsers.set(roomId, legacyUsers);
	}

	@SubscribeMessage('heartbeat')
	async handleHeartbeat(@ConnectedSocket() client: Socket): Promise<void> {
		// Just acknowledge the heartbeat and log the client ID
		this.logger.log(`Heartbeat received from client ${client.id}`);
	}

	@SubscribeMessage('leaveAppointment')
	async handleLeaveRoom(
		@ConnectedSocket() client: Socket,
		@MessageBody() appointmentId: string
	): Promise<void> {
		const roomId = `appointment:${appointmentId}`;
		this.logger.log('SocketAppointemnt Client leaving appointment room', {
			socketId: client.id,
			roomId
		});
		// await client.leave(roomId);
	}

	private transformUsers(users: [number, string][]): UserResponse[] {
		return users.map(([id, initials]) => ({
			id,
			name: initials
		}));
	}

	private getNextUserId(roomId: string): number {
		const users = this.roomUsers.get(roomId) || [];
		return users.length + 1;
	}

	private reassignUserIds(users: [number, string][]): [number, string][] {
		return users.map((user, index) => {
			const newId = index + 1;
			return [newId, user[1]];
		});
	}

	async handleDisconnect(client: Socket): Promise<void> {
		try {
			const clientInfo = this.clientInfoMap.get(client.id);
			if (clientInfo) {
				const { appointmentId, userId } = clientInfo;
				const roomId = `appointment:${appointmentId}`;

				this.logger.log('SocketAppointment Client disconnecting', {
					socketId: client.id,
					userId,
					roomId
				});

				// Leave the socket room
				await client.leave(roomId);

				// Clean up client info
				this.clientInfoMap.delete(client.id);

				// Delete session from database
				await this.sessionService.delete(client.id);

				// Remove user session from tracking
				this.removeUserSession(userId, client.id);

				// Remove socket from room membership
				const userCompletelyLeftRoom = this.removeRoomMembership(
					roomId,
					userId,
					client.id
				);

				// Update room users list only if user completely left the room
				if (userCompletelyLeftRoom) {
					this.updateLegacyRoomUsers(roomId);
					const updatedUsers = this.getRoomUsers(roomId);
					this.server.to(roomId).emit('roomUsers', updatedUsers);

					this.logger.log(
						'SocketAppointment User completely left room, updated user list',
						{
							roomId,
							userId,
							remainingUsers: updatedUsers.length
						}
					);
				} else {
					this.logger.log(
						'SocketAppointment User still has other connections in room',
						{
							roomId,
							userId,
							socketId: client.id
						}
					);
				}

				// Check if room is completely empty
				const room = await this.server.in(roomId).allSockets();
				if (room.size === 0) {
					await this.cleanupRoom(roomId);
					this.roomUsers.delete(roomId);
					this.roomMemberships.delete(roomId);

					this.logger.log(
						'SocketAppointment Room completely empty, cleaned up',
						{
							roomId
						}
					);
				}
			} else {
				this.logger.log(
					'SocketAppointment Disconnect for unknown client',
					{
						socketId: client.id
					}
				);
			}
		} catch (error) {
			this.logger.log('SocketAppointemnt Client disconnect failed', {
				socketId: client.id,
				error
			});
		}
	}

	private async cleanupRoom(roomId: string): Promise<void> {
		try {
			this.logger.log('SocketAppointemnt Cleaning up appointment room', {
				roomId
			});
			await this.sessionChangeRepository.delete({ roomId });
		} catch (error) {
			this.logger.log('SocketAppointemnt Room cleanup failed', {
				roomId,
				error
			});
		}
	}

	@SubscribeMessage('ping')
	async handlePing(
		@ConnectedSocket() client: Socket,
		@MessageBody()
		data: { appointmentId: string; key: string; value: string }
	): Promise<void> {
		try {
			const roomId = `appointment:${data.appointmentId}`;

			// Enhanced validation
			// Check if client is in the room they're trying to modify
			const clientRooms = Array.from(client.rooms.values());
			if (!clientRooms.includes(roomId)) {
				this.logger.log(
					'SocketAppointemnt: Attempted ping for room client is not in',
					{
						socketId: client.id,
						requestedRoomId: roomId,
						clientRooms
					}
				);
				return; // Silently fail to prevent data corruption
			}

			// Check if client info matches this appointment
			const clientInfo = this.clientInfoMap.get(client.id);
			if (
				!clientInfo ||
				clientInfo.appointmentId !== data.appointmentId
			) {
				this.logger.log(
					'SocketAppointemnt: Appointment ID mismatch in ping',
					{
						socketId: client.id,
						clientAppointmentId: clientInfo?.appointmentId,
						requestedAppointmentId: data.appointmentId
					}
				);
				return; // Silently fail to prevent data corruption
			}

			// Additional validation: Check if user is actually in the room
			if (!this.isUserInRoom(roomId, clientInfo.userId)) {
				this.logger.log(
					'SocketAppointemnt: User not found in room membership for ping',
					{
						socketId: client.id,
						userId: clientInfo.userId,
						roomId
					}
				);
				return; // Silently fail to prevent data corruption
			}

			this.logger.log('SocketAppointemnt' + 'Processing ping event', {
				socketId: client.id,
				roomId,
				key: data.key,
				value: data.value
			});

			await this.sessionChangeRepository.upsert(
				{
					roomId,
					key: data.key,
					value: data.value,
					lastUpdatedBy: client.id
				},
				['roomId', 'key']
			);

			// Broadcast to others in room
			client.broadcast.to(roomId).emit('ping', {
				key: data.key,
				value: data.value,
				senderId: client.id,
				timestamp: new Date(),
				appointmentId: data.appointmentId
			});
		} catch (error) {
			this.logger.log('SocketAppointemnt' + 'Failed to handle ping', {
				data,
				error
			});
		}
	}

	@SubscribeMessage('handleaccordion')
	async handleAccordionEvent(
		@ConnectedSocket() client: Socket,
		@MessageBody() payload: AccordionPayload
	): Promise<void> {
		try {
			const appointmentId = payload.appointmentId;
			const roomId = `appointment:${appointmentId}`;

			// Enhanced validation
			// Check if client is in the room they're trying to modify
			const clientRooms = Array.from(client.rooms.values());
			if (!clientRooms.includes(roomId)) {
				this.logger.log(
					'SocketAppointemnt: Attempted accordion event for room client is not in',
					{
						socketId: client.id,
						requestedRoomId: roomId,
						clientRooms
					}
				);
				return; // Silently fail to prevent data corruption
			}

			// Check if client info matches this appointment
			const clientInfo = this.clientInfoMap.get(client.id);
			if (!clientInfo || clientInfo.appointmentId !== appointmentId) {
				this.logger.log(
					'SocketAppointemnt: Appointment ID mismatch in accordion event',
					{
						socketId: client.id,
						clientAppointmentId: clientInfo?.appointmentId,
						requestedAppointmentId: appointmentId
					}
				);
				return; // Silently fail to prevent data corruption
			}

			// Additional validation: Check if user is actually in the room
			if (!this.isUserInRoom(roomId, clientInfo.userId)) {
				this.logger.log(
					'SocketAppointemnt: User not found in room membership for accordion event',
					{
						socketId: client.id,
						userId: clientInfo.userId,
						roomId
					}
				);
				return; // Silently fail to prevent data corruption
			}

			// Broadcast to all clients in the room except sender
			client.to(roomId).emit('handleaccordion', payload);

			this.logger.log('SocketAppointemnt Broadcast accordion event', {
				roomId,
				socketId: client.id,
				payload
			});
		} catch (error) {
			this.logger.log(
				'SocketAppointemnt Error handling accordion event',
				{ error }
			);
		}
	}

	@SubscribeMessage('request-changes')
	async handleRequestChanges(
		@ConnectedSocket() client: Socket
	): Promise<void> {
		try {
			// Get client info from our tracking system
			const clientInfo = this.clientInfoMap.get(client.id);
			if (!clientInfo) {
				this.logger.log(
					'SocketAppointemnt Request changes from unknown client',
					{
						socketId: client.id
					}
				);
				client.emit('sync-error', {
					message: 'Client not found in session'
				});
				return;
			}

			const { appointmentId, userId } = clientInfo;
			const roomId = `appointment:${appointmentId}`;

			// Validate user is actually in the room
			if (!this.isUserInRoom(roomId, userId)) {
				this.logger.log(
					'SocketAppointemnt Request changes from user not in room',
					{
						socketId: client.id,
						userId,
						roomId
					}
				);
				client.emit('sync-error', { message: 'User not in room' });
				return;
			}

			this.logger.log('SocketAppointemnt Processing change request', {
				socketId: client.id,
				userId,
				roomId
			});

			const currentChanges = await this.sessionChangeRepository.find({
				where: { roomId }
			});

			this.logger.log(
				`SocketAppointment Emitting sessionState to ${client.id} for room ${roomId} on request`,
				{
					clientId: client.id,
					roomId,
					sessionChangesCount: currentChanges.length,
					sessionChanges: currentChanges // Log the actual changes
				}
			);
			client.emit('sessionState', currentChanges);
		} catch (error) {
			this.logger.log('SocketAppointemnt Failed to fetch changes', {
				socketId: client.id,
				error
			});
			client.emit('sync-error', { message: 'Failed to fetch changes' });
		}
	}

	async emitToSocket(
		socketId: string,
		event: string,
		payload: any
	): Promise<void> {
		return new Promise((resolve, reject) => {
			this.server.emit(event, payload, (response: any) => {
				if (response?.error) {
					reject(new Error(response.error));
				} else {
					resolve();
				}
			});
		});
	}

	private handleConnectionError(socket: Socket, error: Error): void {
		this.logger.log(
			`SocketAppointemnt Connection error for socket ${socket.id}: ${error.message}`
		);
		socket.emit('exception', 'Authentication error');
		socket.disconnect();
	}

	private async handleRedisMessage(
		channel: string,
		message: string
	): Promise<void> {
		try {
			if (channel === 'appointment-updates') {
				const parsedMessage = JSON.parse(message);
				const { event, appointmentId, data } = parsedMessage;

				if (!event || !appointmentId) {
					this.logger.warn('Invalid Redis message format', {
						message,
						parsedMessage
					});
					return;
				}

				const roomId = `appointment:${appointmentId}`;
				this.logger.log('Broadcasting Redis message to room', {
					roomId,
					event,
					dataKeys: data ? Object.keys(data) : []
				});

				this.server.to(roomId).emit(event, data);
				this.logger.log('DEBUG-EMIT: Redis message broadcast', {
					event,
					roomId,
					data,
					triggeredBy: 'Redis',
					source: 'handleRedisMessage'
				});
			}
			else if (channel === 'calendar-updates') {
				const parsed = JSON.parse(message);
				const { userId } = parsed;
				this.server.emit('calendar-updated', { userId });
				this.logger.log('Broadcast calendar-updated', { userId });
			}
		} catch (error) {
			this.logger.error('Failed to parse Redis message', {
				channel,
				message,
				error: error instanceof Error ? error.message : String(error)
			});
		}
	}
	// (Optional) method to broadcast an update via Redis
	async publishAppointmentUpdate(
		appointmentId: string,
		data: any
	): Promise<void> {
		try {
			const roomId = `appointment:${appointmentId}`;

			this.logger.log('Publishing appointment update', {
				appointmentId,
				roomId,
				dataKey: data.key,
				hasValue: !!data.value
			});

			// Update session changes table for persistence (so new users get latest state)
			if (data.key && data.value !== undefined) {
				await this.sessionChangeRepository.upsert(
					{
						roomId,
						key: data.key,
						value: data.value,
						lastUpdatedBy: 'system' // Mark as system update
					},
					['roomId', 'key']
				);

				this.logger.log(
					'Updated session changes for appointment update',
					{
						appointmentId,
						roomId,
						key: data.key,
						valueType: data.value
					}
				);
			}

			// Broadcast via Redis for real-time updates
			await this.redisPubClient.publish(
				'appointment-updates',
				JSON.stringify({
					event: 'ping',
					appointmentId,
					data
				})
			);

			this.logger.log(
				'Successfully published appointment update via Redis',
				{
					appointmentId
				}
			);
		} catch (error) {
			this.logger.error('Failed to publish appointment update', {
				appointmentId,
				error: error instanceof Error ? error.message : String(error),
				stack: error instanceof Error ? error.stack : undefined
			});
			// Don't throw to avoid breaking the main operation
		}
	}
}
