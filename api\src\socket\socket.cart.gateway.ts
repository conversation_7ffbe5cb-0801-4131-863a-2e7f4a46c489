import { Logger } from '@nestjs/common';
import { Socket, Server } from 'socket.io';
import {
	WebSocketGateway,
	WebSocketServer,
	OnGatewayConnection,
	OnGatewayDisconnect,
	SubscribeMessage,
	MessageBody,
	ConnectedSocket
} from '@nestjs/websockets';
import { RedisClientType } from 'redis';
import { RedisService } from '../utils/redis/redis.service';

interface CartState {
	isAdjustmentInInvoice: boolean;
	discountObject: {
		label: string;
		value: string;
	};
	invoiceAmount: number;
}

interface CartChangePayload {
	appointmentId: string;
	action: 'add' | 'delete' | 'update' | 'checkout' | 'updateState';
	value: any;
	state?: Partial<CartState>;
}

@WebSocketGateway({
	cors: { origin: '*' },
	namespace: 'events/cart/',
	pingInterval: 25000,
	pingTimeout: 60000
})
export class CartGateway implements OnGatewayConnection, OnGatewayDisconnect {
	@WebSocketServer() server!: Server;
	private readonly logger = new Logger('CartGateway');
	protected redisSubClient: RedisClientType;
	protected redisPubClient: RedisClientType;
	private clientInfoMap: Map<string, { appointmentId: string }> = new Map();
	private cartStates: Map<string, CartState> = new Map();

	constructor(private readonly redisService: RedisService) {
		try {
			this.logger.log('Initializing Cart Gateway');

			// Get Redis clients from centralized service
			this.redisSubClient = this.redisService.getSubClient();
			this.redisPubClient = this.redisService.getPubClient();

			this.redisSubClient.subscribe('cart-updates', message => {
				this.handleRedisMessage('cart-updates', message);
			});
		} catch (error) {
			this.logger.error('Failed to initialize Redis clients', error);
			throw error;
		}
	}

	async handleConnection(client: Socket): Promise<void> {
		try {
			this.logger.log(`Client connected to cart socket: ${client.id}`);
		} catch (error) {
			this.logger.error('Cart connection handler failed', error);
		}
	}

	async handleDisconnect(client: Socket): Promise<void> {
		try {
			const cartInfo = this.clientInfoMap.get(client.id);
			if (cartInfo) {
				const roomId = `cart:${cartInfo.appointmentId}`;
				client.leave(roomId);
				this.clientInfoMap.delete(client.id);

				this.logger.log(`Client disconnected from cart: ${client.id}`);
			}
		} catch (error) {
			this.logger.error('Error handling cart disconnect', error);
		}
	}

	@SubscribeMessage('joinCart')
	async handleJoinCart(
		@ConnectedSocket() client: Socket,
		@MessageBody() { appointmentId }: { appointmentId: string }
	): Promise<void> {
		try {
			const roomId = `cart:${appointmentId}`;
			client.join(roomId);
			this.clientInfoMap.set(client.id, { appointmentId });

			// Send current cart state to the joining client
			const currentState = this.cartStates.get(appointmentId) || {
				isAdjustmentInInvoice: false,
				discountObject: { label: '0%', value: '0' },
				invoiceAmount: 0
			};

			client.emit('cartStateSync', currentState);

			this.logger.log(`Client ${client.id} joined cart ${appointmentId}`);

			client.to(roomId).emit('userJoined', {
				clientId: client.id,
				timestamp: new Date().toISOString()
			});
		} catch (error) {
			this.logger.error('Error joining cart', error);
			throw new Error('Failed to join cart');
		}
	}

	@SubscribeMessage('cartChange')
	async handleCartChange(
		@ConnectedSocket() client: Socket,
		@MessageBody() payload: CartChangePayload
	): Promise<void> {
		try {
			const { appointmentId, action, value, state } = payload;
			const roomId = `cart:${appointmentId}`;

			this.logger.log(
				`Received cartChange event from client ${client.id}`,
				{
					action,
					appointmentId,
					value,
					state
				}
			);

			if (action === 'updateState' && state) {
				// Update the stored cart state
				const currentState = this.cartStates.get(appointmentId) || {
					isAdjustmentInInvoice: false,
					discountObject: { label: '0%', value: '0' },
					invoiceAmount: 0
				};

				this.cartStates.set(appointmentId, {
					...currentState,
					...state
				});

				// Broadcast state update to all clients in the room except sender
				client.to(roomId).emit('cartStateUpdated', state);
			} else if (action === 'checkout') {
				client.to(roomId).emit('cartCheckedOut', {
					appointmentId,
					timestamp: new Date().toISOString()
				});
			} else {
				// Regular cart updates
				client.to(roomId).emit('cartUpdated', {
					action,
					appointmentId,
					value,
					timestamp: new Date().toISOString()
				});
			}

			// Publish to Redis for other server instances
			await this.publishCartUpdate(appointmentId, {
				action,
				appointmentId,
				value,
				state,
				timestamp: new Date().toISOString()
			});
		} catch (error) {
			this.logger.error('Error handling cart change', error);
			throw new Error('Failed to process cart change');
		}
	}

	private async handleRedisMessage(
		channel: string,
		message: string
	): Promise<void> {
		if (channel === 'cart-updates') {
			const { event, appointmentId, data } = JSON.parse(message);
			const roomId = `cart:${appointmentId}`;
			this.server.to(roomId).emit(event, data);

			this.logger.log(`Handling Redis message on channel ${channel}`, {
				event,
				appointmentId,
				data
			});
		}
	}

	private async publishCartUpdate(
		appointmentId: string,
		data: any
	): Promise<void> {
		this.logger.log(
			`Publishing cart update to Redis for appointmentId ${appointmentId}`,
			{
				data
			}
		);

		await this.redisPubClient.publish(
			'cart-updates',
			JSON.stringify({
				event: 'cartUpdated',
				appointmentId,
				data
			})
		);
	}
}
