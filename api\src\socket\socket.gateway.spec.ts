import { Test, TestingModule } from '@nestjs/testing';
import { ChatGateway } from './socket.gateway';
import { ChatUserSessionsService } from './chat-user-sessions.service';
import { Socket, Server } from 'socket.io';
import { RedisClientType } from 'redis';
import { CreateChatMessageDto } from '../chat-room/dto/create-chat-message.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';

describe.skip('socket gateway', () => {
	let chatGateway: ChatGateway;
	let chatUserSessionsService: ChatUserSessionsService;
	let logger: jest.Mocked<WinstonLogger>;
	let socket: Socket;
	let server: Partial<Server>;
	let mockRedisClient: Partial<RedisClientType>;
	beforeEach(async () => {
		mockRedisClient = {
			publish: jest.fn()
			// Mock other methods as necessary
		};
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ChatGateway,
				{
					provide: ChatUserSessionsService,
					useValue: {
						create: jest.fn(),
						delete: jest.fn(),
						get: jest.fn(),
						deleteAll: jest.fn(),
						emitToSocket: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		chatGateway = module.get<ChatGateway>(ChatGateway);
		logger = module.get(WinstonLogger);
		chatUserSessionsService = module.get<ChatUserSessionsService>(
			ChatUserSessionsService
		);
		socket = {
			id: 'test-socket-id',
			join: jest.fn(),
			emit: jest.fn(),
			data: {},
			disconnect: jest.fn()
		} as unknown as Socket;

		server = {
			to: jest.fn().mockReturnThis(),
			in: jest.fn().mockReturnThis(),
			emit: jest.fn().mockReturnThis(),
			sockets: {
				sockets: new Map<string, Socket>(),
				eject: jest.fn()
			}
		} as unknown as Partial<Server>;

		chatGateway.server = server as Server;
	});

	describe('handleConnection', () => {
		it('should initialize user connection successfully', async () => {
			await chatGateway.handleConnection(socket);
			expect(socket.data.user).toEqual({
				id: '219c062a-f3aa-4aff-9e16-ff54f6ff14c5',
				name: 'Virat'
			});
		});

		it('should handle errors and call handleConnectionError', async () => {
			const errorMessage = 'Connection error';
			const error = new Error(errorMessage);

			jest.spyOn(
				chatGateway as any,
				'initializeUserConnection'
			).mockImplementation(() => {
				throw error;
			});

			const handleConnectionErrorSpy = jest.spyOn(
				chatGateway,
				'handleConnectionError'
			);

			await chatGateway.handleConnection(socket);

			expect(handleConnectionErrorSpy).toHaveBeenCalledWith(
				socket,
				error
			);
		});
	});

	describe('handleJoinRoom', () => {
		it('should allow a user to join a room', async () => {
			const joinData = { chatRoomId: 'room-1', userId: 'user-1' };
			await chatGateway.handleJoinRoom(socket, joinData);
			expect(socket.join).toHaveBeenCalledWith(joinData.chatRoomId);
			expect(socket.emit).toHaveBeenCalledWith(
				'joinedRoom',
				joinData.chatRoomId
			);
			expect(chatUserSessionsService.create).toHaveBeenCalledWith(
				joinData.userId,
				socket.id
			);
		});
	});

	describe('onSendMessage', () => {
		it('should send a message and emit to the socket', async () => {
			const messageData: CreateChatMessageDto = {
				senderId: 'user-1',
				chatRoomId: 'room-1',
				otherUserId: 'user-2',
				message: 'hey'
			};

			(chatUserSessionsService.get as jest.Mock).mockResolvedValue({
				socketId: 'other-user-socket-id'
			});
			chatGateway.emitToSocket = jest.fn().mockResolvedValue(undefined);
			(mockRedisClient.publish as jest.Mock).mockResolvedValue(1);

			await chatGateway.onSendMessage(messageData, socket);

			expect(chatUserSessionsService.get).toHaveBeenCalledWith(
				messageData.otherUserId
			);

			expect(chatGateway.emitToSocket).toHaveBeenCalledWith(
				'other-user-socket-id',
				'messageSent',
				messageData
			);
		});

		it('should handle errors while sending messages', async () => {
			const messageData: CreateChatMessageDto = {
				senderId: 'user-1',
				chatRoomId: 'room-1',
				otherUserId: 'user-2',
				message: 'hey'
			};

			chatUserSessionsService.get = jest
				.fn()
				.mockRejectedValue(new Error('User not found'));

			await expect(
				chatGateway.onSendMessage(messageData, socket)
			).rejects.toThrow('Error occurred while sending the message.');
		});
	});

	describe('handleDisconnect', () => {
		it('should delete the user session on disconnect', async () => {
			await chatGateway.handleDisconnect(socket);
			expect(chatUserSessionsService.delete).toHaveBeenCalledWith(
				socket.id
			);
		});
	});

	describe('handleConnectionError', () => {
		it('should log the error and emit an exception to the socket', () => {
			const error = new Error('Test connection error');

			chatGateway.handleConnectionError(socket, error);
			expect(socket.emit).toHaveBeenCalledWith(
				'exception',
				'Authentication error'
			);
		});
	});
	describe('emitToSocket', () => {
		it('should resolve when the event is emitted successfully', async () => {
			const socketId = 'test-socket-id';
			const event = 'testEvent';
			const payload = { message: 'Hello' };

			(chatGateway.server.emit as jest.Mock).mockImplementation(
				(
					eventName: string,
					data: any,
					callback: (response: any) => void
				) => {
					callback({});
				}
			);

			await expect(
				chatGateway.emitToSocket(socketId, event, payload)
			).resolves.toBeUndefined();
			expect(chatGateway.server.emit).toHaveBeenCalledWith(
				event,
				payload,
				expect.any(Function)
			);
		});

		it('should reject with an error when the event emits an error response', async () => {
			const socketId = 'test-socket-id';
			const event = 'testEvent';
			const payload = { message: 'Hello' };

			(chatGateway.server.emit as jest.Mock).mockImplementation(
				(
					eventName: string,
					data: any,
					callback: (response: any) => void
				) => {
					callback({ error: 'Test error' });
				}
			);

			await expect(
				chatGateway.emitToSocket(socketId, event, payload)
			).rejects.toThrow('Test error');
			expect(chatGateway.server.emit).toHaveBeenCalledWith(
				event,
				payload,
				expect.any(Function)
			);
		});
	});
});
