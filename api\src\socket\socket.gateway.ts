//TODO :
// 1. See if we can move the join room logic to handleConnection
// 2. Add auth to sockets
// 3. Add redis functionality
import { Logger } from '@nestjs/common';
import { Socket, Server } from 'socket.io';
import {
	WebSocketGateway,
	WebSocketServer,
	OnGatewayConnection,
	OnGatewayDisconnect,
	SubscribeMessage,
	MessageBody,
	WsException,
	ConnectedSocket
} from '@nestjs/websockets';

import { RedisClientType } from 'redis';
import { CreateChatMessageDto } from '../chat-room/dto/create-chat-message.dto';
import { ChatUserSessionsService } from './chat-user-sessions.service';
import { RedisService } from '../utils/redis/redis.service';

// @UseFilters(WsExceptionFilter)
@WebSocketGateway({
	cors: { origin: '*' },
	namespace: '/events/'
	// path: '/socket.io'
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
	@WebSocketServer() server!: Server;
	private readonly logger = new Logger('ChatGateway');
	protected redisSubClient: RedisClientType;
	protected redisPubClient: RedisClientType;
	private currentUser: any;
	constructor(
		private readonly connectedUserService: ChatUserSessionsService,
		private readonly redisService: RedisService
	) {
		try {
			console.log('Constructor called');

			// Get Redis clients from centralized service
			this.redisSubClient = this.redisService.getSubClient();
			this.redisPubClient = this.redisService.getPubClient();

			this.redisSubClient.on(
				'message',
				this.handleRedisMessage.bind(this)
			);
			this.redisSubClient.subscribe('chat-message', message => {
				this.handleRedisMessage('chat-message', message);
			});
			this.currentUser = {
				id: '219c062a-f3aa-4aff-9e16-ff54f6ff14c5',
				name: 'Virat'
			};
		} catch (error) {
			this.logger.error('Error initializing Redis clients', error);
			throw error;
		}
	}

	async onModuleInit(): Promise<void> {
		console.log('ChatGateway initialized');
		this.logger.log('ChatGateway initialized');
		await this.connectedUserService.deleteAll();
	}

	async handleConnection(socket: Socket): Promise<void> {
		try {
			console.log('Handle Connection');
			// const user = this.authenticateSocket(socket);
			await this.initializeUserConnection(
				{ name: this.currentUser.name, id: this.currentUser.id },
				socket
			);
		} catch (error) {
			console.log('error in handle connection');
			this.handleConnectionError(socket, error as Error);
		}
	}

	async handleDisconnect(socket: Socket): Promise<void> {
		await this.connectedUserService.delete(socket.id);
		await this.redisPubClient.hDel(
			`chatRoom:${socket.data.roomId}`,
			socket.id
		);

		this.logger.log(`Client disconnected: ${socket.id}`);
	}

	@SubscribeMessage('joinRoom')
	async handleJoinRoom(
		@ConnectedSocket() client: Socket,
		@MessageBody()
		{ chatRoomId, userId }: { userId: string; chatRoomId: string }
	): Promise<void> {
		console.log('Chat Room Joining Details', { chatRoomId, userId });
		console.log({ client });
		await this.connectedUserService.create(userId, client.id);

		client.join(chatRoomId);
		client.emit('joinedRoom', chatRoomId);
		this.logger.log(`Client ${client.id} joined room ${chatRoomId}`);
	}

	@SubscribeMessage('sendMessage')
	async onSendMessage(
		// @WsCurrentUser() currentUser: UserPayload,
		@MessageBody() createMessageDto: CreateChatMessageDto,
		@ConnectedSocket() client: Socket
	): Promise<void> {
		const userId = createMessageDto.senderId;
		const { chatRoomId, otherUserId } = createMessageDto;
		try {
			// Log client ID for debugging
			this.logger.log(`Client ${client.id} sending message`);

			//Should we add the logic to add the message to  db here?
			this.logger.log(
				`User ID ${userId} sent a new message in Room ID ${chatRoomId}`
			);

			console.log({ chatRoomId, otherUserId });

			const otherUserDetails =
				await this.connectedUserService.get(otherUserId);
			console.log({ otherUserDetails });

			//Publishing event to redis
			await this.redisPubClient.publish(
				'chat-message',
				JSON.stringify({
					event: 'messageSent',
					roomId: otherUserDetails.socketId,
					message: createMessageDto
				})
			);
			//emit message to socket connection
			await this.emitToSocket(
				otherUserDetails.socketId,
				'messageSent',
				createMessageDto
			);

			await this.emitToSocket(
				otherUserDetails.socketId,
				'chat-room-notification',
				{
					some: 'Send something'
				}
			);
		} catch (error: any) {
			console.log('🚀 ~ ChatGateway ~ error:', error);
			this.logger.error(
				`Failed to send message in Room ID ${chatRoomId} by User ID ${userId}: ${error.message}`,
				error.stack
			);
			throw new WsException('Error occurred while sending the message.');
		}
	}

	async emitToSocket(
		socketId: string,
		event: string,
		payload: any
	): Promise<void> {
		return new Promise((resolve, reject) => {
			this.server.emit(event, payload, (response: any) => {
				if (response && response.error) {
					reject(new Error(response.error));
				} else {
					resolve();
				}
			});
		});
	}

	private async initializeUserConnection(
		userPayload: { id: string; name: string },
		socket: Socket
	): Promise<void> {
		socket.data.user = userPayload;
		// console.log("socket-->",);

		// await this.connectedUserService.create(userPayload.id, socket.id);

		// const rooms = await this.roomService.findByUserId(userPayload.id);

		// this.server.to(socket.id).emit('userAllRooms', rooms);
		this.logger.log(
			`Client connected: ${socket.id} - User ID: ${userPayload.id}`
		);
	}

	handleConnectionError(socket: Socket, error: Error): void {
		this.logger.error(
			`Connection error for socket ${socket.id}: ${error.message}`
		);
		socket.emit('exception', 'Authentication error');
		socket.disconnect();
	}

	async handleRedisMessage(channel: string, message: string): Promise<void> {
		if (channel === 'chat-message') {
			const { event, roomId, message: newMessage } = JSON.parse(message);

			this.server.to(roomId).emit(event, newMessage);
		}
	}
}
