import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TabActivitiesController } from './tab-activity.controller';
import { TabActivitiesService } from './tab-activity.service';
import { TabActivityEntity } from './entities/tab-activity.entity';
import { RoleModule } from '../roles/role.module';


@Module({
    imports: [
        TypeOrmModule.forFeature([TabActivityEntity]),
        RoleModule,
    ],
    controllers: [TabActivitiesController],
    providers: [TabActivitiesService],
    exports: [TabActivitiesService]
})
export class TabActivityModule {}