import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TabName, ActionType } from './enums/tab-activity.enums';
import { TabActivityEntity } from './entities/tab-activity.entity';
import { CreateTabActivityDto } from './dto/create-tab-actvity.dto';

@Injectable()
export class TabActivitiesService {
    constructor(
        @InjectRepository(TabActivityEntity)
        private readonly tabActivityRepository: Repository<TabActivityEntity>,
    ) {}

    async create(createTabActivityDto: CreateTabActivityDto, userId: string) {
        const activity = this.tabActivityRepository.create({
            ...createTabActivityDto,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.tabActivityRepository.save(activity);
    }

    async getLastActivity(tabName: TabName, referenceId: string) {
        try {
            const activities = await this.tabActivityRepository
                .createQueryBuilder('activity')
                .where('activity.tabName = :tabName', { tabName })
                .andWhere('activity.referenceId = :referenceId', { referenceId })
                .andWhere('activity.actionType IN (:...actionTypes)', { 
                    actionTypes: [ActionType.SHARE, ActionType.DOWNLOAD] 
                })
                .orderBy('activity.createdAt', 'DESC')
                .leftJoinAndSelect('activity.createdByUser', 'user')
                .getMany();
            
            return {
                status: true,
                data: {
                    share: activities.find(activity => activity.actionType === ActionType.SHARE),
                    download: activities.find(activity => activity.actionType === ActionType.DOWNLOAD)
                },
                message: 'Last activities retrieved successfully'
            };
        } catch (error: any) {
            console.error('Error getting last activities:', error);
            return {
                status: false,
                message: 'Failed to retrieve last activities',
                error: error.message || 'Unknown error occurred'
            };
        }
    }

    async getLastActivitiesByReferenceIds(referenceIds: string[]) {
        const activities = await this.tabActivityRepository
            .createQueryBuilder('activity')
            .where('activity.referenceId IN (:...referenceIds)', { referenceIds })
            .orderBy('activity.createdAt', 'DESC')
            .leftJoinAndSelect('activity.createdByUser', 'user')
            .getMany();

        return activities.reduce((acc, activity) => {
            const key = `${activity.tabName}_${activity.referenceId}`;
            if (!acc[key] || acc[key].createdAt < activity.createdAt) {
                acc[key] = activity;
            }
            return acc;
        }, {} as Record<string, TabActivityEntity>);
    }

    async getActivitiesByReferenceId(referenceId: string) {
        return await this.tabActivityRepository
            .createQueryBuilder('activity')
            .where('activity.referenceId = :referenceId', { referenceId })
            .orderBy('activity.createdAt', 'DESC')
            .leftJoinAndSelect('activity.createdByUser', 'user')
            .getMany();
    }

    async deleteByReferenceId(referenceId: string) {
        return await this.tabActivityRepository.delete({ referenceId });
    }

    async updateByReferenceId(referenceId: string, updateData: Partial<TabActivityEntity>, userId: string) {
        return await this.tabActivityRepository.update(
            { referenceId },
            { ...updateData, updatedBy: userId }
        );
    }
}