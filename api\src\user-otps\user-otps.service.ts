import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserOtp } from './entities/user-otp.entity';
import { User } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';
import { CreateUserOtpDto } from './dto/create-user-otp.dto';
import { GenerateOtpDto } from './dto/generate-otp-dto';
import { RoleService } from '../roles/role.service';
import { Role } from '../roles/role.enum';
import { ValidateOtpDto } from './dto/validate-user-otp.dto';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { JwtService } from '@nestjs/jwt';
import { DEV_SES_EMAIL } from '../utils/constants';
import { isProduction } from '../utils/common/get-login-url';

@Injectable()
export class UserOtpsService {
	constructor(
		@InjectRepository(UserOtp)
		private userOtpsRepository: Repository<UserOtp>,
		private usersServices: UsersService,
		private roleService: RoleService,
		private jwtService: JwtService,
		private readonly mailService: SESMailService
	) {}

	private generateOtp(): string {
		return Math.floor(100000 + Math.random() * 900000).toString();
	}

	async createOtp(
		generateOtpDto: GenerateOtpDto
	): Promise<{ statusCode: number; message: string }> {
		const { email } = generateOtpDto;

		const user = await this.usersServices.findOneByEmail(email);
		if (!user) {
			throw new HttpException('User not found', HttpStatus.NOT_FOUND);
		}

		const role = await this.roleService.findById(user.roleId);
		if (role.name != Role.SUPER_ADMIN) {
			throw new HttpException(
				'Only SuperAdmins can login using Email',
				HttpStatus.UNAUTHORIZED
			);
		}

		const otpCode = this.generateOtp();
		const expiresAt = new Date();
		expiresAt.setMinutes(expiresAt.getMinutes() + 5);

		const createUserOtpDto: CreateUserOtpDto = {
			userId: user.id,
			otp: otpCode,
			otpExpiresAt: expiresAt
		};

		const userOtp = this.userOtpsRepository.create(createUserOtpDto);
		await this.userOtpsRepository.save(userOtp);
		console.log(`Generated OTP for ${email}: ${otpCode}`);
		if (isProduction() && user?.email) {
			this.mailService.sendMail({
				body: `Dear ${user.firstName} ${user.lastName}, 
				Your OTP for logging into the SuperAdmin platform is: ${otpCode}
				
				Please note that this OTP will expire in 5 minutes.
		
				If you did not request this OTP, please contact support immediately.`,
				subject: 'Your SuperAdmin Login OTP',
				toMailAddress: user.email
			});
		} else if (!isProduction()) {
			this.mailService.sendMail({
				body: `Dear ${user.firstName} ${user.lastName}, 
				Your OTP for logging into the SuperAdmin platform is: ${otpCode}
				
				Please note that this OTP will expire in 5 minutes.
		
				If you did not request this OTP, please contact support immediately.`,
				subject: 'Your SuperAdmin Login OTP',
				toMailAddress: DEV_SES_EMAIL //user.email
			});
		}

		return { statusCode: 201, message: 'OTP has been sent to your email.' };
	}
	async validateOtp(
		validateOtpDto: ValidateOtpDto
	): Promise<{ token: string; roleName: string }> {
		const { email, otp } = validateOtpDto;

		const user = await this.usersServices.findOneByEmail(email);
		if (!user) {
			throw new HttpException('User not found', HttpStatus.NOT_FOUND);
		}

		const role = await this.roleService.findById(user.roleId);
		if (role.name !== Role.SUPER_ADMIN) {
			throw new HttpException(
				'Only SuperAdmins can login using Email',
				HttpStatus.UNAUTHORIZED
			);
		}

		const userOtp = await this.userOtpsRepository.findOne({
			where: {
				userId: user.id,
				otp
			},
			order: { otpExpiresAt: 'DESC' }
		});

		if (!userOtp) {
			throw new HttpException('Invalid OTP', HttpStatus.UNAUTHORIZED);
		}

		const currentTime = new Date();
		if (currentTime > userOtp.otpExpiresAt) {
			await this.userOtpsRepository.delete(userOtp.id);
			throw new HttpException('OTP has expired', HttpStatus.UNAUTHORIZED);
		}

		const roleName = role.name;
		const payload = {
			sub: user.id,
			email: user.email,
			role: role.name
		};
		const token = this.jwtService.sign(payload);
		return { token, roleName };
	}
}
