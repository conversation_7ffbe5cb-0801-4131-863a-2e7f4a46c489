import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	IsNotEmpty,
	IsBoolean,
	MaxLength,
	<PERSON>O<PERSON>
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserDto {
	@ApiProperty({ description: 'The first name of the user' })
	@IsString()
	@IsNotEmpty()
	firstName!: string;

	@ApiProperty({ description: 'The last name of the user' })
	@IsString()
	@IsNotEmpty()
	lastName!: string;

	@ApiProperty({ description: 'The email of the user' })
	@IsEmail()
	email!: string;

	@ApiProperty({ description: 'The role ID of the user' })
	@IsUUID()
	roleId!: string;
}
export class UpdateUserDto {
	@ApiPropertyOptional({ description: 'The updated first name of the user' })
	@IsString()
	@IsOptional()
	firstName?: string;

	@ApiPropertyOptional({ description: 'The updated last name of the user' })
	@IsString()
	@IsOptional()
	lastName?: string;

	@ApiPropertyOptional({ description: 'The updated email of the user' })
	@IsEmail()
	@IsOptional()
	email?: string;

	@ApiPropertyOptional({
		description: 'The updated phone number of the user'
	})
	@IsString()
	@IsOptional()
	phoneNumber?: string;

	@ApiPropertyOptional({
		description: 'The mobile number of the user'
	})
	@IsString()
	@IsOptional()
	mobileNumber?: string;

	@ApiPropertyOptional({
		description: 'The country code of the user'
	})
	@IsString()
	@IsOptional()
	countryCode?: string;

	@ApiPropertyOptional({
		description: 'The alternate mobile number of the user'
	})
	@IsString()
	@IsOptional()
	alternateMobileNumber?: string;

	@ApiPropertyOptional({
		description: 'The alternate country code of the user'
	})
	@IsString()
	@IsOptional()
	alternateCountryCode?: string;

	@ApiPropertyOptional({
		description: 'The updated PIN for the user',
		minLength: 6
	})
	@IsString()
	@MinLength(6)
	@IsOptional()
	pin?: string;

	@ApiPropertyOptional({ description: 'The updated role ID of the user' })
	@IsOptional()
	roleId?: string;

	@ApiPropertyOptional({ description: 'The updated clinic ID of the user' })
	@IsUUID()
	@IsOptional()
	clinicId?: string;

	@ApiPropertyOptional({ description: 'Whether the user is active' })
	@IsBoolean()
	@IsOptional()
	isActive?: boolean;
}

export class UpdateAdminDto {
	@ApiPropertyOptional({ description: 'The updated email of the admin' })
	@IsOptional()
	@IsEmail()
	email?: string;

	@ApiPropertyOptional({
		description: 'The updated password of the admin',
		minLength: 8
	})
	@IsOptional()
	@IsString()
	@MinLength(8)
	password?: string;
}

export class UpdateProfileDto {
	@ApiProperty({ description: 'Mobile number of the user' })
	@IsString()
	@MaxLength(15)
	@IsOptional()
	mobileNumber?: string;

	@ApiProperty({ description: 'Country code for the mobile number' })
	@IsString()
	@IsOptional()
	countryCode?: string;

	@ApiProperty({ description: 'Alternate mobile number of the user' })
	@IsString()
	@MaxLength(15)
	@IsOptional()
	alternateMobileNumber?: string;

	@ApiProperty({
		description: 'Country code for the alternate mobile number'
	})
	@IsString()
	@IsOptional()
	alternateCountryCode?: string;

	@ApiProperty({ description: 'License number of the user' })
	@IsString()
	@MaxLength(25)
	licenseNumber?: string;

	@ApiProperty({ description: 'Digital signature of the user' })
	@IsString()
	digitalSignature?: string;
}

export class ResetPinDto {
	@ApiProperty({ description: 'Email of the user' })
	@IsEmail()
	email!: string;
}

class TimeSlot {
	@ApiProperty()
	@IsString()
	startTime?: string;

	@ApiProperty()
	@IsString()
	endTime?: string;
}

class DaySchedule {
	@ApiProperty({ example: '09:00', required: false })
	@IsString()
	@IsOptional()
	startTime!: string | null;

	@ApiProperty({ example: '17:00', required: false })
	@IsString()
	@IsOptional()
	endTime!: string | null;

	@ApiProperty({ example: true })
	@IsBoolean()
	isWorkingDay!: boolean;
}
export class UpdateWorkingHoursDto {
	@IsObject()
	@IsOptional()
	workingHours?: {
		[key: string]: DaySchedule;
	};
}
