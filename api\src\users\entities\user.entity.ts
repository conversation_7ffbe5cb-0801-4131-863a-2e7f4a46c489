import {
	<PERSON><PERSON><PERSON>,
	PrimaryGeneratedC<PERSON>umn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	Join<PERSON><PERSON>umn,
	OneToMany
} from 'typeorm';
import { Role } from '../../roles/entities/role.entity';
import { AppointmentDoctorsEntity } from '../../appointments/entities/appointment-doctor.entity';
import { Task } from '../../tasks/entities/tasks.entity';
import { ChatRoomUser } from '../../chat-room/chat-room-users.entity';
import { ChatUserSessions } from '../../socket/chat-user-sessions.entity';
import { ClinicUser } from '../../clinics/entities/clinic-user.entity';
import { Brand } from '../../brands/entities/brand.entity';
import { GoogleSyncStatus } from '../enums/google-sync-status.enum';

@Entity('users')
export class User {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ length: 50, nullable: true, name: 'first_name' })
	firstName!: string;

	@Column({ length: 50, nullable: true, name: 'last_name' })
	lastName!: string;

	@Column({ unique: true })
	email!: string;

	@Column({ nullable: true, length: 60 })
	pin!: string;

	@Column('uuid', { name: 'role_id' })
	roleId!: string;

	@ManyToOne(() => Role)
	@JoinColumn({ name: 'role_id' })
	role!: Role;

	@Column({ default: true, name: 'is_active' })
	isActive!: boolean;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@Column({ nullable: true, name: 'mobile_number' })
	mobileNumber!: string;

	@Column('varchar', { name: 'country_code', length: 5, nullable: true })
	countryCode!: string;

	@Column('jsonb', { nullable: true })
	working_hours!: any;

	@Column({ nullable: true, name: 'digital_signature' })
	digitalSignature!: string;

	@Column({ default: false })
	registered!: boolean;

	@OneToMany(() => Task, task => task.id)
	tasks!: Task[];

	@OneToMany(() => ClinicUser, clinicUser => clinicUser.user)
	clinicUsers!: ClinicUser[];

	@Column('uuid', { nullable: true, name: 'brand_id' })
	brandId!: string;

	@ManyToOne(() => Brand)
	@JoinColumn({ name: 'brand_id' })
	brand!: Brand;

	@Column({ name: 'license_number' })
	licenseNumber?: string;

	@Column({ nullable: true, name: 'alternate_mobile_number' })
	alternateMobileNumber!: string;

	@Column('varchar', { name: 'alternate_country_code', length: 5, nullable: true })
	alternateCountryCode!: string;

	// Google Calendar Integration Fields
	@Column({ nullable: true, name: 'google_calendar_refresh_token' })
	googleCalendarRefreshToken?: string;

	@Column({ nullable: true, name: 'google_calendar_id' })
	googleCalendarId?: string;

	@Column({ nullable: true, name: 'google_sync_token' })
	googleSyncToken?: string;

	@Column({ nullable: true, name: 'google_webhook_id' })
	googleWebhookId?: string;

	@Column({ nullable: true, name: 'google_webhook_resource_id' })
	googleWebhookResourceId?: string;

	@Column({ nullable: true, name: 'google_webhook_token' })
	googleWebhookToken?: string;

	@Column({ default: false, name: 'is_google_sync_enabled' })
	isGoogleSyncEnabled!: boolean;

	@Column({
		type: 'enum',
		enum: GoogleSyncStatus,
		nullable: true,
		name: 'google_sync_status'
	})
	googleSyncStatus?: GoogleSyncStatus;

	@Column({ nullable: true, name: 'google_sync_error_message' })
	googleSyncErrorMessage?: string;

	@Column({ type: 'timestamptz', nullable: true, name: 'last_google_sync_at' })
	lastGoogleSyncAt?: Date;

	@Column({ type: 'timestamptz', nullable: true, name: 'google_webhook_expires_at' })
	googleWebhookExpiresAt?: Date;
}
