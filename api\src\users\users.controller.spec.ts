import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateUserDto, UpdateUserDto, UpdateWorkingHoursDto } from './dto/user.dto';
import { Role } from '../roles/role.enum';
import { HttpException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { RoleService } from '../roles/role.service';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: UsersService;

  const mockUsersService = {
    createUser: jest.fn(),
    findUsersAcrossClinics: jest.fn(),
    findClinicUsers: jest.fn(),
    updateUserStatus: jest.fn(),
    updateClinicUser: jest.fn(),
    addUserToClinic: jest.fn(),
    findOne: jest.fn(),
    getClinicUserData: jest.fn(),
    remove: jest.fn(),
    updateStaffProfile: jest.fn(),
    updateWorkingHours: jest.fn(),
    getUserClinics: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        { provide: UsersService, useValue: mockUsersService },
        {
			provide: RoleService,
			useValue: {
			  findByName: jest.fn(),
			  findById: jest.fn()
			}
		},
        { provide: WinstonLogger, useValue: mockLogger },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a new user successfully', async () => {
      const createUserDto: CreateUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        roleId: 'some-role-id',
      };
      const clinicId = 'clinic-id';
      const brandId = 'brand-id';
      const req = { user: { userId: 'user-id', role: Role.ADMIN, email: '<EMAIL>' } };

      const expectedUser = { id: 'new-user-id', ...createUserDto };
      mockUsersService.createUser.mockResolvedValue(expectedUser);

      const result = await controller.createUser(createUserDto, req as any, clinicId, brandId);

      expect(result).toEqual(expectedUser);
      expect(mockUsersService.createUser).toHaveBeenCalledWith(createUserDto, clinicId, brandId, req.user);
      expect(mockLogger.log).toHaveBeenCalledWith('Creating new user', { dto: createUserDto, clinicId });
      expect(mockLogger.log).toHaveBeenCalledWith('User created or associated successfully', { userId: expectedUser.id, clinicId });
    });

    it('should handle errors when creating a user', async () => {
      const createUserDto: CreateUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        roleId: 'some-role-id',
      };
      const clinicId = 'clinic-id';
      const brandId = 'brand-id';
      const req = { user: { userId: 'user-id', role: Role.ADMIN, email: '<EMAIL>' } };

      mockUsersService.createUser.mockRejectedValue(new Error('Database error'));

      await expect(controller.createUser(createUserDto, req as any, clinicId, brandId)).rejects.toThrow(HttpException);
      expect(mockLogger.error).toHaveBeenCalledWith('Error creating user', { error: expect.any(Error), clinicId });
    });
  });

  describe('searchUsersAcrossClinics', () => {
    it('should search users across clinics', async () => {
      const brandId = 'brand-id';
      const searchTerm = 'John';
      const excludeClinicId = 'exclude-clinic-id';
      const expectedResult = { users: [{ id: 'user-1', name: 'John Doe' }], total: 1 };

      mockUsersService.findUsersAcrossClinics.mockResolvedValue(expectedResult);

      const result = await controller.searchUsersAcrossClinics(brandId, searchTerm, excludeClinicId);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.findUsersAcrossClinics).toHaveBeenCalledWith(brandId, excludeClinicId, searchTerm);
    });
  });

  describe('getClinicUsers', () => {
    it('should get clinic users with pagination and filtering', async () => {
      const clinicId = 'clinic-id';
      const role = 'doctor';
      const search = 'John';
      const page = 1;
      const limit = 10;
      const orderBy = 'name';
      const expectedResult = { users: [{ id: 'user-1', name: 'John Doe' }], total: 1 };

      mockUsersService.findClinicUsers.mockResolvedValue(expectedResult);

      const result = await controller.getClinicUsers(clinicId, role, search, page, limit, orderBy);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.findClinicUsers).toHaveBeenCalledWith(clinicId, page, limit, role, search, orderBy);
    });
  });

  describe('updateUserStatus', () => {
    it('should update user status', async () => {
      const userId = 'user-id';
      const isActive = true;
      const expectedResult = { id: userId, isActive };

      mockUsersService.updateUserStatus.mockResolvedValue(expectedResult);

      const result = await controller.updateUserStatus(userId, isActive);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.updateUserStatus).toHaveBeenCalledWith(userId, isActive);
    });
  });

  describe('updateClinicUser', () => {
    it('should update clinic user', async () => {
      const userId = 'user-id';
      const updateUserDto: UpdateUserDto = { firstName: 'Updated', lastName: 'User' };
      const req = { user: { userId: 'admin-id' } };
      const expectedResult = { id: userId, ...updateUserDto };

      mockUsersService.updateClinicUser.mockResolvedValue(expectedResult);

      const result = await controller.updateClinicUser(userId, updateUserDto, req as any);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.updateClinicUser).toHaveBeenCalledWith(userId, updateUserDto, 'admin-id');
    });
  });

  describe('addUserToClinic', () => {
    it('should add user to clinic', async () => {
      const userId = 'user-id';
      const clinicId = 'clinic-id';
      const brandId = 'brand-id';
      const isPrimary = true;
      const expectedResult = { message: 'User added to clinic successfully' };

      mockUsersService.addUserToClinic.mockResolvedValue(expectedResult);

      const result = await controller.addUserToClinic(userId, clinicId, brandId, isPrimary);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.addUserToClinic).toHaveBeenCalledWith(userId, clinicId, brandId, isPrimary);
    });
  });

  describe('findOne', () => {
    it('should find a user by id', async () => {
      const userId = 'user-id';
      const expectedUser = { id: userId, name: 'John Doe' };

      mockUsersService.findOne.mockResolvedValue(expectedUser);

      const result = await controller.findOne(userId);

      expect(result).toEqual(expectedUser);
      expect(mockUsersService.findOne).toHaveBeenCalledWith(userId);
      expect(mockLogger.log).toHaveBeenCalledWith('Fetching user by ID', { userId });
      expect(mockLogger.log).toHaveBeenCalledWith('User fetched successfully', { userId });
    });

    it('should handle errors when finding a user', async () => {
      const userId = 'non-existent-id';

      mockUsersService.findOne.mockRejectedValue(new Error('User not found'));

      await expect(controller.findOne(userId)).rejects.toThrow(HttpException);
      expect(mockLogger.error).toHaveBeenCalledWith('Error fetching user', { error: expect.any(Error), userId });
    });
  });

  describe('getClinicUserData', () => {
    it('should get clinic user data', async () => {
      const userId = 'user-id';
      const expectedData = { id: userId, name: 'John Doe', workingHours: {} };

      mockUsersService.getClinicUserData.mockResolvedValue(expectedData);

      const result = await controller.getClinicUserData(userId);

      expect(result).toEqual(expectedData);
      expect(mockUsersService.getClinicUserData).toHaveBeenCalledWith(userId);
    });

    it('should throw NotFoundException when clinic user is not found', async () => {
      const userId = 'non-existent-id';

      mockUsersService.getClinicUserData.mockResolvedValue(null);

      await expect(controller.getClinicUserData(userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove a user', async () => {
      const userId = 'user-id';

      mockUsersService.remove.mockResolvedValue({ affected: 1 });

      const result = await controller.remove(userId);

      expect(result).toEqual({ message: 'User removed successfully' });
      expect(mockUsersService.remove).toHaveBeenCalledWith(userId);
      expect(mockLogger.log).toHaveBeenCalledWith('Removing user', { userId });
      expect(mockLogger.log).toHaveBeenCalledWith('User removed successfully', { userId });
    });

    it('should handle errors when removing a user', async () => {
      const userId = 'non-existent-id';

      mockUsersService.remove.mockRejectedValue(new Error('User not found'));

      await expect(controller.remove(userId)).rejects.toThrow(HttpException);
      expect(mockLogger.error).toHaveBeenCalledWith('Error removing user', { error: expect.any(Error), userId });
    });
  });

  describe('completeProfile', () => {
    it('should complete user profile', async () => {
      const userId = 'user-id';
      const updateProfileDto = { mobileNumber: '**********', licenseNumber: 'ABC123', digitalSignature: 'signature' };
      const expectedResult = { id: userId, ...updateProfileDto };

      mockUsersService.updateStaffProfile.mockResolvedValue(expectedResult);

      const result = await controller.completeProfile(userId, updateProfileDto);

      expect(result).toEqual({ message: 'Profile completed successfully', user: expectedResult });
      expect(mockUsersService.updateStaffProfile).toHaveBeenCalledWith(userId, updateProfileDto);
    });

    it('should handle unauthorized errors', async () => {
      const userId = 'user-id';
      const updateProfileDto = { mobileNumber: '**********' };

      mockUsersService.updateStaffProfile.mockRejectedValue(new UnauthorizedException('Not able to update staff user'));

      await expect(controller.completeProfile(userId, updateProfileDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('updateWorkingHours', () => {
    it('should update working hours', async () => {
      const userId = 'user-id';
      const updateWorkingHoursDto: UpdateWorkingHoursDto = {
        workingHours: {
          monday: { startTime: '09:00', endTime: '17:00', isWorkingDay: true },
          tuesday: { startTime: '09:00', endTime: '17:00', isWorkingDay: true },
        }
      };
      const expectedResult = { id: userId, workingHours: updateWorkingHoursDto.workingHours };

      mockUsersService.updateWorkingHours.mockResolvedValue(expectedResult);

      const result = await controller.updateWorkingHours(userId, updateWorkingHoursDto);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.updateWorkingHours).toHaveBeenCalledWith(userId, updateWorkingHoursDto);
    });
  });

  describe('getUserClinics', () => {
    it('should get user clinics', async () => {
      const userId = 'user-id';
      const expectedClinics = [{ id: 'clinic-1', name: 'Clinic A' }, { id: 'clinic-2', name: 'Clinic B' }];

      mockUsersService.getUserClinics.mockResolvedValue(expectedClinics);

      const result = await controller.getUserClinics(userId);

      expect(result).toEqual(expectedClinics);
      expect(mockUsersService.getUserClinics).toHaveBeenCalledWith(userId);
    });
  });
});