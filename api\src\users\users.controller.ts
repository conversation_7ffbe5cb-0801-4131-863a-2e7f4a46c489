import {
	Controller,
	Get,
	Post,
	Body,
	Patch,
	Param,
	Delete,
	UseGuards,
	HttpException,
	HttpStatus,
	Req,
	ConflictException,
	BadRequestException,
	Query,
	UsePipes,
	ValidationPipe,
	UnauthorizedException,
	InternalServerErrorException,
	NotFoundException,
	Put
} from '@nestjs/common';
import { UsersService } from './users.service';
import {
	CreateUserDto,
	UpdateUserDto,
	UpdateAdminDto,
	UpdateProfileDto,
	ResetPinDto,
	UpdateWorkingHoursDto
} from './dto/user.dto';
import { CalendarWorkingHoursResponseDto } from './dto/calendar-working-hours.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth,
	ApiParam,
	ApiBody,
	ApiQuery
} from '@nestjs/swagger';
import { Request } from 'express';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { CreateExceptionDto, UpdateExceptionDto } from './dto/exception.dto';
import { FindClinicUsersAvailabilityResponse } from './dto/availability-response.dto';
import { RoleService } from '../roles/role.service';

interface RequestWithUser extends Request {
	user: {
		userId: string;
		role: Role;
		email: string;
	};
}
@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
	constructor(
		private readonly usersService: UsersService,
		private readonly logger: WinstonLogger,
		private readonly roleService: RoleService
	) {}

	@Post()
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@ApiOperation({ summary: 'Create a new user or clinic user' })
	@ApiBody({ type: CreateUserDto })
	@ApiResponse({
		status: 201,
		description: 'The user has been successfully created or associated.',
		type: CreateUserDto
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@ApiQuery({ name: 'clinicId', required: false, type: String })
	@TrackMethod('createUser-users')
	async createUser(
		@Body() createUserDto: CreateUserDto,
		@Req() req: RequestWithUser,
		@Query('clinicId') clinicId: string,
		@Query('brandId') brandId: string
	) {
		try {
			this.logger.log('Creating new user', {
				dto: createUserDto,
				clinicId
			});
			const user = await this.usersService.createUser(
				createUserDto,
				clinicId,
				brandId,
				req.user
			);
			this.logger.log('User created or associated successfully', {
				userId: user.id,
				clinicId
			});
			return user;
		} catch (error) {
			this.logger.error('Error creating user', { error, clinicId });
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new HttpException(
				'Failed to create user',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('search')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Search users across clinics under the same brand'
	})
	@ApiQuery({ name: 'brandId', required: true, type: String })
	@ApiQuery({ name: 'searchTerm', required: true, type: String })
	@ApiQuery({ name: 'excludeClinicId', required: false, type: String })
	@TrackMethod('searchUsersAcrossClinics-users')
	async searchUsersAcrossClinics(
		@Query('brandId') brandId: string,
		@Query('searchTerm') searchTerm: string,
		@Query('excludeClinicId') excludeClinicId: string
	) {
		return this.usersService.findUsersAcrossClinics(
			brandId,
			excludeClinicId,
			searchTerm
		);
	}

	@Get('clinic/availability/:clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Get all users availability for a clinic by role and time slot'
	})
	@ApiParam({ name: 'clinicId', type: 'string' })
	@ApiQuery({ name: 'role', type: 'string', required: false })
	@ApiQuery({ name: 'search', type: 'string', required: false })
	@ApiQuery({ name: 'orderBy', type: 'string', required: false })
	@ApiQuery({
		name: 'date',
		type: 'string',
		required: false,
		description: 'Date in YYYY-MM-DD format'
	})
	@ApiQuery({
		name: 'startTime',
		type: 'string',
		required: false,
		description: 'Start time in HH:mm format'
	})
	@ApiQuery({
		name: 'endTime',
		type: 'string',
		required: false,
		description: 'End time in HH:mm format'
	})
	@ApiResponse({
		status: 200,
		description:
			'Return all users availability for the clinic by role, time slot and search criteria.',
		type: [CreateUserDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('getClinicUsersAvailability-users')
	async getClinicUsersAvailability(
		@Param('clinicId') clinicId: string,
		@Query('role') role?: string,
		@Query('search') search?: string,
		@Query('orderBy') orderBy?: string,
		@Query('date') date?: string,
		@Query('startTime') startTime?: string,
		@Query('endTime') endTime?: string
	): Promise<FindClinicUsersAvailabilityResponse> {
		return this.usersService.findClinicUsersAvailability(
			clinicId,
			role,
			search,
			orderBy,
			date,
			startTime,
			endTime
		);
	}

	@Get('clinic/doctors/:clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Get all admin and doctor users for a clinic'
	})
	@ApiParam({ name: 'clinicId', type: 'string' })
	@ApiResponse({
		status: 200,
		description: 'Return all admin and doctor users for the clinic.',
		type: [CreateUserDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('getClinicDoctors-users')
	async getClinicDoctors(@Param('clinicId') clinicId: string) {
		return this.usersService.getClinicDoctors(clinicId);
	}

	@Get('clinic/:clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Get all users for a clinic by role and search with pagination'
	})
	@ApiParam({ name: 'clinicId', type: 'string' })
	@ApiQuery({ name: 'role', type: 'string', required: false })
	@ApiQuery({ name: 'search', type: 'string', required: false })
	@ApiQuery({ name: 'page', type: 'number', required: false })
	@ApiQuery({ name: 'limit', type: 'number', required: false })
	@ApiQuery({ name: 'orderBy', type: 'string', required: false })
	@ApiResponse({
		status: 200,
		description:
			'Return all users for the clinic by role and search criteria with pagination.',
		type: [CreateUserDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('getClinicUsers-users')
	async getClinicUsers(
		@Param('clinicId') clinicId: string,
		@Query('role') role?: string,
		@Query('search') search?: string,
		@Query('page') page?: number,
		@Query('limit') limit?: number,
		@Query('orderBy') orderBy?: string
	) {
		return this.usersService.findClinicUsers(
			clinicId,
			page,
			limit,
			role,
			search,
			orderBy
		);
	}

	@Put(':id/status')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('updateUserStatus-users')
	async updateUserStatus(
		@Param('id') id: string,
		@Body('isActive') isActive: boolean
		// @Req() req: RequestWithUser
	) {
		return this.usersService.updateUserStatus(id, isActive);
	}

	@Put('clinic/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update a clinic user' })
	@TrackMethod('updateClinicUser-users')
	async updateClinicUser(
		@Param('id') id: string,
		@Body() updateUserDto: UpdateUserDto,
		@Req() req: RequestWithUser
	) {
		return this.usersService.updateClinicUser(
			id,
			updateUserDto,
			req.user.userId
		);
	}

	@Post(':userId/add-to-clinic/:clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('addUserToClinic-users')
	async addUserToClinic(
		@Param('userId') userId: string,
		@Param('clinicId') clinicId: string,
		@Param('brandId') brandId: string,
		@Body('isPrimary') isPrimary: boolean = false
	) {
		return this.usersService.addUserToClinic(
			userId,
			clinicId,
			brandId,
			isPrimary
		);
	}

	@Get('clinic-user/:id')
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@ApiOperation({ summary: 'Get clinic user data including working hours' })
	@ApiParam({
		name: 'userId',
		required: true,
		description: 'Clinic user ID (from clinic_users table)'
	})
	@ApiResponse({
		status: 200,
		description: 'Clinic user data retrieved successfully'
	})
	@ApiResponse({ status: 404, description: 'Clinic user not found' })
	@TrackMethod('getClinicUserData-users')
	async getClinicUserData(@Param('id') id: string): Promise<any> {
		const clinicUserData = await this.usersService.getClinicUserData(id);
		if (!clinicUserData) {
			throw new NotFoundException('Clinic user not found');
		}
		return clinicUserData;
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete a user' })
	@ApiParam({ name: 'id', type: 'string' })
	@ApiResponse({
		status: 200,
		description: 'The user has been successfully deleted.'
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@ApiResponse({ status: 404, description: 'User not found.' })
	@TrackMethod('remove-users')
	async remove(@Param('id') id: string) {
		try {
			this.logger.log('Removing user', { userId: id });
			await this.usersService.remove(id);
			this.logger.log('User removed successfully', { userId: id });
			return { message: 'User removed successfully' };
		} catch (error) {
			this.logger.error('Error removing user', { error, userId: id });
			throw new HttpException(
				'Failed to remove user',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('calendar-working-hours')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({
		summary:
			'Get all doctors working hours for a specific date for the calendar view'
	})
	@ApiQuery({
		name: 'date',
		required: true,
		type: String,
		description: 'Date in YYYY-MM-DD format'
	})
	@ApiQuery({
		name: 'clinicId',
		required: true,
		type: String
	})
	@ApiResponse({
		status: 200,
		description: 'Returns all doctors working hours for the specified date',
		type: CalendarWorkingHoursResponseDto
	})
	@ApiResponse({ status: 400, description: 'Bad request.' })
	@TrackMethod('getCalendarWorkingHours-users')
	async getCalendarWorkingHours(
		@Query('date') date: string,
		@Query('clinicId') clinicId: string
	) {
		try {
			this.logger.log('Fetching calendar working hours', {
				date,
				clinicId
			});

			const workingHours =
				await this.usersService.getCalendarWorkingHours(date, clinicId);

			this.logger.log('Calendar working hours fetched successfully', {
				date,
				clinicId,
				doctorsCount: workingHours.doctors.length
			});

			return workingHours;
		} catch (error) {
			this.logger.error('Error fetching calendar working hours', {
				error,
				date,
				clinicId
			});

			if (error instanceof BadRequestException) {
				throw error;
			}

			throw new HttpException(
				'Failed to fetch calendar working hours',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get(':id')
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@ApiOperation({ summary: 'Get user by ID' })
	@ApiParam({ name: 'id', type: 'string' })
	@ApiResponse({
		status: 200,
		description: 'Return the user with the specified ID.'
	})
	@ApiResponse({ status: 404, description: 'User not found.' })
	@TrackMethod('findOne-users')
	async findOne(@Param('id') id: string) {
		try {
			this.logger.log('Fetching user by ID', { userId: id });
			const user = await this.usersService.findOne(id);
			
			// Include role information in the response
			const userWithRole = user?.roleId ? {
				...user,
				role: await this.roleService.findOneById(user.roleId)
			} : user;
			
			this.logger.log('User fetched successfully', { userId: id });
			return userWithRole;
		} catch (error) {
			this.logger.error('Error fetching user', { error, userId: id });
			throw new HttpException(
				'Failed to fetch user',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Put('complete-profile/:userId')
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Complete user profile using userId' })
	@ApiResponse({
		status: 200,
		description: 'Profile completed successfully.'
	})
	@ApiResponse({ status: 401, description: 'Invalid PIN.' })
	@TrackMethod('completeProfile-users')
	async completeProfile(
		@Param('userId') userId: string,
		@Body() updateProfileDto: UpdateProfileDto
	) {
		console.log('updateProfileDto-1');
		try {
			const updatedUser = await this.usersService.updateStaffProfile(
				userId,
				updateProfileDto
			);
			return {
				message: 'Profile completed successfully',
				user: updatedUser
			};
		} catch (error) {
			console.log(error);
			if (error instanceof UnauthorizedException) {
				throw new UnauthorizedException(
					'Not able to update staff user'
				);
			}
			throw error;
		}
	}

	@Put('working-hours/:userId')
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Add working hours for a staff member' })
	@ApiResponse({
		status: 200,
		description: 'Profile completed successfully.'
	})
	@ApiResponse({ status: 401, description: 'Invalid PIN.' })
	@TrackMethod('updateWorkingHours-users')
	async updateWorkingHours(
		@Param('userId') userId: string,
		@Body() updateWorkingHoursDto: UpdateWorkingHoursDto
	) {
		console.log(updateWorkingHoursDto);
		return this.usersService.updateWorkingHours(
			userId,
			updateWorkingHoursDto
		);
	}

	@Get('clinics/:userId')
	@Roles(
		Role.SUPER_ADMIN,
		Role.ADMIN,
		Role.DOCTOR,
		Role.LAB_TECHNICIAN,
		Role.RECEPTIONIST
	)
	@ApiOperation({ summary: 'Get all clinics for a user' })
	@ApiParam({ name: 'userId', type: 'string' })
	@ApiResponse({
		status: 200,
		description: 'Returns all clinics associated with the user'
	})
	@TrackMethod('getUserClinics-users')
	async getUserClinics(@Param('userId') userId: string) {
		return this.usersService.getUserClinics(userId);
	}

	@Post('exceptions')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create a new availability exception' })
	@ApiBody({ type: CreateExceptionDto })
	@ApiResponse({
		status: 201,
		description: 'The exception has been successfully created.'
	})
	@ApiResponse({ status: 400, description: 'Bad request.' })
	@ApiResponse({
		status: 409,
		description: 'Conflict with existing exception.'
	})
	@TrackMethod('createException-users')
	async createException(
		@Body() createExceptionDto: CreateExceptionDto,
		@Req() req: RequestWithUser
	) {
		try {
			this.logger.log('Creating availability exception', {
				dto: createExceptionDto
			});

			const exception = await this.usersService.createException(
				createExceptionDto,
				req.user.userId
			);

			this.logger.log('Availability exception created successfully', {
				exceptionId: exception.id
			});

			return exception;
		} catch (error) {
			this.logger.error('Error creating availability exception', {
				error
			});

			if (
				error instanceof BadRequestException ||
				error instanceof ConflictException ||
				error instanceof NotFoundException
			) {
				throw error;
			}

			throw new HttpException(
				'Failed to create availability exception',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('exceptions/:clinicUserId')
	@ApiResponse({
		status: HttpStatus.OK,
		description:
			'Successfully retrieved all availability exceptions for the clinic user'
	})
	@ApiQuery({
		name: 'includeHistory',
		required: false,
		type: Boolean,
		description: 'Whether to include past exceptions'
	})
	@TrackMethod('getExceptions-users')
	async getExceptions(
		@Param('clinicUserId') clinicUserId: string,
		@Query('includeHistory') includeHistory?: string | boolean
	) {
		this.logger.log(
			`Fetching availability exceptions for clinic user: ${clinicUserId}`
		);
		try {
			// Convert string query params to boolean
			const includeHistoryBool =
				includeHistory === 'true' || includeHistory === true;

			return await this.usersService.getExceptions(
				clinicUserId,
				includeHistoryBool
			);
		} catch (error) {
			this.logger.error(
				`Failed to get availability exceptions for clinic user: ${clinicUserId}`,
				error
			);
			throw new HttpException(
				error instanceof Error
					? error.message
					: 'Failed to get availability exceptions',
				error instanceof HttpException
					? error.getStatus()
					: HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Get('exceptions/detail/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get an availability exception by ID' })
	@ApiParam({ name: 'id', description: 'The exception ID' })
	@ApiResponse({
		status: 200,
		description: 'Returns the requested availability exception.'
	})
	@ApiResponse({ status: 404, description: 'Exception not found.' })
	@TrackMethod('getExceptionById-users')
	async getExceptionById(@Param('id') id: string) {
		try {
			this.logger.log('Fetching availability exception by ID', { id });

			const exception = await this.usersService.getExceptionById(id);

			this.logger.log('Availability exception fetched successfully', {
				id
			});

			return exception;
		} catch (error) {
			this.logger.error('Error fetching availability exception', {
				error,
				id
			});

			if (error instanceof NotFoundException) {
				throw error;
			}

			throw new HttpException(
				'Failed to fetch availability exception',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Put('exceptions/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update an availability exception' })
	@ApiParam({ name: 'id', description: 'The exception ID' })
	@ApiBody({ type: UpdateExceptionDto })
	@ApiResponse({
		status: 200,
		description: 'The exception has been successfully updated.'
	})
	@ApiResponse({ status: 400, description: 'Bad request.' })
	@ApiResponse({ status: 404, description: 'Exception not found.' })
	@ApiResponse({
		status: 409,
		description: 'Conflict with existing exception.'
	})
	@TrackMethod('updateException-users')
	async updateException(
		@Param('id') id: string,
		@Body() updateExceptionDto: UpdateExceptionDto,
		@Req() req: RequestWithUser
	) {
		try {
			this.logger.log('Updating availability exception', {
				id,
				dto: updateExceptionDto
			});

			const exception = await this.usersService.updateException(
				id,
				updateExceptionDto,
				req.user.userId
			);

			this.logger.log('Availability exception updated successfully', {
				id
			});

			return exception;
		} catch (error) {
			this.logger.error('Error updating availability exception', {
				error,
				id,
				dto: updateExceptionDto
			});

			if (
				error instanceof BadRequestException ||
				error instanceof ConflictException ||
				error instanceof NotFoundException
			) {
				throw error;
			}

			throw new HttpException(
				'Failed to update availability exception',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Delete('exceptions/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete an availability exception' })
	@ApiParam({ name: 'id', description: 'The exception ID' })
	@ApiResponse({
		status: 200,
		description: 'The exception has been successfully deleted.'
	})
	@ApiResponse({ status: 404, description: 'Exception not found.' })
	@ApiResponse({
		status: 409,
		description:
			'Conflict: Cannot delete an exception that has already ended or has appointments.'
	})
	@TrackMethod('deleteException-users')
	async deleteException(@Param('id') id: string) {
		try {
			this.logger.log('Deleting availability exception', { id });

			await this.usersService.deleteException(id);

			this.logger.log('Availability exception deleted successfully', {
				id
			});

			return { message: 'Exception deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting availability exception', {
				error,
				id
			});

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}

			throw new HttpException(
				error instanceof Error
					? error.message
					: 'Failed to delete availability exception',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
