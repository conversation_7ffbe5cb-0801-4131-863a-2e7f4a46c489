import {
	Injectable,
	NotFoundException,
	ConflictException,
	BadRequestException,
	InternalServerErrorException,
	UnauthorizedException,
	forwardRef,
	Inject
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, DataSource, Like, Repository } from 'typeorm';
import { User } from './entities/user.entity';
import {
	CreateUserDto,
	UpdateUserDto,
	UpdateProfileDto,
	UpdateWorkingHoursDto
} from './dto/user.dto';
import { Role } from '../roles/role.enum';
import * as bcrypt from 'bcrypt';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { RoleService } from '../roles/role.service';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { DEV_SES_EMAIL, SUPER_ADMIN_EMAIL } from '../utils/constants';
import { getLoginUrl, isProduction } from '../utils/common/get-login-url';
import { BrandService } from '../brands/brands.service';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import {
	AvailabilityExceptionEntity,
	ExceptionType
} from './entities/availability-exception.entity';
import { CreateExceptionDto, UpdateExceptionDto } from './dto/exception.dto';
import {
	CalendarWorkingHoursResponseDto,
	DoctorWorkingHoursDto,
	TimeSlot
} from './dto/calendar-working-hours.dto';
import * as moment from 'moment-timezone';
import { AvailabilityService } from '../availability/availability.service';
import {
	FindClinicUsersAvailabilityResponse,
	UserAvailabilityInfo
} from './dto/availability-response.dto';

interface user {
	userId: string;
	role: Role;
	email: string;
}
interface WorkingHours {
	startTime: string;
	endTime: string;
	isWorkingDay: boolean;
}

@Injectable()
export class UsersService {
	constructor(
		@InjectRepository(User)
		private usersRepository: Repository<User>,
		@InjectRepository(ClinicUser)
		private clinicUsersRepository: Repository<ClinicUser>,
		@InjectRepository(AppointmentDoctorsEntity)
		private appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>,
		@InjectRepository(AvailabilityExceptionEntity)
		private availabilityExceptionRepository: Repository<AvailabilityExceptionEntity>,
		private roleService: RoleService,
		private readonly logger: WinstonLogger,
		private readonly mailService: SESMailService,
		private brandService: BrandService,
		private dataSource: DataSource,
		@Inject(forwardRef(() => AvailabilityService))
		private readonly availabilityService: AvailabilityService
	) {}

	public async generateUniquePin(): Promise<string> {
		let generatedPin: string;
		let isUnique = false;

		do {
			generatedPin = Math.floor(1000 + Math.random() * 9000).toString();
			console.log(`Generated PIN: ${generatedPin}`);
			const storedPins = await this.usersRepository.find({
				select: ['pin']
			});
			isUnique = true;
			for (const user of storedPins) {
				const match = await bcrypt.compare(generatedPin, user.pin);
				if (match) {
					isUnique = false;
					break;
				}
			}
		} while (!isUnique);

		return generatedPin;
	}

	async createUser(
		createUserDto: CreateUserDto,
		clinicId: string,
		brandId: string,
		userObj: user
	): Promise<User> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Creating new user', {
				dto: createUserDto,
				clinicId
			});

			// Check if email already exists
			const existingUser = await this.usersRepository.findOne({
				where: { email: createUserDto.email }
			});

			let user: User;
			let emailPin = '';
			const brandInfo = await this.brandService.getBrandById(brandId);
			const loginUrl = getLoginUrl(brandInfo?.slug as string); //'https://nidanaqa-api.napses.in/sigin/pin';
			const subject = `Welcome to ${brandInfo?.name} – Your Journey Starts Here!`;
			let body = ``;

			if (existingUser) {
				if (clinicId) {
					// If user exists and clinicId provided, check if they're already associated with this clinic
					const existingClinicUser =
						await this.clinicUsersRepository.findOne({
							where: {
								userId: existingUser.id,
								clinicId: clinicId
							}
						});
					if (existingClinicUser) {
						throw new ConflictException(
							'User is already associated with this clinic'
						);
					}
					user = existingUser;
				} else {
					// Create new user
					let pin = '';
					if (userObj.role !== 'super_admin') {
						pin = await this.generateUniquePin();
						emailPin = pin;
						this.logger.log(
							`Mock email sent to ${createUserDto.email} with PIN: ${pin}`
						);
						body = `Dear ${createUserDto.firstName} ${createUserDto.lastName},<br/><br/>
                Welcome to the ${brandInfo?.name} family! We're excited to have you on board as a valued team member. To help you get started, we use Nidana for streamlining our clinic's operations, and we've set up your account for you.<br/><br/>

                    Your unique PIN is ${emailPin}, which you'll need to log in. You can access your account and explore the platform at ${loginUrl}.<br/><br/>
                    
                    If you need any assistance or have questions, feel free to reach out. We're here to support you every step of the way!<br/><br/>
                    
                    Once again, welcome to the team! We're looking forward to achieving great things together.<br/><br/>
                    Best regards,<br/>
                    The ${brandInfo?.name} Team
            `;
						// await this.sendUserCreationEmail(createUserDto.email, pin, role.name);
					}

					pin = await bcrypt.hash(pin, 10);
					user = this.usersRepository.create({
						...createUserDto,
						pin,
						createdBy: userObj.userId,
						updatedBy: userObj.userId
					});
					user = await queryRunner.manager.save(User, user);
				}
			} else {
				// Create new user
				let pin = '';
				if (userObj.role !== 'super_admin') {
					pin = await this.generateUniquePin();
					emailPin = pin;
					body = `Dear ${createUserDto.firstName} ${createUserDto.lastName},<br/><br/>
                Welcome to the ${brandInfo?.name} family! We're excited to have you on board as a valued team member. To help you get started, we use Nidana for streamlining our clinic's operations, and we've set up your account for you.<br/><br/>

                    Your unique PIN is ${emailPin}, which you'll need to log in. You can access your account and explore the platform at ${loginUrl}.<br/><br/>

                    If you need any assistance or have questions, feel free to reach out. We're here to support you every step of the way!<br/><br/>

                    Once again, welcome to the team! We're looking forward to achieving great things together.<br/><br/>
                    Best regards,<br/>
                    The ${brandInfo?.name} Team
            `;
					this.logger.log(
						`Mock email sent to ${createUserDto.email} with PIN: ${pin}`
					);
					// await this.sendUserCreationEmail(createUserDto.email, pin, role.name);
				}

				pin = await bcrypt.hash(pin, 10);
				user = this.usersRepository.create({
					...createUserDto,
					pin,
					createdBy: userObj.userId,
					updatedBy: userObj.userId
				});

				user = await queryRunner.manager.save(User, user);
			}
			console.log('user :>> ', user);
			if (clinicId && userObj.role !== 'super_admin') {
				const clinicUser = this.clinicUsersRepository.create({
					userId: user.id,
					clinicId: clinicId,
					brandId: brandId,
					createdBy: userObj.userId,
					updatedBy: userObj.userId
				});

				await queryRunner.manager.save(ClinicUser, clinicUser);
			}
			if (isProduction() && createUserDto?.email) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: createUserDto.email,
					ccMailAddress: process.env.SUPER_ADMIN_EMAIL
				});
			} else if (!isProduction()) {
				await this.mailService.sendMail({
					body,
					subject,
					toMailAddress: DEV_SES_EMAIL, //createUserDto.email
					ccMailAddress: SUPER_ADMIN_EMAIL
				});
			}
			await queryRunner.commitTransaction();

			this.logger.log('User created/associated successfully', {
				userId: user.id,
				clinicId: clinicId || 'N/A'
			});
			return user;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Error creating/associating user', {
				error,
				clinicId
			});
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while creating/associating the user'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async findOneByPin(pin: string): Promise<User | null> {
		const users = await this.usersRepository.find();
		for (const user of users) {
			const isPinValid = await bcrypt.compare(pin, user.pin);
			if (isPinValid) {
				return user;
			}
		}
		return null;
	}

	async updateStaffProfile(
		userId: string,
		updateProfileDto: UpdateProfileDto
	): Promise<User> {
		console.log(updateProfileDto);
		const user = await this.usersRepository.findOne({
			where: { id: userId }
		});

		if (!user) {
			throw new UnauthorizedException('Invalid PIN');
		}

		Object.assign(user, updateProfileDto, { registered: true });
		return this.usersRepository.save(user);
	}

	async findByRoleId(roleId: string): Promise<User | null> {
		try {
			this.logger.log('Fetching user by role ID', { roleId });
			const user = await this.usersRepository.findOne({
				where: { roleId }
			});
			if (user) {
				this.logger.log('User fetched successfully', { roleId });
			} else {
				this.logger.error('User not found for role', { roleId });
			}
			return user;
		} catch (error) {
			this.logger.error('Error fetching user by role ID', {
				error,
				roleId
			});
			throw new InternalServerErrorException(
				'Failed to fetch user by role ID'
			);
		}
	}

	async updateWorkingHours(
		userId: string,
		updateWorkingHoursDto: UpdateWorkingHoursDto
	): Promise<ClinicUser> {
		try {
			const clinicUser = await this.clinicUsersRepository.findOne({
				where: { id: userId }
			});

			if (!clinicUser) {
				throw new NotFoundException(
					`Clinic user with ID "${userId}" not found`
				);
			}

			console.log(updateWorkingHoursDto);
			clinicUser.workingHours = updateWorkingHoursDto.workingHours;
			clinicUser.isOnboarded = true;

			// Save the updated working hours
			const updatedClinicUser =
				await this.clinicUsersRepository.save(clinicUser);

			// Update availability slots based on working hours change
			try {
				await this.availabilityService.handleWorkingHoursChange(
					updatedClinicUser.id
				);
				this.logger.log(
					'Availability updated after working hours change',
					{
						clinicUserId: updatedClinicUser.id
					}
				);
			} catch (error) {
				this.logger.error(
					'Error updating availability after working hours change',
					{
						clinicUserId: updatedClinicUser.id,
						error
					}
				);
				// Continue since the working hours update was successful
				// The availability system will recover through validation
			}

			return updatedClinicUser;
		} catch (error) {
			throw new InternalServerErrorException(
				'Failed to update user working hours'
			);
		}
	}
	async findByUserId(userId: string): Promise<ClinicUser[]> {
		return this.clinicUsersRepository.find({
			where: { userId },
			select: {
				id: true,
				clinicId: true,
				brandId: true,
				clinic: {
					id: true,
					name: true,
					brand: {
						name: true
					}
				},
				user: {
					id: true,
					firstName: true,
					lastName: true
				},
				createdAt: true
			},
			relations: ['clinic', 'user', 'clinic.brand'],
			order: { createdAt: 'ASC' }
		});
	}

	async findOneByEmail(email: string): Promise<User | null> {
		try {
			this.logger.log('Fetching user by email', { email });
			const user = await this.usersRepository.findOne({
				where: { email }
			});
			if (user) {
				this.logger.log('User fetched successfully', { email });
			} else {
				this.logger.error('User not found', { email });
			}
			return user;
		} catch (error) {
			this.logger.error('Error fetching user by email', { error, email });
			throw new InternalServerErrorException(
				'Failed to fetch user by email'
			);
		}
	}

	async getUserClinics(userId: string): Promise<ClinicEntity[] | undefined> {
		try {
			const clinicUsers = await this.clinicUsersRepository.find({
				where: { userId },
				select: {
					id: true,
					isOnboarded: true,
					clinic: {
						id: true,
						name: true,
						city: true
					}
				},
				relations: ['clinic', 'user']
			});

			if (!clinicUsers.length) {
				throw new NotFoundException(
					`No clinics found for user with ID "${userId}"`
				);
			}

			return clinicUsers.map(clinicUser => ({
				clinic_user_id: clinicUser.id,
				...clinicUser.clinic,
				isOnboarded: clinicUser.isOnboarded
			}));
		} catch (error) {
			throw new InternalServerErrorException(
				'Failed to fetch users clinics'
			);
		}
	}

	async findUsersAcrossClinics(
		brandId: string,
		excludeClinicId: string,
		searchTerm: string,
		page: number = 1,
		limit: number = 10
	): Promise<{ users: User[]; total: number }> {
		try {
			const [clinicUsers, total] =
				await this.clinicUsersRepository.findAndCount({
					where: [
						{ brandId, user: { email: Like(`%${searchTerm}%`) } },
						{
							brandId,
							user: { firstName: Like(`%${searchTerm}%`) }
						},
						{ brandId, user: { lastName: Like(`%${searchTerm}%`) } }
					],
					select: {
						user: {
							id: true,
							email: true,
							firstName: true,
							lastName: true,
							roleId: true,
							createdAt: true
						}
					},
					relations: ['user', 'user.role'],
					skip: (page - 1) * limit,
					take: limit,
					order: { user: { createdAt: 'DESC' } }
				});

			const filteredUsers = clinicUsers
				.filter(cu => cu.clinicId !== excludeClinicId)
				.map(cu => cu.user);

			return {
				users: filteredUsers,
				total: total // This is the total before filtering, you might want to adjust this
			};
		} catch (error) {
			this.logger.error('Error searching users across clinics', {
				error,
				brandId,
				excludeClinicId,
				searchTerm
			});
			throw new InternalServerErrorException(
				'Failed to search users across clinics'
			);
		}
	}

	async addUserToClinic(
		userId: string,
		clinicId: string,
		brandId: string,
		isPrimary: boolean = false
	): Promise<ClinicUser> {
		const user = await this.usersRepository.findOne({
			where: { id: userId }
		});
		if (!user) {
			throw new NotFoundException(`User with ID "${userId}" not found`);
		}

		const clinicUser = this.clinicUsersRepository.create({
			userId,
			clinicId,
			brandId,
			isPrimary
		});

		return this.clinicUsersRepository.save(clinicUser);
	}

	async findClinicUsersAvailability(
		clinicId: string,
		role?: string,
		search?: string,
		_orderBy: string = 'DESC', // Keep parameter for signature, but mark as unused
		date?: string,
		startTime?: string,
		endTime?: string
	): Promise<FindClinicUsersAvailabilityResponse> {
		// Use specific return type
		try {
			// Get role entity if role is specified
			let roleEntity;
			if (role) {
				roleEntity = await this.roleService.findByName(role);
			}

			// Build base query for clinic users
			const queryBuilder = this.clinicUsersRepository
				.createQueryBuilder('clinicUser')
				.innerJoinAndSelect('clinicUser.user', 'user')
				.innerJoinAndSelect('user.role', 'role')
				.leftJoinAndSelect('clinicUser.clinic', 'clinic') // Join clinic for timezone in AvailabilityService
				.where('clinicUser.clinicId = :clinicId', { clinicId })
				.andWhere('role.name != :superAdminRole', {
					superAdminRole: Role.SUPER_ADMIN
				});

			if (roleEntity) {
				queryBuilder.andWhere('role.id = :roleId', {
					roleId: roleEntity.id
				});
			}

			if (search && search.trim() !== '') {
				queryBuilder.andWhere(
					new Brackets(qb => {
						qb.where('user.firstName ILIKE :search', {
							search: `%${search}%`
						}).orWhere('user.lastName ILIKE :search', {
							search: `%${search}%`
						});
					})
				);
			}

			// Execute query to get clinic users
			const [clinicUsers, total] = await queryBuilder.getManyAndCount();

			// If date, startTime, and endTime are provided, use the availability service
			if (date && startTime && endTime) {
				// First, check which users are available during the specific time slot
				const availableUserIds =
					await this.availabilityService.findAvailableUsers(
						clinicId,
						date,
						startTime, // Pass original startTime string
						endTime, // Pass original endTime string
						{ role } // Pass role filter if provided
					);

				// Map results to the expected format including availability check
				const formattedUsers: UserAvailabilityInfo[] =
					await Promise.all(
						clinicUsers.map(async clinicUser => {
							// Check if this user's clinicUser ID is in the list of available users for the slot
							const isAvailable = availableUserIds.includes(
								clinicUser.id // availabilityService returns clinicUser IDs
							);

							let nextAvailableSlotString: string | null = null;

							// If the user is NOT available during the specified slot, find their next general availability
							if (!isAvailable) {
								// Get next available slot starting from the requested date/time
								// The service handles timezone conversion internally
								const nextSlot =
									await this.availabilityService.getNextAvailableSlot(
										clinicUser.id, // Pass clinicUser ID
										date,
										startTime // Find next slot after the requested start time
									);

								// Format the next available slot if found
								if (nextSlot) {
									nextAvailableSlotString = `${nextSlot.date} ${nextSlot.startTime}`;
								}
							}

							// Format user data according to the UserAvailabilityInfo interface
							return {
								doctorId: clinicUser.id, // This is the clinicUser ID
								availability: {
									isAvailable,
									// Only include nextAvailableSlot if the user was unavailable and a next slot was found
									...(nextAvailableSlotString
										? {
												nextAvailableSlot:
													nextAvailableSlotString
											}
										: {})
								}
								// Optionally add more user details here if needed:
								// firstName: clinicUser.user.firstName,
								// lastName: clinicUser.user.lastName,
							};
						})
					);

				return {
					users: formattedUsers,
					total
				};
			} else {
				// No date/time provided, return users without specific availability check
				// Mark them as generally "available" in the context of listing users,
				// or adjust based on desired behavior when no time slot is specified.
				const basicUserInfo = clinicUsers.map(clinicUser => ({
					doctorId: clinicUser.id,
					availability: {
						isAvailable: true // Assuming available if no specific time slot is checked
						// nextAvailableSlot: null // Or fetch next available if needed even without a time slot check? Decided against for now.
					}
					// Add other details if needed
					// firstName: clinicUser.user.firstName,
					// lastName: clinicUser.user.lastName,
					// email: clinicUser.user.email,
					// role: clinicUser.user.role.name,
				}));

				return {
					users: basicUserInfo,
					total
				};
			}
		} catch (error) {
			this.logger.error('Error finding clinic users availability', {
				error,
				clinicId,
				role,
				search,
				date,
				startTime,
				endTime
			});
			// Rethrow the original error or a generic one
			throw error instanceof InternalServerErrorException
				? error
				: new InternalServerErrorException(
						'Failed to find clinic users availability'
					);
		}
	}

	async getClinicDoctors(
		clinicId: string
	): Promise<{ users: any; total: number }> {
		try {
			this.logger.log('Fetching clinic doctors and admins', { clinicId });

			// Get role entities for admin and doctor
			const adminRoleEntity = await this.roleService.findByName(
				Role.ADMIN
			);
			const doctorRoleEntity = await this.roleService.findByName(
				Role.DOCTOR
			);

			const queryBuilder = this.clinicUsersRepository
				.createQueryBuilder('clinicUser')
				.innerJoinAndSelect('clinicUser.user', 'user')
				.innerJoinAndSelect('user.role', 'role')
				.where('clinicUser.clinicId = :clinicId', { clinicId })
				.andWhere(
					'(role.id = :adminRoleId OR role.id = :doctorRoleId)',
					{
						adminRoleId: adminRoleEntity.id,
						doctorRoleId: doctorRoleEntity.id
					}
				);

			const [clinicUsers, total] = await queryBuilder.getManyAndCount();

			const users = clinicUsers.map(clinicUser => ({
				id: clinicUser.id,
				firstName: clinicUser.user.firstName,
				lastName: clinicUser.user.lastName,
				email: clinicUser.user.email,
				isActive: clinicUser.user.isActive,
				createdAt: clinicUser.user.createdAt,
				role: clinicUser.user.role,
				userId: clinicUser.user.id,
				workingHours: clinicUser.workingHours
			}));

			this.logger.log('Clinic doctors and admins fetched successfully', {
				clinicId,
				count: users.length
			});

			return { users, total };
		} catch (error) {
			this.logger.error('Error fetching clinic doctors and admins', {
				error,
				clinicId
			});
			throw new InternalServerErrorException(
				'Failed to fetch clinic doctors and admins'
			);
		}
	}

	async findClinicUsers(
		clinicId: string,
		page: number = 1,
		limit: number = 10,
		role?: string,
		search?: string,
		orderBy: string = 'DESC'
	): Promise<{ users: any; total: number }> {
		try {
			this.logger.log('Fetching clinic users', {
				clinicId,
				page,
				limit,
				role,
				search
			});

			let roleEntity;
			if (role) {
				this.logger.log('Fetching role entity', { role });
				try {
					roleEntity = await this.roleService.findByName(role);
					this.logger.log('Role entity found', { roleEntity });
				} catch (error) {
					this.logger.error('Role not found', { role });
					throw new NotFoundException(
						`Role with name "${role}" not found`
					);
				}
			}

			const queryBuilder = this.clinicUsersRepository
				.createQueryBuilder('clinicUser')
				.innerJoinAndSelect('clinicUser.user', 'user')
				.innerJoinAndSelect('user.role', 'role')
				.where('clinicUser.clinicId = :clinicId', { clinicId })
				.andWhere('role.name != :superAdminRole', {
					superAdminRole: Role.SUPER_ADMIN
				});

			if (roleEntity) {
				queryBuilder.andWhere('role.id = :roleId', {
					roleId: roleEntity.id
				});
			}

			if (search) {
				queryBuilder.andWhere(
					new Brackets(qb => {
						qb.where('user.firstName ILIKE :search', {
							search: `%${search}%`
						})
							.orWhere('user.lastName ILIKE :search', {
								search: `%${search}%`
							})
							.orWhere('user.email ILIKE :search', {
								search: `%${search}%`
							});
					})
				);
			}

			queryBuilder
				.skip((page - 1) * limit)
				.take(limit)
				.orderBy('user.createdAt', orderBy === 'ASC' ? 'ASC' : 'DESC');

			const [clinicUsers, total] = await queryBuilder.getManyAndCount();

			const users = clinicUsers.map(clinicUser => ({
				id: clinicUser.id,
				firstName: clinicUser.user.firstName,
				lastName: clinicUser.user.lastName,
				email: clinicUser.user.email,
				isActive: clinicUser.user.isActive,
				createdAt: clinicUser.user.createdAt,
				role: clinicUser.user.role,
				userId: clinicUser.user.id,
				workingHours: clinicUser.workingHours
			}));

			this.logger.log('Clinic users fetched successfully', {
				clinicId,
				count: users.length
			});

			return { users, total };
		} catch (error) {
			console.log(error);
			this.logger.error('Error fetching clinic users', {
				error,
				clinicId
			});
			throw new InternalServerErrorException(
				'Failed to fetch clinic users'
			);
		}
	}

	async updateUserStatus(
		id: string,
		isActive: boolean,
		updatedBy?: string
	): Promise<User> {
		try {
			this.logger.log('Updating user status', { userId: id, isActive });
			const user = await this.findOne(id);
			user.isActive = isActive;
			//   user.updatedBy = updatedBy;
			const updatedUser = await this.usersRepository.save(user);
			this.logger.log('User status updated successfully', {
				userId: id,
				isActive
			});
			return updatedUser;
		} catch (error) {
			this.logger.error('Error updating user status', {
				error,
				userId: id
			});
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to update user status'
			);
		}
	}

	async updateClinicUser(
		id: string,
		updateUserDto: UpdateUserDto,
		updatedBy?: string
	): Promise<User> {
		try {
			console.log(updateUserDto);
			this.logger.log('Updating clinic user', {
				userId: id,
				dto: updateUserDto
			});
			const user = await this.findOne(id);

			// Check if email is being changed and if it's already in use
			if (updateUserDto.email && updateUserDto.email !== user.email) {
				const existingUser = await this.usersRepository.findOne({
					where: { email: updateUserDto.email }
				});
				if (existingUser) {
					throw new ConflictException('Email already in use');
				}
			}

			//   Object.assign(user, updateUserDto, { updatedBy });
			Object.assign(user, updateUserDto);
			const updatedUser = await this.usersRepository.save(user);
			this.logger.log('Clinic user updated successfully', { userId: id });
			return updatedUser;
		} catch (error) {
			this.logger.error('Error updating clinic user', {
				error,
				userId: id
			});
			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to update clinic user'
			);
		}
	}

	async findOne(id: string): Promise<Partial<User>> {
		try {
			this.logger.log('Fetching user by ID', { userId: id });
			const user = await this.usersRepository.findOne({
				where: { id },
				select: {
					id: true,
					firstName: true,
					lastName: true,
					licenseNumber: true,
					mobileNumber: true,
					countryCode: true,
					alternateMobileNumber: true,
					alternateCountryCode: true,
					digitalSignature: true,
					email: true,
					roleId: true, // Include roleId in selection
					clinicUsers: {
						id: true,
						clinic: {
							id: true,
							name: true,
							city: true,
							state: true,
							country: true
						}
					}
				},
				relations: {
					clinicUsers: {
						clinic: true
					}
				}
			});

			if (!user) {
				this.logger.error('User not found', { userId: id });
				throw new NotFoundException(`User with ID "${id}" not found`);
			}

			// Transform the response to a cleaner format
			const transformedUser = {
				id: user.id,
				firstName: user.firstName,
				lastName: user.lastName,
				licenseNumber: user.licenseNumber,
				mobileNumber: user.mobileNumber,
				countryCode: user.countryCode,
				alternateMobileNumber: user.alternateMobileNumber,
				alternateCountryCode: user.alternateCountryCode,
				digitalSignature: user.digitalSignature,
				email: user.email,
				roleId: user.roleId, // Include roleId in the transformed user
				clinics:
					user.clinicUsers?.map(cu => ({
						id: cu.clinic.id,
						name: cu.clinic.name,
						city: cu.clinic.city,
						state: cu.clinic.state,
						country: cu.clinic.country
					})) || []
			};

			this.logger.log('User fetched successfully', { userId: id });
			return transformedUser;
		} catch (error) {
			this.logger.error('Error fetching user', { error, userId: id });
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException('Failed to fetch user');
		}
	}

	async getClinicUserData(id: string): Promise<any | null> {
		try {
			const clinicUser = await this.clinicUsersRepository.findOne({
				where: { id }
			});

			if (!clinicUser) {
				return null;
			}

			return clinicUser;
		} catch (error) {
			console.log(error);
			throw new InternalServerErrorException(
				'Failed to fetch clinic-user'
			);
		}
	}

	async findAll(brandId: string): Promise<User[]> {
		try {
			const whereConditions: Partial<{
				clinicUsers: { brandId: string };
			}> = {};
			if (brandId) {
				whereConditions.clinicUsers = {
					brandId
				};
			}
			this.logger.log('Fetching all users');
			const users = await this.usersRepository.find({
				where: whereConditions
			});
			this.logger.log('Users fetched successfully', {
				count: users.length
			});
			return users;
		} catch (error) {
			this.logger.error('Error fetching users', { error });
			throw new InternalServerErrorException('Failed to fetch users');
		}
	}

	async remove(id: string): Promise<void> {
		try {
			this.logger.log('Removing user', { userId: id });
			const result = await this.usersRepository.delete(id);
			if (result.affected === 0) {
				this.logger.error('User not found for removal', { userId: id });
				throw new NotFoundException(`User with ID "${id}" not found`);
			}
			this.logger.log('User removed successfully', { userId: id });
		} catch (error) {
			this.logger.error('Error removing user', { error, userId: id });
			if (error instanceof NotFoundException) {
				throw error;
			}
			throw new InternalServerErrorException('Failed to remove user');
		}
	}

	private async sendUserCreationEmail(
		email: string,
		pin: string,
		roleName: string
	): Promise<void> {
		const subject = 'Welcome to Our Platform';
		const body = `
            Dear User,

            Your account has been created successfully. 
            Your role is: ${roleName}
            Your temporary PIN is: ${pin}

        `;

		await this.mailService.sendMail({
			body,
			subject,
			toMailAddress: '<EMAIL>'
		});
	}

	private async sendPinResetEmail(
		email: string,
		newPin: string
	): Promise<void> {
		const subject = 'PIN Reset Notification';
		const body = `
            Dear User,

            Your PIN has been reset as requested.
            Your new temporary PIN is: ${newPin}
        `;

		await this.mailService.sendMail({
			body,
			subject,
			toMailAddress: '<EMAIL>'
		});
	}

	// =========================================================================
	// Exception Management Methods
	// =========================================================================

	/**
	 * Create a new availability exception
	 *
	 * @param createExceptionDto Data for creating the exception
	 * @param userId ID of the user creating the exception
	 * @returns The created exception
	 */
	async createException(
		createExceptionDto: CreateExceptionDto,
		userId: string
	): Promise<AvailabilityExceptionEntity> {
		try {
			this.logger.log('Creating availability exception', {
				dto: createExceptionDto,
				userId
			});

			// Validate the clinicUser exists
			const clinicUser = await this.clinicUsersRepository.findOne({
				where: { id: createExceptionDto.clinicUserId }
			});

			if (!clinicUser) {
				throw new NotFoundException(
					`Clinic user with ID "${createExceptionDto.clinicUserId}" not found`
				);
			}

			// Format the dates correctly - ensure we have proper Date objects
			let startDate: Date;
			try {
				// Handle both Date objects and ISO strings
				if (typeof createExceptionDto.startDate === 'string') {
					// If it's a date without time (like YYYY-MM-DD)
					if (createExceptionDto.startDate.length <= 10) {
						startDate = new Date(
							createExceptionDto.startDate + 'T00:00:00.000Z'
						);
					} else {
						startDate = new Date(createExceptionDto.startDate);
					}
				} else {
					startDate = createExceptionDto.startDate;
				}

				this.logger.log('Parsed startDate', {
					original: createExceptionDto.startDate,
					parsed: startDate
				});
			} catch (error) {
				this.logger.error('Error parsing startDate', {
					value: createExceptionDto.startDate,
					error
				});
				throw new BadRequestException(
					`Invalid start date format: ${createExceptionDto.startDate}`
				);
			}

			let endDate: Date | null = null;
			if (createExceptionDto.endDate) {
				try {
					// Handle both Date objects and ISO strings
					if (typeof createExceptionDto.endDate === 'string') {
						// If it's a date without time (like YYYY-MM-DD)
						if (createExceptionDto.endDate.length <= 10) {
							endDate = new Date(
								createExceptionDto.endDate + 'T00:00:00.000Z'
							);
						} else {
							endDate = new Date(createExceptionDto.endDate);
						}
					} else {
						endDate = createExceptionDto.endDate;
					}

					this.logger.log('Parsed endDate', {
						original: createExceptionDto.endDate,
						parsed: endDate
					});
				} catch (error) {
					this.logger.error('Error parsing endDate', {
						value: createExceptionDto.endDate,
						error
					});
					throw new BadRequestException(
						`Invalid end date format: ${createExceptionDto.endDate}`
					);
				}

				// Validate end date is not before start date
				if (endDate < startDate) {
					throw new BadRequestException(
						'End date cannot be before start date'
					);
				}
			}

			// Validate time slots if not full day
			if (
				!createExceptionDto.isFullDay &&
				(!createExceptionDto.times ||
					createExceptionDto.times.length === 0)
			) {
				throw new BadRequestException(
					'Time slots are required when not setting a full day exception'
				);
			}

			// Validate time slots format and logic
			if (
				createExceptionDto.times &&
				createExceptionDto.times.length > 0
			) {
				for (const timeSlot of createExceptionDto.times) {
					// Validate time format (HH:MM)
					const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
					if (
						!timeRegex.test(timeSlot.startTime) ||
						!timeRegex.test(timeSlot.endTime)
					) {
						throw new BadRequestException(
							'Time must be in format HH:MM (24-hour)'
						);
					}

					// Parse times for comparison
					const [startHour, startMinute] = timeSlot.startTime
						.split(':')
						.map(Number);
					const [endHour, endMinute] = timeSlot.endTime
						.split(':')
						.map(Number);

					// Create Date objects for comparison
					const startDateTime = new Date();
					startDateTime.setHours(startHour, startMinute, 0, 0);

					const endDateTime = new Date();
					endDateTime.setHours(endHour, endMinute, 0, 0);

					// Validate end time is after start time
					if (endDateTime <= startDateTime) {
						throw new BadRequestException(
							'End time must be after start time'
						);
					}
				}
			}

			// Check if the exception has already ended
			const exceptionData = {
				startDate,
				endDate,
				isFullDay: createExceptionDto.isFullDay,
				times: createExceptionDto.times
			};

			if (this.isExceptionEnded(exceptionData)) {
				throw new ConflictException(
					'Cannot create an exception that has already ended'
				);
			}

			// Check for conflicting exceptions
			await this.validateExceptionConflicts(
				createExceptionDto.clinicUserId,
				startDate,
				endDate,
				createExceptionDto.type,
				createExceptionDto.isFullDay,
				createExceptionDto.times
			);

			// If out-of-office, check for existing appointments
			if (createExceptionDto.type === ExceptionType.OUT_OF_OFFICE) {
				await this.validateNoConflictingAppointments(
					createExceptionDto.clinicUserId,
					startDate,
					endDate,
					createExceptionDto.isFullDay,
					createExceptionDto.times
				);
			}

			// Create the exception
			const exception = this.availabilityExceptionRepository.create({
				clinicUserId: createExceptionDto.clinicUserId,
				startDate,
				endDate,
				type: createExceptionDto.type,
				isFullDay: createExceptionDto.isFullDay,
				times: createExceptionDto.times,
				createdBy: userId
			});

			const createdException =
				await this.availabilityExceptionRepository.save(exception);

			// Update availability based on the new exception
			try {
				await this.availabilityService.handleExceptionChange(
					createdException,
					'create'
				);
				this.logger.log(
					'Availability updated after creating exception',
					{
						exceptionId: createdException.id,
						clinicUserId: createdException.clinicUserId
					}
				);
			} catch (error) {
				this.logger.error(
					'Error updating availability after creating exception',
					{
						exceptionId: createdException.id,
						clinicUserId: createdException.clinicUserId,
						error
					}
				);
				// Continue since the exception creation was successful
			}

			return createdException;
		} catch (error: any) {
			// Enhanced error logging to capture detailed information
			this.logger.error('Failed to create availability exception', {
				error: {
					message: error.message,
					stack: error.stack,
					name: error.name,
					code: error.code,
					details: error.detail || error.details
				},
				dto: createExceptionDto,
				userId
			});

			// Log specific exception types with more details
			if (error instanceof NotFoundException) {
				throw error;
			} else if (error instanceof BadRequestException) {
				throw error;
			} else if (error instanceof ConflictException) {
				throw error;
			} else {
				console.error('Unhandled error in createException:', error);
				throw new InternalServerErrorException(
					`Failed to create availability exception: ${error.message}`
				);
			}
		}
	}

	/**
	 * Get all exceptions for a clinic user, optionally including past exceptions
	 *
	 * @param clinicUserId ID of the clinic user
	 * @param includeHistory Whether to include past exceptions
	 * @returns List of exceptions
	 */
	async getExceptions(
		clinicUserId: string,
		includeHistory: boolean = false
	): Promise<AvailabilityExceptionEntity[]> {
		try {
			// Set today's date (without time component)
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			const todayISOString = today.toISOString().split('T')[0]; // YYYY-MM-DD format

			let query = this.availabilityExceptionRepository
				.createQueryBuilder('exception')
				.where('exception.clinic_user_id = :clinicUserId', {
					clinicUserId
				});

			// Only include current and future exceptions unless history is requested
			if (!includeHistory) {
				query = query.andWhere(
					// For exceptions with no end date, include if start date is today or in future
					'(exception.end_date IS NULL AND DATE(exception.start_date) >= :today) OR ' +
						// For exceptions with an end date, include if end date is today or in future
						'(exception.end_date IS NOT NULL AND DATE(exception.end_date) >= :today)',
					{ today: todayISOString }
				);
			}

			return query.orderBy('exception.start_date', 'ASC').getMany();
		} catch (error) {
			this.logger.error('Failed to get availability exceptions', {
				error,
				clinicUserId
			});
			throw new InternalServerErrorException(
				'Failed to get availability exceptions'
			);
		}
	}

	/**
	 * Get a specific exception by ID
	 *
	 * @param id Exception ID
	 * @returns The exception entity
	 */
	async getExceptionById(id: string): Promise<AvailabilityExceptionEntity> {
		try {
			const exception =
				await this.availabilityExceptionRepository.findOne({
					where: { id }
				});

			if (!exception) {
				throw new NotFoundException(
					`Exception with ID "${id}" not found`
				);
			}

			return exception;
		} catch (error) {
			this.logger.error('Failed to get availability exception', {
				error,
				id
			});

			if (error instanceof NotFoundException) {
				throw error;
			}

			throw new InternalServerErrorException(
				'Failed to get availability exception'
			);
		}
	}

	/**
	 * Update an existing exception
	 *
	 * @param id Exception ID
	 * @param updateExceptionDto Data for updating the exception
	 * @param userId ID of the user updating the exception
	 * @returns The updated exception
	 */
	async updateException(
		id: string,
		updateExceptionDto: UpdateExceptionDto,
		userId: string
	): Promise<AvailabilityExceptionEntity> {
		try {
			const exception = await this.getExceptionById(id);

			// Store previous exception data for availability update
			const previousException = {
				startDate: exception.startDate,
				endDate: exception.endDate,
				isFullDay: exception.isFullDay,
				times: exception.times,
				type: exception.type,
				clinicUserId: exception.clinicUserId
			};

			// Check if the exception has already ended
			if (this.isExceptionEnded(exception)) {
				throw new ConflictException(
					'Cannot modify an exception that has already ended'
				);
			}

			// Make sure clinicUserId hasn't changed
			if (
				updateExceptionDto.clinicUserId &&
				updateExceptionDto.clinicUserId !== exception.clinicUserId
			) {
				throw new BadRequestException(
					'Cannot change the clinic user ID of an exception'
				);
			}

			// Format the dates correctly if provided
			let startDate = exception.startDate;
			if (updateExceptionDto.startDate) {
				startDate = new Date(updateExceptionDto.startDate);
			}

			let endDate: Date | null = exception.endDate;
			if (updateExceptionDto.endDate !== undefined) {
				endDate = updateExceptionDto.endDate
					? new Date(updateExceptionDto.endDate)
					: null;
			}

			// Validate end date is not before start date
			if (endDate && endDate < startDate) {
				throw new BadRequestException(
					'End date cannot be before start date'
				);
			}

			// Determine if isFullDay is being updated
			const isFullDay =
				updateExceptionDto.isFullDay !== undefined
					? updateExceptionDto.isFullDay
					: exception.isFullDay;

			// Validate time slots if not full day
			let times = exception.times;
			if (updateExceptionDto.times !== undefined) {
				times = updateExceptionDto.times;
			}

			if (!isFullDay && (!times || times.length === 0)) {
				throw new BadRequestException(
					'Time slots are required when not setting a full day exception'
				);
			}

			// Validate time slots format and logic
			if (times && times.length > 0) {
				for (const timeSlot of times) {
					// Validate time format (HH:MM)
					const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
					if (
						!timeRegex.test(timeSlot.startTime) ||
						!timeRegex.test(timeSlot.endTime)
					) {
						throw new BadRequestException(
							'Time must be in format HH:MM (24-hour)'
						);
					}

					// Parse times for comparison
					const [startHour, startMinute] = timeSlot.startTime
						.split(':')
						.map(Number);
					const [endHour, endMinute] = timeSlot.endTime
						.split(':')
						.map(Number);

					// Create Date objects for comparison
					const startDateTime = new Date();
					startDateTime.setHours(startHour, startMinute, 0, 0);

					const endDateTime = new Date();
					endDateTime.setHours(endHour, endMinute, 0, 0);

					// Validate end time is after start time
					if (endDateTime <= startDateTime) {
						throw new BadRequestException(
							'End time must be after start time'
						);
					}
				}
			}

			// Determine the type if it's being updated
			const type =
				updateExceptionDto.type !== undefined
					? updateExceptionDto.type
					: exception.type;

			// Also check if the updated exception would have already ended
			const updatedExceptionData = {
				startDate,
				endDate,
				isFullDay,
				times
			};

			if (this.isExceptionEnded(updatedExceptionData)) {
				throw new ConflictException(
					'Cannot update an exception to a time that has already ended'
				);
			}

			// Check for conflicting exceptions
			await this.validateExceptionConflicts(
				exception.clinicUserId,
				startDate,
				endDate,
				type,
				isFullDay,
				times,
				id // Exclude current exception
			);

			// If out-of-office, check for existing appointments
			if (type === ExceptionType.OUT_OF_OFFICE) {
				await this.validateNoConflictingAppointments(
					exception.clinicUserId,
					startDate,
					endDate,
					isFullDay,
					times
				);
			}

			// If this is an ADDITIONAL_HOURS exception being modified or changed to a different type,
			// check if there are appointments that would be affected
			if (exception.type === ExceptionType.ADDITIONAL_HOURS) {
				// If type is changing from ADDITIONAL_HOURS to something else
				if (type !== ExceptionType.ADDITIONAL_HOURS) {
					// Check for appointments during these additional hours
					await this.validateNoAppointmentsInAdditionalHours(
						exception.clinicUserId,
						startDate,
						endDate,
						isFullDay,
						times || exception.times
					);
				}
				// If the exception time slots or dates are changing
				else if (
					updateExceptionDto.startDate ||
					updateExceptionDto.endDate !== undefined ||
					updateExceptionDto.isFullDay !== undefined ||
					updateExceptionDto.times !== undefined
				) {
					// For simplicity, validate any change to dates or times by checking all appointments
					// Pass both original and new times to allow extending hours without conflict
					await this.validateNoAppointmentsInAdditionalHours(
						exception.clinicUserId,
						new Date(exception.startDate),
						exception.endDate ? new Date(exception.endDate) : null,
						exception.isFullDay,
						exception.times,
						times // Pass new times for comparison
					);
				}
			}

			// Update the exception
			const updatedData = {
				startDate,
				endDate,
				type,
				isFullDay,
				times,
				updatedBy: userId
			};

			// Apply updates to the existing entity to maintain proper typing
			Object.assign(exception, updatedData);

			// Save and return the updated entity
			const updatedException =
				await this.availabilityExceptionRepository.save(exception);

			// Update availability based on the updated exception
			try {
				await this.availabilityService.handleExceptionChange(
					updatedException,
					'update',
					previousException
				);
				this.logger.log(
					'Availability updated after modifying exception',
					{
						exceptionId: updatedException.id,
						clinicUserId: updatedException.clinicUserId
					}
				);
			} catch (error) {
				this.logger.error(
					'Error updating availability after modifying exception',
					{
						exceptionId: updatedException.id,
						clinicUserId: updatedException.clinicUserId,
						error
					}
				);
				// Continue since the exception update was successful
			}

			return updatedException;
		} catch (error) {
			this.logger.error('Failed to update availability exception', {
				error,
				id,
				dto: updateExceptionDto
			});

			if (
				error instanceof NotFoundException ||
				error instanceof BadRequestException ||
				error instanceof ConflictException
			) {
				throw error;
			}

			throw new InternalServerErrorException(
				'Failed to update availability exception'
			);
		}
	}

	/**
	 * Delete an exception
	 *
	 * @param id Exception ID
	 */
	async deleteException(id: string): Promise<void> {
		try {
			const exception = await this.getExceptionById(id);

			// Check if the exception has already ended
			if (this.isExceptionEnded(exception)) {
				throw new ConflictException(
					'Cannot delete an exception that has already ended'
				);
			}

			// Check if it's an ADDITIONAL_HOURS exception with scheduled appointments
			if (exception.type === ExceptionType.ADDITIONAL_HOURS) {
				// Format dates for validation
				const startDate = new Date(exception.startDate);
				const endDate = exception.endDate
					? new Date(exception.endDate)
					: null;

				// Check for appointments during these additional hours
				// When deleting, we pass null for newTimes since all time slots are being removed
				await this.validateNoAppointmentsInAdditionalHours(
					exception.clinicUserId,
					startDate,
					endDate,
					exception.isFullDay,
					exception.times,
					null // No new time slots when deleting
				);
			}
			await this.availabilityExceptionRepository.remove(exception);

			// Update availability before deleting the exception
			try {
				await this.availabilityService.handleExceptionChange(
					exception,
					'delete'
				);
				this.logger.log(
					'Availability updated before deleting exception',
					{
						exceptionId: exception.id,
						clinicUserId: exception.clinicUserId
					}
				);
			} catch (error) {
				this.logger.error(
					'Error updating availability before deleting exception',
					{
						exceptionId: exception.id,
						clinicUserId: exception.clinicUserId,
						error
					}
				);
				// Continue with deletion
			}
		} catch (error) {
			this.logger.error('Failed to delete availability exception', {
				error,
				id
			});

			if (
				error instanceof NotFoundException ||
				error instanceof ConflictException
			) {
				throw error;
			}

			throw new InternalServerErrorException(
				'Failed to delete availability exception'
			);
		}
	}

	/**
	 * Validate that there are no conflicting exceptions for the same clinic user
	 *
	 * @param clinicUserId The clinic user ID
	 * @param startDate Start date of the exception
	 * @param endDate End date of the exception
	 * @param type Type of exception
	 * @param isFullDay Whether the exception is for a full day
	 * @param times Time slots for the exception if not full day
	 * @param excludeExceptionId Optional exception ID to exclude from conflict check
	 */
	private async validateExceptionConflicts(
		clinicUserId: string,
		startDate: Date,
		endDate: Date | null,
		type: ExceptionType,
		isFullDay: boolean,
		times?: { startTime: string; endTime: string }[] | null,
		excludeExceptionId?: string
	): Promise<void> {
		try {
			this.logger.log('Validating exception conflicts', {
				clinicUserId,
				startDate,
				endDate,
				type,
				isFullDay,
				times,
				excludeExceptionId
			});

			// Format dates for query
			const formattedStartDate = startDate.toISOString().split('T')[0];
			const formattedEndDate = endDate
				? endDate.toISOString().split('T')[0]
				: formattedStartDate;

			// Prepare query to find exceptions that overlap with the date range
			const queryBuilder = this.availabilityExceptionRepository
				.createQueryBuilder('exception')
				.where('exception.clinic_user_id = :clinicUserId', {
					clinicUserId
				});

			if (excludeExceptionId) {
				queryBuilder.andWhere('exception.id != :excludeExceptionId', {
					excludeExceptionId
				});
			}

			// Find opposite type exceptions with exact date overlap
			// This is the key fix: we need a more precise date comparison
			// Only consider it a conflict if the dates actually overlap
			queryBuilder.andWhere(
				'(' +
					// Case 1: The exception has no end date (single day) - must match exactly
					'(exception.end_date IS NULL AND exception.start_date = :formattedStartDate) OR ' +
					// Case 2: The exception has an end date - check for overlap
					'(exception.end_date IS NOT NULL AND ' +
					'exception.start_date <= :formattedEndDate AND ' +
					'exception.end_date >= :formattedStartDate)' +
					')',
				{ formattedStartDate, formattedEndDate }
			);

			// Find exceptions with opposite type
			if (type === ExceptionType.OUT_OF_OFFICE) {
				queryBuilder.andWhere('exception.type = :type', {
					type: ExceptionType.ADDITIONAL_HOURS
				});
			} else {
				queryBuilder.andWhere('exception.type = :type', {
					type: ExceptionType.OUT_OF_OFFICE
				});
			}

			const query = queryBuilder.getQuery();
			this.logger.log('Conflicts query:', { query });

			const conflictingExceptions = await queryBuilder.getMany();
			this.logger.log('Found potential conflicting exceptions', {
				count: conflictingExceptions.length,
				exceptions: conflictingExceptions,
				formattedStartDate,
				formattedEndDate,
				type
			});

			// For each day in the range, check if there's a time conflict
			if (
				conflictingExceptions.length > 0 &&
				!isFullDay &&
				times &&
				times.length > 0
			) {
				// Generate all dates in the range
				const dates: Date[] = [];
				const currentDate = new Date(formattedStartDate);
				const endDay = new Date(formattedEndDate);

				while (currentDate <= endDay) {
					dates.push(new Date(currentDate));
					currentDate.setDate(currentDate.getDate() + 1);
				}

				this.logger.log('Checking date range for conflicts', { dates });

				// For each date, check if there's a conflicting exception
				for (const date of dates) {
					const dateStr = date.toISOString().split('T')[0];
					const formattedDate = new Date(dateStr).toLocaleDateString(
						'en-US',
						{
							weekday: 'long',
							year: 'numeric',
							month: 'long',
							day: 'numeric'
						}
					);

					// Find exceptions that include this date
					const exceptionsForDay = conflictingExceptions.filter(e => {
						// Ensure we handle both string and Date objects
						const exStartDateStr =
							typeof e.startDate === 'string'
								? e.startDate
								: e.startDate.toISOString().split('T')[0];

						const exEndDateStr = e.endDate
							? typeof e.endDate === 'string'
								? e.endDate
								: e.endDate.toISOString().split('T')[0]
							: exStartDateStr;

						// This date must be within the exception's date range
						return (
							dateStr >= exStartDateStr && dateStr <= exEndDateStr
						);
					});

					this.logger.log('Checking exceptions for day', {
						dateStr,
						count: exceptionsForDay.length,
						exceptionsForDay
					});

					// Check for time conflicts
					for (const exception of exceptionsForDay) {
						if (exception.isFullDay) {
							this.logger.error(
								'Found conflicting full-day exception',
								{
									dateStr,
									exceptionId: exception.id,
									exceptionType: exception.type
								}
							);
							const conflictMessage =
								type === ExceptionType.OUT_OF_OFFICE
									? `You've set out-of-office hours for the entire day. Please update or remove those exceptions to continue.`
									: `You've set additional hours for the entire day. Please update or remove those exceptions to continue.`;

							throw new ConflictException(conflictMessage);
						}

						// Check time slot conflicts
						if (exception.times && exception.times.length > 0) {
							for (const timeSlot of times) {
								// Make sure the timeSlot has both startTime and endTime
								if (!timeSlot.startTime || !timeSlot.endTime) {
									continue; // Skip time slots with missing data
								}

								for (const exTimeSlot of exception.times) {
									// Make sure the exception time slot has both startTime and endTime
									if (
										!exTimeSlot.startTime ||
										!exTimeSlot.endTime
									) {
										continue; // Skip exception time slots with missing data
									}

									this.logger.log(
										'Checking time slot conflict',
										{
											date: dateStr,
											newSlot: `${timeSlot.startTime}-${timeSlot.endTime}`,
											existingSlot: `${exTimeSlot.startTime}-${exTimeSlot.endTime}`
										}
									);

									// Check if the time slots overlap
									const [startHour1, startMinute1] =
										timeSlot.startTime
											.split(':')
											.map(Number);
									const [endHour1, endMinute1] =
										timeSlot.endTime.split(':').map(Number);

									const [startHour2, startMinute2] =
										exTimeSlot.startTime
											.split(':')
											.map(Number);
									const [endHour2, endMinute2] =
										exTimeSlot.endTime
											.split(':')
											.map(Number);

									// Create Date objects for comparison
									const start1 = new Date();
									start1.setHours(
										startHour1,
										startMinute1,
										0,
										0
									);

									const end1 = new Date();
									end1.setHours(endHour1, endMinute1, 0, 0);

									const start2 = new Date();
									start2.setHours(
										startHour2,
										startMinute2,
										0,
										0
									);

									const end2 = new Date();
									end2.setHours(endHour2, endMinute2, 0, 0);

									// Format times for display
									const start1Formatted = moment(
										`${startHour1}:${startMinute1}`,
										'HH:mm'
									).format('h:mm A');
									const end1Formatted = moment(
										`${endHour1}:${endMinute1}`,
										'HH:mm'
									).format('h:mm A');
									const start2Formatted = moment(
										`${startHour2}:${startMinute2}`,
										'HH:mm'
									).format('h:mm A');
									const end2Formatted = moment(
										`${endHour2}:${endMinute2}`,
										'HH:mm'
									).format('h:mm A');

									// Check for overlap - allowing exact start/end time matches
									// A starts when B ends or A ends when B starts is OK
									if (
										!(
											start1.getTime() ===
												end2.getTime() ||
											end1.getTime() === start2.getTime()
										)
									) {
										// Otherwise check for any overlap
										if (start1 < end2 && end1 > start2) {
											this.logger.error(
												'Found conflicting time slot',
												{
													date: dateStr,
													newTimeSlot: `${timeSlot.startTime}-${timeSlot.endTime}`,
													existingTimeSlot: `${exTimeSlot.startTime}-${exTimeSlot.endTime}`,
													exceptionId: exception.id,
													exceptionType:
														exception.type,
													start1Time:
														start1.getTime(),
													end1Time: end1.getTime(),
													start2Time:
														start2.getTime(),
													end2Time: end2.getTime()
												}
											);

											const conflictMessage =
												type ===
												ExceptionType.OUT_OF_OFFICE
													? `You've set additional hours during this time. Please update or remove those exceptions to continue.`
													: `You've set out-of-office hours during this time. Please update or remove those exceptions to continue.`;

											throw new ConflictException(
												conflictMessage
											);
										}
									}
								}
							}
						}
					}
				}
			} else if (conflictingExceptions.length > 0) {
				// Check if the dates actually match before reporting conflict
				const hasRealConflict = conflictingExceptions.some(ex => {
					const exStartDate =
						typeof ex.startDate === 'string'
							? ex.startDate
							: ex.startDate.toISOString().split('T')[0];

					const exEndDate = ex.endDate
						? typeof ex.endDate === 'string'
							? ex.endDate
							: ex.endDate.toISOString().split('T')[0]
						: exStartDate;

					// For exceptions with a single date (null endDate), require exact match
					if (ex.endDate === null) {
						return exStartDate === formattedStartDate;
					}

					// For date ranges, check for overlap
					const hasOverlap =
						formattedStartDate <= exEndDate &&
						formattedEndDate >= exStartDate;

					return hasOverlap;
				});

				if (hasRealConflict) {
					// Find the first actual conflicting exception
					const conflictingException = conflictingExceptions.find(
						ex => {
							const exStartDate =
								typeof ex.startDate === 'string'
									? ex.startDate
									: ex.startDate.toISOString().split('T')[0];

							// For single-day exceptions, require exact match
							if (ex.endDate === null) {
								return exStartDate === formattedStartDate;
							}

							return true; // Already filtered by hasRealConflict
						}
					);

					// Since hasRealConflict is true, there must be at least one conflicting exception
					// But add a fallback just in case
					const exceptionToUse =
						conflictingException || conflictingExceptions[0];

					// Format dates for display
					const exStartDate = new Date(
						typeof exceptionToUse.startDate === 'string'
							? exceptionToUse.startDate
							: exceptionToUse.startDate
					);

					const formattedExStartDate = exStartDate.toLocaleDateString(
						'en-US',
						{
							weekday: 'long',
							year: 'numeric',
							month: 'long',
							day: 'numeric'
						}
					);

					let dateRangeText = formattedExStartDate;

					if (exceptionToUse.endDate) {
						const exEndDate = new Date(
							typeof exceptionToUse.endDate === 'string'
								? exceptionToUse.endDate
								: exceptionToUse.endDate
						);

						const formattedExEndDate = exEndDate.toLocaleDateString(
							'en-US',
							{
								weekday: 'long',
								year: 'numeric',
								month: 'long',
								day: 'numeric'
							}
						);

						// Only add end date if it's different from start date
						if (formattedExStartDate !== formattedExEndDate) {
							dateRangeText = `${formattedExStartDate} to ${formattedExEndDate}`;
						}
					}

					this.logger.error('Found conflicting exception', {
						exceptionId: exceptionToUse.id,
						exceptionType: exceptionToUse.type,
						startDate: exceptionToUse.startDate,
						endDate: exceptionToUse.endDate,
						requestedStartDate: formattedStartDate,
						requestedEndDate: formattedEndDate
					});

					const conflictMessage =
						type === ExceptionType.OUT_OF_OFFICE
							? `You've set additional hours during this time. Please update or remove those exceptions to continue.`
							: `You've set out-of-office hours during this time. Please update or remove those exceptions to continue.`;

					throw new ConflictException(conflictMessage);
				} else {
					// No actual conflict despite the query returning results
					this.logger.log(
						'No actual date conflict found after detailed validation'
					);
				}
			}
		} catch (error: any) {
			// Log detailed error information before rethrowing
			if (error instanceof ConflictException) {
				this.logger.error('Exception conflict validation failed', {
					error: error.message,
					clinicUserId,
					startDate,
					endDate,
					type
				});
				throw error;
			} else {
				this.logger.error(
					'Unexpected error in validateExceptionConflicts',
					{
						error: {
							message: error.message,
							stack: error.stack,
							name: error.name
						},
						clinicUserId,
						startDate,
						endDate,
						type
					}
				);
				throw new InternalServerErrorException(
					`Error validating exception conflicts: ${error.message}`
				);
			}
		}
	}

	/**
	 * Validate that there are no conflicting appointments for out-of-office exceptions
	 *
	 * @param clinicUserId The clinic user ID
	 * @param startDate Start date of the exception
	 * @param endDate End date of the exception
	 * @param isFullDay Whether the exception is for a full day
	 * @param times Time slots for the exception if not full day
	 */
	private async validateNoConflictingAppointments(
		clinicUserId: string,
		startDate: Date,
		endDate: Date | null,
		isFullDay: boolean,
		times?: { startTime: string; endTime: string }[] | null
	): Promise<void> {
		try {
			this.logger.log('Validating no conflicting appointments', {
				clinicUserId,
				startDate,
				endDate,
				isFullDay,
				times
			});

			// Set end date to start date if not provided
			const effectiveEndDate = endDate || startDate;

			// Format dates for query
			const formattedStartDate = startDate.toISOString().split('T')[0];
			const formattedEndDate = effectiveEndDate
				.toISOString()
				.split('T')[0];

			// Query for appointments in the date range
			const queryBuilder = this.appointmentDoctorsRepository
				.createQueryBuilder('ad')
				.leftJoinAndSelect('ad.appointment', 'appt')
				.leftJoinAndSelect('appt.patient', 'patient')
				.where('ad.clinic_user_id = :clinicUserId', { clinicUserId })
				.andWhere('appt.deletedAt IS NULL')
				.andWhere(
					'DATE(appt.startTime) BETWEEN :formattedStartDate AND :formattedEndDate',
					{ formattedStartDate, formattedEndDate }
				);

			const query = queryBuilder.getQuery();
			this.logger.log('Appointments conflict query:', { query });

			const appointments = await queryBuilder.getMany();
			this.logger.log('Found potential conflicting appointments', {
				count: appointments.length,
				appointmentIds: appointments.map(a => a.appointment?.id)
			});

			if (appointments.length > 0) {
				// If full day, any appointment conflicts
				if (isFullDay) {
					// Get first appointment date for better error message
					const firstAppDate = new Date(
						appointments[0].appointment.startTime
					);
					const formattedDate = firstAppDate.toLocaleDateString(
						'en-US',
						{
							weekday: 'long',
							year: 'numeric',
							month: 'long',
							day: 'numeric'
						}
					);

					this.logger.error(
						'Found conflicting appointments for full day exception',
						{
							count: appointments.length,
							appointmentIds: appointments.map(
								a => a.appointment?.id
							)
						}
					);
					throw new ConflictException(
						`An appointment has already been booked in this time frame. Please reschedule the appointment to proceed.`
					);
				}

				// Otherwise, check time conflicts
				if (times && times.length > 0) {
					for (const appointment of appointments) {
						const appDate = new Date(
							appointment.appointment.startTime
						);
						const dateStr = appDate.toISOString().split('T')[0];
						const formattedDate = appDate.toLocaleDateString(
							'en-US',
							{
								weekday: 'long',
								year: 'numeric',
								month: 'long',
								day: 'numeric'
							}
						);

						// Get the appointment's start and end times
						const appStart = moment.tz(
							appointment.appointment.startTime,
							'Asia/Kolkata'
						);
						const appEnd = moment.tz(
							appointment.appointment.endTime,
							'Asia/Kolkata'
						);

						const appStartFormatted = appStart.format('h:mm A');
						const appEndFormatted = appEnd.format('h:mm A');

						this.logger.log('Checking appointment for conflicts', {
							appointmentId: appointment.appointment?.id,
							date: dateStr,
							startTime: appStart.format('HH:mm'),
							endTime: appEnd.format('HH:mm')
						});

						// Check if any exception time slot conflicts with this appointment
						for (const timeSlot of times) {
							// Make sure the timeSlot has both startTime and endTime
							if (!timeSlot.startTime || !timeSlot.endTime) {
								continue; // Skip time slots with missing data
							}

							// Parse the time slot
							const [slotStartHour, slotStartMinute] =
								timeSlot.startTime.split(':').map(Number);
							const [slotEndHour, slotEndMinute] =
								timeSlot.endTime.split(':').map(Number);

							// Create moment objects for the slot times on the appointment date
							const slotStart = moment
								.tz('Asia/Kolkata')
								.year(appDate.getFullYear())
								.month(appDate.getMonth())
								.date(appDate.getDate())
								.hour(slotStartHour)
								.minute(slotStartMinute);

							const slotEnd = moment
								.tz('Asia/Kolkata')
								.year(appDate.getFullYear())
								.month(appDate.getMonth())
								.date(appDate.getDate())
								.hour(slotEndHour)
								.minute(slotEndMinute);

							const slotStartFormatted = moment(
								`${slotStartHour}:${slotStartMinute}`,
								'HH:mm'
							).format('h:mm A');
							const slotEndFormatted = moment(
								`${slotEndHour}:${slotEndMinute}`,
								'HH:mm'
							).format('h:mm A');

							this.logger.log(
								'Comparing exception slot with appointment',
								{
									exceptionSlot: `${timeSlot.startTime}-${timeSlot.endTime}`,
									appointmentSlot: `${appStart.format('HH:mm')}-${appEnd.format('HH:mm')}`,
									slotStartBefore: slotStart.isBefore(appEnd),
									slotEndAfter: slotEnd.isAfter(appStart)
								}
							);

							// Check for overlap
							if (
								slotStart.isBefore(appEnd) &&
								slotEnd.isAfter(appStart)
							) {
								this.logger.error(
									'Found conflicting appointment with time slot',
									{
										date: dateStr,
										appointmentId:
											appointment.appointment?.id,
										appointmentSlot: `${appStart.format('HH:mm')}-${appEnd.format('HH:mm')}`,
										exceptionSlot: `${timeSlot.startTime}-${timeSlot.endTime}`
									}
								);
								throw new ConflictException(
									`An appointment has already been booked in this time frame. Please reschedule the appointment to proceed.`
								);
							}
						}
					}
				}
			}
		} catch (error: any) {
			// Log detailed error information before rethrowing
			if (error instanceof ConflictException) {
				this.logger.error('Appointment conflict validation failed', {
					error: error.message,
					clinicUserId,
					startDate,
					endDate,
					isFullDay
				});
				throw error;
			} else {
				this.logger.error(
					'Unexpected error in validateNoConflictingAppointments',
					{
						error: {
							message: error.message,
							stack: error.stack,
							name: error.name
						},
						clinicUserId,
						startDate,
						endDate,
						isFullDay
					}
				);
				throw new InternalServerErrorException(
					`Error validating appointment conflicts: ${error.message}`
				);
			}
		}
	}

	/**
	 * Validate that there are no appointments scheduled during additional hours
	 * before allowing those additional hours to be deleted or modified in a way that would
	 * no longer cover existing appointments
	 *
	 * @param clinicUserId The clinic user ID
	 * @param startDate Start date of the exception
	 * @param endDate End date of the exception
	 * @param isFullDay Whether the exception is for a full day
	 * @param times Time slots for the exception if not full day
	 * @param newTimes Optional new time slots for comparison when updating
	 */
	private async validateNoAppointmentsInAdditionalHours(
		clinicUserId: string,
		startDate: Date,
		endDate: Date | null,
		isFullDay: boolean,
		times?: { startTime: string; endTime: string }[] | null,
		newTimes?: { startTime: string; endTime: string }[] | null
	): Promise<void> {
		try {
			this.logger.log('Validating no appointments in additional hours', {
				clinicUserId,
				startDate,
				endDate,
				isFullDay,
				times,
				newTimes
			});

			// Set end date to start date if not provided
			const effectiveEndDate = endDate || startDate;

			// Format dates for query
			const formattedStartDate = startDate.toISOString().split('T')[0];
			const formattedEndDate = effectiveEndDate
				.toISOString()
				.split('T')[0];

			// Query for appointments in the date range
			const queryBuilder = this.appointmentDoctorsRepository
				.createQueryBuilder('ad')
				.leftJoinAndSelect('ad.appointment', 'appt')
				.leftJoinAndSelect('appt.patient', 'patient')
				.where('ad.clinic_user_id = :clinicUserId', { clinicUserId })
				.andWhere('appt.deletedAt IS NULL')
				.andWhere(
					'DATE(appt.startTime) BETWEEN :formattedStartDate AND :formattedEndDate',
					{ formattedStartDate, formattedEndDate }
				);

			const appointments = await queryBuilder.getMany();
			this.logger.log(
				'Found potential appointments in additional hours',
				{
					count: appointments.length,
					appointmentIds: appointments.map(a => a.appointment?.id)
				}
			);

			if (appointments.length > 0) {
				// For full-day additional hours, any appointment within the day is a conflict
				if (isFullDay) {
					this.logger.error(
						'Found appointments during full day additional hours',
						{
							count: appointments.length,
							appointmentIds: appointments.map(
								a => a.appointment?.id
							)
						}
					);
					throw new ConflictException(
						`An appointment has already been booked in this time frame. Please reschedule the appointment to proceed.`
					);
				}

				// For specific time slots, check each appointment
				if (times && times.length > 0) {
					for (const appointment of appointments) {
						const appDate = new Date(
							appointment.appointment.startTime
						);
						const dateStr = appDate.toISOString().split('T')[0];

						// Get the appointment's start and end times
						const appStart = moment.tz(
							appointment.appointment.startTime,
							'Asia/Kolkata'
						);
						const appEnd = moment.tz(
							appointment.appointment.endTime,
							'Asia/Kolkata'
						);

						// Format appointment times for display
						const appStartFormatted = appStart.format('h:mm A');
						const appEndFormatted = appEnd.format('h:mm A');

						// If we're updating (newTimes is provided), check if the appointment
						// is covered by the new time slots
						if (newTimes && newTimes.length > 0) {
							let appointmentCoveredByNewTimes = false;

							for (const newTimeSlot of newTimes) {
								// Parse the new time slot
								const [newSlotStartHour, newSlotStartMinute] =
									newTimeSlot.startTime
										.split(':')
										.map(Number);
								const [newSlotEndHour, newSlotEndMinute] =
									newTimeSlot.endTime.split(':').map(Number);

								// Create moment objects for the new slot times
								const newSlotStart = moment
									.tz('Asia/Kolkata')
									.year(appDate.getFullYear())
									.month(appDate.getMonth())
									.date(appDate.getDate())
									.hour(newSlotStartHour)
									.minute(newSlotStartMinute);

								const newSlotEnd = moment
									.tz('Asia/Kolkata')
									.year(appDate.getFullYear())
									.month(appDate.getMonth())
									.date(appDate.getDate())
									.hour(newSlotEndHour)
									.minute(newSlotEndMinute);

								// Check if appointment is fully contained in the new time slot
								if (
									appStart.isSameOrAfter(newSlotStart) &&
									appEnd.isSameOrBefore(newSlotEnd)
								) {
									appointmentCoveredByNewTimes = true;
									break;
								}
							}

							// Skip this appointment if it's covered by the new time slots
							if (appointmentCoveredByNewTimes) {
								this.logger.log(
									'Appointment is covered by new time slots, skipping conflict check',
									{
										appointmentId:
											appointment.appointment?.id,
										date: dateStr,
										time: `${appStartFormatted}-${appEndFormatted}`
									}
								);
								continue;
							}
						}

						// Check if any exception time slot contains this appointment
						for (const timeSlot of times) {
							// Make sure the timeSlot has both startTime and endTime
							if (!timeSlot.startTime || !timeSlot.endTime) {
								continue; // Skip time slots with missing data
							}

							// Parse the time slot
							const [slotStartHour, slotStartMinute] =
								timeSlot.startTime.split(':').map(Number);
							const [slotEndHour, slotEndMinute] =
								timeSlot.endTime.split(':').map(Number);

							// Create moment objects for the slot times on the appointment date
							const slotStart = moment
								.tz('Asia/Kolkata')
								.year(appDate.getFullYear())
								.month(appDate.getMonth())
								.date(appDate.getDate())
								.hour(slotStartHour)
								.minute(slotStartMinute);

							const slotEnd = moment
								.tz('Asia/Kolkata')
								.year(appDate.getFullYear())
								.month(appDate.getMonth())
								.date(appDate.getDate())
								.hour(slotEndHour)
								.minute(slotEndMinute);

							// Format slot times for display
							const slotStartFormatted = moment(
								timeSlot.startTime,
								'HH:mm'
							).format('h:mm A');
							const slotEndFormatted = moment(
								timeSlot.endTime,
								'HH:mm'
							).format('h:mm A');

							// Check if appointment falls within this additional hours slot
							if (
								(appStart.isSameOrAfter(slotStart) &&
									appStart.isBefore(slotEnd)) ||
								(appEnd.isAfter(slotStart) &&
									appEnd.isSameOrBefore(slotEnd)) ||
								(appStart.isBefore(slotStart) &&
									appEnd.isAfter(slotEnd))
							) {
								this.logger.error(
									'Found appointment within additional hours time slot',
									{
										date: dateStr,
										appointmentId:
											appointment.appointment?.id,
										appointmentSlot: `${appStartFormatted}-${appEndFormatted}`,
										exceptionSlot: `${slotStartFormatted}-${slotEndFormatted}`
									}
								);
								throw new ConflictException(
									`An appointment has already been booked in this time frame.`
								);
							}
						}
					}
				}
			}
		} catch (error: any) {
			if (error instanceof ConflictException) {
				this.logger.error(
					'Appointment within additional hours validation failed',
					{
						error: error.message,
						clinicUserId,
						startDate,
						endDate,
						isFullDay
					}
				);
				throw error;
			} else {
				this.logger.error(
					'Unexpected error in validateNoAppointmentsInAdditionalHours',
					{
						error: {
							message: error.message,
							stack: error.stack,
							name: error.name
						},
						clinicUserId,
						startDate,
						endDate,
						isFullDay
					}
				);
				throw new InternalServerErrorException(
					`Error validating appointments in additional hours: ${error.message}`
				);
			}
		}
	}
	/**
	 * Get all doctors working hours for a specific date for the calendar view
	 * This combines regular working hours with exceptions (both out-of-office and additional hours)
	 *
	 * @param date The date to get working hours for (YYYY-MM-DD)
	 * @param clinicId The clinic ID
	 * @returns A list of doctors with their available time slots for the specified date
	 */
	async getCalendarWorkingHours(
		date: string,
		clinicId: string
	): Promise<CalendarWorkingHoursResponseDto> {
		try {
			// Validate date format
			const requestedDate = moment.tz(date, 'YYYY-MM-DD', 'Asia/Kolkata');
			if (!requestedDate.isValid()) {
				throw new BadRequestException(
					'Invalid date format. Use YYYY-MM-DD'
				);
			}

			// Get the day of the week (lowercase)
			const dayOfWeek = requestedDate.format('dddd').toLowerCase();

			// Get all doctors for the clinic
			const clinicUsers = await this.clinicUsersRepository.find({
				where: {
					clinicId,
					user: {
						isActive: true
					}
				},
				relations: ['user', 'user.role']
			});

			// Filter to only include active doctors and admins
			const doctors = clinicUsers.filter(
				user =>
					(user.user?.role?.name === Role.DOCTOR ||
						user.user?.role?.name === Role.ADMIN) &&
					user.user?.isActive === true
			);

			// Get all exceptions for the doctors on the requested date
			const doctorIds = doctors.map(doctor => doctor.id);

			// Get the UTC date range for the requested local date
			const requestedDateStr = requestedDate.format('YYYY-MM-DD');

			// Create start of day in Asia/Kolkata and convert to UTC for db query
			const startOfDayLocal = moment.tz(
				`${requestedDateStr} 00:00:00`,
				'YYYY-MM-DD HH:mm:ss',
				'Asia/Kolkata'
			);
			const startOfDayUTC = startOfDayLocal
				.clone()
				.utc()
				.format('YYYY-MM-DD HH:mm:ss');

			// Create end of day in Asia/Kolkata and convert to UTC for db query
			const endOfDayLocal = moment.tz(
				`${requestedDateStr} 23:59:59`,
				'YYYY-MM-DD HH:mm:ss',
				'Asia/Kolkata'
			);
			const endOfDayUTC = endOfDayLocal
				.clone()
				.utc()
				.format('YYYY-MM-DD HH:mm:ss');

			this.logger.log('Date conversion', {
				requestedDate: requestedDateStr,
				localStartDay: startOfDayLocal.format(),
				localEndDay: endOfDayLocal.format(),
				utcStartDay: startOfDayUTC,
				utcEndDay: endOfDayUTC
			});

			// Query for exceptions with proper UTC time conversion
			const exceptions = await this.availabilityExceptionRepository
				.createQueryBuilder('exception')
				.where('exception.clinic_user_id IN (:...doctorIds)', {
					doctorIds
				})
				.andWhere(
					`(
					  (exception.start_date <= :endOfDay AND (exception.end_date IS NULL OR exception.end_date >= :startOfDay))
					)`,
					{
						startOfDay: startOfDayUTC,
						endOfDay: endOfDayUTC
					}
				)
				.getMany();

			this.logger.log('Calendar working hours request', {
				date: requestedDateStr,
				clinicId,
				dayOfWeek,
				exceptionCount: exceptions.length,
				exceptions: exceptions.map(e => ({
					id: e.id,
					type: e.type,
					startDate: moment(e.startDate).format(
						'YYYY-MM-DD HH:mm:ss'
					),
					endDate: e.endDate
						? moment(e.endDate).format('YYYY-MM-DD HH:mm:ss')
						: null,
					clinicUserId: e.clinicUserId,
					isFullDay: e.isFullDay,
					times: e.times
				}))
			});

			// Process each doctor's working hours and exceptions
			const doctorWorkingHours: DoctorWorkingHoursDto[] = [];

			for (const doctor of doctors) {
				this.logger.log(`Processing doctor ${doctor.id}`, {
					doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
					date: requestedDateStr
				});

				// Get the doctor's working hours for this day
				const workingHoursArray =
					doctor.workingHours?.workingHours?.[dayOfWeek];

				// Start with an empty array of available slots
				let availableSlots: TimeSlot[] = [];

				// Skip if no working hours defined for this day
				if (!workingHoursArray || workingHoursArray.length === 0) {
					this.logger.log(
						`No working hours defined for ${dayOfWeek} for doctor ${doctor.id}`
					);
					// Add doctor with empty available slots
					doctorWorkingHours.push({
						doctorId: doctor.id,
						doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
						availableSlots: []
					});
					continue;
				}

				// Process all working hour slots for this day
				for (const workingHoursConfig of workingHoursArray) {
					// Skip if not a working day or missing start/end times
					if (
						!workingHoursConfig.isWorkingDay ||
						!workingHoursConfig.startTime ||
						!workingHoursConfig.endTime
					) {
						continue;
					}

					availableSlots.push({
						startTime: workingHoursConfig.startTime,
						endTime: workingHoursConfig.endTime
					});
				}

				// If no working hours for this day, continue to next doctor
				if (availableSlots.length === 0) {
					this.logger.log(
						`No working day slots available for doctor ${doctor.id}`
					);
					doctorWorkingHours.push({
						doctorId: doctor.id,
						doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
						availableSlots: []
					});
					continue;
				}

				// Get the doctor's exceptions for this date
				const doctorExceptions = exceptions.filter(exception => {
					if (exception.clinicUserId !== doctor.id) {
						return false;
					}

					// Convert the exception start/end dates to the local timezone
					const exceptionStartLocal = moment(exception.startDate).tz(
						'Asia/Kolkata'
					);
					const exceptionStartDateStr =
						exceptionStartLocal.format('YYYY-MM-DD');

					let exceptionEndDateStr = exceptionStartDateStr;
					if (exception.endDate) {
						const exceptionEndLocal = moment(exception.endDate).tz(
							'Asia/Kolkata'
						);
						exceptionEndDateStr =
							exceptionEndLocal.format('YYYY-MM-DD');
					}

					// Check if the requested date falls within the exception date range
					const dateInRange =
						exceptionStartDateStr <= requestedDateStr &&
						requestedDateStr <= exceptionEndDateStr;

					this.logger.log(`Exception date check for ${doctor.id}`, {
						exceptionId: exception.id,
						exceptionType: exception.type,
						exceptionStartDateStr,
						exceptionEndDateStr,
						requestedDate: requestedDateStr,
						dateInRange
					});

					return dateInRange;
				});

				this.logger.log(
					`Exceptions for doctor ${doctor.id} on ${requestedDateStr}`,
					{
						count: doctorExceptions.length,
						exceptions: doctorExceptions.map(e => ({
							id: e.id,
							type: e.type,
							isFullDay: e.isFullDay,
							times: e.times,
							startDate: moment(e.startDate)
								.tz('Asia/Kolkata')
								.format('YYYY-MM-DD')
						}))
					}
				);

				// Process out-of-office exceptions
				const outOfOfficeExceptions = doctorExceptions.filter(
					exception => exception.type === ExceptionType.OUT_OF_OFFICE
				);

				// Check for full-day out-of-office
				const hasFullDayOutOfOffice = outOfOfficeExceptions.some(
					exception => exception.isFullDay
				);

				if (hasFullDayOutOfOffice) {
					// If there's a full-day out-of-office exception, clear all slots
					this.logger.log(
						`Full day out-of-office for doctor ${doctor.id} on ${requestedDateStr}`
					);
					availableSlots = [];
				} else {
					// Process partial out-of-office exceptions
					const partialOutOfOfficeExceptions =
						outOfOfficeExceptions.filter(
							exception =>
								!exception.isFullDay &&
								exception.times &&
								exception.times.length > 0
						);

					this.logger.log(
						`Partial out-of-office exceptions for doctor ${doctor.id}`,
						{
							count: partialOutOfOfficeExceptions.length,
							date: requestedDateStr
						}
					);

					// Apply out-of-office exceptions by splitting available slots
					for (const exception of partialOutOfOfficeExceptions) {
						if (!exception.times) continue;

						for (const time of exception.times) {
							this.logger.log(
								`Processing out-of-office time ${time.startTime}-${time.endTime} for doctor ${doctor.id}`
							);

							const newAvailableSlots: TimeSlot[] = [];

							for (const slot of availableSlots) {
								// Convert times to moments for comparison
								const slotStart = moment.tz(
									`${requestedDateStr} ${slot.startTime}`,
									'YYYY-MM-DD HH:mm',
									'Asia/Kolkata'
								);

								const slotEnd = moment.tz(
									`${requestedDateStr} ${slot.endTime}`,
									'YYYY-MM-DD HH:mm',
									'Asia/Kolkata'
								);

								const exceptionStart = moment.tz(
									`${requestedDateStr} ${time.startTime}`,
									'YYYY-MM-DD HH:mm',
									'Asia/Kolkata'
								);

								const exceptionEnd = moment.tz(
									`${requestedDateStr} ${time.endTime}`,
									'YYYY-MM-DD HH:mm',
									'Asia/Kolkata'
								);

								this.logger.log(
									`Checking slot ${slot.startTime}-${slot.endTime} against out-of-office ${time.startTime}-${time.endTime}`,
									{
										doctorId: doctor.id,
										date: requestedDateStr,
										slotStart: slotStart.format('HH:mm'),
										slotEnd: slotEnd.format('HH:mm'),
										exceptionStart:
											exceptionStart.format('HH:mm'),
										exceptionEnd:
											exceptionEnd.format('HH:mm')
									}
								);

								// Case 1: Exception is completely outside the slot
								if (
									exceptionEnd.isSameOrBefore(slotStart) ||
									exceptionStart.isSameOrAfter(slotEnd)
								) {
									newAvailableSlots.push(slot);
									this.logger.log(
										`Case 1: Exception outside slot - keeping ${slot.startTime}-${slot.endTime}`
									);
									continue;
								}

								// Case 2: Exception completely covers the slot
								if (
									exceptionStart.isSameOrBefore(slotStart) &&
									exceptionEnd.isSameOrAfter(slotEnd)
								) {
									// Skip this slot entirely
									this.logger.log(
										`Case 2: Exception covers slot - removing ${slot.startTime}-${slot.endTime}`
									);
									continue;
								}

								// Case 3: Exception starts before slot and ends within slot
								if (
									exceptionStart.isBefore(slotStart) &&
									exceptionEnd.isAfter(slotStart) &&
									exceptionEnd.isBefore(slotEnd)
								) {
									const newSlot = {
										startTime: exceptionEnd.format('HH:mm'),
										endTime: slot.endTime
									};
									newAvailableSlots.push(newSlot);
									this.logger.log(
										`Case 3: Exception starts before and ends within - new slot ${newSlot.startTime}-${newSlot.endTime}`
									);
									continue;
								}

								// Case 4: Exception starts within slot and ends after slot
								if (
									exceptionStart.isAfter(slotStart) &&
									exceptionStart.isBefore(slotEnd) &&
									exceptionEnd.isAfter(slotEnd)
								) {
									const newSlot = {
										startTime: slot.startTime,
										endTime: exceptionStart.format('HH:mm')
									};
									newAvailableSlots.push(newSlot);
									this.logger.log(
										`Case 4: Exception starts within and ends after - new slot ${newSlot.startTime}-${newSlot.endTime}`
									);
									continue;
								}

								// Case 4-A: Exception starts before slot and ends in the middle of slot
								if (
									exceptionStart.isBefore(slotStart) &&
									exceptionEnd.isAfter(slotStart) &&
									exceptionEnd.isBefore(slotEnd)
								) {
									// Keep only the portion after exception ends
									newAvailableSlots.push({
										startTime: exceptionEnd.format('HH:mm'),
										endTime: slot.endTime
									});

									this.logger.log(
										`Case 4-A: Exception cuts start of slot - new slot ${exceptionEnd.format('HH:mm')}-${slot.endTime}`
									);
									continue;
								}

								// Case 4-B: Exception starts at the beginning of slot and ends before slot ends
								if (
									exceptionStart.isSame(slotStart) &&
									exceptionEnd.isBefore(slotEnd)
								) {
									// Keep only the portion after exception ends
									newAvailableSlots.push({
										startTime: exceptionEnd.format('HH:mm'),
										endTime: slot.endTime
									});

									this.logger.log(
										`Case 4-B: Exception cuts start of slot - new slot ${exceptionEnd.format('HH:mm')}-${slot.endTime}`
									);
									continue;
								}

								// Case 4-C: Exception starts at the beginning of slot and ends after slot ends
								if (
									exceptionStart.isSame(slotStart) &&
									exceptionEnd.isSameOrAfter(slotEnd)
								) {
									// Skip this slot entirely as it's fully covered
									this.logger.log(
										`Case 4-C: Exception covers entire slot - removing ${slot.startTime}-${slot.endTime}`
									);
									continue;
								}

								// Case 5: Exception is fully enclosed within the slot
								if (
									(exceptionStart.isAfter(slotStart) ||
										exceptionStart.isSame(slotStart)) &&
									(exceptionEnd.isBefore(slotEnd) ||
										exceptionEnd.isSame(slotEnd))
								) {
									// If exception exactly matches the slot boundaries, remove the slot
									if (
										exceptionStart.isSame(slotStart) &&
										exceptionEnd.isSame(slotEnd)
									) {
										this.logger.log(
											`Case 5 (exact match): Exception exactly matches slot - removing ${slot.startTime}-${slot.endTime}`
										);
										continue;
									}

									// If exception starts at slot start, keep only second part
									if (exceptionStart.isSame(slotStart)) {
										const secondSlot = {
											startTime:
												exceptionEnd.format('HH:mm'),
											endTime: slot.endTime
										};

										newAvailableSlots.push(secondSlot);
										this.logger.log(
											`Case 5 (start match): Exception starts with slot - new slot ${secondSlot.startTime}-${secondSlot.endTime}`
										);
										continue;
									}

									// If exception ends at slot end, keep only first part
									if (exceptionEnd.isSame(slotEnd)) {
										const firstSlot = {
											startTime: slot.startTime,
											endTime:
												exceptionStart.format('HH:mm')
										};

										newAvailableSlots.push(firstSlot);
										this.logger.log(
											`Case 5 (end match): Exception ends with slot - new slot ${firstSlot.startTime}-${firstSlot.endTime}`
										);
										continue;
									}

									// Split the slot into two parts (standard Case 5)
									const firstSlot = {
										startTime: slot.startTime,
										endTime: exceptionStart.format('HH:mm')
									};

									const secondSlot = {
										startTime: exceptionEnd.format('HH:mm'),
										endTime: slot.endTime
									};

									newAvailableSlots.push(firstSlot);
									newAvailableSlots.push(secondSlot);

									this.logger.log(
										`Case 5: Exception splits slot - new slots ${firstSlot.startTime}-${firstSlot.endTime} and ${secondSlot.startTime}-${secondSlot.endTime}`
									);
									continue;
								}
							}

							availableSlots = newAvailableSlots;
						}
					}

					// Process additional-hours exceptions
					const additionalHoursExceptions = doctorExceptions.filter(
						exception =>
							exception.type === ExceptionType.ADDITIONAL_HOURS
					);

					this.logger.log(
						`Additional hours exceptions for doctor ${doctor.id} on ${requestedDateStr}`,
						{
							count: additionalHoursExceptions.length,
							exceptions: additionalHoursExceptions.map(e => ({
								id: e.id,
								times: e.times
							}))
						}
					);

					// Add additional hours to available slots
					for (const exception of additionalHoursExceptions) {
						if (!exception.times) continue;

						for (const time of exception.times) {
							// Check if this additional time overlaps with any existing slot
							const additionalStart = moment.tz(
								`${requestedDateStr} ${time.startTime}`,
								'YYYY-MM-DD HH:mm',
								'Asia/Kolkata'
							);

							const additionalEnd = moment.tz(
								`${requestedDateStr} ${time.endTime}`,
								'YYYY-MM-DD HH:mm',
								'Asia/Kolkata'
							);
							let overlaps = false;

							this.logger.log(
								`Processing additional time ${time.startTime}-${time.endTime} for doctor ${doctor.id}`
							);

							// Check for overlaps with existing slots
							for (let i = 0; i < availableSlots.length; i++) {
								const slot = availableSlots[i];
								const slotStart = moment.tz(
									`${requestedDateStr} ${slot.startTime}`,
									'YYYY-MM-DD HH:mm',
									'Asia/Kolkata'
								);

								const slotEnd = moment.tz(
									`${requestedDateStr} ${slot.endTime}`,
									'YYYY-MM-DD HH:mm',
									'Asia/Kolkata'
								);

								this.logger.log(
									`Checking additional time against slot`,
									{
										additionalTime: `${time.startTime}-${time.endTime}`,
										slot: `${slot.startTime}-${slot.endTime}`,
										overlapsStart:
											additionalStart.isBefore(slotEnd) &&
											additionalEnd.isAfter(slotStart),
										overlapsEnd:
											slotStart.isBefore(additionalEnd) &&
											slotEnd.isAfter(additionalStart)
									}
								);

								// If the additional time overlaps with an existing slot, merge them
								if (
									(additionalStart.isBefore(slotEnd) &&
										additionalEnd.isAfter(slotStart)) ||
									(slotStart.isBefore(additionalEnd) &&
										slotEnd.isAfter(additionalStart))
								) {
									// Merge the slots
									const newStart = moment.min(
										additionalStart,
										slotStart
									);
									const newEnd = moment.max(
										additionalEnd,
										slotEnd
									);
									availableSlots[i] = {
										startTime: newStart.format('HH:mm'),
										endTime: newEnd.format('HH:mm')
									};
									overlaps = true;
									this.logger.log(
										`Merged with existing slot - new slot ${availableSlots[i].startTime}-${availableSlots[i].endTime}`
									);
									break;
								}
							}

							// If no overlap, add as a new slot
							if (!overlaps) {
								availableSlots.push({
									startTime: time.startTime,
									endTime: time.endTime
								});
								this.logger.log(
									`Added new additional time slot ${time.startTime}-${time.endTime}`
								);
							}
						}
					}
				}

				// Sort available slots by start time
				availableSlots.sort((a, b) => {
					return moment
						.tz(
							`${requestedDateStr} ${a.startTime}`,
							'YYYY-MM-DD HH:mm',
							'Asia/Kolkata'
						)
						.diff(
							moment.tz(
								`${requestedDateStr} ${b.startTime}`,
								'YYYY-MM-DD HH:mm',
								'Asia/Kolkata'
							)
						);
				});

				// Merge overlapping slots
				const mergedSlots: TimeSlot[] = [];
				for (const slot of availableSlots) {
					if (mergedSlots.length === 0) {
						mergedSlots.push(slot);
						continue;
					}

					const lastSlot = mergedSlots[mergedSlots.length - 1];
					const lastSlotEnd = moment.tz(
						`${requestedDateStr} ${lastSlot.endTime}`,
						'YYYY-MM-DD HH:mm',
						'Asia/Kolkata'
					);

					const currentSlotStart = moment.tz(
						`${requestedDateStr} ${slot.startTime}`,
						'YYYY-MM-DD HH:mm',
						'Asia/Kolkata'
					);

					// If the current slot starts at or before the end of the last slot, merge them
					if (currentSlotStart.isSameOrBefore(lastSlotEnd)) {
						const currentSlotEnd = moment.tz(
							`${requestedDateStr} ${slot.endTime}`,
							'YYYY-MM-DD HH:mm',
							'Asia/Kolkata'
						);

						if (currentSlotEnd.isAfter(lastSlotEnd)) {
							lastSlot.endTime = slot.endTime;
							this.logger.log(
								`Merged overlapping slots - updated to ${lastSlot.startTime}-${lastSlot.endTime}`
							);
						}
					} else {
						mergedSlots.push(slot);
					}
				}

				// Add the doctor with their available slots
				doctorWorkingHours.push({
					doctorId: doctor.id,
					doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
					availableSlots: mergedSlots
				});
			}

			return {
				doctors: doctorWorkingHours
			};
		} catch (error: any) {
			this.logger.error('Error in getCalendarWorkingHours', {
				error: {
					message: error.message,
					stack: error.stack
				},
				date,
				clinicId
			});
			throw error;
		}
	}

	/**
	 * Check if an exception has already ended (is in the past)
	 *
	 * @param exception The exception entity or exception data
	 * @returns True if the exception has already ended
	 */
	private isExceptionEnded(exception: {
		startDate: Date | string;
		endDate?: Date | string | null;
		isFullDay: boolean;
		times?: { startTime: string; endTime: string }[] | null;
	}): boolean {
		const now = new Date();
		const today = new Date(
			now.getFullYear(),
			now.getMonth(),
			now.getDate()
		);

		// Convert startDate to Date object if it's a string
		const startDate =
			typeof exception.startDate === 'string'
				? new Date(exception.startDate)
				: exception.startDate;

		// Convert endDate to Date object if it exists and is a string
		const endDate = exception.endDate
			? typeof exception.endDate === 'string'
				? new Date(exception.endDate)
				: exception.endDate
			: null;

		// If the exception has an end date
		if (endDate) {
			// For full day exceptions
			if (exception.isFullDay) {
				// Set to end of day (23:59:59)
				const endOfDay = new Date(endDate);
				endOfDay.setHours(23, 59, 59, 999);
				return endOfDay < now;
			}

			// If end date is in the past, check the latest time slot
			if (endDate < today) {
				return true;
			}

			// If end date is today, check the time slots
			if (
				endDate.getFullYear() === today.getFullYear() &&
				endDate.getMonth() === today.getMonth() &&
				endDate.getDate() === today.getDate()
			) {
				// If no time slots or full day, the exception ends at the end of the day
				if (!exception.times || exception.times.length === 0) {
					return false; // Not ended yet since it's still today
				}

				// Find the latest end time
				let latestEndTime = '00:00';
				for (const timeSlot of exception.times) {
					if (timeSlot.endTime > latestEndTime) {
						latestEndTime = timeSlot.endTime;
					}
				}

				// Check if the latest end time has passed
				const [endHour, endMinute] = latestEndTime
					.split(':')
					.map(Number);
				const endTimeToday = new Date();
				endTimeToday.setHours(endHour, endMinute, 0, 0);

				return endTimeToday < now;
			}

			// End date is in the future
			return false;
		}

		// If there's no end date, use the start date as the end date
		// For full day exceptions
		if (exception.isFullDay) {
			// Set to end of day (23:59:59)
			const endOfDay = new Date(startDate);
			endOfDay.setHours(23, 59, 59, 999);
			return endOfDay < now;
		}

		// Start date is in the past
		if (startDate < today) {
			return true;
		}

		// If start date is today, check the time slots
		if (
			startDate.getFullYear() === today.getFullYear() &&
			startDate.getMonth() === today.getMonth() &&
			startDate.getDate() === today.getDate()
		) {
			// If no time slots or full day, the exception ends at the end of the day
			if (!exception.times || exception.times.length === 0) {
				return false; // Not ended yet since it's still today
			}

			// Find the latest end time
			let latestEndTime = '00:00';
			for (const timeSlot of exception.times) {
				if (timeSlot.endTime > latestEndTime) {
					latestEndTime = timeSlot.endTime;
				}
			}

			// Check if the latest end time has passed
			const [endHour, endMinute] = latestEndTime.split(':').map(Number);
			const endTimeToday = new Date();
			endTimeToday.setHours(endHour, endMinute, 0, 0);

			return endTimeToday < now;
		}

		// Start date is in the future
		return false;
	}
}
