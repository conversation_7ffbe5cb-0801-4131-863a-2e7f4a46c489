import {
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WinstonLogger } from '../../logger/winston-logger.service';
import * as AWS from 'aws-sdk';
import * as path from 'path';
import * as mime from 'mime';
@Injectable()
export class S3Service {
	private readonly bucketName: string;
	private readonly cloudFrontDomain: string;
	private readonly s3Client: AWS.S3;
	private readonly cfUrlSigner: AWS.CloudFront.Signer;

	constructor(
		// @Inject(S3Client) private readonly s3Client: S3Client,
		private configService: ConfigService,
		private loggerService: WinstonLogger
	) {
		this.bucketName = process.env.AWS_S3_BUCKET || '';
		//this.configService.get<string>('AWS_S3_BUCKET_NAME') || '';
		this.cloudFrontDomain =
			this.configService.get<string>(
				'AWS_CLOUDFRONT_USER_FILES_DISTRIBUTION_DOMAIN_NAME'
			) || '';
		this.s3Client = new AWS.S3({
			region: 'ap-south-1',
			accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
			secretAccessKey: process.env.AWS_S3_SECRET_KEY,
			params: {
				Bucket: process.env.AWS_S3_BUCKET
			}
		});

		this.cfUrlSigner = new AWS.CloudFront.Signer(
			process.env.AWS_CLOUD_FRONT_KEY_ID as string,
			process.env.AWS_CLOUD_FRONT_PRIVATE_KEY?.replace(
				/\\n/g,
				'\n'
			) as string
		);
	}

	getSignedUrl = async (fileKey: string) =>
		new Promise((resolve, reject) => {
			const extension = path.extname(fileKey);
			const extensionContentType = mime.lookup(extension);
			const contentType = extensionContentType.split('/');
			const actualContentType = `${contentType[0]}/`;
			console.log(this.s3Client);

			const params = {
				Bucket: this.bucketName,
				Expires: 3600,
				Fields: {
					key: fileKey
				},
				Conditions: [
					['starts-with', '$Content-Type', actualContentType]
				]
			};

			this.s3Client.createPresignedPost(params, (err, data) => {
				if (err) {
					return reject(
						new InternalServerErrorException('some error occurred')
					);
				}

				return resolve(data);
			});
		});

	getViewPreSignedUrl = async (fileKey: string): Promise<string> => {
		return new Promise((resolve, reject) => {
			this.s3Client.headObject(
				{ Bucket: this.bucketName, Key: fileKey },
				err => {
					if (err) {
						if (err.code === 'NotFound') {
							this.loggerService.error('File not found in S3', {
								fileKey
							});
							return reject(
								new NotFoundException('File not found')
							);
						}
						this.loggerService.error(
							'Error checking file existence in S3',
							{ error: err, fileKey }
						);
						return reject(
							new InternalServerErrorException(
								'Error checking file existence'
							)
						);
					}

					// If the object exists, generate the pre-signed URL
					const params = {
						Bucket: this.bucketName,
						Key: fileKey,
						Expires: 3600, // URL expires in 1 hour
						ResponseContentDisposition: 'inline'
					};

					this.s3Client.getSignedUrl(
						'getObject',
						params,
						(err, url) => {
							if (err) {
								this.loggerService.error(
									'Error generating view pre-signed URL',
									{ error: err, fileKey }
								);
								return reject(
									new InternalServerErrorException(
										'Failed to generate view pre-signed URL'
									)
								);
							}

							this.loggerService.log(
								'Generated view pre-signed URL',
								{ fileKey }
							);
							resolve(url);
						}
					);
				}
			);
		});
	};

	async deleteFile(fileKey: string): Promise<void> {
		const params = {
			Bucket: this.bucketName,
			Key: fileKey
		};

		try {
			await this.s3Client.deleteObject(params).promise();
			this.loggerService.log(
				`Successfully deleted file ${fileKey} from S3`
			);
		} catch (error) {
			this.loggerService.error(
				`Error deleting file ${fileKey} from S3`,
				error
			);
			throw new InternalServerErrorException(
				`Failed to delete file from S3`
			);
		}
	}

	getDownloadPreSignedUrl = async (
		fileKey: string,
		fileType: string | null
	): Promise<string> => {
		return new Promise((resolve, reject) => {
			this.s3Client.headObject(
				{ Bucket: this.bucketName, Key: fileKey },
				err => {
					if (err) {
						if (err.code === 'NotFound') {
							this.loggerService.error('File not found in S3', {
								fileKey
							});
							return reject(
								new NotFoundException('File not found')
							);
						}
						this.loggerService.error(
							'Error checking file existence in S3',
							{ error: err, fileKey }
						);
						return reject(
							new InternalServerErrorException(
								'Error checking file existence'
							)
						);
					}

					// If the object exists, generate the pre-signed URL for download
					const params = {
						Bucket: this.bucketName,
						Key: fileKey,
						Expires: 3600, // URL expires in 1 hour
						ResponseContentDisposition: `attachment; filename="${path.basename(fileKey)}${fileKey.includes('.') ? '' : fileType === 'img' ? '.png' : '.pdf'}"` // This prompts download
					};

					this.s3Client.getSignedUrl(
						'getObject',
						params,
						(err, url) => {
							if (err) {
								this.loggerService.error(
									'Error generating download pre-signed URL',
									{ error: err, fileKey }
								);
								return reject(
									new InternalServerErrorException(
										'Failed to generate download pre-signed URL'
									)
								);
							}

							this.loggerService.log(
								'Generated download pre-signed URL',
								{ fileKey }
							);
							resolve(url);
						}
					);
				}
			);
		});
	};

	uploadPdfToS3 = async (pdfBuffer: any, fileKey: string) => {

		return new Promise((resolve) => {
			const params = {
				Bucket: this.bucketName,
				Key: fileKey,
				Body: pdfBuffer,
				ContentType: 'application/pdf'
			};
			this.s3Client.putObject(params, (err: any, data: any) => {
				if (err) {
					console.log('error', err);
				}
				return resolve(data);
			});

		})
	};

	uploadBuffer = async (
		buffer: Buffer,
		fileKey: string,
		contentType: string = 'application/octet-stream'
	) => {
		const params = {
			Bucket: this.bucketName,
			Key: fileKey,
			Body: buffer,
			ContentType: contentType
		};

		return this.s3Client.putObject(params, (err: any, data: any) => {
			if (err) {
				console.log('Upload error:', err);
			}
			return data;
		});
	};
	async getCloudfrontSignedUrl(fileKey: string): Promise<string> {
		console.log(`Starting to generate signed URL for fileKey: ${fileKey}`);

		this.loggerService.log(
			`Starting to generate signed URL for fileKey: ${fileKey}`
		);

		const encodedFileKey = encodeURIComponent(fileKey);
		const params = {
			url: `${process.env.AWS_CLOUD_FRONT_URL}/${encodedFileKey}`,
			expires: Math.floor((Date.now() + 1 * 60 * 60 * 1000) / 1000) // 1 day expiry
		};
		this.loggerService.debug(
			`Generated URL and expiration: ${params.url}, Expires at: ${params.expires}`
		);

		return new Promise((resolve, reject) => {
			this.cfUrlSigner.getSignedUrl(params, (err, data) => {
				if (err) {
					this.loggerService.error(
						`Error generating signed URL for ${fileKey}`,
						err.stack
					);
					console.log('Error in file url generations', err);

					return reject(
						new InternalServerErrorException(
							'Some error occurred while generating signed URL'
						)
					);
				}

				this.loggerService.log(
					`Successfully generated signed URL for ${fileKey}`
				);
				console.log(
					`Successfully generated signed URL for ${fileKey}`,
					data
				);

				return resolve(data);
			});
		});
	}
}

// import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
// import { Inject, Injectable } from '@nestjs/common';
// import { ConfigService } from '@nestjs/config';
// import { WinstonLogger } from '../../logger/winston-logger.service';

// @Injectable()
// export class S3Service {
// 	private readonly bucketName: string;
// 	private readonly cloudFrontDomain: string;

// 	constructor(
// 		@Inject(S3Client) private readonly s3Client: S3Client,
// 		private configService: ConfigService,
// 		private loggerService: WinstonLogger
// 	) {
// 		this.bucketName =
// 			this.configService.get<string>('AWS_S3_BUCKET_NAME') || '';
// 		this.cloudFrontDomain =
// 			this.configService.get<string>(
// 				'AWS_CLOUDFRONT_USER_FILES_DISTRIBUTION_DOMAIN_NAME'
// 			) || '';
// 	}

// 	async uploadFile(fileData: {
// 		fileName: string;
// 		file: Express.Multer.File;
// 	}): Promise<{ coldStorageLink: string; CDNLink: string } | null> {
// 		try {
// 			const { fileName, file } = fileData;
// 			const params = {
// 				Bucket: this.bucketName,
// 				Key: fileName,
// 				Body: file.buffer,
// 				ContentType: file.mimetype
// 			};

// 			await this.s3Client.send(new PutObjectCommand(params));

// 			const coldStorageLink = `https://${this.bucketName}.s3.amazonaws.com/${fileName}`;
// 			const cDNLink = `https://${this.cloudFrontDomain}/${fileName}`;
// 			this.loggerService.log(
// 				`[S3Service][uploadFile]: Successfully uploaded ${fileName} to S3 bucket: ${this.bucketName}`
// 			);
// 			return { coldStorageLink, CDNLink: cDNLink };
// 		} catch (err) {
// 			if (err instanceof Error) {
// 				this.loggerService.error(
// 					`[S3Service][uploadFile]: Error in uploading file to S3. ${err.message}`,
// 					err.stack
// 				);
// 			} else {
// 				this.loggerService.error(
// 					'[S3Service][uploadFile]: Error in uploading file to S3.',
// 					JSON.stringify(err)
// 				);
// 			}
// 			return null;
// 		}
// 	}

// 	async uploadFiles(
// 		fileData: Array<{ fileName: string; file: Express.Multer.File }>
// 	): Promise<{ coldStorageLink: string; CDNLink: string }[]> {
// 		try {
// 			const uploadedUrls: { coldStorageLink: string; CDNLink: string }[] =
// 				[];

// 			const uploadPromises = fileData.map(async ({ fileName, file }) => {
// 				const params = {
// 					Bucket: this.bucketName,
// 					Key: fileName,
// 					Body: file.buffer,
// 					ContentType: file.mimetype
// 				};

// 				await this.s3Client.send(new PutObjectCommand(params));

// 				const coldStorageLink = `https://${this.bucketName}.s3.amazonaws.com/${fileName}`;
// 				const cDNLink = `https://${this.cloudFrontDomain}/${fileName}`;
// 				uploadedUrls.push({ coldStorageLink, CDNLink: cDNLink });

// 				this.loggerService.log(
// 					`[S3Service][uploadFiles]: Successfully uploaded ${fileName} to S3 bucket: ${this.bucketName}`
// 				);
// 			});
// 			await Promise.all(uploadPromises);
// 			return uploadedUrls;
// 		} catch (err) {
// 			if (err instanceof Error) {
// 				this.loggerService.error(
// 					`[S3Service][uploadFiles]: Error in uploading files to S3. ${err.message}`,
// 					err.stack
// 				);
// 			} else {
// 				this.loggerService.error(
// 					'[S3Service][uploadFiles]: Error in uploading files to S3.',
// 					JSON.stringify(err)
// 				);
// 			}
// 			return [];
// 		}
// 	}
// }
