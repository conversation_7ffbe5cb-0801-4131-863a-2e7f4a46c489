import { QueueConfig } from './interfaces/queue-config.interface';
import { ProcessEMRHandler } from './handlers/process_create_emr.handler';
import { ProcessSendDocumentsHandler } from './handlers/process_send_documents.handler';
import { ProcessInvoiceTasksHandler } from './handlers/process_invoice_tasks.handler';
import { ProcessAvailabilityUpdateHandler } from './handlers/process_availability_update.handler';
import { ProcessAvailabilityMaintenanceHandler } from './handlers/process_availability_maintenance.handler';
import { ProcessGoogleCalendarSyncHandler } from './handlers/process_google_calendar_sync.handler';

export const queues: Record<string, QueueConfig> = {
	NidanaCreateEMR: {
		name: 'NidanaCreateEMR',
		delaySeconds: 0,
		handler: ProcessEMRHandler,
		maxReceiveCount: 5,
		messageRetentionPeriod: 86400,
		dlqName: 'NidanaDeadLetterQueue'
	},
	NidanaSendDocuments: {
		name: 'NidanaSendDocuments',
		delaySeconds: 0,
		handler: ProcessSendDocumentsHandler,
		maxReceiveCount: 5,
		messageRetentionPeriod: 86400,
		dlqName: 'NidanaDeadLetterQueue'
	},
	NidanaInvoiceTasks: {
		name: 'NidanaInvoiceTasks',
		delaySeconds: 0,
		handler: ProcessInvoiceTasksHandler,
		maxReceiveCount: 5,
		messageRetentionPeriod: 86400,
		dlqName: 'NidanaDeadLetterQueue'
	},
	NidanaAvailabilityUpdate: {
		name: 'NidanaAvailabilityUpdate',
		delaySeconds: 0,
		handler: ProcessAvailabilityUpdateHandler,
		maxReceiveCount: 5,
		messageRetentionPeriod: 86400,
		dlqName: 'NidanaDeadLetterQueue'
	},
	NidanaAvailabilityMaintenance: {
		name: 'NidanaAvailabilityMaintenance',
		delaySeconds: 0,
		handler: ProcessAvailabilityMaintenanceHandler,
		maxReceiveCount: 3, // Fewer retries for maintenance tasks
		messageRetentionPeriod: 86400,
		dlqName: 'NidanaDeadLetterQueue'
	},
	NidanaGoogleCalendarSync: {
		name: 'NidanaGoogleCalendarSync',
		delaySeconds: 0,
		handler: ProcessGoogleCalendarSyncHandler,
		maxReceiveCount: 5, // Allow retries for network issues
		messageRetentionPeriod: 86400,
		dlqName: 'NidanaDeadLetterQueue'
	}
};

export function getEnvSpecificQueues(env: string): Record<string, QueueConfig> {
	// Use 'Uat' prefix when environment is 'development', otherwise capitalize first letter
	const prefix =
		env === 'development'
			? 'Uat'
			: env.charAt(0).toUpperCase() + env.slice(1);
	const envSpecificQueues: Record<string, QueueConfig> = {};

	Object.entries(queues).forEach(([key, value]) => {
		envSpecificQueues[key] = {
			name: `${prefix}${value.name}`,
			delaySeconds: value.delaySeconds,
			handler: value.handler,
			maxReceiveCount: value.maxReceiveCount,
			messageRetentionPeriod: value.messageRetentionPeriod,
			dlqName: `${prefix}${value.dlqName}`
		};
	});

	return envSpecificQueues;
}
