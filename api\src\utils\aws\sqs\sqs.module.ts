import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { DefaultServiceHandler } from './handlers/default-service.handler';
import { SqsService } from './sqs.service';
import { ProcessEMRHandler } from './handlers/process_create_emr.handler';
import { ProcessSendDocumentsHandler } from './handlers/process_send_documents.handler';
import { ProcessInvoiceTasksHandler } from './handlers/process_invoice_tasks.handler';
import { ProcessAvailabilityUpdateHandler } from './handlers/process_availability_update.handler';
import { ProcessAvailabilityMaintenanceHandler } from './handlers/process_availability_maintenance.handler';
import { ProcessGoogleCalendarSyncHandler } from './handlers/process_google_calendar_sync.handler';
import { EmrModule } from '../../../emr/emr.module';
import { SendDocuments } from '../../common/send-document.service';
import { S3Service } from '../s3/s3.service';
import { SESModule } from '../ses/ses.module';
import { WhatsappModule } from '../../whatsapp-integration/whatsapp.module';
import { AppointmentsModule } from '../../../appointments/appointments.module';
import { AvailabilityModule } from '../../../availability/availability.module';
import { GoogleCalendarModule } from '../../../google-calendar/google-calendar.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PatientVaccination } from '../../../patient-vaccinations/entities/patient-vaccinations.entity';
import { Patient } from '../../../patients/entities/patient.entity';
import { InvoiceEntity } from '../../../invoice/entities/invoice.entity';
import { ClinicProductEntity } from '../../../clinic-products/entities/clinic-product.entity';
import { ClinicVaccinationEntity } from '../../../clinic-vaccinations/entities/clinic-vaccination.entity';
import { ClinicMedicationEntity } from '../../../clinic-medications/entities/clinic-medication.entity';
import { ClinicConsumableEntity } from '../../../clinic-consumables/entities/clinic-consumable.entity';
import { PatientRemindersModule } from '../../../patient-reminders/patient-reminder.module';
import { GlobalReminderModule } from '../../../patient-global-reminders/global-reminders.module';
import * as dotenv from 'dotenv';
import { ClinicEntity } from '../../../clinics/entities/clinic.entity';
import { PaymentDetailsEntity } from '../../../payment-details/entities/payment-details.entity';
import { OwnerBrand } from '../../../owners/entities/owner-brand.entity';
import { PatientsModule } from '../../../patients/patients.module';
import { ClinicLabReportModule } from '../../../clinic-lab-report/clinic-lab-report.module';
import { ClinicUser } from '../../../clinics/entities/clinic-user.entity';
import { LoggerModule } from '../../logger/logger-module';
import { AppointmentDetailsEntity } from '../../../appointments/entities/appointment-details.entity';
import { AppointmentEntity } from '../../../appointments/entities/appointment.entity';
import { User } from '../../../users/entities/user.entity';
import { StatementModule } from '../../../statement/statement.module';
import { AppointmentDoctorsEntity } from '../../../appointments/entities/appointment-doctor.entity';
import { TabActivityModule } from '../../../tab-activity/tab-activity.module';

// Load environment variables
dotenv.config({ path: '.env' });

@Module({
	providers: [SqsService],
	exports: [SqsService]
})
export class SqsModule {
	static forRoot(isSqsEnabled: boolean): DynamicModule {
		console.log(
			'isSqsEnabled',
			isSqsEnabled || process.env.NODE_ENV === 'development'
		);
		if (isSqsEnabled || process.env.NODE_ENV === 'development') {
			SqsService.enableInitialization();
			return {
				module: SqsModule,
				imports: [
					forwardRef(() => EmrModule),
					forwardRef(() => AvailabilityModule),
					forwardRef(() => GoogleCalendarModule),
					LoggerModule,
					SESModule,
					WhatsappModule,
					AppointmentsModule,
					PatientRemindersModule,
					GlobalReminderModule,
					PatientsModule,
					ClinicLabReportModule,
					TabActivityModule,
					forwardRef(() => StatementModule),
					TypeOrmModule.forFeature([
						PatientVaccination,
						Patient,
						InvoiceEntity,
						ClinicProductEntity,
						ClinicVaccinationEntity,
						ClinicMedicationEntity,
						ClinicConsumableEntity,
						AppointmentDoctorsEntity,
						AppointmentEntity,
						User,
						ClinicEntity,
						PaymentDetailsEntity,
						OwnerBrand,
						ClinicUser,
						AppointmentDetailsEntity
					])
				],
				providers: [
					SqsService,
					DefaultServiceHandler,
					ProcessEMRHandler,
					ProcessSendDocumentsHandler,
					ProcessInvoiceTasksHandler,
					ProcessAvailabilityUpdateHandler,
					ProcessAvailabilityMaintenanceHandler,
					ProcessGoogleCalendarSyncHandler,
					SendDocuments,
					S3Service
				],
				exports: [
					SqsService,
					DefaultServiceHandler,
					ProcessEMRHandler,
					ProcessSendDocumentsHandler,
					ProcessInvoiceTasksHandler,
					ProcessAvailabilityUpdateHandler,
					ProcessAvailabilityMaintenanceHandler,
					ProcessGoogleCalendarSyncHandler
				]
			};
		} else {
			// If SQS is not enabled, return an empty module
			SqsService.disableInitialization();
			return {
				module: SqsModule,
				imports: [],
				providers: [SqsService],
				exports: [SqsService]
			};
		}
	}
}
