import {
	Injectable,
	OnApplicationBootstrap,
	OnModuleInit
} from '@nestjs/common';
import {
	SQSClient,
	SendMessageCommand,
	ReceiveMessageCommand,
	QueueAttributeName
} from '@aws-sdk/client-sqs';
import { ConfigService } from '@nestjs/config';
import { getEnvSpecificQueues } from './sqs-queue.config';
import { QueueConfig } from './interfaces/queue-config.interface';
import { WinstonLogger } from '../../logger/winston-logger.service';
import { SendMessageParams } from './interfaces/send-message-params.interface';
import { ModuleRef } from '@nestjs/core';
import { Consumer } from 'sqs-consumer';

@Injectable()
export class SqsService implements OnApplicationBootstrap {
	private sqsClient: SQSClient;
	private readonly queues: Record<string, QueueConfig>;
	private consumers: Map<string, Consumer> = new Map();
	private static shouldInitialize = false;
	private isInitialized = false;

	constructor(
		private configService: ConfigService,
		private loggerService: WinstonLogger,
		private readonly moduleRef: ModuleRef
	) {
		this.sqsClient = new SQSClient({
			region: this.configService.get<string>('aws.sqs.region'),
			endpoint: this.configService.get<string>('aws.sqs.queueUrl'),
			credentials: {
				accessKeyId:
					this.configService.get<string>('AWS_SQS_ACCESS_KEY_ID') ||
					'elasticmq',
				secretAccessKey:
					this.configService.get<string>('AWS_SQS_SECRET_KEY') ||
					'elasticmq'
			}
		});
		this.queues = getEnvSpecificQueues(process.env.NODE_ENV || 'dev');
	}

	async onApplicationBootstrap() {
		if (SqsService.shouldInitialize && !this.isInitialized) {
			this.loggerService.log('-----------SQS initialization starting');
			this.isInitialized = true;
			await this.initializeQueues();
			this.loggerService.log('-----------SQS initialization completed');
			this.loggerService.log(
				`Active consumers: ${Array.from(this.consumers.keys()).join(', ')}`
			);
		} else if (this.isInitialized) {
			this.loggerService.warn(
				'SQS service already initialized, skipping duplicate initialization'
			);
		} else {
			this.loggerService.warn('SQS initialization disabled');
		}
	}

	static enableInitialization() {
		SqsService.shouldInitialize = true;
	}

	static disableInitialization() {
		SqsService.shouldInitialize = false;
	}

	private async initializeQueues() {
		for (const queueKey in this.queues) {
			if (this.queues.hasOwnProperty(queueKey)) {
				const queue = this.queues[queueKey];
				await this.receiveMessage(queue);
			}
		}
	}

	/**
	 * Stop all SQS consumers for graceful shutdown
	 */
	async stopAllConsumers(): Promise<void> {
		this.loggerService.log('Stopping all SQS consumers...');

		for (const [queueName, consumer] of this.consumers) {
			try {
				consumer.stop();
				this.loggerService.log(
					`Stopped SQS consumer for queue ${queueName}`
				);
			} catch (error) {
				this.loggerService.error(
					`Error stopping consumer for queue ${queueName}`,
					error
				);
			}
		}

		this.consumers.clear();
		this.isInitialized = false;
		this.loggerService.log('All SQS consumers stopped');
	}

	private async isMessageInQueue(
		queueUrl: string,
		deduplicationId: string
	): Promise<boolean> {
		try {
			// Check for messages with the specific deduplication ID
			const receiveParams = {
				QueueUrl: queueUrl,
				MaxNumberOfMessages: 1,
				MessageAttributeNames: ['All'],
				VisibilityTimeout: 1, // Minimum visibility timeout
				WaitTimeSeconds: 0,
				AttributeNames: [QueueAttributeName.All] // We'll check the message attributes for deduplication ID
			};

			const command = new ReceiveMessageCommand(receiveParams);
			const response = await this.sqsClient.send(command);

			if (response.Messages) {
				for (const message of response.Messages) {
					const messageDeduplicationId =
						message.MessageAttributes?.DeduplicationId?.StringValue;
					if (messageDeduplicationId === deduplicationId) {
						return true;
					}
				}
			}

			return false;
		} catch (error) {
			this.loggerService.error('[SQS] Error checking message in queue', {
				queueUrl,
				deduplicationId,
				error
			});
			return false;
		}
	}

	async sendMessage({
		queueKey,
		messageBody,
		deduplicationId,
		delaySeconds
	}: SendMessageParams): Promise<void> {
		if (!deduplicationId) {
			throw new Error(
				'deduplicationId is required for message deduplication'
			);
		}

		const queueConfig = this.queues[queueKey];
		if (!queueConfig) {
			this.loggerService.error(
				`Queue configuration not found for key: ${queueKey}`
			);
			return;
		}

		const { name, delaySeconds: defaultDelay } = queueConfig;
		const queueUrl = `${this.configService.get<string>('aws.sqs.queueUrl')}/${name}`;

		// Check if message with this ID exists in queue
		const isDuplicate = await this.isMessageInQueue(
			queueUrl,
			deduplicationId
		);
		if (isDuplicate) {
			this.loggerService.log('Duplicate message detected, skipping', {
				queueKey,
				deduplicationId
			});
			return;
		}

		const params = {
			QueueUrl: queueUrl,
			MessageBody: JSON.stringify(messageBody),
			DelaySeconds: delaySeconds ?? defaultDelay ?? 0,
			MessageAttributes: {
				DeduplicationId: {
					DataType: 'String',
					StringValue: deduplicationId
				}
			}
		};

		try {
			const command = new SendMessageCommand(params);
			const response = await this.sqsClient.send(command);
			const taskType = messageBody.data.taskType;

			this.loggerService.log('[SQS] Message sent to', {
				name,
				response,
				deduplicationId,
				taskType
			});
			console.log(
				`[SQS] Message sent to ${name}:`,
				taskType,
				response.MessageId
			);
		} catch (error) {
			this.loggerService.error('[SQS] Error sending message', {
				name,
				error
			});
			console.error(`[SQS] Error sending message to ${name}:`, error);
		}
	}

	// async onModuleInit() {
	// 	console.log('SQS onModuleInit called');
	// 	for (const queueKey in this.queues) {
	// 		if (this.queues.hasOwnProperty(queueKey)) {
	// 			const queue = this.queues[queueKey];
	// 			await this.receiveMessage(queue);
	// 		}
	// 	}
	// }

	private async receiveMessage(queue: QueueConfig) {
		const { name, handler } = queue;
		const queueUrl = `${this.configService.get<string>('aws.sqs.queueUrl')}/${name}`;

		// Check if consumer already exists for this queue
		if (this.consumers.has(name)) {
			this.loggerService.warn(
				`Consumer already exists for queue ${name}, skipping initialization`
			);
			return;
		}

		const consumer = Consumer.create({
			queueUrl,
			handleMessage: async (message: any) => {
				try {
					const handlerInstance = this.moduleRef.get(handler, {
						strict: false
					});
					await handlerInstance.handle(message);
					this.loggerService.log(
						`Message processed successfully: ${message.MessageId}`
					);
				} catch (error) {
					this.loggerService.error(
						`Error processing message: ${message.MessageId}`,
						error
					);
					throw error;
				}
			},
			sqs: this.sqsClient
		});

		consumer.on('error', (error: Error) => {
			this.loggerService.error(
				`Error in SQS consumer for queue ${name}`,
				error
			);
		});

		consumer.on('processing_error', (error: Error) => {
			this.loggerService.error(
				`Processing error in queue ${name}`,
				error
			);
		});

		consumer.on('timeout_error', (error: Error) => {
			this.loggerService.error(`Timeout error in queue ${name}`, error);
		});

		// Store the consumer to prevent duplicates
		this.consumers.set(name, consumer);

		consumer.start();
		this.loggerService.log(`Started SQS consumer for queue ${name}`);
	}
}
