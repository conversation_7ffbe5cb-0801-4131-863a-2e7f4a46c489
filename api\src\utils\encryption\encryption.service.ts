import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { createCipheriv, createDecipheriv, scryptSync, randomBytes } from 'crypto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EncryptionService implements OnModuleInit {
	private readonly logger = new Logger(EncryptionService.name);
	private key!: Buffer;
	private readonly algorithm = 'aes-256-cbc';

	constructor(private readonly configService: ConfigService) {}

	onModuleInit() {
		const encryptionKey = this.configService.get<string>('ENCRYPTION_KEY');
		if (!encryptionKey || encryptionKey.length !== 64) {
			this.logger.error(
				'ENCRYPTION_KEY is not set or not a 64-character hex string. Please provide a valid 32-byte key in hex format.'
			);
			if (process.env.NODE_ENV === 'production') {
				throw new Error('Critical: ENCRYPTION_KEY is not configured for production.');
			}
			this.logger.warn('Using a default, insecure key for development. This is NOT safe for production.');
			// Use a fixed, known insecure key for development if not set
			this.key = scryptSync('insecure-development-key', 'salt', 32) as Buffer;
		} else {
			this.key = Buffer.from(encryptionKey, 'hex');
		}
	}

	encrypt(text: string): string {
		const iv = randomBytes(16);
		const cipher = createCipheriv(this.algorithm, this.key, iv);
		const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);
		return `${iv.toString('hex')}:${encrypted.toString('hex')}`;
	}

	decrypt(hash: string): string {
		const [ivHex, encryptedHex] = hash.split(':');
		if (!ivHex || !encryptedHex) {
			throw new Error('Invalid encrypted string format.');
		}
		const iv = Buffer.from(ivHex, 'hex');
		const encryptedText = Buffer.from(encryptedHex, 'hex');
		const decipher = createDecipheriv(this.algorithm, this.key, iv);
		const decrypted = Buffer.concat([decipher.update(encryptedText), decipher.final()]);
		return decrypted.toString();
	}
} 