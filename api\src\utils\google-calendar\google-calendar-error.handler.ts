import { Injectable, Logger } from '@nestjs/common';
import { GaxiosError } from 'gaxios';

export interface RetryConfig {
	maxRetries?: number;
	baseDelay?: number;
	maxDelay?: number;
	exponentialFactor?: number;
}

export interface GoogleCalendarError {
	isRetryable: boolean;
	shouldRefreshToken: boolean;
	errorCode: string;
	message: string;
	originalError?: any;
}

@Injectable()
export class GoogleCalendarErrorHandler {
	private readonly logger = new Logger(GoogleCalendarErrorHandler.name);
	
	private readonly defaultRetryConfig: Required<RetryConfig> = {
		maxRetries: 3,
		baseDelay: 1000, // 1 second
		maxDelay: 30000, // 30 seconds
		exponentialFactor: 2
	};

	/**
	 * Analyzes Google API errors and determines appropriate handling strategy
	 */
	analyzeError(error: any): GoogleCalendarError {
		if (this.isGaxiosError(error)) {
			return this.analyzeGaxiosError(error);
		}

		// Generic error handling
		return {
			isRetryable: false,
			shouldRefreshToken: false,
			errorCode: 'UNKNOWN_ERROR',
			message: error.message || 'Unknown error occurred',
			originalError: error
		};
	}

	/**
	 * Executes a function with retry logic and exponential backoff
	 */
	async executeWithRetry<T>(
		operation: () => Promise<T>,
		config: RetryConfig = {}
	): Promise<T> {
		const retryConfig = { ...this.defaultRetryConfig, ...config };
		let lastError: any;

		for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
			try {
				return await operation();
			} catch (error) {
				lastError = error;
				const errorAnalysis = this.analyzeError(error);

				this.logger.warn(
					`Google Calendar API attempt ${attempt + 1}/${retryConfig.maxRetries + 1} failed: ${errorAnalysis.message}`,
					{ errorCode: errorAnalysis.errorCode, attempt }
				);

				// Don't retry on final attempt or non-retryable errors
				if (attempt === retryConfig.maxRetries || !errorAnalysis.isRetryable) {
					break;
				}

				// Calculate delay with exponential backoff and jitter
				const delay = Math.min(
					retryConfig.baseDelay * Math.pow(retryConfig.exponentialFactor, attempt),
					retryConfig.maxDelay
				);
				
				// Add jitter to prevent thundering herd
				const jitteredDelay = delay + Math.random() * 1000;

				this.logger.debug(`Retrying in ${jitteredDelay}ms...`);
				await this.sleep(jitteredDelay);
			}
		}

		// If we get here, all retries failed
		const finalError = this.analyzeError(lastError);
		this.logger.error(
			`Google Calendar API failed after ${retryConfig.maxRetries + 1} attempts: ${finalError.message}`,
			{ errorCode: finalError.errorCode, originalError: finalError.originalError }
		);
		
		throw lastError;
	}

	/**
	 * Analyzes Gaxios errors (used by Google APIs)
	 */
	private analyzeGaxiosError(error: GaxiosError): GoogleCalendarError {
		const status = error.response?.status;
		const data = error.response?.data as any;

		switch (status) {
			case 401:
				return {
					isRetryable: false,
					shouldRefreshToken: true,
					errorCode: 'UNAUTHORIZED',
					message: 'Google Calendar access token expired or invalid',
					originalError: error
				};

			case 403:
				// Check if it's a rate limit error
				if (this.isRateLimitError(data)) {
					return {
						isRetryable: true,
						shouldRefreshToken: false,
						errorCode: 'RATE_LIMITED',
						message: 'Google Calendar API rate limit exceeded',
						originalError: error
					};
				}
				
				return {
					isRetryable: false,
					shouldRefreshToken: false,
					errorCode: 'FORBIDDEN',
					message: 'Insufficient permissions for Google Calendar API',
					originalError: error
				};

			case 404:
				return {
					isRetryable: false,
					shouldRefreshToken: false,
					errorCode: 'NOT_FOUND',
					message: 'Google Calendar or event not found',
					originalError: error
				};

			case 409:
				return {
					isRetryable: false,
					shouldRefreshToken: false,
					errorCode: 'CONFLICT',
					message: 'Conflict with current state of Google Calendar event',
					originalError: error
				};

			case 429:
				return {
					isRetryable: true,
					shouldRefreshToken: false,
					errorCode: 'TOO_MANY_REQUESTS',
					message: 'Too many requests to Google Calendar API',
					originalError: error
				};

			case 500:
			case 502:
			case 503:
			case 504:
				return {
					isRetryable: true,
					shouldRefreshToken: false,
					errorCode: 'SERVER_ERROR',
					message: `Google Calendar API server error: ${status}`,
					originalError: error
				};

			default:
				return {
					isRetryable: status ? status >= 500 : false,
					shouldRefreshToken: false,
					errorCode: 'HTTP_ERROR',
					message: `Google Calendar API HTTP error: ${status}`,
					originalError: error
				};
		}
	}

	/**
	 * Checks if error is a Gaxios error
	 */
	private isGaxiosError(error: any): error is GaxiosError {
		return error && typeof error === 'object' && 'response' in error;
	}

	/**
	 * Checks if the error indicates rate limiting
	 */
	private isRateLimitError(errorData: any): boolean {
		if (!errorData || !errorData.error) return false;
		
		const errorCode = errorData.error.code;
		const errorMessage = errorData.error.message?.toLowerCase() || '';
		
		return errorCode === 403 && (
			errorMessage.includes('rate') ||
			errorMessage.includes('quota') ||
			errorMessage.includes('limit')
		);
	}

	/**
	 * Sleep utility for retry delays
	 */
	private sleep(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}
} 