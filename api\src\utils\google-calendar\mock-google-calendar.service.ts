import { Injectable, Logger } from '@nestjs/common';

export interface MockGoogleEvent {
	id: string;
	summary: string;
	description?: string;
	start: {
		dateTime: string;
		timeZone: string;
	};
	end: {
		dateTime: string;
		timeZone: string;
	};
	attendees?: Array<{
		email: string;
		displayName?: string;
		responseStatus?: string;
	}>;
	status: 'confirmed' | 'tentative' | 'cancelled';
	updated: string;
	creator: {
		email: string;
		displayName?: string;
	};
	organizer: {
		email: string;
		displayName?: string;
	};
}

export interface MockCalendar {
	id: string;
	summary: string;
	description?: string;
	primary?: boolean;
	accessRole: string;
}

@Injectable()
export class MockGoogleCalendarService {
	private readonly logger = new Logger(MockGoogleCalendarService.name);
	private readonly mockEvents = new Map<string, MockGoogleEvent>();
	private readonly mockCalendars = new Map<string, MockCalendar>();
	private eventIdCounter = 1;

	constructor() {
		this.initializeMockData();
	}

	/**
	 * Mock calendar list
	 */
	async listCalendars(accessToken: string): Promise<{ items: MockCalendar[] }> {
		this.validateAccessToken(accessToken);
		this.logger.debug('Mock: Listing calendars');
		
		return {
			items: Array.from(this.mockCalendars.values())
		};
	}

	/**
	 * Mock event creation
	 */
	async createEvent(
		accessToken: string,
		calendarId: string,
		eventData: Partial<MockGoogleEvent>
	): Promise<MockGoogleEvent> {
		this.validateAccessToken(accessToken);
		this.validateCalendarAccess(calendarId);
		
		const eventId = `mock_event_${this.eventIdCounter++}`;
		const now = new Date().toISOString();
		
		const event: MockGoogleEvent = {
			id: eventId,
			summary: eventData.summary || 'Untitled Event',
			description: eventData.description,
			start: eventData.start || {
				dateTime: now,
				timeZone: 'UTC'
			},
			end: eventData.end || {
				dateTime: new Date(Date.now() + 3600000).toISOString(), // +1 hour
				timeZone: 'UTC'
			},
			attendees: eventData.attendees || [],
			status: 'confirmed',
			updated: now,
			creator: {
				email: '<EMAIL>',
				displayName: 'Test User'
			},
			organizer: {
				email: '<EMAIL>',
				displayName: 'Test User'
			}
		};

		this.mockEvents.set(eventId, event);
		this.logger.debug(`Mock: Created event ${eventId}`, { summary: event.summary });
		
		return event;
	}

	/**
	 * Mock event update
	 */
	async updateEvent(
		accessToken: string,
		calendarId: string,
		eventId: string,
		eventData: Partial<MockGoogleEvent>
	): Promise<MockGoogleEvent> {
		this.validateAccessToken(accessToken);
		this.validateCalendarAccess(calendarId);
		
		const existingEvent = this.mockEvents.get(eventId);
		if (!existingEvent) {
			throw new Error(`Event ${eventId} not found`);
		}

		const updatedEvent: MockGoogleEvent = {
			...existingEvent,
			...eventData,
			id: eventId, // Preserve original ID
			updated: new Date().toISOString()
		};

		this.mockEvents.set(eventId, updatedEvent);
		this.logger.debug(`Mock: Updated event ${eventId}`, { summary: updatedEvent.summary });
		
		return updatedEvent;
	}

	/**
	 * Mock event deletion
	 */
	async deleteEvent(
		accessToken: string,
		calendarId: string,
		eventId: string
	): Promise<void> {
		this.validateAccessToken(accessToken);
		this.validateCalendarAccess(calendarId);
		
		if (!this.mockEvents.has(eventId)) {
			throw new Error(`Event ${eventId} not found`);
		}

		this.mockEvents.delete(eventId);
		this.logger.debug(`Mock: Deleted event ${eventId}`);
	}

	/**
	 * Mock event retrieval
	 */
	async getEvent(
		accessToken: string,
		calendarId: string,
		eventId: string
	): Promise<MockGoogleEvent> {
		this.validateAccessToken(accessToken);
		this.validateCalendarAccess(calendarId);
		
		const event = this.mockEvents.get(eventId);
		if (!event) {
			throw new Error(`Event ${eventId} not found`);
		}

		this.logger.debug(`Mock: Retrieved event ${eventId}`);
		return event;
	}

	/**
	 * Mock webhook registration
	 */
	async registerWebhook(
		accessToken: string,
		calendarId: string,
		webhookUrl: string
	): Promise<{ id: string; resourceId: string }> {
		this.validateAccessToken(accessToken);
		this.validateCalendarAccess(calendarId);
		
		const webhookId = `mock_webhook_${Date.now()}`;
		const resourceId = `mock_resource_${Date.now()}`;
		
		this.logger.debug(`Mock: Registered webhook ${webhookId} for calendar ${calendarId}`);
		
		return { id: webhookId, resourceId };
	}

	/**
	 * Mock webhook deregistration
	 */
	async stopWebhook(
		accessToken: string,
		webhookId: string,
		resourceId: string
	): Promise<void> {
		this.validateAccessToken(accessToken);
		this.logger.debug(`Mock: Stopped webhook ${webhookId}`);
	}

	/**
	 * Simulate rate limiting for testing
	 */
	simulateRateLimit(): void {
		const error = new Error('Rate limit exceeded');
		(error as any).response = {
			status: 429,
			data: {
				error: {
					code: 429,
					message: 'Rate limit exceeded'
				}
			}
		};
		throw error;
	}

	/**
	 * Simulate authentication failure for testing
	 */
	simulateAuthFailure(): void {
		const error = new Error('Unauthorized');
		(error as any).response = {
			status: 401,
			data: {
				error: {
					code: 401,
					message: 'Invalid credentials'
				}
			}
		};
		throw error;
	}

	/**
	 * Get all mock events (for testing purposes)
	 */
	getAllMockEvents(): MockGoogleEvent[] {
		return Array.from(this.mockEvents.values());
	}

	/**
	 * Clear all mock data (for testing cleanup)
	 */
	clearMockData(): void {
		this.mockEvents.clear();
		this.mockCalendars.clear();
		this.initializeMockData();
	}

	private initializeMockData(): void {
		// Initialize with a default primary calendar
		const primaryCalendar: MockCalendar = {
			id: 'primary',
			summary: 'Test Calendar',
			description: 'Primary test calendar',
			primary: true,
			accessRole: 'owner'
		};

		this.mockCalendars.set('primary', primaryCalendar);
		
		// Add a secondary calendar
		const secondaryCalendar: MockCalendar = {
			id: 'secondary',
			summary: 'Work Calendar',
			description: 'Work-related events',
			primary: false,
			accessRole: 'writer'
		};

		this.mockCalendars.set('secondary', secondaryCalendar);
	}

	private validateAccessToken(accessToken: string): void {
		if (!accessToken || accessToken === 'invalid_token') {
			this.simulateAuthFailure();
		}

		if (accessToken === 'rate_limited_token') {
			this.simulateRateLimit();
		}
	}

	private validateCalendarAccess(calendarId: string): void {
		if (!this.mockCalendars.has(calendarId)) {
			const error = new Error(`Calendar ${calendarId} not found`);
			(error as any).response = {
				status: 404,
				data: {
					error: {
						code: 404,
						message: 'Calendar not found'
					}
				}
			};
			throw error;
		}
	}
} 