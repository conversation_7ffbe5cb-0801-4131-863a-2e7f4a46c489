export interface InvoiceLineItem {
	description: string;
	quantity: number;
	price: number;
}

export interface RefundItem {
	creditNote: string;
	amount: number;
	date: string;
}

export interface PaymentItem {
	date: string;
	paymentMode: string;
	receiptNumber: string;
	amount: number;
}

export interface WriteoffItem {
	amount: number;
	date: string;
	reason: string;
	by: string;
}

export interface CancellationItem {
	amount: number;
	date: string;
	reason: string;
	by: string;
}

export interface InvoiceData {
	invoiceNumber: string;
	invoiceDate: string;
	clinicName: string;
	clinicAddress: string;
	clinicPhone: string;
	clinicEmail: string;
	clinicWebsite: string;
	customerName: string;
	petName: string;
	petDetails: string;
	lineItems: InvoiceLineItem[];
	subtotal: number;
	invoiceAmount: number;
	taxes: number;
	previousBalance: number;
	discount: number;
	totalDue: number;
	amountPaid: number;
	balanceDue: number;
	customerEmail: string;
	customerPhone: string;
	clinicLogoUrl?: string;
	receiptDate?: string;
	paymentMode?: string;
	receiptNumber?: string;
	creditsUsed?: number;
	refunds?: number;
	refundCreditNote?: string;
	refundAmount?: number;
	refundDate?: string;
	refundItems?: RefundItem[];
	paymentItems?: PaymentItem[];
	writeoff?: WriteoffItem;
	cancellation?: CancellationItem;
}

export const generateInvoice = ({
	invoiceNumber,
	invoiceDate,
	clinicName,
	clinicAddress,
	clinicPhone,
	clinicEmail,
	clinicWebsite,
	customerName,
	petName,
	petDetails,
	lineItems,
	subtotal,
	invoiceAmount,
	taxes,
	previousBalance,
	discount,
	totalDue,
	amountPaid,
	balanceDue,
	customerEmail,
	customerPhone,
	clinicLogoUrl,
	receiptDate,
	paymentMode,
	receiptNumber,
	creditsUsed,
	refunds,
	refundCreditNote,
	refundAmount,
	refundDate,
	refundItems,
	paymentItems,
	writeoff,
	cancellation
}: InvoiceData): string => {
	const itemLists = lineItems.map((item, index) => {
		return `
              <tr>
                <td><p class='${index === lineItems.length - 1 ? '' : 'min-height-md'}'>${index + 1}.</p></td>
                <td><p class='${index === lineItems.length - 1 ? '' : 'min-height-md'}'>${item.description}</p></td>
                <td><p class='${index === lineItems.length - 1 ? '' : 'min-height-md'}'>${item.quantity}</p></td>
                <td><p class='${index === lineItems.length - 1 ? '' : 'min-height-md'} text-align-right'>${
					item.price
				}</p></td>
                <td><p class='${index === lineItems.length - 1 ? '' : 'min-height-md'} text-align-right'>${
					item.price * item.quantity
				}</p></td>
              </tr>
          `;
	});

	let itemLists1, itemList2;
	const size = itemLists.length;
	if (size > 10) {
		itemLists1 = itemLists.slice(0, 10).join('');
		itemList2 = itemLists.slice(10).join('');
	} else if (size > 6 && size <= 10) {
		itemLists1 = itemLists.slice(0, size - 1).join('');
		itemList2 = itemLists.slice(size - 1).join('');
	} else {
		itemLists1 = itemLists.join('');
		itemList2 = '';
	}

	return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice</title>
      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
      <style>
          /* Base Styles */
            @page {
                    size: A4;  
              }

              .padding-0 {
                padding: 0px;
              }
              .margin-0 {
                margin: 0px;
              }
              .border-top {
                border-top: 0.5px solid #D6D6D6;
              }

              .border-bottom {
                border-bottom: 0.5px solid #D6D6D6;
              }

              body {
                  font-family: 'Inter', sans-serif;
                  color: #59645D !important;
                  font-size: 14px;
                  line-height: 1.5;
                  padding: 30px;
              }
  
  
              .invoice-wrapper {
                  margin: 0 auto;
                  box-sizing: border-box;
              }
   
          p {
              margin: 0;
          }
    
          /* Typography Utilities */
          .text-regular {
              font-weight: 400;
          }
    
          .text-medium {
              font-weight: 500;
              color: #59645D;
          }
    
          .text-secondary {
              color: #59645D;
          }
    
          .text-align-center {
              text-align: center;
          }
    
          .text-align-right {
              text-align: right;
          }
    
          /* Spacing Utilities */
          .padding-bottom-sm {
              padding-bottom: 16px;
          }
    
          .padding-bottom-md {
              padding-bottom: 20px;
          }
    
          .padding-bottom-lg {
              padding-bottom: 25px;
          }
    
          .padding-bottom-xl {
              padding-bottom: 28px;
          }
    
          .padding-bottom-2xl {
              padding-bottom: 40px;
          }
    
          .padding-top-md {
              padding-top: 20px !important;
          }
    
          .padding-top-xl {
              padding-top: 28px !important;
          }
    
          .margin-right-lg {
              margin-right: 36px;
          }
    
          .margin-right-xl {
              margin-right: 80px;
          }
    
          .margin-top-xl {
              margin-top: 40px;
          }
    
          /* Layout Utilities */
          .display-block {
              display: block;
          }
    
          .min-height-md {
              min-height: 30px;
          }
          
          .min-width-50{
           min-width: 80px;
          }
    
          /* Dividers */
          .divider-vertical {
              border-left: 0.5px solid #D6D6D6;
              margin: 0 10px;
          }
    
          .divider-horizontal {
              border-top: 0.5px solid #D6D6D6;
          }
         
         .padding-top-x{
            padding-top: 10px;
         }

          /* Page Break */
          .page-break {
              page-break-before: always;
          }
    
          
    
          .invoice-header {
              display: flex;
              justify-content: space-between;
          }
    
          .invoice-title {
              font-size: 54px;
              font-weight: 100;
              line-height: 65.35px;
              margin: 0;
          }
    
          .invoice-subtitle {
              font-size: 18px;
              font-weight: 300;
              line-height: 24px;
          }
    
          .invoice-section {
              border-top: 0.5px solid #D6D6D6;
              padding: 28px 0;
          }
    
          .invoice-section-label {
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              margin: 0;
              color: #59645D;
          }
    
          .invoice-section-title {
                font-family: Inter;
                font-weight: 500;
                font-size: 14px;
                line-height: 16.94px;
                letter-spacing: 0%;
          }
    
          .invoice-info-grid {
              display: flex;
              justify-content: space-between;
              gap: 20px;
              margin-top: 6px;
              font-family: Inter;
              font-weight: 400;
              font-size: 12px;
              line-height: 16px;
              letter-spacing: 0%;
          }
    
          .invoice-info-grid p {
              font-size: 12px;
          }
    
         
    
          /* Table Styles */
          .invoice-table {
              border: none;
              border-collapse: collapse;
              width: 100%;
          }
    
          .invoice-table th {
              color: #59645D;
              font-size: 14px;
              font-weight: 500;
              line-height: 16.94px;
              text-align: left;
              padding: 8px;
          }
    
          .invoice-table th.text-align-right {
              text-align: right;
          }
    
          .invoice-table td {
              color: #59645D;
              font-size: 12px;
              font-weight: 400;
              line-height: 14px;
              padding: 8px;
          }
    
          .invoice-table thead {
              border-bottom: 0.5px solid #D6D6D6;
          }
    
          /* Summary Styles */
          .invoice-summary {
              text-align: right;
          }
    
          .invoice-summary-list {
              display: flex;
              flex-direction: column;
              padding: 12px 0;
          }

          
              .invoice-summary-list-with-border {
              display: flex;
              border-top: 0.5px solid #D6D6D6;
              flex-direction: column;
              gap: 10px;
              padding: 12px 0;
          }
    
          .invoice-summary-row {
              display: flex;
              justify-content: flex-end;
              font-size: 14px;
              font-weight: 500;
              color: #59645D;
          }
    
          .invoice-summary-amount {
              min-width: 80px;
              display: inline-block;
              margin-right: 10px;
          }
    
          @media print {
              body {
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  margin: 0;
                  padding: 30px;
              }
          }
          
          .receipt-info {
              font-family: Inter;
              font-weight: 400;
              font-size: 10px;
              line-height: 16px;
              letter-spacing: 1px;
              vertical-align: middle;
              text-transform: uppercase;
              padding: 0px;
              margin: 0px;
          }

          .invoice-refunds {
            display: flex;
            gap: 8px;
            flex-direction: column;
            justify-content: flex-start;
            font-size: 14px;
            font-weight: 500;
            color: #59645D;
            max-width: 280px;
          }

          .invoice-refunds-title {
            display: flex;
            justify-content: flex-start;
            font-size: 14px;
            font-weight: 500;
            color: #59645D;
            border-bottom: 0.5px solid #D6D6D6;
            max-width: 300px;
            padding-bottom: 4px;
            margin-bottom: 8px;
          }

          .refunds-summary-row{
              display: flex;
              justify-content: space-between;
          }
              
          .invoice-refunds-container{
           margin-top:28px;
          }

          .payment-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 20px;
          }
          
          .payment-item:last-child {
            margin-bottom: 0;
          }

          .refund-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }

      </style>
    </head>
    <body>
      <div class="invoice-wrapper">
          <div class="invoice-header padding-bottom-xl">
              <div>
                  <h1 class="invoice-title">Invoice</h1>
                  <p class="invoice-subtitle">
                      Invoice ID #${invoiceNumber}
                      <span class="divider-vertical"></span>
                      <span class="text-secondary">${invoiceDate}</span>
                  </p>
              </div>
              <div>
                  ${
						clinicLogoUrl
							? `<img src="${clinicLogoUrl}" alt="Clinic Logo" style="max-width: 87px; max-height: 87px; object-fit: cover;" />`
							: ''
					}
              </div>
          </div>
          <div class="invoice-section">
              <p class="invoice-section-title">${clinicName}</p>
              <div class="invoice-info-grid">
                  <div class="invoice-info-col-left">
                      <p>${clinicAddress}</p>
                  </div>
                  <div class="invoice-info-col-right">
                      <p>${clinicPhone}</p>
                      <p>
                          <span class="display-block">${clinicEmail}</span>
                          <span class="display-block">${clinicWebsite}</span>
                      </p>
                  </div>
              </div>
          </div>
          <div class="invoice-section">
              <p>
                  <span class="text-medium">${petName}</span> ${
						petDetails.trim() ? `| ${petDetails}` : ''
					}
              </p>
              <p class="invoice-section-label">${customerName}</p>
              <p class="invoice-section-label">${customerEmail}</p>
              <p class="invoice-section-label">${customerPhone}</p>
          </div>
          <table class="invoice-table divider-horizontal">
              <thead>
                  <tr>
                      <th>No.</th>
                      <th>Description</th>
                      <th>QTY</th>
                      <th class="text-align-right">Price</th>
                      <th class="text-align-right">Total</th>
                  </tr>
              </thead>
              <tbody>
                  ${itemLists1}
              </tbody>
          </table>
          ${
				itemList2
					? `
              <div class="page-break"></div>
              <table class="invoice-table margin-top-xl">
                  <thead>
                      <tr>
                          <th>No.</th>
                          <th>Description</th>
                          <th>QTY</th>
                          <th class="text-align-right">Price</th>
                          <th class="text-align-right">Total</th>
                      </tr>
                  </thead>
                  <tbody>
                      ${itemList2}
                  </tbody>
              </table>
              <div class="invoice-summary">
                <div class="invoice-summary-list-with-border">
                    <div class="invoice-summary-row">
                        <span>Sub total</span>
                        <span class="text-align-right min-width-50">${typeof subtotal === 'string' ? parseFloat(subtotal).toFixed(2) : subtotal.toFixed(2)}</span>
                    </div>
                    <div class="invoice-summary-row">
                        <span>Taxes</span>
                        <span class="text-align-right min-width-50">${typeof taxes === 'string' ? parseFloat(taxes).toFixed(2) : taxes.toFixed(2)}</span>
                    </div>
                    <div class="invoice-summary-row">
                        <span>Discount</span>
                        <span class="text-align-right min-width-50">${discount > 0 ? `- ${typeof discount === 'string' ? parseFloat(discount).toFixed(2) : discount.toFixed(2)}` : `${discount}`}</span>
                    </div>
                    <div class="invoice-summary-row">
                        <span>Invoice Total</span>
                        <span class="text-align-right min-width-50">${typeof totalDue === 'string' ? parseFloat(totalDue).toFixed(2) : totalDue.toFixed(2)}</span>
                    </div>
                </div>
                <div class="invoice-summary-list">
                    ${
						paymentItems && paymentItems.length > 0
							? paymentItems
									.filter(payment => payment.amount > 0)
									.map(
										payment => `
                        <div class="payment-item">
                          <div class="receipt-info border-bottom">${payment.date || ''} ${payment.paymentMode ? '• ' + payment.paymentMode : ''} ${payment.receiptNumber ? '• receipt #' + payment.receiptNumber : ''}</div>
                          <div class="invoice-summary-row">
                            <span>${payment.paymentMode === 'Credits' ? 'Credits Used' : 'Invoice Amount Cleared'}</span>
                            <span class="text-align-right min-width-50">${typeof payment.amount === 'string' ? parseFloat(payment.amount).toFixed(2) : payment.amount.toFixed(2)}</span>
                          </div>
                        </div>
                      `
									)
									.join('')
							: amountPaid > 0
								? `<div class="payment-item">
                        <div class="receipt-info border-bottom">${receiptDate || ''} ${paymentMode ? '• ' + paymentMode : ''} ${receiptNumber ? '• receipt #' + receiptNumber : ''}</div>
                        <div class="invoice-summary-row">
                          <span>${paymentMode === 'Credits' ? 'Credits Used' : 'Invoice Amount Cleared'}</span>
                          <span class="text-align-right min-width-50">${typeof amountPaid === 'string' ? parseFloat(amountPaid).toFixed(2) : amountPaid.toFixed(2)}</span>
                        </div>
                      </div>`
								: ''
					}
                    ${
						writeoff && writeoff.amount > 0
							? `<div class="payment-item">
                        <div class="receipt-info border-bottom">${writeoff.date ? new Date(writeoff.date).toLocaleDateString() : ''} ${writeoff.date ? '• ' + new Date(writeoff.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''} ${writeoff.by ? '• ' + writeoff.by : ''}</div>
                        <div class="invoice-summary-row">
                          <span>Amount written off${writeoff.reason ? ' • Reason : ' + writeoff.reason : ''}</span>
                          <span class="text-align-right min-width-50">${typeof writeoff.amount === 'string' ? parseFloat(writeoff.amount).toFixed(2) : writeoff.amount.toFixed(2)}</span>
                        </div>
                      </div>`
							: ''
					}
                    ${
						cancellation && cancellation.amount > 0
							? `<div class="payment-item">
                        <div class="receipt-info border-bottom">${cancellation.date ? new Date(cancellation.date).toLocaleDateString() : ''} ${cancellation.date ? '• ' + new Date(cancellation.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''} ${cancellation.by ? '• ' + cancellation.by : ''}</div>
                        <div class="invoice-summary-row">
                          <span>Invoice cancelled${cancellation.reason ? ' • Reason : ' + cancellation.reason : ''}</span>
                          <span class="text-align-right min-width-50">${typeof cancellation.amount === 'string' ? parseFloat(cancellation.amount).toFixed(2) : cancellation.amount.toFixed(2)}</span>
                        </div>
                      </div>`
							: ''
					}
                </div>
                 <div class="invoice-summary-list">
                      <div class="invoice-summary-row border-top padding-top-x">
                        <span>Invoice Balance</span>
                        <span class="text-align-right min-width-50">${typeof balanceDue === 'string' ? parseFloat(balanceDue).toFixed(2) : balanceDue.toFixed(2)}</span>
                      </div> 
                </div>
              </div>`
					: `
          <div class="invoice-summary">
              <div class="invoice-summary-list-with-border">
                  <div class="invoice-summary-row">
                      <span>Sub total</span>
                      <span class="text-align-right min-width-50">${typeof subtotal === 'string' ? parseFloat(subtotal).toFixed(2) : subtotal.toFixed(2)}</span>
                  </div>
                  <div class="invoice-summary-row">
                      <span>Taxes</span>
                      <span class="text-align-right min-width-50">${typeof taxes === 'string' ? parseFloat(taxes).toFixed(2) : taxes.toFixed(2)}</span>
                  </div>
                  <div class="invoice-summary-row">
                      <span>Discount</span>
                      <span class="text-align-right min-width-50">${discount > 0 ? `- ${typeof discount === 'string' ? parseFloat(discount).toFixed(2) : discount.toFixed(2)}` : `${discount}`}</span>
                  </div>
                  <div class="invoice-summary-row">
                      <span>Invoice Total</span>
                      <span class="text-align-right min-width-50">${typeof totalDue === 'string' ? parseFloat(totalDue).toFixed(2) : totalDue.toFixed(2)}</span>
                  </div>
              </div>
                <div class="invoice-summary-list">
                    ${
						paymentItems && paymentItems.length > 0
							? paymentItems
									.filter(payment => payment.amount > 0)
									.map(
										payment => `
                        <div class="payment-item">
                          <div class="receipt-info border-bottom">${payment.date || ''} ${payment.paymentMode ? '• ' + payment.paymentMode : ''} ${payment.receiptNumber ? '• receipt #' + payment.receiptNumber : ''}</div>
                          <div class="invoice-summary-row">
                            <span>${payment.paymentMode === 'Credits' ? 'Credits Used' : 'Invoice Amount Cleared'}</span>
                            <span class="text-align-right min-width-50">${typeof payment.amount === 'string' ? parseFloat(payment.amount).toFixed(2) : payment.amount.toFixed(2)}</span>
                          </div>
                        </div>
                      `
									)
									.join('')
							: amountPaid > 0
								? `<div class="payment-item">
                        <div class="receipt-info border-bottom">${receiptDate || ''} ${paymentMode ? '• ' + paymentMode : ''} ${receiptNumber ? '• receipt #' + receiptNumber : ''}</div>
                        <div class="invoice-summary-row">
                          <span>${paymentMode === 'Credits' ? 'Credits Used' : 'Invoice Amount Cleared'}</span>
                          <span class="text-align-right min-width-50">${typeof amountPaid === 'string' ? parseFloat(amountPaid).toFixed(2) : amountPaid.toFixed(2)}</span>
                        </div>
                      </div>`
								: ''
					}
                    ${
						writeoff && writeoff.amount > 0
							? `<div class="payment-item">
                        <div class="receipt-info border-bottom">${writeoff.date ? new Date(writeoff.date).toLocaleDateString() : ''} ${writeoff.date ? '• ' + new Date(writeoff.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''} ${writeoff.by ? '• ' + writeoff.by : ''}</div>
                        <div class="invoice-summary-row">
                          <span>Amount written off${writeoff.reason ? ' • Reason : ' + writeoff.reason : ''}</span>
                          <span class="text-align-right min-width-50">${typeof writeoff.amount === 'string' ? parseFloat(writeoff.amount).toFixed(2) : writeoff.amount.toFixed(2)}</span>
                        </div>
                      </div>`
							: ''
					}
                    ${
						cancellation && cancellation.amount > 0
							? `<div class="payment-item">
                        <div class="receipt-info border-bottom">${cancellation.date ? new Date(cancellation.date).toLocaleDateString() : ''} ${cancellation.date ? '• ' + new Date(cancellation.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''} ${cancellation.by ? '• ' + cancellation.by : ''}</div>
                        <div class="invoice-summary-row">
                          <span>Invoice cancelled${cancellation.reason ? ' • Reason : ' + cancellation.reason : ''}</span>
                          <span class="text-align-right min-width-50">${typeof cancellation.amount === 'string' ? parseFloat(cancellation.amount).toFixed(2) : cancellation.amount.toFixed(2)}</span>
                        </div>
                      </div>`
							: ''
					}
                </div>
                 <div class="invoice-summary-list">
                      <div class="invoice-summary-row border-top padding-top-x">
                        <span>Invoice Balance</span>
                        <span class="text-align-right min-width-50">${typeof balanceDue === 'string' ? parseFloat(balanceDue).toFixed(2) : balanceDue.toFixed(2)}</span>
                      </div> 
                </div>
          </div>`
			}
        <div class="invoice-footer">
            ${
				refunds
					? `
            <div class="invoice-refunds-container">
                <div class="invoice-refunds-title" style="font-family: Inter; font-weight: 500; font-size: 14px; line-height: 100%; letter-spacing: 0%; vertical-align: middle;">
                    Refunds
                </div>
                <div class="invoice-refunds">
                    ${
						refundItems && refundItems.length > 0
							? refundItems
									.map(
										(item, index) => `
                        ${index > 0 ? '<div class="border-top" style="margin: 10px 0;"></div>' : ''}
                        <div class="refund-item">
                          <div class="refunds-summary-row">
                              <span>Credit Note</span>
                              <div><span>${'#' + item.creditNote || ''}</span></div>
                          </div>
                          <div class="refunds-summary-row">
                              <span>Refund Amount</span>
                              <div><span>${item.amount ? (typeof item.amount === 'string' ? parseFloat(item.amount).toFixed(2) : item.amount.toFixed(2)) : '0.00'}</span></div>
                          </div>
                          <div class="refunds-summary-row">
                              <span>Date</span>
                              <div>
                                  <span class="min-width-50">${item.date || ''}</span>
                              </div>
                          </div>
                        </div>
                      `
									)
									.join('')
							: `<div class="refund-item">
                          <div class="refunds-summary-row">
                              <span>Credit Note</span>
                              <div><span>${'#' + refundCreditNote || ''}</span></div>
                          </div>
                          <div class="refunds-summary-row">
                              <span>Refund Amount</span>
                              <div><span>${refundAmount ? (typeof refundAmount === 'string' ? parseFloat(refundAmount).toFixed(2) : refundAmount.toFixed(2)) : '0.00'}</span></div>
                          </div>
                          <div class="refunds-summary-row">
                              <span>Date</span>
                              <div>
                                  <span class="min-width-50">${refundDate || ''}</span>
                              </div>
                          </div>
                      </div>`
					}
                </div>
            </div>`
					: ''
			}  
        </div>  
      </div>
    </body>
    </html>
    `;
};
