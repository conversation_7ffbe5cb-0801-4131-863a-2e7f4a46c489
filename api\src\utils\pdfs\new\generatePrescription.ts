export const generatePrescription = ({
	prescriptionId,
	prescriptionDate,
	clinicName,
	clinicAddress,
	clinicPhone,
	clinicEmail,
	clinicWebsite,
	customerName,
	petName,
	petDetails,
	lineItems,
	dischargeInstructions,
	vetName,
	vetLicenseNo,
	digitalSignature,
	customerEmail,
	customerPhone,
	clinicLogoUrl,
	followUpDate
}: any) => {
	const formattedDischargeInstructions = dischargeInstructions
		.split('\n')
		.map((line: string) => `${line}<br/>`)
		.join('');
	// Create item list HTML elements
	const itemLists = lineItems.map((item: any, index: number) => {
		return `
      <tr>
        <td><p class="min-height-md">${index + 1}.</p></td>
        <td><p class="min-height-md">${item.medication}</p></td>
        <td><p class="min-height-md">${item.comments}</p></td>
      </tr>
      `;
	});

	// Split items across pages
	let itemLists1, itemList2;
	const size = itemLists.length;
	if (size > 14) {
		itemLists1 = itemLists.slice(0, 14).join('');
		itemList2 = itemLists.slice(14).join('');
	} else if (size > 10 && size <= 14) {
		itemLists1 = itemLists.slice(0, size - 1).join('');
		itemList2 = itemLists.slice(size - 1).join('');
	} else {
		itemLists1 = itemLists.slice(0, size).join('');
		itemList2 = itemLists.slice(size).join('');
	}

	return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Prescription</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Qwitcher+Grypen:wght@400;700&display=swap" rel="stylesheet">
        <style>
           @page {
              size: A4;
          }
  
          body {
              font-family: 'Inter', sans-serif;
              color: #59645D;
              font-size: 14px;
              line-height: 1.5;
          }
  
  
          .prescription-wrapper {
             
              padding: 30px;
              box-sizing: border-box;
          }
  
            p {
                margin: 0;
            }
      
            /* Typography Utilities */
            .text-regular {
                font-weight: 400;
            }
      
            .text-medium {
                font-weight: 500;
                color: #59645D;
            }
      
            .text-secondary {
                color: #9A9797;
            }
      
            .text-align-center {
                text-align: center;
            }
      
            .text-align-right {
                text-align: right;
            }
      
            /* Spacing Utilities */
            .padding-bottom-sm {
                padding-bottom: 16px;
            }
      
            .padding-bottom-md {
                padding-bottom: 20px;
            }
      
            .padding-bottom-lg {
                padding-bottom: 25px;
            }
      
            .padding-bottom-xl {
                padding-bottom: 28px;
            }
      
            .padding-bottom-2xl {
                padding-bottom: 40px;
            }
          
            .padding-top-sm {
                padding-top: 10px !important;
            }
  
            .padding-top-md {
                padding-top: 20px !important;
            }
      
            .padding-top-xl {
                padding-top: 28px !important;
            }
      
            /* Layout Utilities */
            .display-block {
                display: block;
            }
      
            .min-height-md {
                min-height: 30px;
            }
      
            /* Dividers */
            .divider-vertical {
                border-left: 0.5px solid #D6D6D6;
                margin: 0 10px;
            }
      
            .divider-horizontal {
                border-top: 0.5px solid #D6D6D6;
            }
            
            /* Page Break */
            .page-break {
                page-break-before: always;
            }
            
            .margin-top-xl {
                margin-top: 40px;
            }
      
            /* Prescription Layout Components */
           
      
            .prescription-header {
                display: flex;
                justify-content: space-between;
            }
      
            .prescription-title {
                font-size: 54px;
                font-weight: 100;
                line-height: 65.35px;
                margin: 0;
            }
      
            .prescription-subtitle {
                font-size: 18px;
                font-weight: 300;
                line-height: 24px;
            }
      
            .prescription-section {
                border-top: 0.5px solid #D6D6D6;
                padding: 28px 0;
            }
      
            .prescription-section-label {
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                margin: 0;
                color: #59645D;
            }
      
            .prescription-section-title {
                font-size: 14px;
                font-weight: 500;
                line-height: 16.52px;
                margin: 0;
                color: #333333;
            }
      
            .prescription-info-grid {
                display: flex;
                justify-content: space-between;
                gap: 20px;
                margin-top: 6px;
            }
      
            .prescription-info-grid p {
                font-size: 12px;
            }
      
            .prescription-info-col-left {
                max-width: 170px;
            }
      
            .prescription-info-col-right {
                max-width: 200px;
            }
      
            /* Table Styles */
            .prescription-table {
                border: none;
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 10px;
            }
      
            .prescription-table th {
                color: #59645D;
                font-size: 14px;
                font-weight: 500;
                line-height: 16.94px;
                text-align: left;
                padding: 8px;
            }
      
            .prescription-table td {
                color: #59645D;
                font-size: 12px;
                font-weight: 400;
                line-height: 14px;
                padding: 8px;
            }
      
            .prescription-table thead {
                border-bottom: 0.5px solid #D6D6D6;
            }
    
            /* Discharge Instructions */
            .discharge-instructions {
                margin-top: 40px;
            }
    
            .discharge-instructions .heading {
                font-size: 14px;
                color: #59645D;
                font-weight: 500;
                border-bottom: 0.5px solid #D6D6D6;
                padding-bottom: 6px;
                margin-bottom: 10px;
            }
    
            .discharge-instructions p {
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                color: #59645D;
            }
    
            /* Signature Styles */
            .signature {
                margin: 20px 0 0 auto;
                max-width: max-content;
                text-align: right;
            }
    
            .signature-text {
                font-family: 'Qwitcher Grypen', cursive;
                font-size: 55px;
                font-weight: 700;
                color: #504947;
                margin-bottom: 5px;
            }
    
            .signature-name {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: 16.94px;
                margin: 10px 0 3px;
            }
    
            .signature-license {
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                color: #333333;
            }
      
            @media print {
                body {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    margin: 0;
                }
            }
        </style>
      </head>
      <body>
        <div class="prescription-wrapper">
            <div class="prescription-header padding-bottom-xl">
                <div>
                    <h1 class="prescription-title">Prescription</h1>
                    <p class="prescription-subtitle">
                        Prescription ID #${prescriptionId}
                        <span class="divider-vertical"></span>
                        <span class="text-secondary">${prescriptionDate}</span>
                    </p>
                </div>
                <div>
                      ${clinicLogoUrl ? `<img src="${clinicLogoUrl}" alt="Clinic Logo" style="max-width: 87px; max-height: 87px; object-fit: cover;" />` : ''}
                </div>
            </div>
    
            <div class="prescription-section">
                <h6 class="prescription-section-title">${clinicName}</h6>
                <div class="prescription-info-grid">
                    <div class="prescription-info-col-left">
                        <p>${clinicAddress}</p>
                    </div>
                    <div class="prescription-info-col-right">
                        <p>${clinicPhone}</p>
                        <p>
                            <span class="display-block">${clinicEmail}</span>
                            <span class="display-block">${clinicWebsite}</span>
                        </p>
                    </div>
                </div>
            </div>
    
            <div class="prescription-section">
                <p>
                    <span class="text-medium">${petName}</span> ${
						petDetails.trim() ? `| ${petDetails}` : ''
					}
                </p>
                <p class="prescription-section-label">${customerName}</p>
                <p class="prescription-section-label">${customerEmail}</p>
                <p class="prescription-section-label">${customerPhone}</p>
            </div>
    
            ${
				lineItems.length > 0
					? `
            <table class="prescription-table divider-horizontal">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>Medication</th>
                        <th>Comments</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemLists1}
                </tbody>
            </table>
            ${
				itemList2.length > 0
					? `
              <div class="page-break"></div>
              <table class="prescription-table margin-top-xl">
                  <thead>
                      <tr>
                          <th>No.</th>
                          <th>Medication</th>
                          <th>Comments</th>
                      </tr>
                  </thead>
                  <tbody>
                      ${itemList2}
                  </tbody>
              </table>`
					: ''
			}
            `
					: ''
			}
    
            <div class="discharge-instructions">
                <p class="heading">Discharge Instructions</p>
                <p>${formattedDischargeInstructions}</p>
                ${followUpDate ? `<p style="margin-top: 20px; font-weight: 500;">${followUpDate}</p>` : ''}
            </div>
    
            <div class="signature">
                ${
					digitalSignature && digitalSignature.includes('<svg')
						? digitalSignature.replace(
								/<svg /,
								'<svg style="max-width: 200px; max-height: 100px; width: 100%; height: 100%; aspect-ratio: 2 / 4;"' // Set fixed dimensions with aspect ratio for SVG
							) // Render SVG directly with specified aspect ratio
						: `<p class="signature-text">${digitalSignature || ''}</p>` // Use text signature with font-Ridenation
				}
                <p class="signature-name">Dr. ${vetName}</p>
                ${
					vetLicenseNo
						? `<p class="signature-license">License no. ${vetLicenseNo}</p>`
						: ''
				}
            </div>
        </div>
      </body>
      </html>
      `;
};
