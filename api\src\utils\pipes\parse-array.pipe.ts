import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseArrayPipe implements PipeTransform {
	transform(value: any) {
		// Allow undefined / null to be treated as empty array for convenience
		if (value === undefined || value === null || value === '') {
			return [];
		}

		// If already an array, return as-is
		if (Array.isArray(value)) {
			return value;
		}

		if (typeof value === 'string') {
			// First, try to parse JSON array strings: "[1,2,3]" or "[\"a\",\"b\"]"
			try {
				const parsed = JSON.parse(value);
				if (Array.isArray(parsed)) {
					return parsed;
				}
			} catch (_) {
				// Not a JSON string – continue to comma-separated handling
			}

			// Fallback: treat as comma-separated list → split and trim
			return value
				.split(',')
				.map(v => v.trim())
				.filter(v => v !== '');
		}

		// Anything else is invalid
		throw new BadRequestException(
			'Validation failed: expected array or array-like string.'
		);
	}
}
