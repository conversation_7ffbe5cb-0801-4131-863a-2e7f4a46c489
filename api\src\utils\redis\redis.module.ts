import { Module, Global } from '@nestjs/common';
import { createClient } from 'redis';
import { RedisService } from './redis.service';
import { Logger } from '@nestjs/common';

/**
 * Global Redis Module that provides centralized Redis connections for the entire application.
 * Uses a single connection pool to improve performance and resource utilization.
 */
@Global()
@Module({
	providers: [
		RedisService,
		{
			provide: 'REDIS_CLIENT',
			useFactory: async () => {
				const logger = new Logger('RedisModule');

				// Unified connection creation with fallback
				const client = createClient(getRedisConfig());

				client.on('connect', () => {
					logger.log('Main Redis client connected successfully');
				});

				client.on('error', err => {
					logger.error('Main Redis client error:', err);
				});

				await client.connect().catch(err => {
					logger.error('Error connecting main Redis client:', err);
				});

				return client;
			}
		},
		{
			provide: 'REDIS_PUB_CLIENT',
			useFactory: async () => {
				const logger = new Logger('RedisModule');

				// Unified connection creation with fallback
				const pubClient = createClient(getRedisConfig());

				pubClient.on('connect', () => {
					logger.log('Redis publisher client connected successfully');
				});

				pubClient.on('error', err => {
					logger.error('Redis publisher client error:', err);
				});

				await pubClient.connect().catch(err => {
					logger.error(
						'Error connecting Redis publisher client:',
						err
					);
				});

				return pubClient;
			}
		},
		{
			provide: 'REDIS_SUB_CLIENT',
			useFactory: async () => {
				const logger = new Logger('RedisModule');

				// Unified connection creation with fallback
				const subClient = createClient(getRedisConfig());

				subClient.on('connect', () => {
					logger.log(
						'Redis subscriber client connected successfully'
					);
				});

				subClient.on('error', err => {
					logger.error('Redis subscriber client error:', err);
				});

				await subClient.connect().catch(err => {
					logger.error(
						'Error connecting Redis subscriber client:',
						err
					);
				});

				return subClient;
			}
		}
	],
	exports: [
		RedisService,
		'REDIS_CLIENT',
		'REDIS_PUB_CLIENT',
		'REDIS_SUB_CLIENT'
	]
})
export class RedisModule {}

/**
 * Helper function to generate Redis config options based on available environment variables
 * Supports both REDIS_URL and individual parameters (host, port, password)
 */
function getRedisConfig() {
	if (process.env.REDIS_URL) {
		return {
			url: process.env.REDIS_URL
		};
	} else {
		return {
			socket: {
				host: process.env.REDIS_HOST || 'localhost',
				port: +(process.env.REDIS_PORT || 6379)
			},
			password: process.env.REDIS_PASSWORD || undefined
		};
	}
}
