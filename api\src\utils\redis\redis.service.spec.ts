import { Test, TestingModule } from '@nestjs/testing';
import { RedisService } from './redis.service';
import { RedisClientType } from 'redis';

describe('RedisService', () => {
	let service: RedisService;
	let mockRedisClient: Partial<RedisClientType>;
	let mockRedisPubClient: Partial<RedisClientType>;
	let mockRedisSubClient: Partial<RedisClientType>;

	beforeEach(async () => {
		// Mock Redis clients
		mockRedisClient = {
			set: jest.fn(),
			del: jest.fn(),
			ttl: jest.fn(),
			exists: jest.fn(),
			quit: jest.fn().mockResolvedValue(undefined)
		};

		mockRedisPubClient = {
			quit: jest.fn().mockResolvedValue(undefined)
		};

		mockRedisSubClient = {
			quit: jest.fn().mockResolvedValue(undefined)
		};

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				RedisService,
				{
					provide: 'REDIS_CLIENT',
					useValue: mockRedisClient
				},
				{
					provide: 'REDIS_PUB_CLIENT',
					useValue: mockRedisPubClient
				},
				{
					provide: 'REDIS_SUB_CLIENT',
					useValue: mockRedisSubClient
				}
			]
		}).compile();

		service = module.get<RedisService>(RedisService);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('getClient', () => {
		it('should return the Redis client', () => {
			expect(service.getClient()).toBe(mockRedisClient);
		});
	});

	describe('getPubClient', () => {
		it('should return the Redis pub client', () => {
			expect(service.getPubClient()).toBe(mockRedisPubClient);
		});
	});

	describe('getSubClient', () => {
		it('should return the Redis sub client', () => {
			expect(service.getSubClient()).toBe(mockRedisSubClient);
		});
	});

	describe('setLock', () => {
		it('should set a lock with the correct parameters', async () => {
			(mockRedisClient.set as jest.Mock).mockResolvedValue('OK');
			const result = await service.setLock('test_lock', 'locked', 60);

			expect(mockRedisClient.set).toHaveBeenCalledWith(
				'test_lock',
				'locked',
				{
					EX: 60,
					NX: true
				}
			);
			expect(result).toBe(true);
		});

		it('should return false if lock cannot be acquired', async () => {
			(mockRedisClient.set as jest.Mock).mockResolvedValue(null);
			const result = await service.setLock('test_lock', 'locked', 60);

			expect(result).toBe(false);
		});
	});

	describe('releaseLock', () => {
		it('should delete the lock key', async () => {
			await service.releaseLock('test_lock');
			expect(mockRedisClient.del).toHaveBeenCalledWith('test_lock');
		});
	});

	describe('getTtl', () => {
		it('should return the TTL of the key', async () => {
			(mockRedisClient.ttl as jest.Mock).mockResolvedValue(30);
			const result = await service.getTtl('test_key');

			expect(mockRedisClient.ttl).toHaveBeenCalledWith('test_key');
			expect(result).toBe(30);
		});
	});

	describe('exists', () => {
		it('should return true if key exists', async () => {
			(mockRedisClient.exists as jest.Mock).mockResolvedValue(1);
			const result = await service.exists('test_key');

			expect(mockRedisClient.exists).toHaveBeenCalledWith('test_key');
			expect(result).toBe(true);
		});

		it('should return false if key does not exist', async () => {
			(mockRedisClient.exists as jest.Mock).mockResolvedValue(0);
			const result = await service.exists('test_key');

			expect(result).toBe(false);
		});
	});

	describe('onModuleDestroy', () => {
		it('should close all Redis connections', async () => {
			await service.onModuleDestroy();

			expect(mockRedisClient.quit).toHaveBeenCalled();
			expect(mockRedisPubClient.quit).toHaveBeenCalled();
			expect(mockRedisSubClient.quit).toHaveBeenCalled();
		});

		it('should handle errors gracefully', async () => {
			const error = new Error('Connection error');
			(mockRedisClient.quit as jest.Mock).mockRejectedValue(error);

			await expect(service.onModuleDestroy()).resolves.not.toThrow();
		});
	});
});
