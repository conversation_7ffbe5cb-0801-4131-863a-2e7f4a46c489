import { Injectable, Inject, OnModule<PERSON><PERSON>roy, <PERSON><PERSON> } from '@nestjs/common';
import { RedisClientType } from 'redis';

/**
 * Redis service that provides access to Redis clients throughout the application.
 * This centralizes Redis connection management and eliminates redundant connections.
 */
@Injectable()
export class RedisService implements OnModuleDestroy {
	private readonly logger = new Logger(RedisService.name);

	constructor(
		@Inject('REDIS_CLIENT') private readonly redisClient: RedisClientType,
		@Inject('REDIS_PUB_CLIENT')
		private readonly redisPubClient: RedisClientType,
		@Inject('REDIS_SUB_CLIENT')
		private readonly redisSubClient: RedisClientType
	) {
		this.logger.log('Redis service initialized');
	}

	/**
	 * Get the main Redis client for general operations
	 */
	getClient(): RedisClientType {
		return this.redisClient;
	}

	/**
	 * Get the Redis client for publishing messages
	 */
	getPubClient(): RedisClientType {
		return this.redisPubClient;
	}

	/**
	 * Get the Redis client for subscribing to channels
	 */
	getSubClient(): RedisClientType {
		return this.redisSubClient;
	}

	/**
	 * Set a lock with NX option (only set if key doesn't exist)
	 */
	async setLock(
		key: string,
		value: string,
		expirySeconds: number
	): Promise<boolean> {
		const result = await this.redisClient.set(key, value, {
			EX: expirySeconds,
			NX: true
		});
		return result === 'OK';
	}

	/**
	 * Release a lock by deleting the key
	 */
	async releaseLock(key: string): Promise<void> {
		await this.redisClient.del(key);
	}

	/**
	 * Check the remaining TTL of a key
	 */
	async getTtl(key: string): Promise<number> {
		return this.redisClient.ttl(key);
	}

	/**
	 * Check if a key exists
	 */
	async exists(key: string): Promise<boolean> {
		const result = await this.redisClient.exists(key);
		return result > 0;
	}

	/**
	 * Get the lock status for specified keys
	 * @param lockKeys Array of lock keys to check
	 * @returns Object with lock status information
	 */
	async getLockStatus(
		lockKeys: string[]
	): Promise<
		Record<string, { exists: boolean; ttl: number; value: string | null }>
	> {
		const result: Record<
			string,
			{ exists: boolean; ttl: number; value: string | null }
		> = {};

		for (const key of lockKeys) {
			const exists = await this.exists(key);
			const ttl = exists ? await this.getTtl(key) : -2;
			const value = exists ? await this.redisClient.get(key) : null;

			result[key] = {
				exists,
				ttl,
				value
			};
		}

		return result;
	}

	/**
	 * Clean up connections when the module is destroyed
	 */
	async onModuleDestroy() {
		this.logger.log('Closing Redis connections...');
		try {
			await this.redisClient.quit();
			await this.redisPubClient.quit();
			await this.redisSubClient.quit();
			this.logger.log('Redis connections closed successfully');
		} catch (error) {
			this.logger.error('Error closing Redis connections:', error);
		}
	}
}
