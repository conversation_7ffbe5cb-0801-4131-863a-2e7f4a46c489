import { NextRequest, NextResponse } from 'next/server';
import type { ResponseCookie } from 'next/dist/compiled/@edge-runtime/cookies';
import { getBrandBySlug } from './app/services/brands.services';

export const config = {
    // Update matcher to include all routes that need middleware processing
    matcher: ['/', '/patientportal/:path*'],
};

// Define an interface for the expected brand information
interface BrandInfo {
    data?: {
        id?: string;
    };
}

export default async function middleware(req: NextRequest) {
    // Prevent middleware bypass attacks
    if (req.headers.get('x-middleware-subrequest')) {
        return new Response('Unauthorized', { status: 403 });
    }

    const url = req.nextUrl;
    const pathname = url.pathname;
    const hostname = req.headers.get('host') ?? '';
    const isProduction = hostname.includes('nidana.io');
    const isQA = hostname.includes('nidanaqa-api.napses.in');
    const isUAT = hostname.includes('nidanauat.napses.in');
    const isLocal = hostname.includes('localhost:4202');

    // Public routes that don't need authentication for booking portal
    const publicRoutes = ['/', '/patientportal', '/patientportal'];

    // Check if the route is public
    if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + '/'))) {
        return NextResponse.next();
    }

    // Get auth_owner from cookies for authenticated routes
    const auth_owner = req.cookies.get('AUTH_OWNER');
    if (!auth_owner?.value && !publicRoutes.includes(pathname)) {
        return NextResponse.redirect(new URL('/booking_portal/login', req.url));
    }

    let currentHost = '';

    if (isProduction) {
        currentHost = hostname.replace('.nidana.io', '');
    } else if (isQA) {
        currentHost = hostname.replace('.nidanaqa-api.napses.in', '');
    } else if (isUAT) {
        currentHost = hostname.replace('.nidanauat.napses.in', '');
    } else if (isLocal) {
        currentHost = hostname.replace('.localhost:4202', '');
    }

    const response = NextResponse.next();

    // Handle brand identification and set brand cookie
    if (currentHost && currentHost !== hostname) {
        try {
            // Use the defined BrandInfo interface
            const brandInfo: BrandInfo = await getBrandBySlug(currentHost);
            const brandId = brandInfo?.data?.id;
            const isSecure = req.headers.get('x-forwarded-proto') === 'https';
            
            // Ensure brandId is a string before setting the cookie
            if (typeof brandId === 'string') {
                response.cookies.set('BRAND_ID', brandId, {
                    secure: isProduction || isQA || isUAT ? isSecure : false,
                    path: '/',
                    maxAge: 60 * 60 * 24 * 7,
                    sameSite: 'lax',
                } as Partial<ResponseCookie>);
            }
        } catch (error) {
            console.error('Error fetching brand by slug:', error);
        }
    }

    return response;
}
