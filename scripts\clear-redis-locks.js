#!/usr/bin/env node

/**
 * Redis Lock Cleanup Script
 *
 * This script connects to Redis and clears lock keys that may be preventing cron jobs
 * from executing. It supports both direct REDIS_URL configuration and individual params.
 *
 * Usage:
 *   - With .env file: node clear-redis-locks.js
 *   - Manual params: REDIS_HOST=localhost REDIS_PORT=6379 REDIS_PASSWORD=pass node clear-redis-locks.js
 */

require("dotenv").config();
const redis = require("redis");

// Lock keys that need to be cleared
const LOCK_KEYS = [
  "reminder_cron_lock",
  "upcoming_appointment_reminder_cron_lock",
];

async function clearLocks() {
  // Create Redis client with fallback configuration
  let client;

  if (process.env.REDIS_URL) {
    console.log(
      `Connecting to Redis using URL: ${maskRedisUrl(process.env.REDIS_URL)}`
    );
    client = redis.createClient({
      url: process.env.REDIS_URL,
    });
  } else {
    const host = process.env.REDIS_HOST || "localhost";
    const port = process.env.REDIS_PORT || 6379;
    console.log(`Connecting to Redis at ${host}:${port}`);

    client = redis.createClient({
      socket: {
        host,
        port,
      },
      password: process.env.REDIS_PASSWORD,
    });
  }

  client.on("error", (err) => {
    console.error("Redis connection error:", err);
    process.exit(1);
  });

  try {
    // Connect to Redis
    await client.connect();
    console.log("Connected to Redis successfully");

    // Clear each lock key
    for (const key of LOCK_KEYS) {
      // Check if key exists
      const exists = await client.exists(key);

      if (exists) {
        // Get TTL before deletion
        const ttl = await client.ttl(key);
        console.log(`Found key: ${key} (TTL: ${ttl} seconds)`);

        // Delete the key
        await client.del(key);
        console.log(`Deleted lock key: ${key}`);
      } else {
        console.log(`Lock key ${key} doesn't exist, nothing to delete`);
      }
    }

    console.log("Lock cleanup completed successfully");
  } catch (error) {
    console.error("Error during lock cleanup:", error);
  } finally {
    // Close Redis connection
    await client.quit();
    console.log("Redis connection closed");
  }
}

// Mask Redis URL to hide credentials in logs
function maskRedisUrl(url) {
  try {
    const parsedUrl = new URL(url);
    if (parsedUrl.password) {
      parsedUrl.password = "****";
    }
    return parsedUrl.toString();
  } catch (e) {
    return "Invalid Redis URL";
  }
}

// Execute the function
clearLocks().catch(console.error);
