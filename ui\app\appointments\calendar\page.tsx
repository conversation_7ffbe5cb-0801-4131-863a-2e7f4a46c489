'use client';

import { useCallback, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, Heading, Label } from '../../atoms';
import {
    useAppointmentMutation,
    useClinicAppointments,
    useDeleteAppointment,
    useUpdateAppointmentFeilds,
    useUpdateAppointmentStatus,
} from '../../services/appointment.queries';
import {
    CreateAppointmentType,
    EnumAppointmentStatus,
} from '../../types/appointment';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
    useClinicDoctors,
    useClinicProviders,
} from '../../services/providers.queries';
import { GetDoctorsType } from '../../types/provider';
import { PatientT } from '../../types/patient';
import {
    useCreateOrUpdatePatient,
    usePatients,
} from '../../services/patient.queries';
import {
    APPOINTMENT_TRIAGE_OPTIONS,
    APPOINTMENT_TYPE,
} from '../../utils/constant';
import moment from 'moment';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { Table } from '../../molecules';
import { useClinicRooms } from '../../services/clinic.queries';
import CreateAppointmentStaff, {
    AppointmentStaffStepT,
    AppStaffStepData,
} from '../../organisms/appointment/CreateAppointmentStaff';
import { ResponseStatus } from '../../molecules/PatientDropdown';
import DropdownMenu, { MenuListType } from '../../molecules/DropdownMenu';
import IconDots from '../../atoms/customIcons/IconDots.svg';
import * as _ from 'lodash';
import { updateAppointmentStatus } from '../../services/appointment.service';
import ConfirmModal from '../../organisms/patient/ConfirmModal';
import { AlertT } from '../../atoms/Alert';
import classNames from 'classnames';
import AppointmentDetails from '../../template/AppointmentDetails';
import { useInfiniteQuery } from '@tanstack/react-query';
import AddPatient from '../../organisms/patient/AddPatient';
import { searchPatientByPhone } from '../../services/patient.service';
import { validationSchema as patientValidationSchema } from '../../utils/validation-schema/patientValidation';
import CalendarView from '@/app/template/CalendarView';
import { DateProvider } from '@/context/date-provider';
import { getAuth } from '@/app/services/identity.service';
import { processInfiniteQueryAppointments } from '@/app/utils/appointment-utils';

export default function AssignemtsPage() {
    const CLINIC_ID = getAuth()?.clinicId;
    const [confirmModal, setConfirmModal] = useState<{
        isOpen: boolean;
        type: 'cancelAppointment' | 'PatientAddedSuccessfully';
    }>({
        isOpen: false,
        type: 'cancelAppointment',
    });
    const [showAlert, setShowAlert] = useState<AlertT>({
        isOpen: false,
        label: 'errorMsg',
        variant: 'warning',
    });
    const isNonEmptyObject = (message: string) => {
        return yup
            .object()
            .required()
            .test(
                'is-empty',
                message,
                (value) => value && Object.keys(value).length > 0
            );
    };

    const providerQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        orderBy: 'ASC',
    };
    const doctorQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        orderBy: 'DESC',
        role: 'doctor',
    };

    const [patientParams, setPatientParams] = useState({
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        search: '',
    });

    const { data: doctorData, status: doctorStauts } =
        useClinicDoctors(doctorQueryParams);

    const { data: providerData, status: providerStatus } =
        useClinicProviders(providerQueryParams);

    const { data: patientsData, status: patientsStatus } =
        usePatients(patientParams);

    const {
        data,
        fetchNextPage,
        hasNextPage,
        isFetching,
        isFetchingNextPage,
        status,
    } = useClinicAppointments();
    const totalAppointments = data?.pages[0].data.total;

    const appointmentData = processInfiniteQueryAppointments(data);
    const { data: clinicRoomsData, status: clinicRoomsStatus } = useClinicRooms(
        {
            clinicId: CLINIC_ID,
        }
    );

    const updateAppointmentStatusMutation = useUpdateAppointmentStatus();
    const deleteAppointmentMutation = useDeleteAppointment();
    const { updateAppointmentMutation } = useUpdateAppointmentFeilds();

    const [step, setStep] = useState<AppointmentStaffStepT>('');
    const [patientList, setPatientList] = useState([]);
    const [selectedPatient, setSelectedPatient] = useState<null | PatientT>(
        null
    );

    const [listStatus, setListStatus] = useState<ResponseStatus>('pending');
    const [showPatientDropdown, setShowPatientDropdown] = useState(false);

    const [viewMode, setViewMode] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [patientData, setPatientData] = useState({});
    const [selectedAppointmentData, setSelectedAppointmentData] = useState({});
    const [appointmentId, setAppointmentId] = useState('');
    const [valuesChanged, setValuesChanged] = useState(false);
    const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
    const [currentStep, setCurrentStep] = useState(1);

    const createPatientMutation = useCreateOrUpdatePatient();

    const validationSchemaAppointment = yup.object().shape({
        // patient: isNonEmptyObject('Please enter a patient'),
        doctors: yup.array().min(1, 'Atleast one required'),
        patientSearch: yup.object().required('Required'),
        provider: yup.array(
            yup.object().shape({
                label: yup.string(),
                value: yup.string(),
            })
        ),
        date: yup.string().required('Please enter a date').optional(),
        startTime: yup.string().required('Please enter a start time'),
        endTime: yup.string().required('Please enter a end time'),
        reason: yup.string().optional(),
        type: isNonEmptyObject('Please enter appointment type').shape({
            label: yup.string(),
            value: yup.string(),
        }),
        status: yup.string(),
        notes: yup.string().max(200),
        triage: yup
            .object()
            .shape({
                label: yup.string(),
                value: yup.string(),
            })
            .optional(),
        room: yup
            .object()
            .shape({
                label: yup.string(),
                value: yup.string(),
            })
            .optional(),
        weight: yup.number().max(200).nullable(),
        // patientSearch: yup.string().required('Required'),
    });

    const combiineSchma = yup.object().shape({
        ...validationSchemaAppointment.fields,
        ...(isAddPatientModalOpen ? patientValidationSchema.fields : {}),
    });

    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors, isDirty, dirtyFields },
        watch,
        trigger,
    } = useForm({
        resolver: yupResolver(combiineSchma),
        mode: 'onChange',
    });

    useEffect(() => {
        const weight: string = String(getValues('weight'));
        if (weight === '') setValue('weight', null);
    }, [watch('weight')]);

    const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const { createAppointmentMutation } = useAppointmentMutation(
        setShowAppointmentModal
    );

    const handlePatientCreation = async () => {
        const isValid = await trigger([
            'ownerFirstName',
            'ownerLastName',
            'patientName',
            'phoneNumber',
        ]);
        if (!isValid) {
            return false;
        }

        const formData = getValues();

        const patientRenderingData = {
            id: '',
            patientName: formData.patientName,
            patientOwners: [
                {
                    owner: {
                        phoneNumber: formData.phoneNumberNational,
                        firstName: formData.ownerFirstName,
                        lastName: formData.ownerLastName,
                    },
                },
            ],
        };

        try {
            const createdPatientData = await createPatientMutation.mutateAsync({
                type: 'create',
                data: {
                    patientName: formData.patientName,
                    clinicId: CLINIC_ID,
                    ownersData: [
                        {
                            firstName: formData.ownerFirstName,
                            lastName: formData.ownerLastName,
                            phoneNumber: formData.phoneNumberNational,
                            countryCode: formData.countryCode,
                            isPrimary: true,
                        },
                    ],
                },
            });
            patientRenderingData.id = createdPatientData.data.id;
            setPatientData(patientRenderingData);
            setValue('patientSearch', patientRenderingData);
            setValue(
                'ownerId',
                createdPatientData.data.patientOwners[0].ownerBrand.id
            );
            setValue('patientId', createdPatientData.data.id);
            return true;
        } catch (error) {
            console.error('Error creating patient:', error);
            return false;
        }
    };

    const debouncedSetPatientParams = _.debounce((setParams, searchTerm) => {
        setParams({
            clinicId: CLINIC_ID,
            limit: 10,
            page: 1,
            search: searchTerm,
        });
    }, 500);

    const getPatientOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        debouncedSetPatientParams(setPatientParams, search);

        // setPatientParams({
        //     clinicId: CLINIC_ID,
        //     limit: 10,
        //     page: 1,
        //     search: search,
        // });
        const options = patientsData?.data.patients.map((item: any) => {
            return item;
        });
        return {
            options,
            hasMore: false,
        };
    };

    const handleAddAppointment = (DefaultPatientData: boolean = true) => {
        reset();
        if (DefaultPatientData) {
            setPatientData({});
        }
        if (!DefaultPatientData) {
            setValue('patientSearch', patientData);
        }
        setSelectedPatient(null);
        setEditMode(false);
        const now = moment();

        const nextQuarter = moment()
            .startOf('hour')
            .add(Math.ceil(now.minute() / 15) * 15, 'minutes');

        const appointmentStartTime = nextQuarter.format('D-MMM-YYYY HH:mm');

        const appointmentEndTime = nextQuarter
            .add(30, 'minutes')
            .format('D-MMM-YYYY HH:mm');

        setValue('date', moment().format('D-MMM-YYYY'), {
            shouldValidate: true,
        });
        setValue('startTime', appointmentStartTime, {
            shouldValidate: true,
        });
        setValue('endTime', appointmentEndTime, {
            shouldValidate: true,
        });
        setValue(
            'type',
            {
                value: 'General Visit',
                label: 'General Visit',
            },
            { shouldValidate: true }
        );
        setValue('status', 'Scheduled', { shouldValidate: true });
        setShowAppointmentModal(true);
    };

    useEffect(() => {
        const search = watch('patientSearch');

        if (search) {
            setShowPatientDropdown(true);
            setListStatus('pending');
            // getPatientOptions
            getPatientOptions(search, []).then((data: any) => {
                setPatientList(data.options);
                setListStatus('success');
            });
        } else {
            setShowPatientDropdown(false);
        }
    }, [watch('patientSearch')]);

    useEffect(() => {
        const values = getValues();
        if (!_.isEqual(values, selectedAppointmentData)) {
            setValuesChanged(true);
        } else {
            setValuesChanged(false);
        }
    }, [watch()]);

    // const filterTableData = appointmentData?.data?.appointments.filter((list: any) => list.deletedAt === null);
    return (
        <>
            <div className="h-full w-full flex flex-col gap-10">
                {doctorStauts === 'pending' ||
                clinicRoomsStatus === 'pending' ||
                providerStatus === 'pending' ? (
                    <div>Loading....</div>
                ) : (
                    <>
                        {/* <Heading type="h3" className="text-neutral-900">
                                Appointments Page
                            </Heading>
                            <div className="w-fit mr-0 ml-auto justify-end flex ">
                                <Button
                                    id="create-appointment"
                                    type="button"
                                    variant="primary"
                                    label="Create Appointment"
                                    onClick={handleAddAppointment}
                                />
                            </div>

                            <div className="bg-white rounded-lg  w-full h-full p-4 ">
                                <Table
                                    className=""
                                    columns={columns}
                                    tableData={
                                        appointmentData?.data.appointments ?? []
                                    }
                                    // pageCount={appointmentData?.data.total}
                                    listLoadStatus={appointmentStatus}
                                />
                            </div> */}

                        {/* {showAppointmentModal && (
                                <CreateAppointmentStaff
                                    patientProps={{
                                        showPatientDropdown,
                                        setShowPatientDropdown,
                                        listStatus,
                                        onMenuClick,
                                        patientList,
                                        selectedPatient,
                                    }}
                                    appointmentOptions={getAppointmentTypeOptions}
                                    assignRoomOptions={getRoomOptions}
                                    doctorOptions={getDoctorOptions}
                                    isOpen={showAppointmentModal}
                                    onClose={() => {
                                        setPatientData({});
                                        setShowAppointmentModal(false);
                                        setViewMode(false);
                                        setEditMode(false);
                                    }}
                                    getPatientOptions={getPatientOptions}
                                    reasonOptions={getReasonOptions}
                                    control={control}
                                    errors={errors}
                                    setValue={setValue}
                                    watch={watch}
                                    register={register}
                                    handleSubmit={handleSubmit}
                                    key={'Create Appointment'}
                                    triageOptions={getTriggerOption}
                                    modalTitle={
                                        editMode
                                            ? 'Edit Appointment'
                                            : 'Create Appointment'
                                    }
                                    handleCancel={handleCancel}
                                    onProviderDelete={handleProviderDelete}
                                    handleAddProvider={handleAddProvider}
                                    handleCreateAppointment={
                                        handleCreateAppointment
                                    }
                                    onStepHandler={onStepHandler}
                                    step={step}
                                    providerOptions={getProviderOptions}
                                    getValues={getValues}
                                    isEditable={true}
                                    patientData={patientData}
                                    editMode={editMode}
                                    handleUpdateAppointment={
                                        handleUpdateAppointment
                                    }
                                    valuesChanged={valuesChanged}
                                    scheduleStepHandler={scheduleStepHandler}
                                    onMoreActionClick={onCreateAppointmentAction}
                                />
                            )} */}
                        <DateProvider>
                            <CalendarView
                                totalAppointments={totalAppointments || 0}
                                appointmentList={appointmentData || []}
                                fetchNextPage={fetchNextPage}
                                clinicRoomsData={clinicRoomsData}
                                doctorData={doctorData}
                                patientsData={patientsData}
                                providerData={providerData}
                            />
                        </DateProvider>
                    </>
                )}

                {/* {confirmModal.isOpen &&
                    confirmModal.type === 'cancelAppointment' && (
                        <ConfirmModal
                            isOpen={confirmModal.isOpen}
                            alertType="warning"
                            modalDescription="sub-text"
                            modalTitle="Are you sure you want cancel this appointment?"
                            primaryBtnProps={{
                                dataAutomation: 'cancel-appointment-yes',
                                label: 'Yes',
                                onClick: () => {
                                    handleDeleteAppointment();
                                },
                            }}
                            onClose={() => {
                                setConfirmModal({
                                    isOpen: false,
                                    type: 'cancelAppointment',
                                });
                            }}
                            secondaryBtnProps={{
                                dataAutomation: 'cancel-appointment-no',
                                label: 'No',
                                onClick: () => {
                                    setShowAppointmentModal(true);
                                    setConfirmModal({
                                        isOpen: false,
                                        type: 'cancelAppointment',
                                    });
                                },
                            }}
                        />
                    )}

                {
                    <Alert
                        className={classNames(
                            'font-semibold fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[560px] transition-opacity',
                            showAlert.isOpen
                                ? 'opacity-100 scale-100'
                                : 'opacity-0 scale-0'
                        )}
                        {...showAlert}
                        onClose={() => {
                            setShowAlert({
                                isOpen: false,
                                variant: 'warning',
                                label: '',
                                isLight: true,
                            });
                        }}
                    />
                } */}
            </div>
        </>
    );
}
