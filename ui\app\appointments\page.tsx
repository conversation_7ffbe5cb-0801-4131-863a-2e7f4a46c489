'use client';

import { useEffect, useState } from 'react';
import { Alert } from '../atoms';
import { useClinicAppointments } from '../services/appointment.queries';
import { usePatients } from '../services/patient.queries';
import {
    useClinicDoctors,
    useClinicProviders,
} from '../services/providers.queries';
import { AppointmentParams } from '../types/appointment';
import { GetDoctorsType } from '../types/provider';
// import { CLINIC_ID } from '../utils/constant';
import moment from 'moment';
import { useClinicRooms } from '../services/clinic.queries';
import {
    useGetClinic,
    useGetClinicDetails,
} from '@/app/services/clinic.queries';
import { DateProvider } from '@/context/date-provider';
import classNames from 'classnames';
import { AlertT } from '../atoms/Alert';
import { getAuth } from '../services/identity.service';
import AppointmentDetails from '../template/AppointmentDetails';
import { getProfileImage } from '../utils/common';
import { processInfiniteQueryAppointments } from '../utils/appointment-utils';

export default function AssignemtsPage() {
    const CLINIC_ID = getAuth()?.clinicId;

    const {
        data: clinicData,
        isLoading,
        error,
        refetch,
    } = useGetClinic(CLINIC_ID);

    const [showAlert, setShowAlert] = useState<AlertT>({
        isOpen: false,
        label: 'Appointment created successfully',
        variant: 'success',
        isLight: true,
    });

    const [customRule, setCustomRule] = useState({
        patientLastNameAsOwnerLastName: false,
    });

    const { data: clinicDetails, status: clinicDetailsStatus } =
        useGetClinicDetails(CLINIC_ID);

    useEffect(() => {
        if (clinicDetails?.data?.customRule) {
            setCustomRule(clinicDetails.data.customRule);
        }
    }, [clinicDetails, clinicDetailsStatus]);

    // Log whenever customRule changes
    useEffect(() => {
        console.log('Current Custom Rule State:', customRule);
    }, [customRule]);

    const providerQueryParams: GetDoctorsType = {
        //TODO: need to fix this from the api side
        clinicId: CLINIC_ID,
        limit: 50,
        page: 1,
        orderBy: 'ASC',
    };
    const doctorQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 50,
        page: 1,
        orderBy: 'DESC',
        //role: 'doctor',
    };

    const [patientParams, setPatientParams] = useState({
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        search: '',
    });

    const { data: doctorData, status: doctorStauts } =
        useClinicDoctors(doctorQueryParams);

    const { data: providerData, status: providerStatus } =
        useClinicProviders(providerQueryParams);

    const { data: patientsData, status: patientsStatus } =
        usePatients(patientParams);
    const [appointmentParams, setAppointmentParams] =
        useState<AppointmentParams>({
            page: 1,
            limit: 10,
            orderBy: 'ASC',
            date: moment().format('YYYY-MM-DD').toString(),
            search: '',
            doctors: [],
            status: [],
            onlyPrimary: false,
        });
    const {
        data,
        fetchNextPage,
        hasNextPage,
        isFetching,
        isFetchingNextPage,
        status,
    } = useClinicAppointments(appointmentParams);
    const totalAppointments = data?.pages[0].data.total;

    const appointmentData = processInfiniteQueryAppointments(data);
    const { data: clinicRoomsData, status: clinicRoomsStatus } = useClinicRooms(
        {
            clinicId: CLINIC_ID,
        }
    );

    useEffect(() => {
        if (showAlert.isOpen) {
            const timer = setTimeout(() => {
                setShowAlert((prevState) => ({
                    ...prevState,
                    isOpen: false,
                }));
            }, 5000);

            return () => clearTimeout(timer);
        }
    }, [showAlert.isOpen]);

    const formattedWorkingHours = {
        Monday: [],
        Tuesday: [],
        Wednesday: [],
        Thursday: [],
        Friday: [],
        Saturday: [],
        Sunday: [],
    };

    if (clinicData?.data?.working_hours?.workingHours) {
        Object.entries(clinicData.data.working_hours.workingHours).forEach(
            ([day, hours]) => {
                const capitalizedDay =
                    day.charAt(0).toUpperCase() + day.slice(1);
                if (Array.isArray(hours)) {
                    formattedWorkingHours[capitalizedDay] = hours.map(
                        (slot) => ({
                            startTime: slot.startTime,
                            endTime: slot.endTime,
                            isWorkingDay: slot.isWorkingDay,
                        })
                    );
                }
            }
        );
    }

    return (
        <>
            <div className="h-full w-full flex flex-col gap-10">
                {doctorStauts === 'pending' ||
                clinicRoomsStatus === 'pending' ||
                clinicDetailsStatus === 'pending' ||
                providerStatus === 'pending' ? (
                    <div>Loading....</div>
                ) : (
                    <>
                        <DateProvider>
                            <AppointmentDetails
                                totalAppointments={totalAppointments || 0}
                                appointmentList={appointmentData || []}
                                fetchNextPage={fetchNextPage}
                                hasNextPage={hasNextPage}
                                clinicRoomsData={clinicRoomsData}
                                doctorData={doctorData}
                                patientsData={{
                                    patients: patientsData?.data?.patients?.map(
                                        (item: any) => {
                                            return {
                                                ...item,
                                                profileImage: getProfileImage({
                                                    breedValue: item.breed,
                                                    species: item.species,
                                                }),
                                            };
                                        }
                                    ),
                                }}
                                providerData={providerData}
                                setAppointmentParams={setAppointmentParams}
                                appointmentParams={appointmentParams}
                                setPatientParams={setPatientParams}
                                setShowAlert={setShowAlert}
                                patientStatus={patientsStatus}
                                clinicDetails={clinicDetails?.data}
                                workinghours={formattedWorkingHours}
                                customRule={customRule}
                            />
                        </DateProvider>
                    </>
                )}

                <Alert
                    className={classNames(
                        'font-semibold fixed top-4 left-1/2 -translate-x-1/2 z-50 !w-[560px] transition-opacity',
                        showAlert.isOpen
                            ? 'opacity-100 scale-100'
                            : 'opacity-0 scale-0'
                    )}
                    {...showAlert}
                    onClose={() => {
                        setShowAlert({
                            isOpen: false,
                            variant: 'success',
                            label: 'Appointment created succesfully',
                            isLight: true,
                        });
                    }}
                />
            </div>
        </>
    );
}
