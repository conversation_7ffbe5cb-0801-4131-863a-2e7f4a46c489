import { Button, Heading } from '@/app/atoms';
import IconEdit from '@/app/atoms/customIcons/IconEdit.svg';
import { Tooltip } from '@/app/molecules';

interface SectionProps {
    title: string;
    onEdit?: () => void;
    children: React.ReactNode;
    showEdit?: boolean;
    className?: string;
    childrenMarginrTop?: string;
    icon?: JSX.Element;
    tooltipContent?: string;
    actionComponent?: React.ReactNode; // Optional custom action element to show on the right side
}

export const Section = ({
    title,
    onEdit,
    children,
    showEdit = true,
    className = '',
    childrenMarginrTop = 'mt-4',
    icon = <IconEdit size={16} className="text-gray-500" />,
    tooltipContent = 'Edit',
    actionComponent,
}: SectionProps) => (
    <div className={`p-3 ${className}`}>
        <div className="flex justify-between items-center mb-2">
            <Heading
                type="h5"
                className="text-xl font-medium"
                textColor="text-gray-900"
            >
                {title}
            </Heading>
            {actionComponent ? (
                <div>{actionComponent}</div>
            ) : (
                showEdit && (
                    <Tooltip content={tooltipContent} position="bottom">
                        <div
                            onClick={onEdit}
                            className="h-8 w-8 rounded-full bg-primary-50 hover:bg-primary-100 flex justify-center items-center cursor-pointer"
                        >
                            <Button
                                id={`edit-${title.toLowerCase().replace(/\s+/g, '-')}`}
                                onlyIcon
                                size="mini"
                                onClick={onEdit}
                                variant="secondary"
                                icon={icon}
                            />
                        </div>
                    </Tooltip>
                )
            )}
        </div>
        <div className="border-b border-neutral-300"></div>
        <div className={childrenMarginrTop}>{children}</div>
    </div>
);
