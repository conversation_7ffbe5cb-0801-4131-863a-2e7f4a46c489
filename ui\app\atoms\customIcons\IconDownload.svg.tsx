import React from 'react';
import Svg, { SvgT } from './Svg';

interface IconDownloadT extends SvgT {}

const IconDownload = ({ viewBox = '0 0 20 20', ...rest }: IconDownloadT) => {
    return (
        <Svg viewBox={viewBox} {...rest}>
            <path
                fill="none"
                d="M10.099 12.8623L10.099 2.82812"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                fill="none"
                d="M12.5312 10.4219L10.1013 12.8619L7.67125 10.4219"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                fill="none"
                d="M13.9572 6.77344H14.7347C16.4305 6.77344 17.8047 8.1476 17.8047 9.84427V13.9143C17.8047 15.6059 16.4339 16.9768 14.7422 16.9768H5.45885C3.76302 16.9768 2.38802 15.6018 2.38802 13.9059L2.38802 9.8351C2.38802 8.14427 3.75969 6.77344 5.45052 6.77344L6.23552 6.77344"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </Svg>
    );
};

export default IconDownload;
