// components/Layout/ClientLayout.tsx
'use client';

import { useEffect, useState } from 'react';
import FetchedLayout from './components/Layout/FetchedLayout';

export default function ClientLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    return <>{isMounted && <FetchedLayout>{children}</FetchedLayout>}</>;
}
