import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import io, { Socket } from 'socket.io-client';

let socket: Socket | null = null;

export const useCalendarSocket = (clinicId?: string) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    // Avoid multiple sockets
    if (!socket) {
      const base = process.env.NEXT_PUBLIC_SOCKET_URL || window.location.origin;
      console.log('[useCalendarSocket] Connecting to socket at', `${base}/events/appointments/`);
      
      socket = io(`${base}/events/appointments/`, {
        transports: ['websocket'],
      });
      
      // Log connection events for debugging
      socket.on('connect', () => {
        console.log('[useCalendarSocket] Socket connected:', socket?.id);
      });
      
      socket.on('connect_error', (err) => {
        console.error('[useCalendarSocket] Socket connection error:', err);
      });
    }

    // Join clinic room if clinicId is provided
    if (clinicId) {
      console.log('[useCalendarSocket] Joining clinic room:', `clinic:${clinicId}`);
      socket.emit('join', `clinic:${clinicId}`);
    }

    const handler = (data: { userId?: string }) => {
      console.log('[useCalendarSocket] Received calendar-updated event:', data);
      // Invalidate any query whose key starts with 'appointments'
      queryClient.invalidateQueries({ predicate: q => q.queryKey[0] === 'appointments' });
      // Immediately refetch to get latest data without waiting for focus/refetch interval
      queryClient.refetchQueries({ predicate: q => q.queryKey[0] === 'appointments' });
    };

    socket.on('calendar-updated', handler);
    console.log('[useCalendarSocket] Listening for calendar-updated events');

    return () => {
      socket?.off('calendar-updated', handler);
      if (clinicId) {
        socket?.emit('leave', `clinic:${clinicId}`);
      }
    };
  }, [queryClient, clinicId]);
}; 