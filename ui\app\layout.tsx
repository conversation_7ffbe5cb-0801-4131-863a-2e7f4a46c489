import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import FetchedLayout from './components/Layout/FetchedLayout';
import ReactQueryProvider from './utils/provider';
import ClientLayout from './clientLayout';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'Nidana',
    description: 'App',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en" className={inter.className}>
            <body>
                <ReactQueryProvider>
                    <ClientLayout>{children}</ClientLayout>
                </ReactQueryProvider>
            </body>
        </html>
    );
}
