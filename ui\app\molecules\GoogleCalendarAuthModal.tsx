'use client';

import React, { useEffect, useState } from 'react';
import { Text, But<PERSON>, Heading } from '@/app/atoms';
import { useSearchParams } from 'next/navigation';

/**
 * GoogleCalendarAuthModal
 * -----------------------
 * Detects the `google-auth` query parameter appended after the backend OAuth
 * redirect and displays a full-screen modal indicating success or failure.
 *
 * - ?google-auth=success  →  success message
 * - ?google-auth=failed   →  failure message
 *
 * Once the modal is closed, the parameter is removed from the URL so the
 * popup does not re-appear on page refresh.
 */
const GoogleCalendarAuthModal: React.FC = () => {
    const searchParams = useSearchParams();
    const [status, setStatus] = useState<string | null>(null);
    const [dismissed, setDismissed] = useState(false);

    // Capture the status once; ignore subsequent changes (in case the query param disappears)
    useEffect(() => {
        if (!status) {
            const s = searchParams.get('google-auth');
            if (s) setStatus(s);
        }
    }, [searchParams, status]);

    if (!status || dismissed) return null;

    const isSuccess = status === 'success';
    const title = isSuccess
        ? 'Google Calendar Connected'
        : 'Google Calendar Connection Failed';
    const subtitle = isSuccess
        ? 'Your Google Calendar has been successfully connected.'
        : 'We could not connect your Google Calendar. Please try again.';

    const handleCloseTab = () => {
        // Attempt to close the current window (works when opened via window.open)
        window.close();
        // Fallback: simply hide the overlay if window cannot be closed
        setDismissed(true);
    };

    return (
        <div
            className="fixed inset-0 w-screen h-screen bg-[#303D34] bg-opacity-40 backdrop-blur-md flex justify-center items-center z-[999]"
            role="dialog"
            aria-modal="true"
            data-automation="google-calendar-auth-modal"
        >
            <div className="bg-basic-white rounded-2xl p-8 max-w-md w-full text-center flex flex-col items-center gap-4">
                <Heading type="h5" className="text-neutral-900 font-semibold">
                    {title}
                </Heading>
                <Text className="text-neutral-700">{subtitle}</Text>
                <Button
                    id="close-tab-btn"
                    variant="primary"
                    onClick={handleCloseTab}
                    label="Continue"
                />
            </div>
        </div>
    );
};

export default GoogleCalendarAuthModal;
