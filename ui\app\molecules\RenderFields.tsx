import React from 'react';
import classNames from 'classnames';
import { Button, Input, Textarea } from '../atoms';
import IconDelete from '../atoms/customIcons/IconDelete';
import AsyncReactSelectPaginate, {
    OptionType,
} from './AsyncReactSelectPaginate';
import CheckboxButtonGroup, {
    CheckboxButtonItemsT,
} from './CheckboxButtonGroup';
import DatePicker from './DatePicker';
import RadioButtonGroup, { RadioButtonItemsT } from './RadiobuttonGroup';
import NumberWithCountry from './NumberWithCountry';
import ReactSelectPaginate from './reactSelectPaginate';
import NumberWithBadge from './NumberWithBadge';
import PhoneInput from '../atoms/PhoneInput';
import RangeDatePicker from './RangeDatePicker';

export type ErrorMessage = {
    [key: string]: { message: string };
};

export type fieldsType = {
    type?:
        | 'text-input'
        | 'password-input'
        | 'textarea'
        | 'date'
        | 'range-date'
        | 'select'
        | 'multi-select'
        | 'number-input'
        | 'checkbox'
        | 'radio'
        | 'async-select'
        | 'number-with-country'
        | 'react-select'
        | 'number-with-Badge'
        | 'phone-number-input';
    fieldVariant?: 'default' | 'basicField' | 'borderField' | 'timeField';
    name: string;
    id: string;
    label?: string;
    placeholder?: string;
    required?: boolean;
    hidden?: boolean;
    width?: string;
    fieldSize?: 'large' | 'small' | 'medium';
    defaultValue?: string;
    options?: OptionType[];
    column?: string;
    value?: any;
    isEdit?: boolean;
    handleInputEdit?: () => void;
    maxLength?: number;
    isAutoGrow?: boolean;
    isSearchable?: boolean;
    isShowSearchIcon?: boolean;
    centerAlign?: boolean;
    showEyeIcon?: boolean;
    showBadge?: boolean;
    customBadgeText?: string;
    badgeVariant?: 'success' | 'warning' | 'error';

    checkboxButtonItems?: CheckboxButtonItemsT[];
    groupTitle?: string;
    direction?: 'column' | 'row';
    radioButtonItems?: RadioButtonItemsT[];
    loadOptions?: any;
    disabled?: boolean;
    isMulti?: boolean;
    dateFormat?: string;
    showTimeSelect?: boolean;
    showTimeSelectOnly?: boolean;
    timeIntervals?: any;
    timeCaption?: string;
    minDate?: Date;
    maxDate?: Date;
    onChange?: (change: any) => void;
    rows?: number;
    onBlur?: (event: any) => void;
    className?: string;
    minHeight?: string;
    maxHeight?: string;
    inline?: boolean;
    pattern?: string;
    isFit?: boolean;
    errorMessage?: string;
    resizeValue?: string;
    isHideMaxLengthIndicator?: boolean;
    wrapperGap?: string;
    extraSpacing?: boolean;
    togglePasswordVisibility?: any;
};

export interface RenderFieldsTypes {
    className?: string;
    fields: fieldsType[];
    grid?: string;
    control: null | object;
    register?: any;
    errors: ErrorMessage | any;
    setValue: any;
    watch: any;
    gap?: string;
    width?: string;
    fieldSize?: 'large' | 'small' | 'medium';
    onDelete?: (item: fieldsType) => void;
    maxLength?: number;
    isAutoGrow?: boolean;
}

const RenderFields = ({
    fields,
    grid = 'grid-cols-1',
    gap = 'gap-5',
    width = 'w-full',
    className = '',
    control,
    register,
    errors,
    setValue,
    watch,
    onDelete,
}: RenderFieldsTypes) => {
    function getErrorMessage(name: string): string | undefined {
        return errors[name]?.message;
    }

    return (
        <div className={`grid ${gap} ${className} ${grid} ${width}`}>
            {fields.map((field: fieldsType) => {
                const {
                    type = 'text-input',
                    name,
                    id,
                    rows,
                    hidden = false,
                    fieldSize,
                    dateFormat = 'yyyy-MM-dd',
                    value,
                    isEdit,
                    handleInputEdit,
                    minHeight,
                    maxHeight,
                    pattern,
                    errorMessage,
                    centerAlign,
                    wrapperGap,
                    showEyeIcon,
                    ...rest
                } = field;

                if (hidden) return null;
                switch (type) {
                    case 'text-input':
                        return (
                            <Input
                                key={id}
                                id={id}
                                name={name}
                                errorMessage={
                                    errorMessage
                                        ? errorMessage
                                        : getErrorMessage(name)
                                }
                                variant={rest?.fieldVariant}
                                size={fieldSize}
                                register={register}
                                containerClass={rest.column}
                                isEdit={isEdit}
                                handleEdit={handleInputEdit}
                                value={watch(name) ?? value}
                                {...rest}
                            />
                        );
                    case 'phone-number-input':
                        return (
                            <PhoneInput
                                key={id}
                                id={id}
                                name={name}
                                errorMessage={
                                    errorMessage
                                        ? errorMessage
                                        : getErrorMessage(name)
                                }
                                size={fieldSize}
                                containerClass={rest.column}
                                onChange={handleInputEdit}
                                value={watch(name) ?? value}
                                {...rest}
                            />
                        );

                    case 'number-input':
                        return (
                            <Input
                                type="number"
                                key={id}
                                id={id}
                                name={name}
                                errorMessage={
                                    errorMessage
                                        ? errorMessage
                                        : getErrorMessage(name)
                                }
                                variant={rest?.fieldVariant}
                                size={fieldSize}
                                register={register}
                                containerClass={rest.column}
                                isEdit={isEdit}
                                handleEdit={handleInputEdit}
                                value={watch(name)}
                                {...rest}
                            />
                        );

                    case 'password-input':
                        return (
                            <Input
                                key={id}
                                id={id}
                                name={name}
                                errorMessage={
                                    errorMessage
                                        ? errorMessage
                                        : getErrorMessage(name)
                                }
                                variant={rest?.fieldVariant}
                                size={fieldSize}
                                register={register}
                                containerClass={rest.column}
                                isEdit={isEdit}
                                handleEdit={handleInputEdit}
                                value={watch(name)}
                                type="password"
                                centerAlign={centerAlign}
                                isHideMaxLengthIndicator={
                                    rest?.isHideMaxLengthIndicator
                                }
                                showEyeIcon={showEyeIcon}
                                {...rest}
                                {...(rest?.onChange && { register: undefined })}
                            />
                        );
                    case 'textarea':
                        return (
                            <Textarea
                                key={id}
                                id={id}
                                name={name}
                                rows={rows}
                                value={watch(name) ?? value}
                                errorMessage={
                                    errorMessage
                                        ? errorMessage
                                        : getErrorMessage(name)
                                }
                                variant={rest?.fieldVariant}
                                size={fieldSize}
                                register={register}
                                containerClass={rest.column}
                                maxLength={rest.maxLength}
                                isAutoGrow={rest.isAutoGrow}
                                {...rest}
                            />
                        );
                    case 'date':
                        return (
                            <DatePicker
                                key={id}
                                id={id}
                                name={name}
                                dateFormat={dateFormat}
                                register={register}
                                errorMessage={getErrorMessage(name)}
                                showTimeSelect={rest.showTimeSelect}
                                showTimeSelectOnly={rest.showTimeSelectOnly}
                                timeIntervals={rest.timeIntervals}
                                timeCaption={rest.timeCaption}
                                containerClass={rest.column}
                                variant={rest.fieldVariant}
                                value={value}
                                watch={watch}
                                onDateChange={(date: Date | null) => {
                                    setValue(name, date, {
                                        shouldValidate: true,
                                    });
                                    rest.onChange?.(date);
                                }}
                                {...rest}
                            />
                        );

                    case 'range-date':
                        return (
                            <RangeDatePicker
                                key={id}
                                id={id}
                                name={name}
                                dateFormat={dateFormat}
                                register={register}
                                errorMessage={getErrorMessage(name)}
                                containerClass={rest.column}
                                variant={rest.fieldVariant}
                                startDateValue={rest.startDate}
                                endDateValue={rest.endDate}
                                watch={watch}
                                onDateChange={({ startDate, endDate }) => {
                                    setValue(
                                        name,
                                        { startDate, endDate },
                                        { shouldValidate: true }
                                    );
                                    rest.onChange?.({ startDate, endDate });
                                }}
                                {...rest}
                            />
                        );

                    case 'checkbox':
                        return (
                            <CheckboxButtonGroup
                                key={id}
                                id={id}
                                checkboxButtonItems={
                                    rest.checkboxButtonItems || []
                                }
                                groupTitle={rest.groupTitle}
                                direction={rest.direction ?? 'column'}
                                errorMessage={getErrorMessage(name)}
                                name={name}
                                register={register}
                                required={rest.required}
                                isFit={rest.isFit}
                                onChange={rest?.onChange}
                                wrapperGap={wrapperGap}
                                {...rest}
                            />
                        );

                    case 'radio':
                        return (
                            <RadioButtonGroup
                                key={id}
                                id={id}
                                radioButtonItems={rest.radioButtonItems ?? []}
                                groupTitle={rest.groupTitle}
                                direction={rest.direction ?? 'column'}
                                errorMessage={getErrorMessage(name)}
                                name={name}
                                register={register}
                                required={rest.required}
                            />
                        );

                    case 'async-select':
                        return (
                            <div key={id} className="flex gap-5">
                                <AsyncReactSelectPaginate
                                    key={id}
                                    errorMessage={errors[name]?.message}
                                    id={id}
                                    name={name}
                                    className="w-full cursor-pointer"
                                    loadOptions={rest?.loadOptions}
                                    control={control}
                                    register={register}
                                    errors={errors}
                                    isMulti={rest?.isMulti}
                                    defaultValue={watch(name)}
                                    required={rest?.required}
                                    placeholder={rest?.placeholder ?? ''}
                                    disabled={rest?.disabled}
                                    containerClass={rest.column + ' w-full'}
                                    onChange={(data: any) => {
                                        setValue(name, data);
                                        rest?.onChange?.(data);
                                    }}
                                    variant={rest?.fieldVariant}
                                    options={rest?.options}
                                    value={value}
                                    setValue={setValue}
                                    isSearchable={rest?.isSearchable}
                                    {...rest}
                                />
                                {onDelete && (
                                    <Button
                                        id="delete-button"
                                        onlyIcon
                                        type="button"
                                        variant="borderless"
                                        size="small"
                                        className={classNames(
                                            'group shrink-0 !min-w-10 !min-h-10',
                                            rest?.label && 'mt-7'
                                        )}
                                        onClick={() => onDelete(field)}
                                    >
                                        <IconDelete
                                            className="text-primary-900 group-hover:text-error-100 transition-all"
                                            size={24}
                                        />
                                    </Button>
                                )}
                            </div>
                        );

                    case 'react-select':
                        return (
                            <div
                                key={id}
                                className={`flex gap-5 ${rest.column || ''}`}
                            >
                                <ReactSelectPaginate
                                    key={id}
                                    errorMessage={errors[name]?.message}
                                    id={id}
                                    name={name}
                                    className="w-full cursor-pointer"
                                    loadOptions={rest?.loadOptions}
                                    control={control}
                                    register={register}
                                    errors={errors}
                                    isMulti={rest?.isMulti}
                                    defaultValue={watch(name)}
                                    required={rest?.required}
                                    placeholder={rest?.placeholder ?? ''}
                                    disabled={rest?.disabled}
                                    containerClass={rest.column + ' w-full'}
                                    onChange={(data: any) => {
                                        setValue(name, data);
                                        rest?.onChange?.(data);
                                    }}
                                    variant={rest?.fieldVariant}
                                    options={rest?.options}
                                    value={value}
                                    setValue={setValue}
                                    isSearchable={rest?.isSearchable}
                                    {...rest}
                                />
                                {onDelete && (
                                    <Button
                                        id="delete-button"
                                        onlyIcon
                                        type="button"
                                        variant="borderless"
                                        size="small"
                                        className={classNames(
                                            'group shrink-0 !min-w-10 !min-h-10',
                                            rest?.label && 'mt-7'
                                        )}
                                        onClick={() => onDelete(field)}
                                    >
                                        <IconDelete
                                            className="text-primary-900 group-hover:text-error-100 transition-all"
                                            size={24}
                                        />
                                    </Button>
                                )}
                            </div>
                        );

                    case 'number-with-country':
                        return (
                            <NumberWithCountry
                                value={
                                    name !== 'phoneNumber' ? value : watch(name)
                                }
                                key={id}
                                id={id}
                                name={name}
                                containerClass={rest.column}
                                errorMessage={
                                    errorMessage
                                        ? errorMessage
                                        : getErrorMessage(name)
                                }
                                onChange={(mobileNumber) => {
                                    setValue(name, mobileNumber);
                                }}
                                {...rest}
                            />
                        );
                    case 'number-with-Badge':
                        return (
                            <div className={classNames('w-full', rest.column)}>
                                <NumberWithBadge
                                    value={
                                        name !== 'phoneNumber' ? value : watch(name)
                                    }
                                    key={id}
                                    id={id}
                                    name={name}
                                    containerClass="w-full"
                                    errorMessage={
                                        errorMessage
                                            ? errorMessage
                                            : getErrorMessage(name)
                                    }
                                    onChange={(mobileNumber) => {
                                        setValue(name, mobileNumber);
                                    }}
                                    showBadge={rest.showBadge}
                                    customBadgeText={rest.customBadgeText || ''}
                                    badgeVariant={rest.badgeVariant}
                                    {...rest}
                                />
                            </div>
                        );
                    default:
                        return null;
                }
            })}
        </div>
    );
};

export default RenderFields;
