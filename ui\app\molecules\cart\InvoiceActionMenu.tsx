import React from 'react';
import { Button } from '../../atoms';
import DropdownMenu, { MenuListType } from '../DropdownMenu';
import IconEdit from '@/app/atoms/customIcons/IconEdit.svg';
import IconWarning from '@/app/atoms/customIcons/IconWarning.svg';
import { PaymentStatus } from './InvoiceBasicDetail';

export interface InvoiceActionMenuProps {
    onEdit?: () => void;
    onDelete?: () => void;
    onWriteOff?: () => void;
    onSaveChanges?: () => void;
    isEditing?: boolean;
    isSaveDisabled?: boolean;
    isSaving?: boolean;
    paymentStatus?: PaymentStatus;
    className?: string;
}

const InvoiceActionMenu: React.FC<InvoiceActionMenuProps> = ({
    onEdit,
    onDelete,
    onWriteOff,
    onSaveChanges,
    isEditing = false,
    isSaveDisabled = false,
    isSaving = false,
    paymentStatus,
    className = '',
}) => {
    if (isEditing) {
        return (
            <div className="flex items-center justify-end p-2 shadow-bottom-md z-[5]">
                {isEditing && !isSaveDisabled && (
                    <p className="text-[#DC2020] text-sm pr-3">
                        You have unsaved edits.
                    </p>
                )}
                <div className="pr-2">
                    <Button
                        className={`flex items-center justify-center ${className}`}
                        id="invoice-save-changes-btn"
                        type="button"
                        variant="link"
                        size="extraSmall"
                        icon={
                            <IconWarning size={16} className="text-gray-500" />
                        }
                        iconPosition="left"
                        disabled={isSaveDisabled}
                        onClick={onSaveChanges}
                    >
                        {isSaving ? 'Saving...' : 'Save Edits'}
                    </Button>
                </div>
            </div>
        );
    }

    const isEditDeleteDisabled = paymentStatus !== PaymentStatus.PENDING;
    const isWriteOffDisabled =
        paymentStatus !== PaymentStatus.PENDING &&
        paymentStatus !== PaymentStatus.PARTIALLY_PAID;

    const menuItems: MenuListType[] = [];

    if (onEdit && !isEditDeleteDisabled) {
        menuItems.push({ id: 'edit', label: 'Edit Invoice' });
    }
    if (onDelete && !isEditDeleteDisabled) {
        menuItems.push({ id: 'delete', label: 'Cancel' });
    }
    if (onWriteOff && !isWriteOffDisabled) {
        menuItems.push({ id: 'write-off', label: 'Write-Off ' });
    }

    if (
        menuItems.length === 0 ||
        paymentStatus === PaymentStatus.CANCELLED ||
        paymentStatus === PaymentStatus.WRITTEN_OFF ||
        paymentStatus === PaymentStatus.PAID
    ) {
        return null;
    }

    const handleMenuClick = (item: MenuListType) => {
        if (item.id === 'edit') {
            onEdit && onEdit();
        } else if (item.id === 'delete') {
            onDelete && onDelete();
        } else if (item.id === 'write-off') {
            onWriteOff && onWriteOff();
        }
    };

    return (
        <div className="flex items-center justify-end p-2 shadow-bottom-md z-[5]">
            <div className="pr-2">
                <DropdownMenu
                    minWidth="min-w-[160px]"
                    menuDirection="rightAuto"
                    onMenuClick={handleMenuClick}
                    menuList={menuItems}
                >
                    <Button
                        className={`flex items-center justify-center ${className}`}
                        id="invoice-action-menu-btn"
                        type="button"
                        variant="link"
                        size="extraSmall"
                        icon={<IconEdit size={16} className="text-gray-500" />}
                        iconPosition="left"
                    >
                        Make Changes
                    </Button>
                </DropdownMenu>
            </div>
        </div>
    );
};

export default InvoiceActionMenu;
