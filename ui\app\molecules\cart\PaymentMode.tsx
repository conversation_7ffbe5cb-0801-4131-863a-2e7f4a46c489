import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import AsyncReactSelectPaginate from '../AsyncReactSelectPaginate';
import CurrencyInput, {
    CurrencyInputOnChangeValues,
} from 'react-currency-input-field';
import { PAYMENT_MODE_OPTIONS } from '@/app/utils/constant';

export type PaymentModeType = 'Cash' | 'Card' | 'Cheque' | 'Wallet' | 'Bank Transfer';
export type onChangeAmount = (
    value: string | undefined,
    name?: string,
    values?: CurrencyInputOnChangeValues
) => void;
export type onPaymentModeChange = (value: PaymentModeType) => void;

interface PaymentModeT {
    paymentModeValue?: PaymentModeType;
    onPaymentModeChange: onPaymentModeChange;
    totalPayableAmount: number;
    onChangeAmount: onChangeAmount;
    className?: string;
    classname?: string;
}
type PAYMENT_MODE_OPTIONS_TYPE =
    | { label: 'Cash'; value: 'Cash' }
    | { label: 'Cheque'; value: 'Cheque' }
    | { label: 'Card'; value: 'Card' }
    | { label: 'Wallet'; value: 'Wallet' }
    | { label: 'Bank Transfer'; value: 'Bank Transfer' };

const PaymentMode = (props: PaymentModeT) => {
    const {
        paymentModeValue = { label: 'Cash', value: 'cash' },
        onPaymentModeChange,
        totalPayableAmount,
        onChangeAmount,
        className = 'rounded-3xl',
        classname = 'rounded-3xl',
    } = props;

    const [amount, setAmount] = useState<string | undefined>('0'); // Always start with 0
    const [selectedMode, setSelectedMode] = useState<PAYMENT_MODE_OPTIONS_TYPE>(
        { label: 'Cash', value: 'Cash' }
    );

    // Remove the useEffect that automatically updates amount based on totalPayableAmount
    // Users should manually enter the amount they want to pay

    const getPaymentModeOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: PAYMENT_MODE_OPTIONS,
            hasMore: false,
        };
    };

    const onChange = (
        value: string | undefined,
        name?: string,
        values?: CurrencyInputOnChangeValues
    ) => {
        if (value) {
            const [actualVal, decimalVa] = value?.split('.');
            let finalVal = value;

            if (Number(actualVal) > 99999999) {
                finalVal =
                    actualVal.slice(0, 8) + (decimalVa ? '.' + decimalVa : '');
                setAmount(finalVal);
            } else {
                setAmount(finalVal);
            }
            onChangeAmount(finalVal, name, values);
        } else {
            setAmount(value);
            onChangeAmount(value, name, values);
        }
    };

    return (
        <div
            className={classNames(
                className,
                'bg-primary-50  grid grid-cols-[1fr_auto] px-4 '
            )}
        >
            <CurrencyInput
                prefix="₹"
                className={classNames(
                    'outline-0 bg-transparent w-full text-sm',
                    classname
                )}
                id="input-example"
                name="input-name"
                placeholder="Amount Payable"
                value={amount}
                decimalsLimit={2}
                onValueChange={onChange}
            />

            <AsyncReactSelectPaginate
                isSearchable={false}
                id={'body-map-select'}
                name={'body-map-select'}
                className="w-20"
                variant={'blank'}
                loadOptions={getPaymentModeOptions}
                defaultValue={selectedMode}
                placeholder={''}
                onChange={(change: PAYMENT_MODE_OPTIONS_TYPE) => {
                    setSelectedMode(change);
                    onPaymentModeChange(change.value as PaymentModeType);
                }}
                menuPlacement="top"
                value={selectedMode}
                control={{}}
                errors={{}}
                register={() => {}}
                setValue={() => {}}
                isCurrencyInput={true}
            />
        </div>
    );
};

export default PaymentMode;

// import classNames from 'classnames';
// import React from 'react';
// import AsyncReactSelectPaginate from '../AsyncReactSelectPaginate';
// import CurrencyInput, {
//     CurrencyInputOnChangeValues,
// } from 'react-currency-input-field';

// export type onChangeAmount =
//     | ((
//           value: string | undefined,
//           name?: string,
//           values?: CurrencyInputOnChangeValues
//       ) => void)
//     | undefined;
// interface PaymentModeType {
//     paymentModeValue?: { label: string; value: string };
//     onPaymentModeChange: (value: string) => void;
//     totalPayableAmount: number;
//     onChangeAmount:
//         | ((
//               value: string | undefined,
//               name?: string,
//               values?: CurrencyInputOnChangeValues
//           ) => void)
//         | undefined;
// }

// const PaymentMode = (props: PaymentModeType) => {
//     const {
//         paymentModeValue = { label: 'Cash', value: 'cash' },
//         onPaymentModeChange,
//         totalPayableAmount,
//         onChangeAmount,
//     } = props;

//     const getPaymentModeOptions = async (
//         search: string,
//         loadedOptions: unknown[]
//     ) => {
//         return {
//             options: [
//                 { label: 'Cash', value: 'cash' },
//                 { label: 'Cheque', value: 'Check' },
//                 { label: 'Card', value: 'Card' },
//                 { label: 'Wallet', value: 'Wallet' },
//             ],
//             hasMore: false,
//         };
//     };
//     return (
//         <div
//             className={classNames(
//                 classNames,
//                 'bg-primary-50 rounded-3xl grid grid-cols-[1fr_80px] gap-3 px-4'
//             )}
//         >
//             <CurrencyInput
//                 prefix="₹"
//                 className="outline-none bg-transparent w-full text-sm"
//                 id="input-example"
//                 name="input-name"
//                 placeholder="Amount Payable"
//                 defaultValue={totalPayableAmount}
//                 decimalsLimit={2}
//                 onValueChange={onChangeAmount}
//             />

//             <AsyncReactSelectPaginate
//                 isSearchable={false}
//                 id={'body-map-select'}
//                 variant="minimalField"
//                 name={'body-map-select'}
//                 className="w-full"
//                 loadOptions={getPaymentModeOptions}
//                 errorMessage=""
//                 isMulti={false}
//                 required={false}
//                 defaultValue={paymentModeValue}
//                 placeholder={''}
//                 onChange={(change) => onPaymentModeChange(change)}
//                 menuPlacement="top"
//                 value={paymentModeValue}
//                 control={{}}
//                 errors={{}}
//                 register={() => {}}
//                 setValue={() => {}}
//             />
//         </div>
//     );
// };

// export default PaymentMode;
