import React, { useEffect } from 'react';
import { Control, FieldErrors, UseFormReturn } from 'react-hook-form';
import Modal from '../Modal';
import RenderFields from '@/app/molecules/RenderFields';
import { parsePhoneNumber } from 'react-phone-number-input';
import { Button } from '@/app/atoms';
import { useUpdateUserMutation } from '@/app/services/user.queries';

interface EditBasicDetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    control: Control<any>;
    errors: FieldErrors;
    setValue: any;
    getValues: any;
    register: any;
    watch: (name: string) => any;
    userDetails: any;
}

const EditBasicDetailsModal: React.FC<EditBasicDetailsModalProps> = ({
    isOpen,
    onClose,
    control,
    errors,
    setValue,
    getValues,
    register,
    watch,
    userDetails,
}) => {
    const updateUserMutation = useUpdateUserMutation();

    // Only set form values when the modal opens
    useEffect(() => {
        if (isOpen && userDetails) {
            setValue('firstName', userDetails.firstName || '');
            setValue('lastName', userDetails.lastName || '');
            setValue('mobileNumber', userDetails.mobileNumber || '');
            setValue('countryCode', userDetails.countryCode || '');
            setValue(
                'alternateMobileNumber',
                userDetails.alternateMobileNumber || ''
            );
            setValue(
                'alternateCountryCode',
                userDetails.alternateCountryCode || ''
            );
            setValue('licenseNumber', userDetails.licenseNumber || '');
        }
    }, [isOpen, userDetails, setValue]);

    // Check if form is valid (all required fields are filled)
    const isFormValid = () => {
        const firstName = watch('firstName');
        const lastName = watch('lastName');
        const mobileNumber = watch('mobileNumber');

        return Boolean(
            firstName &&
                firstName.trim() !== '' &&
                lastName &&
                lastName.trim() !== '' &&
                mobileNumber &&
                mobileNumber.trim() !== '' &&
                Object.keys(errors).length === 0
        );
    };

    // Get form validity
    const isValid = isFormValid();

    const getErrorMessage = (fieldName: string): any => {
        return errors[fieldName]?.message;
    };

    // Handle phone number change with country code
    const handlePhoneNumberChange = (value: string) => {
        const phoneNumber = parsePhoneNumber(value);
        if (phoneNumber && phoneNumber.countryCallingCode) {
            setValue('countryCode', phoneNumber.countryCallingCode);
            setValue('mobileNumber', phoneNumber.nationalNumber);
        } else {
            setValue('mobileNumber', value);
        }
    };

    // Handle alternate phone number change with country code
    const handleAlternatePhoneNumberChange = (value: string) => {
        const phoneNumber = parsePhoneNumber(value);
        if (phoneNumber && phoneNumber.countryCallingCode) {
            setValue('alternateCountryCode', phoneNumber.countryCallingCode);
            setValue('alternateMobileNumber', phoneNumber.nationalNumber);
        } else {
            setValue('alternateMobileNumber', value);
        }
    };

    const handleUpdate = () => {
        // Get raw values from form
        const firstName = watch('firstName');
        const lastName = watch('lastName');
        const countryCode = watch('countryCode');
        const alternateCountryCode = watch('alternateCountryCode');
        const licenseNumber = watch('licenseNumber');

        // Get mobile numbers and clean them up
        let mobileNumber = watch('mobileNumber') || '';
        let alternateMobileNumber = watch('alternateMobileNumber') || '';

        // Remove country code prefix if present
        if (mobileNumber.startsWith('+') && countryCode) {
            mobileNumber = mobileNumber.replace(`+${countryCode}`, '').trim();
        }

        if (alternateMobileNumber.startsWith('+') && alternateCountryCode) {
            alternateMobileNumber = alternateMobileNumber
                .replace(`+${alternateCountryCode}`, '')
                .trim();
        }

        const userData = {
            firstName,
            lastName,
            mobileNumber,
            countryCode,
            alternateMobileNumber,
            alternateCountryCode,
            licenseNumber,
        };

        updateUserMutation
            .mutateAsync({
                userId: userDetails.id,
                userData: userData,
            })
            .then(() => {
                onClose();
            })
            .catch((error) => {
                console.error('Update failed:', error);
            });
    };

    // Create mobileNumber with country code for display purposes
    const formattedMobileNumber =
        watch('countryCode') && watch('mobileNumber')
            ? `+${watch('countryCode')} ${watch('mobileNumber')}`
            : '';

    // Create alternate mobile number with country code for display purposes
    const formattedAlternateMobileNumber =
        watch('alternateCountryCode') && watch('alternateMobileNumber')
            ? `+${watch('alternateCountryCode')} ${watch('alternateMobileNumber')}`
            : '';

    // Define fields for the form with proper types
    const fields: any[] = [
        {
            id: 'firstName',
            name: 'firstName',
            label: 'First Name',
            placeholder: 'Enter first name',
            defaultValue: getValues('firstName') || '',
            value: getValues('firstName'),
            onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
                setValue('firstName', e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('firstName'),
        },
        {
            id: 'lastName',
            name: 'lastName',
            label: 'Last Name',
            placeholder: 'Enter last name',
            defaultValue: getValues('lastName') || '',
            value: getValues('lastName'),
            onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
                setValue('lastName', e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('lastName'),
        },
        {
            id: 'mobileNumber',
            name: 'mobileNumber',
            label: 'Phone Number',
            placeholder: 'Enter phone number',
            defaultValue: formattedMobileNumber || '',
            value: formattedMobileNumber,
            onChange: function (value: string) {
                handlePhoneNumberChange(value);
            },
            type: 'number-with-country',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('mobileNumber'),
        },
        {
            id: 'alternateMobileNumber',
            name: 'alternateMobileNumber',
            label: 'Alternate Phone',
            placeholder: 'Enter alternate phone',
            defaultValue: formattedAlternateMobileNumber || '',
            value: formattedAlternateMobileNumber,
            onChange: function (value: string) {
                handleAlternatePhoneNumberChange(value);
            },
            type: 'number-with-country',
            fieldSize: 'medium',
            errorMessage: getErrorMessage('alternateMobileNumber'),
        },
        {
            id: 'licenseNumber',
            name: 'licenseNumber',
            label: 'License Number',
            placeholder: 'Enter license number',
            defaultValue: getValues('licenseNumber') || '',
            value: getValues('licenseNumber'),
            onChange: (e: React.ChangeEvent<HTMLInputElement>) =>
                setValue('licenseNumber', e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            errorMessage: getErrorMessage('licenseNumber'),
        },
    ];

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            modalTitle="Edit Basic Details"
            modalWidth="max-w-xl"
            childrenPt="pt-0"
            childrenPr="pr-0"
            modalFooter={
                <div className="w-full flex gap-5 justify-end">
                    <Button
                        id="cancel"
                        variant="secondary"
                        type="button"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        id="Update"
                        variant="primary"
                        type="submit"
                        disabled={!isValid}
                        onClick={handleUpdate}
                    >
                        Update
                    </Button>
                </div>
            }
        >
            <div className="py-4">
                <RenderFields
                    control={control}
                    errors={errors}
                    fields={fields}
                    setValue={setValue}
                    register={register}
                    watch={watch}
                    grid="grid-cols-2 gap-6"
                />
            </div>
        </Modal>
    );
};

export default EditBasicDetailsModal;
