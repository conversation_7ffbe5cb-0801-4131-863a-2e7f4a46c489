import React, { useState } from 'react';
import Image from 'next/image';
import Button from '@/app/atoms/Button';
import Tag from '@/app/atoms/Tags';
import { Heading } from '@/app/atoms';
import { BadgeCheck, BadgeX } from 'lucide-react';
import EditClientBookingSettingsModal from '../ClientBooking/EditClientBookingSettingsModal';
import {
    ClientBookingSettingsDto,
    DoctorDto,
    TimeDuration,
    UpdateClientBookingSettingsDto,
} from '@/app/services/clinic.queries';
import { Tooltip } from '@/app/molecules';
import IconEdit from '@/app/atoms/customIcons/IconEdit.svg';
import HorizontalTabs, { TabItemType } from '@/app/atoms/HorizontalTabs';
import DetailItem from '@/app/molecules/DetailItem';

// Define the structure for a single working hour item if not already defined in DTOs
interface WorkingHourSlot {
    isWorkingDay: boolean;
    startTime: string | null;
    endTime: string | null;
}

// Define the structure for working hours dictionary
type WorkingHoursType = Record<string, WorkingHourSlot[] | undefined>;

// Define DoctorInfo structure if not imported (it should be if exported from queries.ts)
// interface DoctorInfo {
//     id: string;
//     name: string;
// }

// Define the structure for the settings state, aligning with the updated DTO
// We might need to explicitly type the state if inference isn't sufficient
// interface ClientBookingSettingsState extends ClientBookingSettingsDto {}

interface ClientBookingSettingsProps {
    initialSettings: ClientBookingSettingsDto | null;
    doctors: DoctorDto[];
    isLoading: boolean;
    updateSettingsMutation: (
        data: UpdateClientBookingSettingsDto
    ) => Promise<any>;
    clinicId: string;
}

/**
 * Displays the current client booking settings for a clinic.
 * Allows editing these settings via a modal.
 */
const ClientBookingSettings: React.FC<ClientBookingSettingsProps> = ({
    initialSettings,
    doctors,
    isLoading,
    updateSettingsMutation,
    clinicId,
}) => {
    const [settings, setSettings] = useState<ClientBookingSettingsDto>(
        initialSettings ?? {
            isEnabled: false,
            workingHours: {} as WorkingHoursType,
            allowedDoctorIds: null,
            minBookingLeadHours: 24,
            modificationDeadlineHours: 24,
            minBookingLeadTime: { days: 1, hours: 0, minutes: 0 },
            modificationDeadlineTime: { days: 1, hours: 0, minutes: 0 },
            maxAdvanceBookingTime: { days: 30, hours: 0, minutes: 0 },
            allowedDoctorsInfo: null,
            allowAllDoctors: false,
        }
    );
    const [isModalOpen, setIsModalOpen] = useState(false);

    React.useEffect(() => {
        if (initialSettings) {
            setSettings({
                ...initialSettings,
                minBookingLeadHours:
                    initialSettings.minBookingLeadHours ?? null,
                modificationDeadlineHours:
                    initialSettings.modificationDeadlineHours ?? null,
                minBookingLeadTime: initialSettings.minBookingLeadTime ?? null,
                modificationDeadlineTime:
                    initialSettings.modificationDeadlineTime ?? null,
                maxAdvanceBookingTime:
                    initialSettings.maxAdvanceBookingTime ?? null,
                allowedDoctorsInfo: initialSettings.allowedDoctorsInfo ?? null,
                allowAllDoctors: initialSettings.allowAllDoctors,
            });
        }
    }, [initialSettings]);

    const handleEdit = () => {
        if (!isLoading) {
            setIsModalOpen(true);
        }
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
    };

    const handleSaveChanges = async (
        updatedSettings: UpdateClientBookingSettingsDto
    ) => {
        await updateSettingsMutation(updatedSettings);
        setSettings((prev) => ({
            ...prev,
            isEnabled:
                updatedSettings.isEnabled !== undefined
                    ? updatedSettings.isEnabled
                    : prev.isEnabled,
            workingHours:
                updatedSettings.workingHours !== undefined
                    ? updatedSettings.workingHours
                    : prev.workingHours,
            allowedDoctorIds:
                updatedSettings.allowedDoctorIds !== undefined
                    ? updatedSettings.allowedDoctorIds
                    : prev.allowedDoctorIds,

            // Update all time fields
            minBookingLeadHours:
                updatedSettings.minBookingLeadHours !== undefined
                    ? updatedSettings.minBookingLeadHours
                    : prev.minBookingLeadHours,
            modificationDeadlineHours:
                updatedSettings.modificationDeadlineHours !== undefined
                    ? updatedSettings.modificationDeadlineHours
                    : prev.modificationDeadlineHours,
            minBookingLeadTime:
                updatedSettings.minBookingLeadTime !== undefined
                    ? updatedSettings.minBookingLeadTime
                    : prev.minBookingLeadTime,
            modificationDeadlineTime:
                updatedSettings.modificationDeadlineTime !== undefined
                    ? updatedSettings.modificationDeadlineTime
                    : prev.modificationDeadlineTime,
            maxAdvanceBookingTime:
                updatedSettings.maxAdvanceBookingTime !== undefined
                    ? updatedSettings.maxAdvanceBookingTime
                    : prev.maxAdvanceBookingTime,

            allowAllDoctors:
                updatedSettings.allowAllDoctors !== undefined
                    ? updatedSettings.allowAllDoctors
                    : prev.allowAllDoctors,
        }));
        setIsModalOpen(false);
    };

    // Define the desired order of days
    const dayOrder = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
    ];

    const formatTime = (time: string | null): string => {
        if (!time) return 'Not Set';
        const [hour, minute] = time.split(':');
        const hourNum = parseInt(hour, 10);
        const ampm = hourNum >= 12 ? 'PM' : 'AM';
        const formattedHour = hourNum % 12 === 0 ? 12 : hourNum % 12;
        return `${formattedHour}:${minute} ${ampm}`;
    };

    const formatMinutesToReadable = (
        minutes: number | null | undefined
    ): string => {
        if (minutes === null || minutes === undefined) return 'Not Set';
        if (minutes === 0) return 'No notice required';
        if (minutes < 60) {
            return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        let result = `${hours} hr${hours !== 1 ? 's' : ''}`;
        if (remainingMinutes > 0) {
            result += ` ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
        }
        return result;
    };

    const hoursToMinutes = (
        hours: number | null | undefined
    ): number | null => {
        if (hours === null || hours === undefined) return null;
        return hours * 60;
    };

    // Format a TimeDuration object into a human-readable string
    const formatTimeDuration = (
        duration: TimeDuration | null | undefined
    ): string => {
        if (!duration) return 'Not Set';

        // If all values are null or 0, return 'None'
        if (
            (!duration.days || duration.days === 0) &&
            (!duration.hours || duration.hours === 0) &&
            (!duration.minutes || duration.minutes === 0)
        ) {
            return 'None';
        }

        const parts: string[] = [];

        if (duration.days && duration.days > 0) {
            parts.push(`${duration.days} day${duration.days !== 1 ? 's' : ''}`);
        }

        if (duration.hours && duration.hours > 0) {
            parts.push(
                `${duration.hours} hour${duration.hours !== 1 ? 's' : ''}`
            );
        }

        if (duration.minutes && duration.minutes > 0) {
            parts.push(
                `${duration.minutes} minute${duration.minutes !== 1 ? 's' : ''}`
            );
        }

        return parts.join(', ');
    };

    const getDoctorNames = (
        settingsData: ClientBookingSettingsDto | null
    ): string[] | string => {
        if (isLoading || !settingsData) return 'Loading...';

        if (
            settingsData.allowedDoctorsInfo &&
            settingsData.allowedDoctorsInfo.length > 0
        ) {
            return settingsData.allowedDoctorsInfo.map((doc) => doc.name);
        }

        const ids = settingsData.allowedDoctorIds;
        if (ids === null || ids === undefined) {
            return 'All Doctors';
        }
        if (ids.length === 0) {
            return settingsData.allowedDoctorIds === null
                ? 'All Doctors'
                : 'No specific doctors selected';
        }

        return ids.map((id) => {
            // Find the doctor directly in the 'doctors' array
            const doctor = doctors.find((d: DoctorDto) => d.id === id);
            return doctor
                ? `${doctor.firstName} ${doctor.lastName}`
                : 'Unknown Doctor';
        });
    };
    // Map directly over the 'doctors' array
    const doctorsForModal = doctors.map((doc: DoctorDto) => ({
        id: doc.id,
        name: `${doc.firstName} ${doc.lastName}`,
    }));

    // Helper to render the doctor list or status string
    const renderAllowedDoctors = () => {
        const doctorsValue = getDoctorNames(settings);

        if (Array.isArray(doctorsValue)) {
            return (
                <div className="flex flex-col">
                    {doctorsValue.map((name, index) => (
                        <span key={index}>{`Dr. ${name}`}</span>
                    ))}
                </div>
            );
        }
        return doctorsValue;
    };

    return (
        <div className="mt-3  pt-6">
            <div className="flex items-center justify-between gap-2 mb-6 border-b border-neutral-300 pb-2">
                <div className="flex items-center gap-3">
                    <Heading type="h5" fontWeight="font-semibold">
                        Client Booking Settings
                    </Heading>
                    {settings.isEnabled ? (
                        <Tag
                            label="Active"
                            variant="neutral"
                            isLight={true}
                            size="medium"
                            className="bg-transparent text-success-700 border border-success-700 !px-4 !py-1 cursor-default"
                        />
                    ) : (
                        <Tag
                            label="Inactive"
                            variant="neutral"
                            isLight={true}
                            size="medium"
                            className="bg-transparent text-neutral-600 border border-neutral-600  !px-4 !py-1 cursor-default"
                        />
                    )}
                </div>
                <Tooltip content="Edit Settings" position="bottom">
                    <div
                        onClick={handleEdit}
                        className="h-8 w-8 rounded-full bg-primary-50 hover:bg-primary-100 hover:text-primary-800 flex justify-center items-center cursor-pointer"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ')
                                handleEdit();
                        }}
                        aria-label="Edit client booking settings"
                    >
                        <IconEdit
                            className="text-neutral-900 transition-all"
                            size={16}
                        />
                    </div>
                </Tooltip>
            </div>
            <div className="space-y-4">
                {isLoading && (
                    <div className="text-center text-neutral-500 py-4">
                        Loading settings...
                    </div>
                )}
                {!isLoading && (
                    <div className="flex flex-row">
                        <div className="w-1/2 pr-2">
                            {settings.isEnabled && (
                                <>
                                    <DetailItem
                                        label="Allowed Doctors"
                                        value={renderAllowedDoctors()}
                                    />
                                </>
                            )}
                            {!settings.isEnabled && (
                                <Image
                                    src="/images/online-pawrtal.png"
                                    alt="Client booking is inactive - decorative image of a dog with a calendar"
                                    width={150}
                                    height={150}
                                    objectFit="contain"
                                />
                            )}
                        </div>
                        <div className="w-1/2 pl-2">
                            {settings.isEnabled && (
                                <>
                                    <DetailItem
                                        label="Booking Lead Time"
                                        value={
                                            settings.minBookingLeadTime
                                                ? formatTimeDuration(
                                                      settings.minBookingLeadTime
                                                  )
                                                : formatMinutesToReadable(
                                                      hoursToMinutes(
                                                          settings.minBookingLeadHours
                                                      )
                                                  ) || '-'
                                        }
                                    />
                                    <DetailItem
                                        label="Maximum Advance Booking"
                                        value={
                                            formatTimeDuration(
                                                settings.maxAdvanceBookingTime
                                            ) || '-'
                                        }
                                    />
                                    <DetailItem
                                        label="Cancellation/Modification Deadline"
                                        value={
                                            settings.modificationDeadlineTime
                                                ? formatTimeDuration(
                                                      settings.modificationDeadlineTime
                                                  )
                                                : formatMinutesToReadable(
                                                      hoursToMinutes(
                                                          settings.modificationDeadlineHours
                                                      )
                                                  ) || '-'
                                        }
                                    />
                                </>
                            )}
                        </div>
                    </div>
                )}
            </div>

            {isModalOpen && (
                <EditClientBookingSettingsModal
                    isOpen={isModalOpen}
                    onClose={handleModalClose}
                    initialData={settings}
                    doctors={doctorsForModal}
                    onSubmit={handleSaveChanges}
                    clinicId={clinicId}
                />
            )}
        </div>
    );
};

export default ClientBookingSettings;
