import { Button, Input, Text } from '@/app/atoms';
import { Modal } from '@/app/molecules';
import { ModalFooter } from '@/app/molecules/Modal';
import QuantityControl from '@/app/molecules/QuantityControl';
import Tabs from '@/app/molecules/Tabs';
import React, { useEffect, useState, useRef } from 'react';
import CurrencyInput from 'react-currency-input-field';

interface MedicationFormData {
    id?: string;
    name: string;
    isRestricted: 'YES' | 'NO';
    chargeablePrice: number;
    tax: number;
    currentStock: number;
    minimumQuantity: number;
    isAddedByUser: boolean;
}

interface AddEditMedicationProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: MedicationFormData) => void;
    isEdit: boolean;
    initialData?: MedicationFormData;
    existingMedications?: Array<{
        id: string;
        name: string;
        isRestricted: 'YES' | 'NO';
        chargeablePrice: number;
        tax: number;
        currentStock: number;
        minimumQuantity: number;
        isAddedByUser: boolean;
    }>;
    onExistingItemSelect: (medication: MedicationFormData) => void;
}

const AddEditMedication: React.FC<AddEditMedicationProps> = ({
    isOpen,
    onClose,
    onSubmit,
    isEdit,
    initialData,
    existingMedications = [],
    onExistingItemSelect,
}) => {
    const defaultFormData: MedicationFormData = {
        name: '',
        isRestricted: 'NO',
        chargeablePrice: 0,
        tax: 0,
        currentStock: 0,
        minimumQuantity: 0,
        isAddedByUser: false,
    };

    const [formData, setFormData] =
        useState<MedicationFormData>(defaultFormData);
    const [nameError, setNameError] = useState<string>('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [matchingItems, setMatchingItems] = useState<
        Array<(typeof existingMedications)[0]>
    >([]);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!isOpen) {
            setFormData(defaultFormData);
            setNameError('');
            setShowDropdown(false);
        } else if (initialData) {
            setFormData({
                ...initialData,
                isRestricted:
                    typeof initialData.isRestricted === 'boolean'
                        ? initialData.isRestricted
                            ? 'YES'
                            : 'NO'
                        : initialData.isRestricted,
            });
            setNameError('');
        }
    }, [isOpen, initialData]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node)
            ) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () =>
            document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSubmit = () => {
        if (!formData.name.trim()) {
            setNameError('Item name is required');
            return;
        }
        onSubmit(formData);
        setFormData(defaultFormData);
        setNameError('');
    };

    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        // If we're in edit mode and the name is changed, switch back to add mode
        // if (isEdit && value !== initialData?.name) {
        //     setFormData({
        //         name: value,
        //         isRestricted: 'NO',
        //         chargeablePrice: 0,
        //         tax: 0,
        //         currentStock: 0,
        //         minimumQuantity: 0,
        //         isAddedByUser: false,
        //     });
        //     onExistingItemSelect({
        //         name: value,
        //         isRestricted: 'NO',
        //         chargeablePrice: 0,
        //         tax: 0,
        //         currentStock: 0,
        //         minimumQuantity: 0,
        //         isAddedByUser: false,
        //     });
        // } else {
        setFormData((prev) => ({ ...prev, name: value }));
        // }

        setNameError('');

        if (value.trim()) {
            const matches = existingMedications.filter((item) =>
                item.name?.toLowerCase().includes(value.toLowerCase())
            );
            setMatchingItems(matches);
            setShowDropdown(matches.length > 0);
        } else {
            setShowDropdown(false);
            setMatchingItems([]);
        }
    };

    const handleItemSelect = (item: (typeof existingMedications)[0]) => {
        const selectedItem = {
            id: item.id,
            name: item.name,
            isRestricted:
                typeof item.isRestricted === 'boolean'
                    ? item.isRestricted
                        ? 'YES'
                        : 'NO'
                    : item.isRestricted,
            chargeablePrice: Number(item.chargeablePrice),
            tax: Number(item.tax),
            currentStock: Number(item.currentStock),
            minimumQuantity: Number(item.minimumQuantity),
            isAddedByUser: item.isAddedByUser,
        };
        setFormData(selectedItem);
        onExistingItemSelect(selectedItem);
        setShowDropdown(false);
    };

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            isHeaderBorder={true}
            modalTitle={`${isEdit ? 'Edit Medication' : 'Add Medication'}`}
        >
            <div className="flex flex-col gap-y-5">
                <div
                    className="flex flex-col gap-y-2 relative"
                    ref={dropdownRef}
                >
                    <Text variant="bodySmall">Item Name</Text>
                    <Input
                        id="name"
                        name="name"
                        variant="basicField"
                        placeholder="Enter item name"
                        required={true}
                        errorMessage={nameError}
                        value={formData.name}
                        onChange={handleNameChange}
                        autoComplete="off"
                    />
                    {showDropdown && matchingItems.length > 0 && (
                        <div className="absolute z-10 w-full bg-white mt-1 rounded-xl border border-neutral-50 shadow-lg max-h-48 overflow-y-auto top-[4.5rem]">
                            {matchingItems.map((item) => (
                                <div
                                    key={item.id}
                                    className="px-4 py-2 hover:bg-primary-50 cursor-pointer text-sm"
                                    onClick={() => handleItemSelect(item)}
                                >
                                    <div className="font-medium">
                                        {item.name}
                                    </div>
                                    <div className="text-xs text-neutral-500 mt-0.5">
                                        Price: ₹{item.chargeablePrice} | Tax:{' '}
                                        {item.tax}% | Stock: {item.currentStock}{' '}
                                        | Min Qty: {item.minimumQuantity} |
                                        Restricted:{' '}
                                        {typeof item.isRestricted === 'boolean'
                                            ? item.isRestricted
                                                ? 'Yes'
                                                : 'No'
                                            : item.isRestricted}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Restricted Substance</Text>
                    <Tabs
                        className="w-[8rem]"
                        defaultActiveTab={formData.isRestricted}
                        variant="secondary"
                        marginTabsTop="0"
                        onTabClick={(tabId) =>
                            setFormData((prev) => ({
                                ...prev,
                                isRestricted: tabId.id as 'YES' | 'NO',
                            }))
                        }
                        tabs={[
                            {
                                id: 'YES',
                                label: 'Yes',
                            },
                            {
                                id: 'NO',
                                label: 'No',
                            },
                        ]}
                    />
                </div>

                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Unit price (INR)</Text>
                    <CurrencyInput
                        prefix="₹"
                        className="outline-0 w-[150px] rounded-3xl text-sm border border-neutral-50 py-2.5 px-4"
                        id="chargeablePrice"
                        name="chargeablePrice"
                        placeholder="Price"
                        value={formData.chargeablePrice}
                        decimalsLimit={2}
                        onValueChange={(value) =>
                            setFormData((prev) => ({
                                ...prev,
                                chargeablePrice: Number(value || 0),
                            }))
                        }
                    />
                </div>

                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Tax (%)</Text>
                    <Input
                        className="!w-[100px]"
                        id="tax"
                        name="tax"
                        variant="basicField"
                        placeholder="Enter tax"
                        value={formData.tax.toString()}
                        errorMessage={''}
                        onChange={(e) =>
                            setFormData((prev) => ({
                                ...prev,
                                tax: Number(e.target.value),
                            }))
                        }
                    />
                </div>
                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Current Stock</Text>
                    <QuantityControl
                        quantity={formData.currentStock}
                        onQuantityChange={(qty) =>
                            setFormData((prev) => ({
                                ...prev,
                                currentStock: Number(qty),
                            }))
                        }
                    />
                </div>
                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Minimum Quantity</Text>
                    <QuantityControl
                        quantity={formData.minimumQuantity}
                        onQuantityChange={(qty) =>
                            setFormData((prev) => ({
                                ...prev,
                                minimumQuantity: Number(qty),
                            }))
                        }
                    />
                </div>
            </div>
            <ModalFooter isShowBorder={false} className="mt-8">
                <Button
                    size="small"
                    id="cancel"
                    variant="secondary"
                    onClick={onClose}
                    data-automation={`cancel`}
                    label="Cancel"
                />

                <Button
                    size="small"
                    id="add"
                    variant="primary"
                    type="submit"
                    onClick={handleSubmit}
                    data-automation={`add`}
                    label={`${isEdit ? 'Save' : 'Add'}`}
                />
            </ModalFooter>
        </Modal>
    );
};

export default AddEditMedication;
