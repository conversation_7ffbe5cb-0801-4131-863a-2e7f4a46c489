import { Button, Text } from '@/app/atoms';
import IconDelete from '@/app/atoms/customIcons/IconDelete';
import { Searchbar, Table } from '@/app/molecules';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { Edit2 } from 'iconsax-react';
import { CircleAlert, Plus, Trash2 } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import ConfirmModal from '../../../patient/ConfirmModal';
import AddEditMedication from './AddEditMedication';
import {
    useClinicMedications,
    useCreateClinicMedication,
    useUpdateClinicMedication,
    useDeleteClinicMedication,
} from '@/app/services/clinic.queries';
import { getAuth } from '@/app/services/identity.service';
import AdminEmptyState from '@/app/molecules/AdminEmptyState';

interface MedicationTabProps {
    totalPages: number;
    listLoadStatus: string;
}

interface MedicationData {
    id: string;
    name: string;
    isRestricted: 'YES' | 'NO';
    chargeablePrice: number;
    tax: number;
    currentStock: number;
    minimumQuantity: number;
    isAddedByUser: boolean;
}

interface MedicationFormInput {
    id?: string;
    name: string;
    isRestricted: 'YES' | 'NO';
    chargeablePrice: number;
    tax: number;
    currentStock: number;
    minimumQuantity: number;
    isAddedByUser: boolean;
}

interface MedicationPayload {
    clinicId: string;
    brandId: string;
    name: string;
    isRestricted: 'YES' | 'NO';
    chargeablePrice: number;
    tax: number;
    currentStock: number;
    minimumQuantity: number;
    isAddedByUser: boolean;
}

const MedicationTab = ({ totalPages, listLoadStatus }: MedicationTabProps) => {
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isModal, setIsModal] = useState(false);
    const [isEditModal, setIsEditModal] = useState(false);

    const [itemToDelete, setItemToDelete] = useState<{
        itemId: string;
    } | null>(null);

    const columnHelper = createColumnHelper<MedicationData>();
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    });

    const auth = getAuth();
    const clinicId = auth?.clinicId;

    const { data: medicationsData, isLoading: isLoadingMedications } =
        useClinicMedications(
            clinicId,
            pagination.pageIndex + 1,
            pagination.pageSize
        );

    const { mutate: createMedication } = useCreateClinicMedication();

    const handleAddMedication = (formData: MedicationFormInput) => {
        const payload: MedicationPayload = {
            clinicId: auth?.clinicId!,
            brandId: auth?.brandId!,
            name: formData.name,
            isRestricted: formData.isRestricted,
            chargeablePrice: formData.chargeablePrice,
            tax: formData.tax,
            currentStock: formData.currentStock,
            minimumQuantity: formData.minimumQuantity,
            isAddedByUser: formData.isAddedByUser,
        };

        createMedication(payload, {
            onSuccess: () => {
                setIsModal(false);
            },
            onError: (error) => {
                console.error('Error creating medication:', error);
            },
        });
    };

    const [editingMedication, setEditingMedication] =
        useState<MedicationFormInput | null>(null);

    const { mutate: updateMedication } = useUpdateClinicMedication();

    const handleEditMedication = (formData: MedicationFormInput) => {
        if (editingMedication?.id) {
            const payload = {
                id: editingMedication.id,
                data: {
                    name: formData.name,
                    isRestricted: formData.isRestricted,
                    chargeablePrice: formData.chargeablePrice,
                    tax: formData.tax,
                    currentStock: formData.currentStock,
                    minimumQuantity: formData.minimumQuantity,
                    isAddedByUser: formData.isAddedByUser,
                },
            };

            updateMedication(payload, {
                onSuccess: () => {
                    setIsModal(false);
                    setEditingMedication(null);
                },
                onError: (error) => {
                    console.error('Error updating medication:', error);
                },
            });
        }
    };

    const { mutate: deleteMedication } = useDeleteClinicMedication();

    const handleConfirmDelete = () => {
        if (itemToDelete) {
            deleteMedication(itemToDelete.itemId, {
                onSuccess: () => {
                    setIsDeleteModalOpen(false);
                    setItemToDelete(null);
                },
                onError: (error) => {
                    console.error('Error deleting medication:', error);
                },
            });
        }
    };

    const [searchTerm, setSearchTerm] = useState('');

    const filteredMedications = useMemo(() => {
        if (!medicationsData?.data) return [];

        const data = [...medicationsData.data].reverse();
        if (!searchTerm.trim()) return data;

        return data.filter((item) =>
            item.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [medicationsData?.data, searchTerm]);

    const onChangeMedication = (value: string) => {
        setSearchTerm(value);
    };

    const columns = [
        columnHelper.accessor('name', {
            id: 'medicationName',
            header: 'Medication name',
            size: 20,
            meta: {
                thClassName: '',
            },
            cell: (info) => <Text>{info.getValue()}</Text>,
        }),
        columnHelper.accessor('isRestricted', {
            id: 'restrictedSubstance',
            header: 'Restricted Substance',
            size: 20,
            meta: {
                thClassName: '',
            },
            cell: (info) => <Text>{info.getValue() ? 'Yes' : 'No'}</Text>,
        }),
        columnHelper.accessor('chargeablePrice', {
            id: 'unitPrice',
            header: 'Chargeable Price Per Unit',
            size: 20,
            meta: {
                thClassName: '',
            },
            cell: (info) => <Text>₹{info.getValue()}</Text>,
        }),
        columnHelper.accessor('tax', {
            id: 'tax',
            header: 'Tax %',
            size: 20,
            meta: {
                thClassName: '',
            },
            cell: (info) => <Text>{`${info.getValue()}%`}</Text>,
        }),
        columnHelper.accessor('currentStock', {
            id: 'currentStock',
            header: 'Current Stock',
            size: 20,
            meta: {
                thClassName: '',
            },
            cell: (info) => {
                const row = info.row.original;
                return (
                    <div className="flex items-center gap-x-2">
                        <Text>{info.getValue()}</Text>
                        {row.currentStock < row.minimumQuantity && (
                            <CircleAlert size={20} color="#E99400" />
                        )}
                    </div>
                );
            },
        }),
        columnHelper.accessor('minimumQuantity', {
            id: 'minimumQuantity',
            header: 'Minimum Quantity',
            size: 20,
            meta: {
                thClassName: '',
            },
            cell: (info) => <Text>{info.getValue()}</Text>,
        }),
        columnHelper.accessor((row) => ``, {
            id: 'moreAction',
            header: 'Action',
            size: 20,
            meta: {
                thClassName: 'w-[150px] text-center',
            },
            cell: (info) => (
                <div className="flex gap-x-3">
                    <Button
                        id={'edit'}
                        onlyIcon={true}
                        icon={<Edit2 size={16} />}
                        size="small"
                        variant="primary"
                        onClick={() => {
                            setEditingMedication({
                                id: info.row.original.id,
                                name: info.row.original.name,
                                isRestricted:
                                    typeof info.row.original.isRestricted ===
                                    'boolean'
                                        ? info.row.original.isRestricted
                                            ? 'YES'
                                            : 'NO'
                                        : info.row.original.isRestricted,
                                chargeablePrice:
                                    info.row.original.chargeablePrice,
                                tax: info.row.original.tax,
                                currentStock: info.row.original.currentStock,
                                minimumQuantity:
                                    info.row.original.minimumQuantity,
                                isAddedByUser: info.row.original.isAddedByUser,
                            });
                            setIsEditModal(true);
                            setIsModal(true);
                        }}
                    />
                    <Button
                        id={'delete'}
                        onlyIcon={true}
                        icon={<Trash2 size={16} color="#DC2020" />}
                        size="small"
                        variant="secondary"
                        className="!bg-error-50"
                        onClick={() => {
                            setItemToDelete({ itemId: info.row.original.id });
                            setIsDeleteModalOpen(true);
                        }}
                    />
                </div>
            ),
        }),
    ];

    return (
        <div className="">
            <div className="flex items-center justify-between py-6">
                <Searchbar
                    id="search-bar"
                    name="SearchBar"
                    placeholder="Search..."
                    variant="secondary"
                    onChange={onChangeMedication}
                />
                <Button
                    id={'add-medication'}
                    label="Add Medication"
                    icon={<Plus size={16} />}
                    iconPosition="left"
                    size="small"
                    onClick={() => {
                        setIsModal(true);
                        setIsEditModal(false);
                        setEditingMedication(null);
                    }}
                />
            </div>

            <div
                className={`h-[calc(100dvh-25rem)] ${
                    medicationsData?.data == undefined ||
                    medicationsData?.data?.length === 0
                        ? 'flex justify-center items-center overflow-auto'
                        : 'mt-6'
                }`}
            >
                {isLoadingMedications ? (
                    <div>Loading...</div>
                ) : medicationsData?.data?.length === 0 ? (
                    <AdminEmptyState
                        title="Nothing Added Yet"
                        image="/images/care-kennels.png"
                        variant="vertical"
                        className="mb-14"
                    />
                ) : (
                    <Table
                        tableData={filteredMedications}
                        columns={columns}
                        pagination={pagination}
                        setPagination={setPagination}
                        pageCount={totalPages}
                        listLoadStatus="success"
                        customTableWrapperClass=""
                    />
                )}
            </div>

            <AddEditMedication
                isEdit={isEditModal}
                isOpen={isModal}
                onClose={() => {
                    setIsModal(false);
                    setEditingMedication(null);
                    setIsEditModal(false);
                }}
                onSubmit={
                    isEditModal ? handleEditMedication : handleAddMedication
                }
                initialData={editingMedication || undefined}
                existingMedications={medicationsData?.data || []}
                onExistingItemSelect={(medication) => {
                    if (!medication.id) {
                        // If no ID, it's a new medication
                        setEditingMedication(null);
                        setIsEditModal(false);
                    } else {
                        setEditingMedication(medication);
                        setIsEditModal(true);
                    }
                }}
            />

            <ConfirmModal
                isOpen={isDeleteModalOpen}
                modalTitle="Delete item"
                modalDescription="Are you sure want to delete item?"
                alertType="delete"
                onClose={() => {
                    setIsDeleteModalOpen(false);
                }}
                primaryBtnProps={{
                    label: 'Delete',
                    onClick: handleConfirmDelete,
                    dataAutomation: 'confirm-delete',
                }}
                secondaryBtnProps={{
                    label: 'Cancel',
                    onClick: () => {
                        setIsDeleteModalOpen(false);
                    },
                    dataAutomation: 'cancel-delete',
                }}
                dataAutomation="confirm-modal"
                primaryBtnDisabled={false}
            />
        </div>
    );
};

export default MedicationTab;
