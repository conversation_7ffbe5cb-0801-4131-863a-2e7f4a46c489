import { Button, Input, Text } from '@/app/atoms';
import { Modal } from '@/app/molecules';
import { ModalFooter } from '@/app/molecules/Modal';
import React, { useEffect, useState, useRef } from 'react';
import CurrencyInput from 'react-currency-input-field';

interface ServiceFormData {
    id?: string;
    serviceName: string;
    chargeablePrice: number;
    tax: number;
}

interface AddEditServiceProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: ServiceFormData) => void;
    isEdit: boolean;
    initialData?: ServiceFormData;
    existingServices: Array<{
        id: string;
        serviceName: string;
        chargeablePrice: number;
        tax: number;
    }>;
    onExistingItemSelect: (service: ServiceFormData) => void;
}

const AddEditService: React.FC<AddEditServiceProps> = ({
    isOpen,
    onClose,
    onSubmit,
    isEdit,
    initialData,
    existingServices = [],
    onExistingItemSelect,
}) => {
    const defaultFormData: ServiceFormData = {
        serviceName: '',
        chargeablePrice: 0,
        tax: 0,
    };

    const [formData, setFormData] = useState<ServiceFormData>(defaultFormData);
    const [nameError, setNameError] = useState<string>('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [matchingServices, setMatchingServices] = useState<
        Array<(typeof existingServices)[0]>
    >([]);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!isOpen) {
            setFormData(defaultFormData);
            setNameError('');
            setShowDropdown(false);
        } else if (initialData) {
            setFormData(initialData);
            setNameError('');
        }
    }, [isOpen, initialData]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node)
            ) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () =>
            document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSubmit = () => {
        if (!formData.serviceName.trim()) {
            setNameError('Service name is required');
            return;
        }
        onSubmit(formData);
        setFormData(defaultFormData);
        setNameError('');
    };

    const handleServiceNameChange = (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = e.target.value;

        // If we're in edit mode and the name is changed, switch back to add mode
        // if (isEdit && value !== initialData?.serviceName) {
        //     setFormData({
        //         serviceName: value,
        //         chargeablePrice: 0,
        //         tax: 0,
        //     });
        //     onExistingItemSelect({
        //         serviceName: value,
        //         chargeablePrice: 0,
        //         tax: 0,
        //     });
        // } else {
        setFormData((prev) => ({ ...prev, serviceName: value }));
        // }

        setNameError('');

        if (value.trim()) {
            const matches = existingServices.filter((service) =>
                service.serviceName.toLowerCase().includes(value.toLowerCase())
            );
            setMatchingServices(matches);
            setShowDropdown(matches.length > 0);
        } else {
            setShowDropdown(false);
            setMatchingServices([]);
        }
    };

    const handleServiceSelect = (service: (typeof existingServices)[0]) => {
        const selectedService = {
            id: service.id,
            serviceName: service.serviceName,
            chargeablePrice: service.chargeablePrice,
            tax: service.tax,
        };
        onExistingItemSelect(selectedService);
        setShowDropdown(false);
    };

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            isHeaderBorder={true}
            modalTitle={`${isEdit ? 'Edit Service' : 'Add Service'}`}
        >
            <div className="flex flex-col gap-y-5">
                <div
                    className="flex flex-col gap-y-2 relative"
                    ref={dropdownRef}
                >
                    <Text variant="bodySmall">Item Name</Text>
                    <Input
                        id="serviceName"
                        name="serviceName"
                        variant="basicField"
                        placeholder="Enter service name"
                        value={formData.serviceName}
                        errorMessage={nameError}
                        onChange={handleServiceNameChange}
                        autoComplete="off"
                    />
                    {showDropdown && matchingServices.length > 0 && (
                        <div className="absolute z-10 w-full bg-white mt-1 rounded-xl border border-neutral-50 shadow-lg max-h-48 overflow-y-auto top-[4.5rem]">
                            {matchingServices.map((service) => (
                                <div
                                    key={service.id}
                                    className="px-4 py-2 hover:bg-primary-50 cursor-pointer text-sm"
                                    onClick={() => handleServiceSelect(service)}
                                >
                                    <div className="font-medium">
                                        {service.serviceName}
                                    </div>
                                    <div className="text-xs text-neutral-500 mt-0.5">
                                        Price: ₹{service.chargeablePrice} | Tax:{' '}
                                        {service.tax}%
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Unit price (INR)</Text>
                    <CurrencyInput
                        prefix="₹"
                        className="outline-0 w-[150px] rounded-3xl text-sm border border-neutral-50 py-2.5 px-4"
                        id="chargeablePrice"
                        name="chargeablePrice"
                        placeholder="Price"
                        value={formData.chargeablePrice}
                        decimalsLimit={2}
                        onValueChange={(value) =>
                            setFormData((prev) => ({
                                ...prev,
                                chargeablePrice: Number(value || 0),
                            }))
                        }
                    />
                </div>

                <div className="flex flex-col gap-y-2">
                    <Text variant="bodySmall">Tax (%)</Text>
                    <Input
                        className="!w-[100px]"
                        id="tax"
                        name="tax"
                        variant="basicField"
                        placeholder="Enter tax"
                        value={formData.tax.toString()}
                        errorMessage={''}
                        onChange={(e) =>
                            setFormData((prev) => ({
                                ...prev,
                                tax: Number(e.target.value),
                            }))
                        }
                    />
                </div>
            </div>
            <ModalFooter isShowBorder={false} className="mt-8">
                <Button
                    size="small"
                    id="cancel"
                    variant="secondary"
                    onClick={onClose}
                    data-automation={`cancel`}
                    label="Cancel"
                />

                <Button
                    size="small"
                    id="add"
                    variant="primary"
                    type="submit"
                    onClick={handleSubmit}
                    data-automation={`add`}
                    label={`${isEdit ? 'Save' : 'Add'}`}
                />
            </ModalFooter>
        </Modal>
    );
};

export default AddEditService;
