import { Button, Text } from '@/app/atoms';
import IconDelete from '@/app/atoms/customIcons/IconDelete';
import { Searchbar, Table } from '@/app/molecules';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { Edit2 } from 'iconsax-react';
import { Plus, Trash2 } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import ConfirmModal from '../../../patient/ConfirmModal';
import AddEditService from './AddEditService';
import {
    useClinicServices,
    useCreateClinicService,
    useUpdateClinicService,
    useDeleteClinicService,
} from '@/app/services/clinic.queries';
import { getAuth } from '@/app/services/identity.service';
import AdminEmptyState from '@/app/molecules/AdminEmptyState';

interface ServiceFormInput {
    id?: string;
    serviceName: string;
    chargeablePrice: number;
    tax: number;
}

interface ServicePayload {
    clinicId: string;
    brandId: string;
    serviceName: string;
    chargeablePrice: number;
    tax: number;
}

interface ServicesTabProps {
    totalPages: number;
    listLoadStatus: string;
}

interface ServiceData {
    id: string;
    serviceName: string;
    chargeablePrice: number;
    tax: number;
}

const ServicesTab = ({ totalPages, listLoadStatus }: ServicesTabProps) => {
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isModal, setIsModal] = useState(false);
    const [isEditModal, setIsEditModal] = useState(false);

    const [itemToDelete, setItemToDelete] = useState<{
        itemId: string;
    } | null>(null);

    const columnHelper = createColumnHelper();
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    });

    const auth = getAuth();
    const clinicId = auth?.clinicId;

    const { data: servicesData, isLoading: isLoadingServices } =
        useClinicServices(
            clinicId,
            pagination.pageIndex + 1,
            pagination.pageSize
        );

    const { mutate: createService } = useCreateClinicService();

    const handleAddService = (formData: ServiceFormInput) => {
        const payload: ServicePayload = {
            clinicId: auth?.clinicId!,
            brandId: auth?.brandId!,
            serviceName: formData.serviceName,
            chargeablePrice: formData.chargeablePrice,
            tax: formData.tax,
        };

        createService(payload, {
            onSuccess: () => {
                setIsModal(false);
            },
            onError: (error) => {
                console.error('Error creating service:', error);
            },
        });
    };
    const [editingService, setEditingService] =
        useState<ServiceFormInput | null>(null); // Add editing state
    const { mutate: updateService } = useUpdateClinicService();
    const handleEditService = (formData: ServiceFormInput) => {
        if (editingService?.id) {
            const payload = {
                id: editingService.id,
                data: {
                    serviceName: formData.serviceName,
                    chargeablePrice: formData.chargeablePrice,
                    tax: formData.tax,
                },
            };

            updateService(payload, {
                onSuccess: () => {
                    setIsModal(false);
                    setEditingService(null);
                },
                onError: (error) => {
                    console.error('Error updating service:', error);
                },
            });
        }
    };

    const { mutate: deleteService } = useDeleteClinicService(); // Initialize the mutation

    const handleConfirmDelete = () => {
        if (itemToDelete) {
            deleteService(itemToDelete.itemId, {
                onSuccess: () => {
                    setIsDeleteModalOpen(false);
                    setItemToDelete(null);
                },
                onError: (error) => {
                    console.error('Error deleting service:', error);
                },
            });
        }
    };

    const [searchTerm, setSearchTerm] = useState('');

    const filteredServices = useMemo(() => {
        if (!servicesData?.data) return [];

        const data = [...servicesData.data].reverse();
        if (!searchTerm.trim()) return data;

        return data.filter((item) =>
            item.serviceName.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [servicesData?.data, searchTerm]);

    // Add this before the return statement
    const onChangeService = (value: string) => {
        setSearchTerm(value);
    };

    const columns = [
        columnHelper.accessor('serviceName', {
            id: 'serviceName',
            header: 'Service Name',
            meta: {
                thClassName: '',
            },
            cell: (info) => (
                <Text>{(info.row.original as ServiceData).serviceName}</Text>
            ),
        }),
        columnHelper.accessor('chargeablePrice', {
            id: 'chargeablePrice',
            header: 'Chargeable Price Per Unit',
            meta: {
                thClassName: '',
            },
            cell: (info) => (
                <Text>
                    {(info.row.original as ServiceData).chargeablePrice}
                </Text>
            ),
        }),
        columnHelper.accessor('tax', {
            id: 'tax',
            header: 'Tax %',
            meta: {
                thClassName: '',
            },
            cell: (info) => (
                <Text>{`${(info.row.original as ServiceData).tax}%`}</Text>
            ),
        }),
        columnHelper.accessor((row) => ``, {
            id: 'moreAction',
            header: 'Action',
            meta: {
                thClassName: 'w-[150px] text-center',
            },
            cell: (info) => (
                <div className="flex gap-x-3">
                    <Button
                        id={'edit'}
                        onlyIcon={true}
                        icon={<Edit2 size={16} />}
                        size="small"
                        variant="primary"
                        onClick={() => {
                            setEditingService({
                                id: (info.row.original as ServiceData).id,
                                serviceName: (info.row.original as ServiceData)
                                    .serviceName,
                                chargeablePrice: (
                                    info.row.original as ServiceData
                                ).chargeablePrice,
                                tax: (info.row.original as ServiceData).tax,
                            });
                            setIsEditModal(true);
                            setIsModal(true);
                        }}
                    />
                    <Button
                        id={'delete'}
                        onlyIcon={true}
                        icon={<Trash2 size={16} color="#DC2020" />}
                        size="small"
                        variant="secondary"
                        className="!bg-error-50"
                        onClick={() => {
                            setItemToDelete({
                                itemId: (info.row.original as ServiceData).id,
                            });
                            setIsDeleteModalOpen(true);
                        }}
                    />
                </div>
            ),
        }),
    ] as const;
    // const tableData = [
    //     {
    //         userName: 'fesfse',
    //     },
    // ];

    // const handleConfirmDelete = () => {
    //     setIsDeleteModalOpen(false);
    //     setItemToDelete(null);
    // };

    return (
        <div className="">
            <div className="flex items-center justify-between py-6">
                <Searchbar
                    id="search-bar"
                    name="SearchBar"
                    placeholder="Search..."
                    variant="secondary"
                    onChange={onChangeService}
                />
                <Button
                    id={'add-service'}
                    label="Add Service"
                    icon={<Plus size={16} />}
                    iconPosition="left"
                    size="small"
                    onClick={() => {
                        setIsModal(true);
                        setIsEditModal(false);
                    }}
                />
            </div>

            <div
                className={`h-[calc(100dvh-25rem)] ${
                    servicesData?.data == undefined ||
                    servicesData?.data?.length === 0
                        ? 'flex justify-center items-center overflow-auto'
                        : 'mt-6'
                }`}
            >
                {isLoadingServices ? (
                    <div>Loading...</div>
                ) : servicesData?.data?.length === 0 ? (
                    <AdminEmptyState
                        title="Nothing Added Yet"
                        image="/images/care-kennels.png"
                        variant="vertical"
                        className="mb-14"
                    />
                ) : (
                    <Table
                        tableData={filteredServices}
                        columns={columns}
                        pagination={pagination}
                        setPagination={setPagination}
                        pageCount={totalPages}
                        listLoadStatus="success"
                    />
                )}
            </div>

            <AddEditService
                isEdit={isEditModal}
                isOpen={isModal}
                onClose={() => {
                    setIsModal(false);
                    setEditingService(null);
                    setIsEditModal(false);
                }}
                onSubmit={isEditModal ? handleEditService : handleAddService}
                initialData={editingService || undefined}
                existingServices={servicesData?.data || []}
                onExistingItemSelect={(service) => {
                    if (!service.id) {
                        // If no ID, it's a new service
                        setEditingService(null);
                        setIsEditModal(false);
                    } else {
                        setEditingService(service);
                        setIsEditModal(true);
                    }
                }}
            />

            <ConfirmModal
                isOpen={isDeleteModalOpen}
                modalTitle="Delete item"
                modalDescription="Are you sure want to delete item?"
                alertType="delete"
                onClose={() => {
                    setIsDeleteModalOpen(false);
                }}
                primaryBtnDisabled={false}
                primaryBtnProps={{
                    label: 'Delete',
                    onClick: handleConfirmDelete,
                    dataAutomation: 'confirm-delete',
                }}
                secondaryBtnProps={{
                    label: 'Cancel',
                    onClick: () => {
                        setIsDeleteModalOpen(false);
                    },
                    dataAutomation: 'cancel-delete',
                }}
                dataAutomation="confirm-modal"
            />
        </div>
    );
};

export default ServicesTab;
