import React, { useEffect } from 'react';
import RenderFields, { fieldsType } from '@/app/molecules/RenderFields';
import { useForm, SubmitHandler } from 'react-hook-form';
import { Button } from '@/app/atoms';
import { Modal } from '@/app/molecules';
import { Add } from 'iconsax-react';
import { UserFormDataT } from '@/app/types/user';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

interface RoleOption {
    id: string;
    name: string;
    description: string;
}

interface AddEditUserModalProps {
    openAddExistingUserModal: () => void;
    isOpen: boolean;
    onClose: () => void;
    roleOptions: RoleOption[];
    onSubmit: (data: UserFormDataT) => void;
    user?: {
        firstName: string;
        lastName: string;
        email: string;
        role: {
            id: string;
            name: string;
        };
    };
}

const schema = yup.object().shape({
    firstName: yup
        .string()
        .required('First name is required')
        .min(1)
        .max(50, 'First name must not exceed 50 characters').trim(),
    lastName: yup
        .string()
        .required('Last name is required')
        .min(1)
        .max(50, 'Last name must not exceed 50 characters').trim(),
    email: yup
        .string()
        .required('Email is required')
        .email('Invalid email format')
        .max(50, 'Email must not exceed 50 characters'),
    role: yup.object().nullable().shape({
        value: yup.string(),
        label: yup.string(),
    }),
});

const AddEditUserModal: React.FC<AddEditUserModalProps> = ({
    openAddExistingUserModal,
    isOpen,
    onClose,
    roleOptions,
    onSubmit,
    user,
}) => {
    const formatRole = (role: string) => {
        return role.charAt(0).toUpperCase() + role.slice(1).replace('_', ' ');
    };
    const {
        control,
        setValue,
        register,
        handleSubmit,
        watch,
        formState: { errors, isDirty, isValid },
        reset,
    } = useForm({
        defaultValues: {
            firstName: user?.firstName || '',
            lastName: user?.lastName || '',
            email: user?.email || '',
            role: user?.role
                ? { value: user.role.id, label: user.role.name }
                : null,
        },
        resolver: yupResolver(schema),
        mode: 'onChange',
    });

    useEffect(() => {
        if (user) {
            reset({
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role
                    ? {
                        value: user.role.id,
                        label: formatRole(user.role.name),
                    }
                    : null,
            });
        } else {
            reset({
                firstName: '',
                lastName: '',
                email: '',
                role: null,
            });
        }
    }, [user, reset]);

    const loadRoleOptions = async (search: string) => {
        const filteredOptions = roleOptions
            .filter((role) =>
                role.name.toLowerCase().includes(search.toLowerCase())
            )
            .map((role) => ({
                value: role.id,
                label:
                    role.name.charAt(0).toUpperCase() +
                    role.name.slice(1).replace('_', ' '),
            }));
        return {
            options: filteredOptions,
            hasMore: false,
        };
    };

    const fields: fieldsType[] = [
        {
            type: 'text-input',
            name: 'firstName',
            id: 'firstName',
            label: 'First Name',
            placeholder: 'Enter first name',
            required: true,
        },
        {
            type: 'text-input',
            name: 'lastName',
            id: 'lastName',
            label: 'Last Name',
            placeholder: 'Enter last name',
            required: true,
        },
        {
            type: 'async-select',
            name: 'role',
            id: 'role',
            label: 'Role',
            placeholder: 'Select a role',
            required: true,
            loadOptions: loadRoleOptions,
            value: watch('role'),
            onChange: (selected) => {
                setValue('role', selected, { shouldValidate: true });
            },
            disabled: watch('role')?.label === 'Admin',
        },
        {
            type: 'text-input',
            name: 'email',
            id: 'email',
            label: 'Email ID',
            placeholder: 'Enter email',
            required: true,
        },
    ];

    const onFormSubmit: SubmitHandler<any> = (data) => {
        const transformedData: UserFormDataT = {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.email,
            roleId: data.role.value,
        };
        onSubmit(transformedData);
    };

    return (
        <Modal
            dataAutomation="add-edit-user-modal"
            isOpen={isOpen}
            onClose={onClose}
            modalTitle={user ? 'Edit User' : 'Create New User'}
            modalWidth="max-w-[708px]"
            headerRightContent={
                !user && (
                    <Button
                        id="add-existing-user-button"
                        icon={<Add size={16} />}
                        onClick={openAddExistingUserModal}
                        size="small"
                        variant="link"
                        iconPosition="left"
                    >
                        Add Existing User
                    </Button>
                )
            }
        >
            <RenderFields
                fields={fields}
                control={control}
                register={register}
                errors={errors}
                setValue={setValue}
                watch={watch}
                grid="grid-cols-2"
                className="pb-4"
            />
            <div className="flex gap-3 justify-end ">
                <Button
                    id="cancel-button"
                    onClick={onClose}
                    size="small"
                    variant="secondary"
                >
                    Cancel
                </Button>
                <Button
                    id="submit-button"
                    onClick={handleSubmit(onFormSubmit)}
                    disabled={!isValid}
                    size="small"
                >
                    {user ? 'Update' : 'Add'}
                </Button>
            </div>
        </Modal>
    );
};

export default AddEditUserModal;
