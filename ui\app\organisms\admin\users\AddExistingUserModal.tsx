import React, { useState } from 'react';
import Dropdown from '@/app/molecules/Dropdown';
import Checkbox from '@/app/atoms/Checkbox';
import { Modal } from '@/app/molecules';
import { Add } from 'iconsax-react';
import { Button } from '@/app/atoms';
import { UserSearchResultT } from '@/app/types/user';

interface AddExistingUserModalProps {
    openCreateNewUserModal: () => void;
    isOpen?: boolean;
    onClose: () => void;
    users: UserSearchResultT[];
    onSubmit: (selectedUsers: UserSearchResultT[]) => void;
    setValue: any;
    handleSearch: (searchTerm: string) => Promise<{ users: UserSearchResultT[]; total: number }>;
    handleAddUserToClinic: (userId: string) => Promise<void>;
    isSearching: boolean;
    isAddingUser: boolean;
}

const AddExistingUserModal: React.FC<AddExistingUserModalProps> = ({
    openCreateNewUserModal,
    isOpen = false,
    onClose,
    users,
    onSubmit,
    setValue,
    handleSearch,
    handleAddUserToClinic,
    isSearching,
    isAddingUser,
}) => {
    const [selectedUsers, setSelectedUsers] = useState<UserSearchResultT[]>([]);
    const [searchedUsers, setSearchedUsers] = useState<UserSearchResultT[]>([]);
    const [inputValue, setInputValue] = useState('');

    const handleUserSelection = (menu: any) => {
        setSearchedUsers((prev) => [...prev, menu]);
        handleCheckboxToggle(menu)
        setInputValue('')
        // const selected = searchedUsers.find(user => user.id === menu.id);
        // if (selected) {
        //     setSelectedUsers((prevSelected) =>
        //         prevSelected.some((u) => u.id === selected.id)
        //             ? prevSelected.filter((u) => u.id !== selected.id)
        //             : [...prevSelected, selected]
        //     );
        // }
    };

    const handleAddUsers = async () => {
        for (const user of selectedUsers) {
            await handleAddUserToClinic(user.id);
        }
        onSubmit(selectedUsers);
        setSelectedUsers([]);
        setSearchedUsers([]);
        onClose()
    };

    const handleDropdownSearch = async (search: string) => {
        const response = await handleSearch(search);
        const filteredUser = response?.users.filter((user) =>
            !users.find(u => u.id === user.id)
        );
        // const users = response?.users;
        if (filteredUser?.length > 0) {
            // setSearchedUsers(users);
            return filteredUser.map(user => ({
                id: user.id,
                title: `${user.firstName} ${user.lastName}`,
                subList: [formatRole(user.role.name), user.email],
            }));
        }
    };

    const handleCheckboxToggle = (user: UserSearchResultT) => {
        setSelectedUsers((prevSelected) =>
            prevSelected.some((u) => u.id === user.id)
                ? prevSelected.filter((u) => u.id !== user.id)
                : [...prevSelected, user]
        );
    };

    const formatRole = (role: string) => {
        return role.charAt(0).toUpperCase() + role.slice(1).replace('_', ' ')
    }

    return (
        <Modal
            dataAutomation="add-existing-user-modal"
            isOpen={isOpen}
            onClose={onClose}
            modalTitle="Add Existing User"
            modalWidth="max-w-[528px]"
            headerRightContent={
                <Button
                    id="create-new-user-button"
                    icon={<Add size={16} />}
                    onClick={openCreateNewUserModal}
                    size="small"
                    variant="link"
                    iconPosition="left"
                >
                    Create New User
                </Button>
            }
        >
            <Dropdown
                id="user-dropdown"
                name="search"
                placeholder="Search and select users"
                className="w-full"
                onMenuClick={handleUserSelection}
                setValue={setValue}
                onSearch={handleDropdownSearch}
                dataNotFoundMsg="No users found"
                theme="default"
                inputClass='placeholder:text-lg placeholder:font-bold placeholder:text-primary-400 min-h-[40px]'
                isLoading={isSearching}
                setInputValue={setInputValue}
                inputValue={inputValue}
            />

            <div className="bg-primary-50 flex flex-col gap-4 mt-4">
                {searchedUsers.map((user) => (
                    <Checkbox
                        key={user.id}
                        wrapperClass="px-4 first:pt-4 last:pb-4"
                        labelPosition="left"
                        labelclass="flex gap-8 mr-8"
                        labelTextColor="text-neutral-900"
                        id={user.id}
                        checked={selectedUsers.some(u => u.id === user.id)}
                        onChange={() => handleCheckboxToggle(user)}
                        size="small"
                        label={
                            <>
                                <span className="w-[80px]">{user.title} {user.lastName}</span>
                                <span className="w-[50px]">{formatRole(user.subList[0])}</span>
                                <span className="w-[160px]">{user.subList[1]}</span>
                            </>
                        }
                    />
                ))}
            </div>

            <div className="flex gap-3 justify-end border-t border-primary-50 mt-4 pt-3">
                <Button id="cancel-button" onClick={onClose} size="small" variant="secondary">
                    Cancel
                </Button>
                <Button
                    id="submit-button"
                    onClick={handleAddUsers}
                    disabled={selectedUsers.length === 0 || isAddingUser}
                    size="small"
                >
                    {isAddingUser ? 'Adding...' : 'Add'}
                </Button>
            </div>
        </Modal>
    );
};

export default AddExistingUserModal;