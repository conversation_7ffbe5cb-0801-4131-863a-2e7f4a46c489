import React, {
    <PERSON><PERSON><PERSON>,
    Mouse<PERSON>ventHand<PERSON>,
    useEffect,
    useState,
} from 'react';
import { Modal } from '@/app/molecules';
import Button from '@/app/atoms/Button';
import PatientDropdown, {
    ResponseStatus,
} from '@/app/molecules/PatientDropdown';
import { PatientT } from '@/app/types/patient';
import RenderBasicFields, {
    basicFieldsType,
} from '@/app/molecules/RenderBasicFields';
import { OptionType } from '@/app/molecules/AsyncReactSelectPaginate';
import PatientMiniCard from '@/app/molecules/PatientMiniCard';
import { AppointmentStaffFormValues } from '@/app/types/appointment';
import DropdownMenu, { MenuListType } from '@/app/molecules/DropdownMenu';
import IconDots from '@/app/atoms/customIcons/IconDots.svg';
import IconNote from '@/app/atoms/customIcons/IconNote.svg';
import IconQuestion from '@/app/atoms/customIcons/IconQuestion.svg';
import { <PERSON><PERSON> } from '@/app/atoms';
import { PortalModalFooter } from '@/app/molecules/Modal';
import IconSquareTick from '@/app/atoms/customIcons/IconSquareTick.svg';
import { Add, AddCircle } from 'iconsax-react';
import IconCheckIn from '@/app/atoms/customIcons/IconCheckIn.svg';
import IconTreatment from '@/app/atoms/customIcons/IconTreatment.svg';
import IconRestartCircle from '@/app/atoms/customIcons/IconRestartCircle.svg';
import IconCart from '@/app/atoms/customIcons/IconCart.svg';
import IconSquareTime from '@/app/atoms/customIcons/IconSquareTime.svg';
import moment from 'moment';
import { getReasonOptions as getReasonOptionsUtil } from '@/app/utils/patient-details-utils/create-appointment-utils';

export type AppointmentStaffStepT =
    | 'checkIn'
    | 'beginTreatment'
    | 'continueTreatment'
    | 'complete';

export interface CreateAppointmentStaffProps {
    isOpen: boolean;
    onClose: () => void;
    modalTitle?: string;
    control: any;
    errors: any;
    register: any;
    setValue: Function;
    watch: (name: string) => any;
    handleCancel: () => void;
    handleCreateAppointment: (data: AppointmentStaffFormValues) => void;
    handleSubmit: Function;
    patientProps: {
        patientList: PatientT[];
        onMenuClick: (item: PatientT) => void;
        listStatus: ResponseStatus;
        showPatientDropdown: boolean;
        setShowPatientDropdown: React.Dispatch<React.SetStateAction<boolean>>;
        selectedPatient?: null | PatientT;
    };
    doctorProps: {
        showDoctorDropdown: boolean;
        setShowDoctorDropdown: React.Dispatch<React.SetStateAction<boolean>>;
        onDoctorMenuClick: () => void;
    };
    providerOptions: Function;
    isEditable: boolean;
    onMoreActionClick?: (item: MenuListType) => void;
    onNote?: MouseEventHandler<HTMLButtonElement>;
    onQuestion?: MouseEventHandler<HTMLButtonElement>;
    step: AppointmentStaffStepT;
    setStep?: React.Dispatch<React.SetStateAction<AppointmentStaffStepT>>;
    onStepHandler?: Function;
    doctorOptions: Function;
    reasonOptions: Function;
    triageOptions: Function;
    assignRoomOptions: Function;
    appointmentOptions: Function;
    getPatientOptions: Function;
    onProviderDelete: Function;
    handleAddProvider: Function;
    getValues: Function;
    patientData: { id: string };
    editMode: boolean;
    handleUpdateAppointment: Function;
    handleSearchAdd?: () => void;
    valuesChanged: boolean;
    scheduleStepHandler: Function;
    handlePatientSearch: (e: ChangeEvent<HTMLInputElement> | any) => void;
    patientsData?: any;
    workinghours?: any;
}

type appStaffStepDataT = {
    [key in AppointmentStaffStepT]: {
        buttonText: string;
        icon?: string;
        status:
            | 'scheduled'
            | 'checkingIn'
            | 'receivingCare'
            | 'checkingOut'
            | 'complete';
    };
};

export const AppStaffStepData: appStaffStepDataT = {
    checkIn: {
        buttonText: 'Check-in patient',
        status: 'scheduled',
    },
    beginTreatment: {
        buttonText: 'Begin treatment',
        status: 'Checkedin',
    },
    continueTreatment: {
        buttonText: 'Ready to check-out',
        status: 'Receiving Care',
    },
    readyToCheckout: {
        buttonText: 'Checkout',
        status: 'ready to checkout',
    },
    checkout: {
        buttonText: 'Check-out',
        status: 'checkout',
    },
    complete: {
        buttonText: 'Complete',
        status: 'complete',
    },
};

export const getStatusIcon = (status: string) => {
    switch (status) {
        case 'scheduled':
            return <IconCheckIn size={16} />;
        case 'Checkedin':
            return <IconTreatment size={16} />;
        case 'Receiving Care':
            return <IconCart size={16} />;
        case 'Ready to check-out':
            return <IconCart size={16} />;
        case 'checkout':
            return <IconSquareTick size={16} />;
        default:
            return <AddCircle size={16} />;
    }
};
const CreateAppointmentStaff: React.FC<CreateAppointmentStaffProps> = ({
    isOpen,
    onClose,
    modalTitle = 'Create appointment',
    control,
    errors,
    setValue,
    watch,
    handleCancel,
    handleSubmit,
    register,
    patientProps,
    doctorProps,
    providerOptions,
    isEditable,
    onMoreActionClick,
    onNote,
    onQuestion,
    step,
    setStep,
    onStepHandler,
    handleCreateAppointment,
    doctorOptions,
    reasonOptions,
    triageOptions,
    assignRoomOptions,
    appointmentOptions,
    patientData,
    editMode,
    handleUpdateAppointment,
    valuesChanged,
    handleSearchAdd,
    scheduleStepHandler,
    getValues,
    handlePatientSearch,
    getPatientOptions,
    patientsData = [],
    workinghours,
}) => {
    const closeModal = () => {
        onClose();
    };
    useEffect(() => {
        if (getValues('weight'))
            setValue('weight', getValues('weight')?.replace(/[a-zA-Z]/, ''), {
                shouldValidate: true,
            });
    }, [watch('weight')]);

    // Add state near other state declarations
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Add debounced handler before return statement
    const debouncedHandleSubmit = async (data: any) => {
        if (isSubmitting) return;

        setIsSubmitting(true);
        try {
            await handleCreateAppointment(data);
        } finally {
            // Reset after 1 second to prevent rapid re-clicks
            setTimeout(() => {
                setIsSubmitting(false);
            }, 1000);
        }
    };
    const fields: basicFieldsType[] = [
        {
            id: 'date',
            type: 'date',
            label: 'Date',
            name: 'date',
            placeholder: 'Add date',
            isEditable: true,
            required: true,
            displayOnly: true,
            onDateChange: (change) => {
                setValue('date', moment(change).format('DD-MMM-YYYY'), {
                    shouldValidate: true,
                });
            },
            value: patientData.id ? watch('date') : '',
            minDate: new Date(),
        },
        {
            id: 'time',
            type: 'time-range-picker',
            label: 'Time',
            name: 'time',
            placeholder: 'Add time',
            required: true,
            workinghours: workinghours,
            selectedDate: new Date(watch('date')),
            value: patientData?.id
                ? `${watch('startTime')} - ${watch('endTime')}`
                : '',
        },
        {
            id: 'status',
            type: 'status',
            label: 'Status',
            name: 'status',
            placeholder: 'Add status',
            isEditable: false,
        },
        {
            id: 'custom-dropdown',
            type: 'custom-dropdown',
            label: 'Doctor',
            name: 'doctors',
            placeholder: 'Add doctor',
            isEditable: false,
            loadOptions: doctorOptions,
            required: true,
            value: watch('doctors'),
        },
        {
            id: 'type',
            type: 'async-select',
            label: 'Type',
            name: 'type',
            placeholder: 'Add type',
            required: true,
            loadOptions: appointmentOptions,
            value: watch('type'),
        },
        {
            id: 'reason',
            type: 'async-select',
            label: 'Add reason',
            name: 'reason',
            placeholder: 'Type to search or add custom',
            loadOptions: getReasonOptionsUtil,
            value: watch('reason'),
            onChange: (selectedOption: any) => {
                setValue('reason', selectedOption);
            },
        },
        {
            type: 'async-select',
            loadOptions: providerOptions,
            name: 'provider',
            id: 'provider',
            label: 'Provider',
            placeholder: 'Add provider ',
            disabled: false,
            isMulti: true,
            value: watch('provider'),
        },
        {
            id: 'room',
            type: 'async-select',
            label: 'Room',
            name: 'room',
            placeholder: 'Add room',
            loadOptions: assignRoomOptions,
            disabled: false,
            value: watch('room'),
        },
        {
            id: 'triage',
            type: 'async-select',
            label: 'Triage',
            name: 'triage',
            placeholder: 'Add triage',
            loadOptions: triageOptions,
            value: watch('triage'),
        },
        {
            id: 'weight',
            type: 'text-input',
            label: 'Weight (kgs)',
            name: 'weight',
            // inputType: 'number',
            placeholder: 'Add weight',
            onChange: (e) => {
                console.log(e);
            },
        },
        // {
        //     id: 'notes',
        //     type: 'text-input',
        //     label: 'Notes:',
        //     name: 'notes',
        //     placeholder: 'Add notes',
        // },
    ];

    const removeQuotes = (str: string) => {
        if (str.startsWith("'") && str.endsWith("'")) {
            return str.slice(1, -1);
        }
        return str;
    };

    const headerRightContent = (
        <div className="flex gap-2.5 items-center">
            {/* <Button
                onClick={onQuestion}
                id="action-question-icon"
                onlyIcon
                type="button"
                variant="secondary"
                size="extraSmall"
                className="relative"
            >
                <div className="w-2 h-2 bg-success-100 rounded-full top-[5px] left-[5px] absolute animate-ping"></div>
                <div className="w-2 h-2 bg-success-100 rounded-full top-[5px] left-[5px] absolute"></div>
                <IconQuestion size={16} />
            </Button> */}

            {/* <Button
                onClick={onNote}
                id="action-note-icon"
                onlyIcon
                type="button"
                variant="secondary"
                size="extraSmall"
            >
                <IconNote size={16} />
            </Button> */}

            <DropdownMenu
                minWidth="min-w-[143px]"
                menuDirection="rightAuto"
                onMenuClick={(action) => {
                    onMoreActionClick(action);
                }}
                menuList={
                    [
                        // { id: 'cancel-appointment', label: 'Cancel appointment' },
                    ]
                }
            >
                <Button
                    id="action-create-appointment-staff"
                    onlyIcon
                    type="button"
                    variant="borderless"
                    size="extraSmall"
                >
                    <IconDots
                        className="text-neutral-900 hover:text-secondary-900"
                        size={24}
                    />
                </Button>
            </DropdownMenu>
        </div>
    );

    const modalFooter = (
        <>
            {/* Edit Mode */}
            {!editMode && (
                <>
                    <Button
                        id="create-appointment-cancel"
                        onClick={closeModal}
                        size="small"
                        variant="secondary"
                    >
                        Cancel
                    </Button>

                    <Button
                        id="create-appointment-confirm"
                        onClick={handleSubmit((data: any) =>
                            debouncedHandleSubmit(data)
                        )}
                        size="small"
                        icon={<Add size={16} />}
                        iconPosition="left"
                        variant="primary"
                        disabled={
                            !Boolean(
                                watch('patientSearch') &&
                                    watch('date') &&
                                    watch('startTime') &&
                                    watch('endTime') &&
                                    watch('doctors')
                            )
                        }
                    >
                        Create appointment
                    </Button>
                </>
            )}

            {/* View Mode */}
            {editMode && (
                <div className="flex flex-col gap-2 w-full">
                    <div className="flex w-full justify-end h-fit ml-auto gap-x-3">
                        {step === 'beginTreatment' && (
                            <Button
                                id="create-appointment-cancel"
                                onClick={() => {
                                    handleSubmit((data: any) =>
                                        handleUpdateAppointment(
                                            data,
                                            'markAsSchedule'
                                        )
                                    )();
                                    scheduleStepHandler();
                                }}
                                size="small"
                                variant="borderless"
                                className=""
                                icon={<IconSquareTime size={16} />}
                                iconPosition="left"
                            >
                                Mark as schedule
                            </Button>
                        )}

                        <Button
                            id="create-appointment-confirm"
                            onClick={() => {
                                handleSubmit((data: any) =>
                                    handleUpdateAppointment(
                                        data,
                                        'updateLabel',
                                        true
                                    )
                                )();
                                // onStepHandler();
                            }}
                            iconPosition="left"
                            icon={getStatusIcon(AppStaffStepData[step]?.status)}
                            size="small"
                            variant="primary"
                        >
                            {AppStaffStepData[step]?.buttonText}
                        </Button>
                    </div>
                </div>
            )}
        </>
    );

    return (
        <Modal
            isOpen={isOpen}
            onClose={closeModal}
            modalTitle={modalTitle}
            dataAutomation={'create-appointment'}
            // isHideCloseBtn={true}
            // headerRightContent={editMode && headerRightContent}
        >
            <div
                className={`pr-2 relative ${editMode && valuesChanged ? '' : ''} max-h-[calc(100vh-300px)] overflow-y-auto `}
            >
                {patientData.id || patientProps.selectedPatient ? (
                    <PatientMiniCard
                        priorityStatus={watch('triage')?.value}
                        patientInfo={
                            patientData.id
                                ? patientData
                                : patientProps?.selectedPatient
                        }
                        patientData={patientData}
                        profileImage={
                            patientProps.selectedPatient
                                ? patientProps.selectedPatient.profileImage
                                : patientData.profileImage
                        }
                    />
                ) : (
                    <PatientDropdown
                        className="mb-2"
                        id="patient-search"
                        name="patientSearch"
                        placeholder="Search patient..."
                        register={register}
                        setValue={setValue}
                        onAddClick={handleSearchAdd}
                        handlePatientSearch={handlePatientSearch}
                        // patientList={patientsData}
                        {...patientProps}
                    />
                )}

                <RenderBasicFields
                    className="my-2"
                    control={control}
                    errors={errors}
                    fields={fields}
                    setValue={setValue}
                    watch={watch}
                    register={register}
                    isEditable={isEditable}
                    getValues={getValues}
                    doctorProps={doctorProps}
                />

                {editMode && valuesChanged && (
                    <Alert
                        isHideIcon
                        className="w-full justify-between sticky bottom-2 left-0"
                        isLight
                        label="You have unsaved changes"
                        size="small"
                        variant="warning"
                        rightSideContent={
                            <button
                                className="text-neutral-900 font-semibold text-sm hover:bg-primary-100/40 py-1"
                                onClick={handleSubmit((data: any) =>
                                    handleUpdateAppointment(data, 'buttonCall')
                                )}
                            >
                                Update
                            </button>
                        }
                    />
                )}

                <PortalModalFooter target="#portal-drawer-footer ">
                    {modalFooter}
                </PortalModalFooter>
            </div>
        </Modal>
    );
};

export default CreateAppointmentStaff;
