import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useGetCalendarWorkingHours } from '@/app/services/user.queries';
import moment from 'moment';
import { useDate } from '@/context/date-provider';
import { Text } from '@/app/atoms';
import { Check, Phone } from 'lucide-react';
import IconAngleLeft from '@/app/atoms/customIcons/IconAngleLeft.svg';
import IconAngleRight from '@/app/atoms/customIcons/IconAngleRight';
import { Tooltip } from '@/app/molecules';
import { dayNumbers } from '@/app/utils/constant';
import { getAuth } from '@/app/services/identity.service';

// DEBUG: remove after verification
// eslint-disable-next-line no-console
console.debug('[Day.tsx] Component loaded');

import { DoctorWorkingHoursDto, TimeSlot } from '@/app/types/calendar';

const SLOT_HEIGHT_IN_PIXELS = 48;
const SLOT_HEIGHT_IN_MINUTES = 30;
const PIXEL_PER_MINUTE = SLOT_HEIGHT_IN_PIXELS / SLOT_HEIGHT_IN_MINUTES;

const Day = (props: {
    handleCalendarAppointment: any;
    fetchNextPage: () => void;
    hasNextPage: boolean;
}) => {
    const { handleCalendarAppointment, fetchNextPage, hasNextPage } = props;
    const { selectedDate, events, selectedUser, allDoctors, clinicDetails } =
        useDate();

    const CLINIC_ID = getAuth()?.clinicId;

    // Fetch calendar working hours data
    const { data: calendarWorkingHours, isLoading: isLoadingWorkingHours } =
        useGetCalendarWorkingHours(
            selectedDate.format('YYYY-MM-DD'),
            CLINIC_ID || ''
        );

    // Create a map of doctor IDs to their available slots for quick lookup
    const doctorAvailableSlotsMap = useMemo(() => {
        const map = new Map<string, TimeSlot[]>();
        if (calendarWorkingHours?.data?.doctors) {
            calendarWorkingHours.data.doctors.forEach(
                (doctor: DoctorWorkingHoursDto) => {
                    map.set(doctor.doctorId, doctor.availableSlots);
                }
            );
        }
        return map;
    }, [calendarWorkingHours]);

    const getEventColor = useCallback(
        (type: any, isGoogleEvent: boolean = false) => {
            switch (type) {
                case 'Vaccination':
                    return { background: '#EAF3F7', border: '#96C0D8' };
                case 'Surgery':
                    return { background: '#F5F2FC', border: '#B9B1ED' };
                case 'Walk In':
                    return { background: '#E8F5ED', border: '#8DCCA3' };
                case 'Consultation':
                    return { background: '#FFEDD2', border: '#FFBF40' };
                case 'Diagnostic':
                    return { background: '#FFEAF1', border: '#FF95BB' };
                case 'Saline':
                    return { background: '#FDFFCB', border: '#D8DC6A' };
                case 'Google Event':
                    return { background: '#F3F4F6', border: '#9CA3AF' };
                default:
                    return { background: '#FFEDD2', border: '#FFBF40' };
            }
        },
        []
    );

    const [isFetching, setIsFetching] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            if (!isFetching && hasNextPage) {
                setIsFetching(true);
                await fetchNextPage();
                setIsFetching(false);
            }
        };

        fetchData();
    }, [hasNextPage, events.length]);

    const startOfDay = selectedDate.clone().startOf('day').hour(8);

    const currentTime = moment();
    const formattedDate = currentTime.format('MMMM DD, YYYY');

    const numberOfSlots = (21 - 8) * 2;

    const halfHourSlots = [...Array(numberOfSlots)].map((_, i) =>
        startOfDay.clone().add(i * 30, 'minutes')
    );

    const usersToDisplay = useMemo(() => {
        return selectedUser?.length > 0
            ? selectedUser
            : [{ value: '', label: false }];
    }, [selectedUser]);

    const handleEventClick = (
        e: React.MouseEvent<HTMLDivElement, MouseEvent>,
        hour: moment.Moment,
        user: any
    ) => {
        const clickPositionY = e.nativeEvent.offsetY;
        const minutesInSlot =
            (clickPositionY / SLOT_HEIGHT_IN_PIXELS) * SLOT_HEIGHT_IN_MINUTES;
        const clickedTime = hour.clone().add(minutesInSlot, 'minutes');
        handleCalendarAppointment({
            date: selectedDate,
            time: clickedTime,
            user,
        });
    };

    const sortedEvents = useMemo(() => {
        return events.sort(
            (
                a: { scheduleData: { startTime: moment.MomentInput } },
                b: { scheduleData: { startTime: moment.MomentInput } }
            ) => {
                const aStart = moment(a.scheduleData.startTime, 'HH:mm A');
                const bStart = moment(b.scheduleData.startTime, 'HH:mm A');
                return aStart.diff(bStart);
            }
        );
    }, [events]);

    // Debug: log whenever events change
    useEffect(() => {
        // eslint-disable-next-line no-console
        console.debug('[Day.tsx] Received events', {
            total: events.length,
            google: events.filter((e: any) => e.googleEventId).length,
            sample: events.slice(0, 3),
        });
    }, [events]);

    const getOverlappingEvents = useCallback(
        (
            event: {
                scheduleData: {
                    startTime: moment.MomentInput;
                    endTime: moment.MomentInput;
                };
                treatmentData?: {
                    doctors: any[];
                };
                googleEventId?: string;
            },
            sortedEvents: any[],
            currentDoctor: string
        ) => {
            const eventStart = moment(event.scheduleData.startTime, 'HH:mm A');
            const eventEnd = moment(event.scheduleData.endTime, 'HH:mm A');
            const isGoogleEvent = Boolean(event.googleEventId);

            // Only consider events for the current doctor
            return sortedEvents.filter(
                (e: {
                    scheduleData: {
                        startTime: moment.MomentInput;
                        endTime: moment.MomentInput;
                    };
                    treatmentData?: {
                        doctors: any[];
                    };
                    googleEventId?: string;
                }) => {
                    // For Google events, they should take full width and not overlap with regular appointments
                    // Check if the event belongs to the current doctor
                    const eventDoctors = e.treatmentData?.doctors || [];
                    if (!eventDoctors.includes(currentDoctor)) {
                        return false;
                    }

                    const eStart = moment(e.scheduleData.startTime, 'HH:mm A');
                    const eEnd = moment(e.scheduleData.endTime, 'HH:mm A');
                    return eStart < eventEnd && eEnd > eventStart;
                }
            );
        },
        []
    );

    const getEventWidthAndPosition = useCallback(
        (
            event: {
                id: any;
                scheduleData: {
                    startTime: moment.MomentInput;
                    endTime: moment.MomentInput;
                };
                treatmentData?: {
                    doctors: any[];
                };
                googleEventId?: string;
            },
            sortedEvents: any,
            currentDoctor: string
        ) => {
            const overlappingEvents = getOverlappingEvents(
                event,
                sortedEvents,
                currentDoctor
            );
            const totalOverlaps = overlappingEvents.length;
            const eventIndex = overlappingEvents.findIndex(
                (e: { id: any }) => e.id === event.id
            );

            // If there are no overlapping events for this doctor, use full width
            if (totalOverlaps === 1) {
                return { eventWidth: 97, eventLeftPosition: 1 };
            }

            const eventWidth = 97 / totalOverlaps;
            let eventLeftPosition = 1;
            for (let i = 0; i < eventIndex; i++) {
                eventLeftPosition += 97 / totalOverlaps;
            }

            return { eventWidth, eventLeftPosition };
        },
        [getOverlappingEvents]
    );

    const getSlotColor = (hour: moment.Moment, user: { value: any }) => {
        // If no user selected or no working hours data yet, return empty
        if (!user.value || isLoadingWorkingHours) {
            return '';
        }

        const actualHour = hour.clone();
        const hourTime = actualHour.format('HH:mm');

        // Get available slots for this doctor from our new endpoint
        const availableSlots = doctorAvailableSlotsMap.get(user.value) || [];

        // If there are no available slots, the doctor is not working this day
        if (availableSlots.length === 0) {
            return 'bg-neutral-10';
        }

        // Check if the current hour is within any of the available slots
        const isAvailable = availableSlots.some((slot) => {
            const slotStart = moment(slot.startTime, 'HH:mm');
            const slotEnd = moment(slot.endTime, 'HH:mm');
            const currentTime = moment(hourTime, 'HH:mm');

            return (
                currentTime.isSameOrAfter(slotStart) &&
                currentTime.isBefore(slotEnd)
            );
        });

        return isAvailable ? '' : 'bg-neutral-10';
    };

    const renderEvent = useCallback(
        (
            event: {
                scheduleData: {
                    startTime: moment.MomentInput;
                    endTime: moment.MomentInput;
                    name: any;
                    ownerNumber: any;
                    owner: any;
                    status: string;
                };
                id: any;
                handleMenu: (arg0: { id: string }, arg1: any) => void;
                treatmentData?: {
                    type: any;
                    reason: any;
                    doctors: any[];
                };
                googleEventId?: string; // Add Google event identifier
            },
            currentDoctor: string
        ) => {
            const { eventWidth, eventLeftPosition } = getEventWidthAndPosition(
                event,
                sortedEvents,
                currentDoctor
            );

            const eventStart = moment(event.scheduleData.startTime, 'HH:mm A');
            const eventEnd = moment(event.scheduleData.endTime, 'HH:mm A');
            const eventHeight =
                eventEnd.diff(eventStart, 'minutes') * PIXEL_PER_MINUTE;
            const eventTop =
                eventStart.diff(startOfDay, 'minutes') * PIXEL_PER_MINUTE;

            const isShortEvent = eventEnd.diff(eventStart, 'minutes') < 30;
            const isCompleted = event.scheduleData.status === 'Completed';
            const isGoogleEvent =
                typeof event.id === 'string' && event.id.startsWith('google_');

            return (
                <div
                    style={{
                        top: `${eventTop}px`,
                        height: `${eventHeight}px`,
                        width: `${eventWidth}%`,
                        left: `${eventLeftPosition}%`,
                    }}
                    className="absolute p-1 pr-0"
                >
                    <div
                        key={event.id}
                        onClick={() => {
                            // Don't allow editing Google events
                            if (!isGoogleEvent) {
                                event.handleMenu({ id: 'edit' }, event.id);
                            }
                        }}
                        className={`relative h-full border-l-4 pl-2 pr-1 rounded-lg text-neutral-700 flex flex-col pt-1 ${
                            isGoogleEvent ? 'cursor-default' : 'cursor-pointer'
                        }`}
                        style={{
                            backgroundColor: getEventColor(
                                event.treatmentData?.type || 'Google Event',
                                isGoogleEvent
                            ).background,
                            borderColor: getEventColor(
                                event.treatmentData?.type || 'Google Event',
                                isGoogleEvent
                            ).border,
                            opacity: isCompleted
                                ? 0.8
                                : isGoogleEvent
                                  ? 0.7
                                  : 1,
                            filter: isCompleted ? 'saturate(0.2)' : 'none',
                        }}
                    >
                        <div className="flex items-center gap-x-1">
                            <Text
                                variant="bodySmall"
                                fontWeight="font-semibold"
                                className={`truncate ${isGoogleEvent ? 'text-gray-600' : 'text-primary-900'}`}
                            >
                                {event.scheduleData.name}
                            </Text>
                        </div>
                        {!isShortEvent && (
                            <Text
                                variant="caption"
                                className="text-primary-700"
                            >
                                {moment(
                                    event.scheduleData.startTime,
                                    'HH:mm A'
                                ).format('HH:mm')}{' '}
                                -
                                {moment(
                                    event.scheduleData.endTime,
                                    'HH:mm A'
                                ).format('HH:mm')}
                            </Text>
                        )}
                        <div className="absolute right-1 bottom-1 flex items-center gap-x-1">
                            {event.scheduleData.ownerNumber && (
                                <Tooltip
                                    content={
                                        <>
                                            <Text variant="bodySmall">
                                                {event.scheduleData?.owner}
                                            </Text>
                                            <Text
                                                variant="bodySmall"
                                                fontWeight="font-semibold"
                                            >
                                                {' '}
                                                {
                                                    event.scheduleData
                                                        ?.ownerNumber
                                                }
                                            </Text>
                                        </>
                                    }
                                    position="top"
                                    tooltipClass={''}
                                >
                                    <div className="w-4 h-4 rounded-full bg-white flex items-center justify-center">
                                        <Phone
                                            fill="#A2A8A4"
                                            strokeWidth={0}
                                            size={10}
                                        />
                                    </div>
                                </Tooltip>
                            )}
                        </div>
                    </div>
                </div>
            );
        },
        [getEventWidthAndPosition, sortedEvents, startOfDay, getEventColor]
    );

    const currentLineRef = useRef<HTMLDivElement>(null);
    const childOneRef = useRef<HTMLDivElement>(null);
    const childTwoRef = useRef<HTMLDivElement>(null);

    const calendarContainerRef = useRef<HTMLDivElement>(null);
    const [currentScrollIndex, setCurrentScrollIndex] = useState(0);

    useEffect(() => {
        if (currentLineRef.current) {
            currentLineRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
            });
        }
    }, []);

    const getDoctorSectionWidth = useCallback(() => {
        if (childOneRef.current) {
            const doctorSections = childOneRef.current.children;
            if (doctorSections.length > 0) {
                return doctorSections[0].getBoundingClientRect().width;
            }
        }

        return 288.5;
    }, []);

    const getVisibleDoctors = useCallback(() => {
        if (childOneRef.current) {
            const containerWidth =
                calendarContainerRef.current?.offsetWidth || 0;
            const doctorWidth = getDoctorSectionWidth();
            return Math.floor(containerWidth / doctorWidth);
        }
        return 3;
    }, [getDoctorSectionWidth]);

    const handleScroll = useCallback(
        (direction: string) => {
            if (childOneRef.current && childTwoRef.current) {
                const sectionWidth = getDoctorSectionWidth();
                const visibleDoctors = getVisibleDoctors();
                const maxScrollIndex =
                    (usersToDisplay?.length || 1) - visibleDoctors;

                let newIndex =
                    direction === 'right'
                        ? Math.min(
                              currentScrollIndex + visibleDoctors,
                              maxScrollIndex
                          )
                        : Math.max(currentScrollIndex - visibleDoctors, 0);

                setCurrentScrollIndex(newIndex);

                const newScrollLeft = newIndex * sectionWidth;

                childOneRef.current.scrollTo({
                    left: newScrollLeft,
                    behavior: 'smooth',
                });

                childTwoRef.current.scrollTo({
                    left: newScrollLeft,
                    behavior: 'smooth',
                });
            }
        },
        [
            currentScrollIndex,
            getDoctorSectionWidth,
            usersToDisplay?.length,
            getVisibleDoctors,
        ]
    );

    useEffect(() => {
        const childOne = childOneRef.current;
        const childTwo = childTwoRef.current;

        if (!childOne || !childTwo) return;

        const handleScroll = (event: Event) => {
            const target = event.target as HTMLElement;
            const scrollLeft = target.scrollLeft;
            if (target === childOne) {
                if (childTwo.scrollLeft !== scrollLeft) {
                    childTwo.scrollLeft = scrollLeft;
                }
            } else if (target === childTwo) {
                if (childOne.scrollLeft !== scrollLeft) {
                    childOne.scrollLeft = scrollLeft;
                }
            }

            const sectionWidth = getDoctorSectionWidth();
            setCurrentScrollIndex(Math.round(scrollLeft / sectionWidth));
        };

        childOne.addEventListener('scroll', handleScroll);
        childTwo.addEventListener('scroll', handleScroll);

        return () => {
            childOne.removeEventListener('scroll', handleScroll);
            childTwo.removeEventListener('scroll', handleScroll);
        };
    }, [getDoctorSectionWidth]);

    return (
        <div className="w-full relative" ref={calendarContainerRef}>
            <div
                id="stickHere"
                className={`w-full flex items-center  sticky top-[73px] bg-white z-[6] mb-0`}
            >
                <div className="w-[5.30rem]"></div>
                <div
                    className={`w-full flex ${selectedUser[0]?.value !== getAuth()?.userId ? 'justify-between' : 'justify-center'} items-center border border-b-0 border-secondary-50 p-4 rounded-tr-2xl rounded-tl-2xl h-[65px]`}
                >
                    {selectedUser[0]?.value !== getAuth()?.userId && (
                        <div
                            onClick={() => handleScroll('left')}
                            className="w-8 h-8 cursor-pointer rounded-full bg-secondary-50 flex items-center justify-center"
                        >
                            <IconAngleLeft size={16} color="#333333" />
                        </div>
                    )}
                    <Text variant="caption" className="text-primary-600">
                        {`Showing ${selectedUser.length}/${allDoctors.filter((item) => item.role.name === 'admin' || item.role.name === 'doctor').length} doctors`}
                    </Text>
                    {selectedUser[0]?.value !== getAuth()?.userId && (
                        <div
                            onClick={() => handleScroll('right')}
                            className="w-8 h-8 cursor-pointer rotate-180 rounded-full bg-secondary-50 flex items-center justify-center"
                        >
                            <IconAngleLeft size={16} color="#333333" />
                        </div>
                    )}
                </div>
            </div>

            <div
                className="grid grid-cols-[80px_1fr]"
                style={{ width: '100%' }}
            >
                <div
                    className={`col-span-1 border-r border-secondary-50 ${usersToDisplay.length >= 1 && 'pt-12'}`}
                >
                    {halfHourSlots.map((hour, i) => (
                        <div
                            key={i}
                            className="flex items-start h-12 text-xs text-gray-500"
                        >
                            {hour.format('h:mm A')}
                        </div>
                    ))}
                </div>

                <div className="grid border-r border-neutral-50 ">
                    <div
                        id="childOne"
                        ref={childOneRef}
                        className={`grid grid-flow-col ${usersToDisplay.length > 3 ? 'auto-cols-[288.5px]' : 'auto-cols'} 
                        gap-0 overflow-x-auto scrollbar-hide overflow-y-clip sticky top-[138px] bg-white z-[5]`}
                    >
                        {usersToDisplay?.map(
                            (
                                user: {
                                    label:
                                        | string
                                        | number
                                        | bigint
                                        | boolean
                                        | React.ReactElement<
                                              any,
                                              | string
                                              | React.JSXElementConstructor<any>
                                          >
                                        | Iterable<React.ReactNode>
                                        | React.ReactPortal
                                        | Promise<React.AwaitedReactNode>
                                        | null
                                        | undefined;
                                },
                                userIndex: React.Key | null | undefined
                            ) => (
                                <div
                                    key={userIndex}
                                    className="relative border-r border-secondary-50 w-full snap-start"
                                >
                                    <div className="flex justify-center py-3 w-full border-t border-b border-secondary-50">
                                        <Text
                                            variant="bodySmall"
                                            className="text-primary-900"
                                        >
                                            {user.label
                                                ? user.label
                                                : selectedUser?.length === 1 &&
                                                    selectedUser[0]?.value ===
                                                        getAuth()?.userId
                                                  ? 'My Appointments'
                                                  : 'No User Selected'}
                                        </Text>
                                    </div>
                                </div>
                            )
                        )}
                    </div>

                    <div
                        id="childTwo"
                        ref={childTwoRef}
                        className={`grid grid-flow-col ${usersToDisplay.length > 3 ? 'auto-cols-[288.5px]' : 'auto-cols'}  
                    gap-0 overflow-x-auto overflow-y-clip`}
                    >
                        {usersToDisplay?.map(
                            (
                                user: { value: any },
                                userIndex: React.Key | null | undefined
                            ) => (
                                <div
                                    key={userIndex}
                                    className="relative  border-r border-secondary-50 w-full snap-start"
                                >
                                    {halfHourSlots.map((hour, i) => (
                                        <div
                                            key={i}
                                            className={`border-t h-12 w-full  ${getSlotColor(hour, user)}`}
                                            onClick={(e) =>
                                                handleEventClick(e, hour, user)
                                            }
                                        ></div>
                                    ))}

                                    {/* Render events for each user */}
                                    <div className="absolute top-0 left-0 w-full">
                                        {sortedEvents
                                            .filter((event: any) =>
                                                (
                                                    event.treatmentData
                                                        ?.doctors || []
                                                ).includes(user.value)
                                            )
                                            .map((event: any) =>
                                                renderEvent(event, user.value)
                                            )}
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                    {moment().isSame(selectedDate, 'day') && (
                        <div
                            className="absolute bg-red-500 w-[calc(100vw-42rem)]"
                            ref={currentLineRef}
                            style={{
                                top: `${currentTime.diff(startOfDay, 'minutes') * PIXEL_PER_MINUTE + 112}px`,
                                height: '2px',
                                zIndex: 1,
                            }}
                        ></div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Day;
