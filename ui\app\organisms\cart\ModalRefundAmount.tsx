import React from 'react';
import { <PERSON><PERSON>, Text, Textarea } from '@/app/atoms';
import { Modal } from '@/app/molecules';
import { Tags } from '@/app/atoms';
import PaymentMode, {
    PaymentModeType,
    onChangeAmount,
} from '@/app/molecules/cart/PaymentMode';

import Price from '@/app/atoms/Price';
import { useState } from 'react';
import CurrencyInput, {
    CurrencyInputOnChangeValues,
} from 'react-currency-input-field';
import { CREDIT_TYPES, PAYMENT_TYPES } from '@/app/utils/constant';
export interface FormValuesFileUpload {
    refunds: {
        [key: string]: boolean;
    };
}

const formatCurrency = (value: string | number | undefined): string => {
    if (value === undefined) return '₹0';
    const numValue = typeof value === 'string' ? Number(value) : value;
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'INR',
    }).format(numValue);
};

interface ModalRefundAmountType {
    isOpen: boolean;
    onClose: () => void;
    modalTitle: string;
    onConfirmRefund: () => void;
    totalPayableAmount: number;
    onChangeAmount: onChangeAmount;
    refundAmount: number;
    onChangePaymentMode: (mode: string) => void;
    onChangeNotes?: (notes: string) => void;
    notes?: string;
}

const ModalRefundAmount: React.FC<ModalRefundAmountType> = (props) => {
    const {
        isOpen,
        onClose,
        modalTitle,
        onConfirmRefund,
        totalPayableAmount,
        onChangeAmount,
        refundAmount,
        onChangePaymentMode,
        onChangeNotes,
        notes,
    } = props;

    const [collectAmountValue, setCollectAmountValue] = useState<
        string | undefined
    >(String(refundAmount) || '0');
    const [collectAmountError, setCollectAmountError] =
        useState<boolean>(false);
    const [amountLessThanRefund, setAmountLessThanRefund] =
        useState<boolean>(false);

    const onSubmit = () => {
        onConfirmRefund();
    };

    const [paymentMode, setPaymentMode] = useState<PaymentModeType>(
        PAYMENT_TYPES.Cash
    );

    const modalFooter = (
        <div className="flex gap-2 justify-end">
            <Button
                id={'refund-cancel'}
                onClick={onClose}
                size="small"
                className="shrink-0"
                variant="secondary"
            >
                Cancel
            </Button>
            <Button
                id={'refund-confirm'}
                onClick={() => onSubmit()}
                size="small"
                className="shrink-0"
                disabled={collectAmountError}
            >
                Confirm
            </Button>
        </div>
    );

    const handleCollectAmountChange = (
        value: string | undefined,
        name?: string,
        values?: CurrencyInputOnChangeValues
    ) => {
        if (value) {
            const [actualVal, decimalVal] = value?.split('.');
            let finalVal = value;

            if (Number(actualVal) > 99999999) {
                finalVal =
                    actualVal.slice(0, 8) +
                    (decimalVal ? '.' + decimalVal : '');
            }

            const numValue = Number(finalVal);
            setCollectAmountValue(finalVal);

            // Check if amount is greater than refund amount (error condition)
            if (numValue > refundAmount) {
                setCollectAmountError(true);
                setAmountLessThanRefund(false);
                // Keep the previous valid amount for onChangeAmount
                onChangeAmount(refundAmount.toString());
            } else {
                setCollectAmountError(false);
                // Check if amount is less than refund amount (warning condition)
                setAmountLessThanRefund(numValue < refundAmount);
                onChangeAmount(finalVal);
            }
        } else {
            setCollectAmountValue('0');
            setCollectAmountError(false);
            setAmountLessThanRefund(true); // 0 is less than refund amount
            onChangeAmount('0');
        }
    };

    return (
        <Modal
            dataAutomation="refundAmount"
            isOpen={isOpen}
            onClose={onClose}
            modalTitle={modalTitle}
            modalFooter={modalFooter}
            isShowBorder={false}
        >
            <div className="border-2 rounded-2xl">
                <div className="p-4">
                    <div className="flex justify-center border-b-2 pb-2 px-2">
                        <Text
                            variant="body"
                            className="font-medium text-[16px] text-neutral-900 mr-4"
                        >
                            Refund Amount {formatCurrency(refundAmount)}
                        </Text>
                    </div>
                    <div>
                        <div className="mt-5">
                            <PaymentMode
                                totalPayableAmount={Number(collectAmountValue)}
                                onChangeAmount={(value) => {
                                    handleCollectAmountChange(value);
                                }}
                                onPaymentModeChange={(mode) => {
                                    setPaymentMode(mode);
                                    onChangePaymentMode(mode);
                                }}
                                paymentModeValue={paymentMode}
                                className={'rounded-2xl  p-1'}
                                classname={'rounded-2xl'}
                            />

                            {collectAmountError && (
                                <Tags
                                    variant="error"
                                    isLight={true}
                                    size="small"
                                    className="text-xs p-1 w-full mt-2"
                                    label="Amount cannot exceed the refund amount."
                                />
                            )}

                            {!collectAmountError && amountLessThanRefund && (
                                <Tags
                                    variant="warning"
                                    isLight={true}
                                    size="small"
                                    className="text-[9px] p-1 w-full mt-2"
                                    label="Any additional amount will be added to the owner's credits."
                                />
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <div className="my-6">
                <Textarea
                    id="refundNotes"
                    name="refundNotes"
                    label="Add Note"
                    placeholder="type something here"
                    value={notes}
                    onChange={(e) => onChangeNotes?.(e.target.value)}
                    rows={3}
                    maxLength={500}
                    variant="basicField"
                    isAutoGrow={true}
                    resizeValue="resize-none"
                    className="!bg-white !border-[#EDF0EC]"
                />
            </div>
        </Modal>
    );
};

export default ModalRefundAmount;
