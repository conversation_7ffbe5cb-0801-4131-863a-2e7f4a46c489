import React, { useEffect, useState, useMemo } from 'react';
import DaySummaryList, { DaySummaryListT } from '../invoice/DaySummaryList';
import InvoiceBasicDetail, {
    InvoiceBasicDetailT,
} from '../../molecules/cart/InvoiceBasicDetail';
import FileSummeryList from './FileSummeryList';
import InvoiceSummeryList from './InvoiceSummeryList';
import PriceSummeryCard, { PriceSummeryCardType } from './PriceSummeryCard';
import { FileSummeryType } from '../../molecules/FileSummery';
import { Card } from '../../molecules';
import { CartItemType, onQuantityChange } from '../../molecules/CartSummery';
import ModalRefund, { FormValuesFileUpload } from './ModalRefund';
import ModalRefundAmount from './ModalRefundAmount';
import { onChangeAmount } from '@/app/molecules/cart/PaymentMode';
import { CREDIT_TYPES, TAB_TYPES, CART_TYPES } from '@/app/utils/constant';
import { Tags } from '@/app/atoms';
import moment from 'moment';
import InvoiceReceiptCard from './InvoiceReceiptCard';
import EmptyState from '@/app/molecules/EmptyState';
import {
    calculateRefund,
    createCreditNoteData,
    createInvoiceCartItems,
    createPaymentDetailsCall,
    createYearListWithData,
    getInvoiceId,
} from '@/app/utils/payment/payment-details-utils';
import {
    useCreateInvoiceMutation,
    useCreateInvoiceWithPaymentMutation,
    useInvoiceDocumentMutation,
} from '@/app/services/invoice.queries';
import { useCreatePaymentDetailsMutation } from '@/app/services/payment-details.queries';
import { usePaymentDocumentMutation } from '@/app/services/payment-details.queries';
import ShareMultipleDocumentsModal from '../ShareMultipleDocumentsModal';
import FileShareSuccessModal from '../FileShareSuccessModal';
import { sendDocumentsFromInvoiceTab } from '@/app/services/emr.service';
import NotFoundModal from '../NotFoundModal';
import StartInvoice from '../invoice/StartInvoice';
import { getAuth } from '@/app/services/identity.service';
import { debounce } from 'lodash';
import RenderFields from '@/app/molecules/RenderFields';
import ModalReconcile from '../invoice/ModalReconcile';
import { Text } from '@/app/atoms';
import { useLastTabActivity } from '@/app/services/tab-activity.queries';
import InvoiceDetailsView from '../owner/InvoiceDetailsView';
import PaymentDetailsView from '../owner/PaymentDetailsView';
import RefundDetailsView from '../owner/RefundDetailsView';

const formatCurrency = (value: string): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'INR',
    }).format(Number(value));
};
interface PatientPaymentDetailsData {
    patientReceiptsData: any;
    patientReceiptsStatus: any;
    newPatientInvoicesData: any;
    newPatientInvoicesStatus: any;
    patientRefundsData: any;
    patientRefundsStatus: any;
}
const frameInvoiceCartItems = (currentSelectedInvoiceItem: any) => {
    if (currentSelectedInvoiceItem) {
        // First create base cart items using the utility function
        const baseCartItems = createInvoiceCartItems(
            JSON.parse(JSON.stringify(currentSelectedInvoiceItem))
        );

        // If we have invoice details with refund information, enhance the cart items
        if (currentSelectedInvoiceItem?.invoice?.details) {
            const invoiceDetails = currentSelectedInvoiceItem.invoice.details;

            // Map through the base cart items and add refund tracking fields
            return baseCartItems.map((cartItem) => {
                // Find matching invoice detail for this cart item
                const matchingDetail = invoiceDetails.find(
                    (detail: any) => detail.id === cartItem.id
                );

                if (matchingDetail) {
                    return {
                        ...cartItem,
                        // Important: Make sure the item property references the original invoice
                        item: currentSelectedInvoiceItem,
                        // Include refund tracking fields from API
                        refundedQuantity: matchingDetail.refundedQuantity || 0,
                        remainingRefundQuantity:
                            matchingDetail.remainingRefundQuantity !== undefined
                                ? matchingDetail.remainingRefundQuantity
                                : matchingDetail.quantity,
                        canBeRefunded:
                            matchingDetail.canBeRefunded !== undefined
                                ? matchingDetail.canBeRefunded
                                : matchingDetail.quantity > 0,
                    };
                }

                return {
                    ...cartItem,
                    // Ensure item property is always set
                    item: currentSelectedInvoiceItem,
                };
            });
        }

        // Make sure all cart items have the item property referencing the invoice
        return baseCartItems.map((cartItem) => ({
            ...cartItem,
            item: currentSelectedInvoiceItem,
        }));
    }
    return [];
};

const showCreditNoteData = (currentSelectedInvoiceItem: any) => {
    return createCreditNoteData(currentSelectedInvoiceItem);
};

// Transform patient payment data into combined invoice list
const createCombinedPatientData = (
    patientPaymentDetailsData: PatientPaymentDetailsData
) => {
    const { patientReceiptsData, newPatientInvoicesData, patientRefundsData } =
        patientPaymentDetailsData;

    const combinedData: any[] = [];

    // Add receipts data - correct path is paymentdetails
    if (patientReceiptsData?.data?.paymentDetails) {
        patientReceiptsData.data.paymentDetails.forEach((receipt: any) => {
            // Skip write-off receipts – they are not meant for UI display
            if (receipt.type === 'Write Off Invoice') {
                return;
            }
            combinedData.push({
                ...receipt,
                // Keep original type but add source to differentiate
                source: 'receipt', // Mark as coming from receipt/payment details
                visitType: 'Receipt', // This will be set by createYearListWithData
                reason: 'Receipt',
                createdAt: receipt.createdAt || receipt.date,
                referenceAlphaId:
                    receipt.referenceAlphaId || receipt.receiptAlphaId,
            });
        });
    }

    // Add invoices data
    if (newPatientInvoicesData?.data?.invoices) {
        newPatientInvoicesData.data.invoices.forEach((invoice: any) => {
            combinedData.push({
                ...invoice,
                type: 'Invoice',
                source: 'invoice', // Mark as coming from actual invoice data
                visitType: 'Invoice', // This will be set by createYearListWithData
                createdAt: invoice.createdAt || invoice.date,
                referenceAlphaId:
                    invoice.referenceAlphaId || invoice.invoiceNumber,
            });
        });
    }

    // Add refunds data
    if (patientRefundsData?.data?.invoices) {
        patientRefundsData.data.invoices.forEach((refund: any) => {
            combinedData.push({
                ...refund,
                type: 'Credit Note',
                source: 'creditnote', // Mark as coming from actual credit note data
                visitType: 'Credit Note', // This will be set by createYearListWithData
                createdAt: refund.createdAt || refund.date,
                referenceAlphaId:
                    refund.referenceAlphaId || refund.invoiceNumber,
            });
        });
    }

    // Sort by date (newest first)
    return combinedData.sort(
        (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
};

const framePatientYearListWithData = (
    combinedData: any[],
    setCurrentSelectedInvoiceItem: any,
    currentSelectedInvoiceItem: any
) => {
    const result = createYearListWithData(combinedData);
    if (currentSelectedInvoiceItem === null && result.firstItem) {
        // Prefer the first invoice over any other item
        const firstInvoice = combinedData.find(
            (item: any) => item.type === 'Invoice'
        );
        setCurrentSelectedInvoiceItem(firstInvoice || result.firstItem);
    }
    return result.resultFinal;
};

const getPatientInvoiceCalendar = (
    currentSelectedInvoiceItem: any,
    setCurrentSelectedInvoiceItem: any,
    setActiveDay: any,
    combinedData: any[]
) => {
    return {
        activeDay: currentSelectedInvoiceItem?.id,
        onActiveDayChange: (data: any) => {
            setCurrentSelectedInvoiceItem(data?.item);
            setActiveDay(data.id);
            // Just update the selected item, don't show full detail view
        },
        onSearchAppCalender: () => {},
        yearList: framePatientYearListWithData(
            combinedData,
            setCurrentSelectedInvoiceItem,
            currentSelectedInvoiceItem
        ),
    };
};

interface PatientPaymentDetailsType {
    patientPaymentDetailsData: PatientPaymentDetailsData;
    cartOptions: any;
    patientId: string;
    currentMainBalance: any;
    setIsLoadingState: Function;
    isStartInvoice: boolean;
    setIsStartInvoice: Function;
    ownerId: string;
    latestAppointment: any;
    isCancelInvoice: boolean;
    setIsCancelInvoice: Function;
    ownerDetails: any;
    patientName: string;
    activeItem: string;
    startInvoiceData: any;
    planProps: any;
    plansDropDownProps: any;
    // IDEXX related props from PatientDetailsTemplate
    showIdexxURL?: boolean;
    setShowIdexxURL: (show: boolean) => void;
    setShowIdexxCreation: (show: boolean) => void;
    showIdexxOrderCreation?: boolean;
    showIdexxCreationModalText?: string;
    setShowIdexxCreationModalText?: (text: string) => void;
    idexxURL?: string;
    setIdexxURL: (url: string) => void;
    handleIdexxComplete?: () => void;
}

const PatientPaymentDetails = (props: PatientPaymentDetailsType) => {
    const {
        patientPaymentDetailsData,
        cartOptions,
        patientId,
        currentMainBalance,
        setIsLoadingState,
        ownerId,
        isStartInvoice,
        setIsStartInvoice,
        latestAppointment,
        isCancelInvoice,
        setIsCancelInvoice,
        ownerDetails,
        patientName,
        activeItem,
        startInvoiceData,
        planProps,
        plansDropDownProps,
        showIdexxURL,
        setShowIdexxURL,
        showIdexxCreationModalText,
        setShowIdexxCreationModalText,
        idexxURL,
        setIdexxURL,
        handleIdexxComplete,
        setShowIdexxCreation,
        showIdexxOrderCreation,
    } = props;
    // Create combined data from new APIs
    const allInvoices = useMemo(() => {
        return createCombinedPatientData(patientPaymentDetailsData);
    }, [patientPaymentDetailsData]);

    const [localAllInvoices, setLocalAllInvoices] = useState(allInvoices);
    const { createInvoiceMutation } = useCreateInvoiceMutation();
    const { createInvoiceWithPaymentMutation } =
        useCreateInvoiceWithPaymentMutation();
    const { createPaymentDetailsMutation } = useCreatePaymentDetailsMutation(
        1,
        10,
        '',
        'true'
    );
    const [isShowRefund, setIsShowRefund] = useState(false);
    const [isShowReconcile, setIsShowReconcile] = useState(false);
    const [currentSelectedInvoiceItem, setCurrentSelectedInvoiceItem] =
        useState<any>(() => {
            // If we have an activeItem, find it in allInvoices
            if (activeItem && allInvoices?.length) {
                // First, try to find a direct match for invoices or credit notes
                const directInvoiceMatch = allInvoices.find(
                    (item: any) =>
                        // Direct invoice or credit note match
                        (item.type === 'Invoice' ||
                            item.type === 'Credit Note') &&
                        item.referenceAlphaId === activeItem
                );

                if (directInvoiceMatch) {
                    return directInvoiceMatch;
                }

                // If no direct invoice match, look for receipt
                const directReceiptMatch = allInvoices.find(
                    (item: any) =>
                        // Direct receipt match - check for source='receipt' or traditional receipt types
                        ((item.source === 'receipt' ||
                            item.type === 'Collect' ||
                            item.type === 'Return' ||
                            item.type === 'Reconcile Invoice' ||
                            item.type === 'Bulk Reconcile Invoice') &&
                            item.referenceAlphaId === activeItem) ||
                        // Receipt reference match
                        item.receiptAlphaId === activeItem
                );

                if (directReceiptMatch) {
                    return directReceiptMatch;
                }

                // Finally, check for invoice references in reconciliation records
                const reconcileMatch = allInvoices.find(
                    (item: any) => item.invoice?.referenceAlphaId === activeItem
                );

                if (reconcileMatch) {
                    return reconcileMatch;
                }
            }

            // Default to the first invoice if no activeItem or it wasn't found
            const firstInvoice = allInvoices.find(
                (item: any) => item.type === 'Invoice'
            );
            return (
                firstInvoice || createYearListWithData(allInvoices).firstItem
            );
        });
    const [isRefundAmountModalShown, setIsRefundAmountModalShown] = useState<{
        isOpen: boolean;
        payload?: any;
    }>({
        isOpen: false,
        payload: undefined,
    });
    const [selectedItemForRefund, setSelectedItemForRefund] = useState<object>(
        {}
    );
    const [refundNotes, setRefundNotes] = useState('');
    const [isShareModal, setIsShareModal] = useState(false);
    const [isFileShareSuccessModal, setIsFileShareSuccessModal] =
        useState(false);
    const [openNotFoundModal, setOpenNotFoundModal] = useState(false);
    const [isRefundSubmitting, setIsRefundSubmitting] = useState(false);

    const ownerDetailsForInvoiceView = ownerDetails && {
        ownerBrand: {
            firstName: ownerDetails.ownerBrand.firstName,
            lastName: ownerDetails.ownerBrand.lastName,
            ownerBalance: ownerDetails.ownerBrand.ownerBalance || 0,
            ownerCredits: Number(ownerDetails.ownerBrand.ownerCredits) || 0,
            globalOwner: {
                phoneNumber: ownerDetails.ownerBrand.globalOwner.phoneNumber,
                countryCode:
                    ownerDetails.ownerBrand.globalOwner.countryCode || '',
            },
            address: ownerDetails.address || null,
        },
    };

    // Detail views are now always shown in the right panel based on currentSelectedInvoiceItem

    const type = currentSelectedInvoiceItem?.type;
    const receiptAmount = currentSelectedInvoiceItem?.amount;
    const cartItems = frameInvoiceCartItems(currentSelectedInvoiceItem);
    const [activeDay, setActiveDay] = useState(() => {
        if (activeItem && allInvoices?.length) {
            // First, try to find a direct match for invoices or credit notes
            const directInvoiceMatch = allInvoices.find(
                (item: any) =>
                    // Direct invoice or credit note match
                    (item.type === 'Invoice' || item.type === 'Credit Note') &&
                    item.referenceAlphaId === activeItem
            );

            if (directInvoiceMatch) {
                return directInvoiceMatch.id;
            }

            // If no direct invoice match, look for receipt
            const directReceiptMatch = allInvoices.find(
                (item: any) =>
                    // Direct receipt match - check for source='receipt' or traditional receipt types
                    ((item.source === 'receipt' ||
                        item.type === 'Collect' ||
                        item.type === 'Return' ||
                        item.type === 'Reconcile Invoice' ||
                        item.type === 'Bulk Reconcile Invoice') &&
                        item.referenceAlphaId === activeItem) ||
                    // Receipt reference match
                    item.receiptAlphaId === activeItem
            );

            if (directReceiptMatch) {
                return directReceiptMatch.id;
            }

            // Finally, check for invoice references in reconciliation records
            const reconcileMatch = allInvoices.find(
                (item: any) => item.invoice?.referenceAlphaId === activeItem
            );

            if (reconcileMatch) {
                return reconcileMatch.id;
            }
        }

        // Default to the first invoice ID if available
        const firstInvoice = allInvoices.find(
            (item: any) => item.type === 'Invoice'
        );
        return (
            firstInvoice?.id ||
            createYearListWithData(allInvoices).firstItem?.id
        );
    });
    // const currentSelectedInvoiceItem = currentSelectedInvoiceItem;
    const [invoiceCalender, setInvoiceCalender] = useState(
        getPatientInvoiceCalendar(
            currentSelectedInvoiceItem,
            setCurrentSelectedInvoiceItem,
            setActiveDay,
            allInvoices
        )
    );

    const invoiceSummary = {
        patientId:
            currentSelectedInvoiceItem?.patientId ||
            currentSelectedInvoiceItem?.invoice?.patientId,
        paymentNotes: currentSelectedInvoiceItem?.paymentNotes,
        payments: currentSelectedInvoiceItem?.payments,
        createdBy: currentSelectedInvoiceItem?.createdByName,
        createdAt: currentSelectedInvoiceItem?.createdAt,
        balanceDue:
            (currentSelectedInvoiceItem?.balanceDue ||
                currentSelectedInvoiceItem?.invoice?.balanceDue ||
                0) * -1,
        paymentStatus: currentSelectedInvoiceItem?.status,
        paymentMode:
            currentSelectedInvoiceItem?.type === 'Credit Note'
                ? 'Credits'
                : currentSelectedInvoiceItem?.invoice
                  ? currentSelectedInvoiceItem?.invoice?.paymentMode
                  : currentSelectedInvoiceItem?.paymentType,
        totalPrice:
            currentSelectedInvoiceItem?.totalprice ||
            currentSelectedInvoiceItem?.invoice?.totalPrice ||
            0,
        totalDiscount:
            currentSelectedInvoiceItem?.discountAmount ||
            currentSelectedInvoiceItem?.invoice?.totalDiscount ||
            0,
        totalTax:
            currentSelectedInvoiceItem?.totalTax ||
            currentSelectedInvoiceItem?.invoice?.totalTax ||
            0,
        invoiceAmount:
            currentSelectedInvoiceItem?.invoiceAmount ||
            currentSelectedInvoiceItem?.invoice?.invoiceAmount ||
            0,
        previousBalance:
            currentSelectedInvoiceItem?.previousBalance ||
            currentSelectedInvoiceItem?.invoice?.previousBalance ||
            0,
        collectedAmount:
            currentSelectedInvoiceItem?.totalCollected ||
            currentSelectedInvoiceItem?.invoice?.amountPaid ||
            currentSelectedInvoiceItem?.amount ||
            0,
        differenceAmount:
            currentSelectedInvoiceItem?.balanceDue ||
            (currentSelectedInvoiceItem?.invoice
                ? currentSelectedInvoiceItem?.invoice?.amountPayable -
                  currentSelectedInvoiceItem?.invoice?.amountPaid
                : 0),
        totalPayableAmount:
            currentSelectedInvoiceItem?.invoiceAmount ||
            currentSelectedInvoiceItem?.invoice?.amountPayable ||
            0,
        type: currentSelectedInvoiceItem?.type,
        referenceAlphaId: currentSelectedInvoiceItem?.referenceAlphaId,
        creditNoteReferenceAlphaId:
            currentSelectedInvoiceItem?.type === 'Credit Note'
                ? currentSelectedInvoiceItem?.referenceAlphaId
                : '',
    };

    // Transform invoice data to match InvoiceDetailsView expectations
    const transformedCurrentInvoice = useMemo(() => {
        if (
            !currentSelectedInvoiceItem ||
            currentSelectedInvoiceItem.type !== 'Invoice'
        ) {
            return currentSelectedInvoiceItem;
        }

        // Format the status to match what InvoiceDetailsView expects
        let status;
        if (currentSelectedInvoiceItem.status === 'fully_paid') {
            status = 'Paid';
        } else if (currentSelectedInvoiceItem.status === 'partially_paid') {
            status = 'Partially Paid';
        } else if (currentSelectedInvoiceItem.status === 'cancelled') {
            status = 'Cancelled';
        } else if (currentSelectedInvoiceItem.status === 'written_off') {
            status = 'Written Off';
        } else if (currentSelectedInvoiceItem.status === 'unknown') {
            status = '';
        } else {
            status = 'Pending';
        }

        // Format date
        const formattedDate =
            currentSelectedInvoiceItem.date ||
            new Date(currentSelectedInvoiceItem.createdAt).toLocaleDateString(
                'en-GB',
                {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                }
            );
        const formattedTime = moment(
            currentSelectedInvoiceItem.createdAt
        ).format('hh:mm A');
        return {
            id: currentSelectedInvoiceItem.id,
            invoiceNumber: `# ${currentSelectedInvoiceItem.referenceAlphaId || ''}`,
            status,
            date: formattedDate,
            time: formattedTime,
            user: currentSelectedInvoiceItem.createdByName || '',
            comment: currentSelectedInvoiceItem.comment || '',
            invoiceAmount:
                Number(currentSelectedInvoiceItem.invoiceAmount) || 0,
            amountCleared:
                Number(currentSelectedInvoiceItem.totalCollected) || 0,
            invoiceBalance: Number(currentSelectedInvoiceItem.balanceDue) || 0,
            totalprice:
                Number(currentSelectedInvoiceItem.totalprice) ||
                Number(currentSelectedInvoiceItem.invoiceAmount) ||
                0,
            discount: Number(currentSelectedInvoiceItem.discount) || 0,
            discountAmount:
                Number(currentSelectedInvoiceItem.discountAmount) || 0,
            patientId: currentSelectedInvoiceItem.patientId || '',
            cartId: currentSelectedInvoiceItem.cartId || '',
            appointmentId: currentSelectedInvoiceItem.appointmentId || '',
            metadata: currentSelectedInvoiceItem.metadata || {},
            creditNotes: currentSelectedInvoiceItem.creditNotes || [],
            ownerId: ownerId,
            // Cast payments to correct type
            payments:
                currentSelectedInvoiceItem.payments?.map((payment: any) => ({
                    id: payment.id,
                    amount: Number(payment.amount || 0),
                    paymentType: payment.paymentType || '',
                    paymentMode: payment.paymentMode || '',
                    createdAt: payment.createdAt || '',
                    createdBy: payment.createdBy || '',
                    createdByName: payment.createdByName || '',
                    isCreditUsed: Boolean(payment.isCreditUsed),
                    creditAmountUsed: Number(payment.creditAmountUsed || 0),
                    referenceAlphaId: payment.referenceAlphaId || '',
                })) || [],
            details: currentSelectedInvoiceItem.details || [],
            auditLogs: currentSelectedInvoiceItem.auditLogs || [],
        };
    }, [currentSelectedInvoiceItem, ownerId]);

    // Transform credit note data to match RefundsTab expectations
    const transformedCurrentCreditNote = useMemo(() => {
        if (
            !currentSelectedInvoiceItem ||
            currentSelectedInvoiceItem.type !== 'Credit Note'
        ) {
            return currentSelectedInvoiceItem;
        }

        // Format the status to match what RefundsTab expects
        let status;
        if (currentSelectedInvoiceItem.status === 'fully_paid') {
            status = 'Paid';
        } else if (currentSelectedInvoiceItem.status === 'partially_paid') {
            status = 'Partially Paid';
        } else if (currentSelectedInvoiceItem.status === 'unknown') {
            status = '';
        } else {
            status = 'Pending';
        }

        // Format date
        const formattedDate = moment(
            currentSelectedInvoiceItem.createdAt
        ).format('DD MMM YYYY');
        const formattedTime = moment(
            currentSelectedInvoiceItem.createdAt
        ).format('hh:mm A');

        return {
            id: currentSelectedInvoiceItem.id,
            invoiceNumber: `#${currentSelectedInvoiceItem.referenceAlphaId || ''}`,
            status,
            date: formattedDate,
            time: formattedTime,
            user: currentSelectedInvoiceItem.createdByName || '',
            comment: currentSelectedInvoiceItem.comment || '',
            invoiceAmount: currentSelectedInvoiceItem.invoiceAmount || 0,
            amountCleared: currentSelectedInvoiceItem.totalCollected || 0,
            invoiceBalance: currentSelectedInvoiceItem.balanceDue || 0,
            originalInvoiceReferenceAlphaId:
                currentSelectedInvoiceItem.originalInvoiceReferenceAlphaId ||
                '',
            patientId: currentSelectedInvoiceItem.patientId || '',
            ownerId: ownerId, // Add owner ID to each credit note for reconciliation
            // Cast payments to correct type
            payments:
                currentSelectedInvoiceItem.payments?.map((payment: any) => ({
                    id: payment.id,
                    amount: Number(payment.amount || 0),
                    paymentType: payment.paymentType || '',
                    paymentMode: payment.paymentMode || '',
                    createdAt: payment.createdAt || '',
                    createdBy: payment.createdBy || '',
                    createdByName: payment.createdByName || '',
                    isCreditUsed: Boolean(payment.isCreditUsed),
                    creditAmountUsed: Number(payment.creditAmountUsed || 0),
                    referenceAlphaId: payment.referenceAlphaId || '',
                })) || [],
            details: currentSelectedInvoiceItem.details || [], // Include credit note details/line items
        };
    }, [currentSelectedInvoiceItem, ownerId]);

    // onChangeAmount={() => {
    //     alert('onChangeAmount called');
    // }}
    const onQuantityChange = (data: any) => {};
    const onNext = (data: any) => {
        const finalRefundItemData = calculateRefund(data, patientId);
        setSelectedItemForRefund(finalRefundItemData);

        setIsRefundAmountModalShown({
            isOpen: true,
            payload: data,
        });
    };
    // onConfirmRefund={(data) => {}}
    // refundAmount={39399}
    const refundCartItems = frameInvoiceCartItems(currentSelectedInvoiceItem);
    const refundsData = showCreditNoteData(currentSelectedInvoiceItem);
    const totalPayableAmount =
        currentSelectedInvoiceItem?.invoiceAmount ||
        currentSelectedInvoiceItem?.invoice?.amountPayable;

    // const isShowRefund = isShowRefund;
    // setIsShowRefund = { setIsShowRefund };
    const onRefundHandler = async (data: FormValuesFileUpload) => {
        onNext(data);
        setIsShowRefund(false);
    };

    const createReceiptSummaryUI = (text: string, isCreditAdded: boolean) => {
        return (
            <InvoiceReceiptCard
                paymentMode={invoiceSummary.paymentMode}
                amount={receiptAmount}
                text={text}
                createdAt={invoiceSummary.createdAt}
                createdBy={invoiceSummary.createdBy}
                notes={invoiceSummary.paymentNotes}
                creditsAdded={isCreditAdded}
                referenceAlphaId={invoiceSummary.referenceAlphaId}
                findAndNavigateToReference={findAndNavigateToReference}
                patientId={invoiceSummary.patientId}
                creditNoteReferenceAlphaId={
                    invoiceSummary.creditNoteReferenceAlphaId
                }
            />
        );
    };

    const hasInvoices = true; // cartItems?.length > 0;

    const localFileSummaries = ['Collect', 'Return'].includes(
        currentSelectedInvoiceItem?.type
    )
        ? {
              id: currentSelectedInvoiceItem?.id,
              fileName: currentSelectedInvoiceItem?.receipt?.fileName,
              fileLink: currentSelectedInvoiceItem?.receipt?.fileKey,
              fileType: 'PDF',
              systemGenerated: 'true',
          }
        : currentSelectedInvoiceItem?.invoice?.fileUrl
          ? {
                id: currentSelectedInvoiceItem?.id,
                fileName: currentSelectedInvoiceItem?.invoice?.fileUrl
                    ?.invoiceFileKey
                    ? `${currentSelectedInvoiceItem?.invoice?.fileUrl?.invoiceFileKey}.pdf`
                    : `${currentSelectedInvoiceItem?.invoice?.fileUrl?.creditNoteFileKey}.pdf`,
                fileLink:
                    currentSelectedInvoiceItem?.invoice?.fileUrl
                        ?.invoiceFileKey ??
                    currentSelectedInvoiceItem?.invoice?.fileUrl
                        ?.creditNoteFileKey,
                fileType: 'PDF',
                systemGenerated: 'true',
            }
          : {};
    const cardData: Array<{ id: string; title: string; text: string }> = [];

    if (
        currentSelectedInvoiceItem &&
        currentSelectedInvoiceItem?.type === 'Invoice'
    ) {
        // Handle both old and new data structures
        const cartId =
            currentSelectedInvoiceItem.cartId ||
            currentSelectedInvoiceItem.invoice?.cartId;
        if (!cartId) return;

        // Find all matching refunds instead of just the first one
        const refundItems = allInvoices.filter((item: any) => {
            const itemCartId = item.cartId || item.invoice?.cartId;
            return (
                itemCartId &&
                itemCartId === cartId &&
                item.type === 'Credit Note' &&
                // Filter out zero amount refunds
                Number(item.invoiceAmount || item.amount) > 0
            );
        });

        // Deduplicate refund items using a composite key of referenceId + amount
        // This handles cases where there might be multiple distinct refunds with the same reference
        const uniqueRefundKeys = new Set();

        // Add each unique refund to the cardData array
        refundItems.forEach((refundItem: any) => {
            const referenceId =
                refundItem.referenceAlphaId ??
                refundItem.invoice?.referenceAlphaId ??
                refundItem.invoice?.referenceId ??
                refundItem.id;

            // Skip if we've already added this unique combination
            if (uniqueRefundKeys.has(referenceId)) return;

            // Add to our set of processed reference IDs
            uniqueRefundKeys.add(referenceId);

            cardData.push({
                id: refundItem.id || refundItem.invoice?.id,
                title: `There was a refund of ${formatCurrency(
                    refundItem.invoiceAmount ||
                        refundItem.invoice?.invoiceAmount ||
                        refundItem.amount
                )} initiated for this Invoice on ${refundItem.date || refundItem.createdAt}`,
                text: `Refund ID Credit Note #${referenceId}`,
            });
        });
    }

    if (
        currentSelectedInvoiceItem &&
        currentSelectedInvoiceItem.type === 'Credit Note'
    ) {
        const cartId =
            currentSelectedInvoiceItem.cartId ||
            currentSelectedInvoiceItem.invoice?.cartId;

        // Keep track of unique invoice references using a composite key
        const uniqueInvoiceKeys = new Set();

        // Add card with original invoice reference (using either the property from backend or finding it in allInvoices)
        if (currentSelectedInvoiceItem.originalInvoiceReferenceAlphaId) {
            // Use the reference directly from the backend data
            const refId =
                currentSelectedInvoiceItem.originalInvoiceReferenceAlphaId;
            // Create a unique key that includes the invoice ID itself to ensure uniqueness
            const uniqueKey = `original-${refId}`;
            uniqueInvoiceKeys.add(uniqueKey);

            cardData.push({
                id:
                    currentSelectedInvoiceItem.invoiceId ||
                    currentSelectedInvoiceItem.id,
                title: `This refund is regarding the invoice`,
                text: `Invoice ID: Invoice #${refId}`,
            });
        } else if (cartId) {
            // Fallback - search for matching invoice in current data
            const matchingInvoices = allInvoices.filter((item: any) => {
                const itemCartId = item.cartId || item.invoice?.cartId;
                return (
                    itemCartId &&
                    itemCartId === cartId &&
                    item.type === 'Invoice'
                );
            });

            // Add a card for each unique matching invoice
            matchingInvoices.forEach((matchingInvoice: any) => {
                const refId =
                    matchingInvoice.referenceAlphaId ??
                    matchingInvoice.invoice?.referenceAlphaId ??
                    matchingInvoice.invoice?.referenceId ??
                    matchingInvoice.id;
                // Create a unique key with invoice ID to ensure uniqueness
                const uniqueKey = `${refId}-${matchingInvoice.id}`;

                // Skip if we've already added this reference ID
                if (uniqueInvoiceKeys.has(uniqueKey)) return;

                // Add to our set of processed keys
                uniqueInvoiceKeys.add(uniqueKey);

                cardData.push({
                    id: matchingInvoice.id,
                    title: `This refund is regarding the invoice`,
                    text: `Invoice ID: Invoice #${refId}`,
                });
            });
        }
    }
    const [filteredCalender, setFilterCalender] = useState(invoiceCalender);

    const onSearchAppCalender = (searchText: string) => {
        setFilterCalender((prev) => ({
            ...prev,
            yearList: invoiceCalender.yearList
                .map(({ dayList, ...args }) => ({
                    ...args,
                    dayList: dayList.filter(({ reason, doctor }) => {
                        return (
                            doctor
                                ?.toLowerCase()
                                ?.includes(searchText?.toLowerCase()) ||
                            reason
                                ?.toLowerCase()
                                ?.includes(searchText?.toLowerCase())
                        );
                    }),
                }))
                .filter(({ dayList }) => dayList.length > 0),
        }));
    };

    const onClear = () => {
        setFilterCalender(invoiceCalender);
    };

    // Check if invoice data has actually changed (not just length)
    const hasInvoiceDataChanged = useMemo(() => {
        if (allInvoices.length !== localAllInvoices.length) {
            return true;
        }

        // Check if any invoice status, amount, or other key fields have changed
        return allInvoices.some((invoice: any, index: number) => {
            const localInvoice = localAllInvoices[index];
            if (!localInvoice) return true;

            return (
                invoice.status !== localInvoice.status ||
                invoice.invoiceAmount !== localInvoice.invoiceAmount ||
                invoice.balanceDue !== localInvoice.balanceDue ||
                invoice.totalCollected !== localInvoice.totalCollected ||
                invoice.id !== localInvoice.id
            );
        });
    }, [allInvoices, localAllInvoices]);

    useEffect(() => {
        if (hasInvoiceDataChanged) {
            const temp = getPatientInvoiceCalendar(
                currentSelectedInvoiceItem,
                setCurrentSelectedInvoiceItem,
                setActiveDay,
                allInvoices
            );
            setInvoiceCalender(temp);
            setFilterCalender(temp);
            setLocalAllInvoices(allInvoices);
        }
    }, [hasInvoiceDataChanged, allInvoices, currentSelectedInvoiceItem]);

    // Create memoized debounced version of actOnRefundCOnfirmClick
    const debouncedActOnRefundCOnfirmClick = useMemo(
        () =>
            debounce(
                async () => {
                    // Prevent multiple submissions
                    if (isRefundSubmitting) return;

                    try {
                        setIsRefundSubmitting(true);

                        if (currentSelectedInvoiceItem) {
                            const dataValue: any = selectedItemForRefund;
                            const originalInvoiceAppointmentId =
                                currentSelectedInvoiceItem?.invoice
                                    ?.appointmentId;

                            const invoiceData = {
                                ...dataValue,
                                ownerId: ownerId,
                                invoiceAmount: dataValue.amountPayable,
                            };

                            const paymentData = {
                                patientId: patientId,
                                amount: Number(dataValue.amountPaid),
                                type: CREDIT_TYPES.CreditNote,
                                paymentType: (selectedItemForRefund as any)
                                    .paymentMode,
                                amountPayable: Number(dataValue.amountPayable),
                                mainBalance:
                                    Number(currentMainBalance) -
                                    (Number(dataValue.amountPaid) -
                                        Number(dataValue.amountPayable)),
                                previousBalance: Number(currentMainBalance),
                                transactionAmount: Number(
                                    dataValue.amountPayable
                                ),
                                ownerId: ownerId,
                                ...(refundNotes?.trim()
                                    ? { paymentNotes: refundNotes.trim() }
                                    : {}),
                            };

                            createInvoiceWithPaymentMutation.mutate(
                                {
                                    data: {
                                        appointmentId:
                                            originalInvoiceAppointmentId,
                                        invoiceData: invoiceData,
                                        paymentData: paymentData,
                                    },
                                },
                                {
                                    onSuccess: (data: any) => {
                                        setIsRefundAmountModalShown({
                                            isOpen: false,
                                        });
                                    },
                                    onError: (error) => {
                                        console.error(
                                            'Atomic refund confirmation failed:',
                                            error
                                        );
                                    },
                                }
                            );
                        }
                    } catch (error) {
                        console.error('Refund processing failed:', error);
                    } finally {
                        setIsRefundSubmitting(false);
                    }
                },
                1000, // 1 second delay
                { leading: true, trailing: false }
            ),
        [
            isRefundSubmitting,
            currentSelectedInvoiceItem,
            selectedItemForRefund,
            createInvoiceMutation,
            refundNotes,
        ]
    );

    // Add cleanup for debounced function
    useEffect(() => {
        return () => {
            debouncedActOnRefundCOnfirmClick.cancel();
        };
    }, [debouncedActOnRefundCOnfirmClick]);

    const createEntryForInvoiceInPaymentDetailsForRefund = (
        invoiceId: string,
        invoiceType: CREDIT_TYPES,
        amountPaid: number,
        amountPayable: number,
        currentPaymentMode: string,
        notes: string
    ) => {
        let currentTransaciton: number = 0;

        currentTransaciton = Number(amountPaid) - Number(amountPayable);

        let dFinalMainBalance: number = 0;

        dFinalMainBalance = Number(currentMainBalance) - currentTransaciton;

        const trimmedNote = notes?.trim();

        const data = {
            patientId: patientId,
            amount: Number(amountPaid),
            type: invoiceType,
            paymentType: currentPaymentMode,
            amountPayable: Number(amountPayable),
            mainBalance: Number(dFinalMainBalance),
            invoiceId: invoiceId,
            previousBalance: Number(currentMainBalance),
            transactionAmount: Number(amountPayable),
            ownerId: ownerId,
            ...(trimmedNote ? { paymentNotes: trimmedNote } : {}),
        };

        createPaymentDetailsCall(createPaymentDetailsMutation, data);
    };

    // Add this effect to refresh the current invoice data when payments are made
    useEffect(() => {
        if (currentSelectedInvoiceItem?.id) {
            // Find the fresh invoice data
            const updatedInvoice = allInvoices.find(
                (invoice: any) => invoice.id === currentSelectedInvoiceItem.id
            );

            if (updatedInvoice) {
                // Update the current invoice with fresh data
                setCurrentSelectedInvoiceItem(updatedInvoice);
            }
        }
    }, [allInvoices, currentSelectedInvoiceItem?.id]);

    const handleOnShare = () => {
        setIsShareModal(true);
    };

    const { invoiceDocumentMutation } = useInvoiceDocumentMutation();
    const { paymentDocumentMutation } = usePaymentDocumentMutation();
    const referenceAlphaId = (() => {
        // For BulkReconcileInvoice and ReconcileInvoice, use receiptAlphaId
        if (
            currentSelectedInvoiceItem?.type ===
                CREDIT_TYPES.BulkReconcileInvoice ||
            currentSelectedInvoiceItem?.type === CREDIT_TYPES.ReconcileInvoice
        ) {
            return currentSelectedInvoiceItem?.receiptAlphaId;
        }

        // For Invoice and Credit Note, use referenceAlphaId
        if (
            currentSelectedInvoiceItem?.type === CREDIT_TYPES.Invoice ||
            currentSelectedInvoiceItem?.type === CREDIT_TYPES.CreditNote
        ) {
            return (
                currentSelectedInvoiceItem?.referenceAlphaId ||
                currentSelectedInvoiceItem?.id
            );
        }

        // Default fallback for other types
        return (
            currentSelectedInvoiceItem?.referenceAlphaId ||
            currentSelectedInvoiceItem?.receiptAlphaId ||
            currentSelectedInvoiceItem?.id
        );
    })();

    const tabActivityResult = useLastTabActivity(
        patientId,
        'invoices',
        referenceAlphaId
    );

    const handleDownload = async () => {
        setIsLoadingState(true);
        try {
            // Determine document type based on the current selected invoice
            if (
                currentSelectedInvoiceItem?.type === 'Invoice' ||
                currentSelectedInvoiceItem?.type === 'Credit Note'
            ) {
                // Handle invoice download
                const referenceAlphaId =
                    currentSelectedInvoiceItem?.referenceAlphaId ||
                    currentSelectedInvoiceItem?.id;

                if (!referenceAlphaId) {
                    setIsLoadingState(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                invoiceDocumentMutation.mutate(
                    {
                        referenceAlphaId,
                        action: 'download',
                        patientId: patientId,
                    },
                    {
                        onSettled: () => {
                            // Set loading state to false when the mutation is complete
                            // (either success or error)
                            setIsLoadingState(false);
                        },
                    }
                );
            } else {
                // Handle other payment documents (receipts, etc.)
                const referenceAlphaId =
                    currentSelectedInvoiceItem?.receiptAlphaId;

                if (!referenceAlphaId) {
                    setIsLoadingState(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                paymentDocumentMutation.mutate(
                    {
                        referenceAlphaId,
                        documentType: 'payment-details',
                        action: 'download',
                        patientId: patientId,
                    },
                    {
                        onSuccess: (response) => {
                            if (tabActivityResult.refetch) {
                                tabActivityResult.refetch();
                            }
                        },
                        onSettled: () => {
                            // Set loading state to false when the mutation is complete
                            setIsLoadingState(false);
                        },
                    }
                );
            }
        } catch (error) {
            console.error('Error downloading document:', error);
            setIsLoadingState(false);
            setOpenNotFoundModal(true);
        }
    };

    async function sendDocuments(data: any) {
        setIsLoadingState(true);
        try {
            // Determine sharing modes based on user selection
            let shareMethod: 'whatsapp' | 'email' | 'both' | undefined;
            if (data.shareViaEmail && data.shareViaWhatsapp) {
                shareMethod = 'both';
            } else if (data.shareViaEmail) {
                shareMethod = 'email';
            } else if (data.shareViaWhatsapp) {
                shareMethod = 'whatsapp';
            }

            if (!shareMethod) {
                setIsLoadingState(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
                return;
            }

            // Route to the appropriate service based on document type
            if (
                currentSelectedInvoiceItem?.type === 'Invoice' ||
                currentSelectedInvoiceItem?.type === 'Credit Note'
            ) {
                // Handle invoice/credit note sharing
                const referenceAlphaId =
                    currentSelectedInvoiceItem?.referenceAlphaId ||
                    currentSelectedInvoiceItem?.id;

                if (!referenceAlphaId) {
                    setIsLoadingState(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                // For custom recipient, get email and phone number from form
                const emailValue =
                    data.recipient === 'other' && data.shareViaEmail
                        ? data.email
                        : '';
                const phoneValue =
                    data.recipient === 'other' && data.shareViaWhatsapp
                        ? data.number
                        : '';

                console.log(
                    'Invoice: Using email:',
                    emailValue,
                    'phone:',
                    phoneValue
                );

                const response = await invoiceDocumentMutation.mutateAsync({
                    referenceAlphaId,
                    action: 'share',
                    shareMethod,
                    patientId: patientId,
                    recipient: data.recipient,
                    email: emailValue,
                    whatsapp: phoneValue,
                });

                if (response?.status) {
                    setIsLoadingState(false);
                    setIsShareModal(false);
                    setIsFileShareSuccessModal(true);
                    if (tabActivityResult.refetch) {
                        tabActivityResult.refetch();
                    }
                } else {
                    setIsLoadingState(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                }
            } else {
                // Handle other payment documents (receipts, etc.)
                const referenceAlphaId =
                    currentSelectedInvoiceItem?.receiptAlphaId;

                if (!referenceAlphaId) {
                    setIsLoadingState(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                // For custom recipient, get email and phone number from form
                const emailValue =
                    data.recipient === 'other' && data.shareViaEmail
                        ? data.email
                        : '';
                const phoneValue =
                    data.recipient === 'other' && data.shareViaWhatsapp
                        ? data.number
                        : '';

                console.log(
                    'Payment: Using email:',
                    emailValue,
                    'phone:',
                    phoneValue
                );

                const response = await paymentDocumentMutation.mutateAsync({
                    referenceAlphaId,
                    documentType: 'payment-details',
                    action: 'share',
                    patientId: patientId,
                    shareMethod,
                    recipient: data.recipient,
                    email: emailValue,
                    whatsapp: phoneValue,
                });

                if (response?.status) {
                    setIsLoadingState(false);
                    setIsShareModal(false);
                    setIsFileShareSuccessModal(true);
                    if (tabActivityResult.refetch) {
                        tabActivityResult.refetch();
                    }
                } else {
                    setIsLoadingState(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                }
            }
        } catch (error) {
            console.error('Error sharing document:', error);
            setIsLoadingState(false);
            setIsShareModal(false);
            setOpenNotFoundModal(true);
        }
    }

    const latestAppointmentStatus = latestAppointment?.status;

    // Helper function to find and navigate to an invoice/receipt/credit note by reference
    const findAndNavigateToReference = (
        referenceAlphaId: string,
        referenceType: string = 'Invoice',
        relatedPatientId?: string
    ) => {
        if (!referenceAlphaId) {
            return false;
        }

        // Check if we need to redirect to another patient
        if (
            relatedPatientId &&
            relatedPatientId !== patientId &&
            typeof window !== 'undefined'
        ) {
            window.location.href = `/patients/${relatedPatientId}/details?tab=invoices&activeItem=${referenceAlphaId}`;
            return true;
        }

        // Default types to look for based on referenceType
        let targetTypes: string[] = [];
        if (referenceType === 'Invoice') targetTypes = ['Invoice'];
        if (referenceType === 'CreditNote' || referenceType === 'Credit Note')
            targetTypes = ['Credit Note'];
        if (referenceType === 'Receipt')
            targetTypes = [
                'Collect',
                'Return',
                'ReconcileInvoice',
                'BulkReconcileInvoice',
            ];

        // Search in the calendar data - prioritize direct type matches first
        let found = false;
        let foundDayItem = null;

        // First pass: Look for exact match on type and referenceAlphaId
        if (targetTypes.length > 0) {
            for (const year of invoiceCalender.yearList) {
                for (const day of year.dayList) {
                    if (
                        targetTypes.includes(day.item?.type) &&
                        day.item?.referenceAlphaId === referenceAlphaId
                    ) {
                        invoiceCalender.onActiveDayChange(day);
                        found = true;
                        foundDayItem = day;
                        break;
                    }
                }
                if (found) break;
            }
        }

        // Second pass: If not found and looking for Receipt, check receiptAlphaId
        if (!found && referenceType === 'Receipt') {
            for (const year of invoiceCalender.yearList) {
                for (const day of year.dayList) {
                    if (day.item?.receiptAlphaId === referenceAlphaId) {
                        invoiceCalender.onActiveDayChange(day);
                        found = true;
                        foundDayItem = day;
                        break;
                    }
                }
                if (found) break;
            }
        }

        // Third pass: Check for reference in reconciliation records
        if (!found) {
            for (const year of invoiceCalender.yearList) {
                for (const day of year.dayList) {
                    if (
                        day.item?.invoice?.referenceAlphaId === referenceAlphaId
                    ) {
                        invoiceCalender.onActiveDayChange(day);
                        found = true;
                        foundDayItem = day;
                        break;
                    }
                }
                if (found) break;
            }
        }

        // If found, scroll to it
        if (found && foundDayItem) {
            setTimeout(() => {
                const dayElement = document.getElementById(
                    `day-item-${foundDayItem.id}`
                );
                if (dayElement) {
                    dayElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                    });
                } else {
                    const activeElement = document.querySelector(
                        '.day-item .bg-secondary-100'
                    );
                    if (activeElement) {
                        activeElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                        });
                    }
                }
            }, 300);
            return true;
        }

        return false;
    };

    // Add useEffect to highlight and scroll to the active item when component loads
    useEffect(() => {
        // Check if we have an activeItem and we've found a corresponding invoice
        if (activeItem && currentSelectedInvoiceItem) {
            // If this item has the active reference we're looking for, scroll to it
            const isActiveItemSelected =
                currentSelectedInvoiceItem.referenceAlphaId === activeItem ||
                currentSelectedInvoiceItem.invoice?.referenceAlphaId ===
                    activeItem ||
                currentSelectedInvoiceItem.receiptAlphaId === activeItem;

            if (isActiveItemSelected) {
                // Wait for DOM to update, then scroll to the selected item
                setTimeout(() => {
                    const dayElement = document.getElementById(
                        `day-item-${currentSelectedInvoiceItem.id}`
                    );
                    if (dayElement) {
                        dayElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                        });
                    } else {
                        // Try to find the element by a different strategy - scroll to active day
                        const activeElement = document.querySelector(
                            '.day-item .bg-secondary-100'
                        );
                        if (activeElement) {
                            activeElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center',
                            });
                        }
                    }
                }, 500);
            }
        }
    }, [activeItem, currentSelectedInvoiceItem]);

    // Fix the ModalRefundAmount component by using type assertion
    const ModalRefundAmountComponent = ModalRefundAmount as any;

    return (
        <>
            {isStartInvoice ? (
                <StartInvoice
                    currentMainBalance={currentMainBalance}
                    plansDropDownProps={plansDropDownProps}
                    setIsStartInvoice={setIsStartInvoice}
                    patientId={patientId}
                    ownerId={ownerId}
                    isCancelInvoice={isCancelInvoice}
                    setIsCancelInvoice={setIsCancelInvoice}
                    showIdexxURL={showIdexxURL}
                    setShowIdexxURL={setShowIdexxURL}
                    setShowIdexxCreationModalText={
                        setShowIdexxCreationModalText
                    }
                    setShowIdexxCreation={setShowIdexxCreation}
                    idexxURL={idexxURL}
                    setIdexxURL={setIdexxURL}
                    {...planProps}
                    {...startInvoiceData}
                />
            ) : (
                <div
                    className={`grid items-start ${allInvoices.length > 0 ? 'grid-cols-[270px_1fr] h-full' : ''}`}
                >
                    <ShareMultipleDocumentsModal
                        isOpen={isShareModal}
                        onClose={() => {
                            setIsShareModal(false);
                        }}
                        handleCancel={() => {
                            setIsShareModal(false);
                        }}
                        handleShare={(data) => {
                            setIsLoadingState(true);
                            sendDocuments(data);
                        }}
                        title={`Share ${
                            currentSelectedInvoiceItem?.type === 'Invoice'
                                ? 'Invoice'
                                : currentSelectedInvoiceItem?.type ===
                                    'Credit Note'
                                  ? 'Credit Note'
                                  : 'Receipt'
                        } 
                               #${currentSelectedInvoiceItem?.referenceAlphaId || currentSelectedInvoiceItem?.receiptAlphaId || currentSelectedInvoiceItem?.id}`}
                        documentAvailability={{
                            invoices: true,
                        }}
                    />

                    <FileShareSuccessModal
                        isOpen={isFileShareSuccessModal}
                        onClose={() => {
                            setIsFileShareSuccessModal(false);
                        }}
                    />
                    <NotFoundModal
                        isOpen={openNotFoundModal}
                        onClose={() => {
                            setOpenNotFoundModal(false);
                        }}
                    />
                    {allInvoices.length > 0 ? (
                        <>
                            <div className="h-full border-r border-primary-100">
                                <DaySummaryList
                                    key={`day-summary-${allInvoices.length}-${JSON.stringify(allInvoices.map((inv: any) => `${inv.id}-${inv.status}-${inv.invoiceAmount}`).slice(0, 10))}`}
                                    {...(filteredCalender as any)}
                                    onSearchAppCalender={onSearchAppCalender}
                                    onClear={onClear}
                                    isStartInvoiceButton={true}
                                    setIsStartInvoice={setIsStartInvoice}
                                    activeDay={activeDay}
                                    latestAppointmentStatus={
                                        latestAppointmentStatus
                                    }
                                />
                            </div>

                            <div className="h-[calc(100vh-14.1rem)] overflow-auto">
                                {currentSelectedInvoiceItem ? (
                                    currentSelectedInvoiceItem.type ===
                                        'Invoice' &&
                                    currentSelectedInvoiceItem.source ===
                                        'invoice' ? (
                                        <InvoiceDetailsView
                                            patientpaymentdetails={true}
                                            invoice={transformedCurrentInvoice}
                                            cartOptions={cartOptions}
                                            ownerId={ownerId}
                                            currentMainBalance={
                                                currentMainBalance
                                            }
                                            ownerDetails={
                                                ownerDetailsForInvoiceView
                                            }
                                            showIdexxURL={showIdexxURL}
                                            setShowIdexxURL={setShowIdexxURL}
                                            idexxURL={idexxURL}
                                            setIdexxURL={setIdexxURL}
                                            showIdexxOrderCreation={
                                                showIdexxOrderCreation
                                            }
                                            setShowIdexxOrderCreation={
                                                setShowIdexxCreation
                                            }
                                            idexxCreationModalText={
                                                showIdexxCreationModalText
                                            }
                                            setIdexxCreationModalText={
                                                setShowIdexxCreationModalText
                                            }
                                            onIdexxComplete={() => {
                                                // Handle IDEXX completion if needed
                                                setShowIdexxURL(false);
                                                setIdexxURL('');
                                                setShowIdexxCreation(false);
                                            }}
                                            refetchInvoices={() => {}} // TODO: Implement refetch if needed
                                            onReconcileTriggered={() => {
                                                setIsShowReconcile(true);
                                            }}
                                        />
                                    ) : currentSelectedInvoiceItem.source ===
                                      'receipt' ? (
                                        <PaymentDetailsView
                                            payment={currentSelectedInvoiceItem}
                                            ownerId={ownerId}
                                        />
                                    ) : currentSelectedInvoiceItem.source ===
                                      'creditnote' ? (
                                        <RefundDetailsView
                                            invoice={
                                                transformedCurrentCreditNote
                                            }
                                            ownerId={ownerId}
                                        />
                                    ) : null
                                ) : (
                                    <EmptyState
                                        emptyTableMessage="No invoices"
                                        emptyStateHeight="h-[60vh]"
                                    />
                                )}
                            </div>
                        </>
                    ) : (
                        <EmptyState
                            emptyTableMessage="No invoices"
                            emptyStateHeight="h-[60vh]"
                            emptyTableButtonLabel="Create Invoice"
                            emptyTableButtonHandle={() =>
                                setIsStartInvoice(true)
                            }
                        />
                    )}
                    {isShowReconcile && (
                        <ModalReconcile
                            isOpen={{
                                invoice: currentSelectedInvoiceItem || {},
                                ownerName: `${ownerDetails?.ownerBrand?.firstName || ''} ${ownerDetails?.ownerBrand?.lastName || ''}`,
                                ownerPhone: `${ownerDetails?.ownerBrand?.globalOwner?.countryCode || ''} ${ownerDetails?.ownerBrand?.globalOwner?.phoneNumber || ''}`,
                                ownerBalance:
                                    ownerDetails?.ownerBrand?.ownerBalance ||
                                    '0',
                                ownerCredits:
                                    ownerDetails?.ownerBrand?.ownerCredits ||
                                    '0',
                                amountPayable:
                                    invoiceSummary.invoiceAmount.toString(),
                                balanceDue: Math.abs(
                                    invoiceSummary.balanceDue
                                ).toString(),
                                petName: patientName || '',
                                ownerId: ownerId,
                                patientId: patientId,
                                id: currentSelectedInvoiceItem?.id || '',
                            }}
                            onClose={() => {
                                setIsShowReconcile(false);
                            }}
                        />
                    )}
                    {isShowRefund && (
                        <ModalRefund
                            isOpen={isShowRefund}
                            modalTitle="Refund"
                            onClose={() => setIsShowRefund(false)}
                            refundCartItems={refundCartItems}
                            onQuantityChange={onQuantityChange}
                            onRefund={onRefundHandler}
                        />
                    )}
                    {isRefundAmountModalShown.isOpen && (
                        <ModalRefundAmountComponent
                            isOpen={isRefundAmountModalShown.isOpen}
                            onClose={() => {
                                setIsRefundAmountModalShown({
                                    isOpen: false,
                                });
                            }}
                            modalTitle="Refund Amount"
                            onConfirmRefund={() => {
                                setSelectedItemForRefund({
                                    ...selectedItemForRefund,
                                    paymentMode: (selectedItemForRefund as any)
                                        .paymentMode,
                                    paymentNotes: refundNotes,
                                });

                                debouncedActOnRefundCOnfirmClick();
                            }}
                            totalPayableAmount={
                                (selectedItemForRefund as any).amountPaid
                            }
                            onChangeAmount={(data: any) => {
                                setSelectedItemForRefund({
                                    ...selectedItemForRefund,
                                    amountPaid: data,
                                });
                            }}
                            onChangePaymentMode={(data: any) => {
                                setSelectedItemForRefund({
                                    ...selectedItemForRefund,
                                    paymentMode: data,
                                });
                            }}
                            onChangeNotes={(notes: any) =>
                                setRefundNotes(notes)
                            }
                            notes={refundNotes}
                            refundAmount={
                                (selectedItemForRefund as any).amountPayable
                            }
                            disabled={isRefundSubmitting}
                            confirmButtonLabel={
                                isRefundSubmitting ? 'Processing...' : 'Confirm'
                            }
                        />
                    )}
                    {/* {isShowRefundAmount && (
                <ModalRefundAmount
                    isOpen={isShowRefundAmount}
                    modalTitle="Refund"
                    onClose={() => setIsShowRefundAmount(false)}
                    onConfirmRefund={onConfirmRefundHandler}
                    onChangeAmount={onChangeAmount}
                    refundAmount={refundAmount}
                    totalPayableAmount={totalPayableAmount}
                />
            )} */}
                </div>
            )}
        </>
    );
};

export default PatientPaymentDetails;
