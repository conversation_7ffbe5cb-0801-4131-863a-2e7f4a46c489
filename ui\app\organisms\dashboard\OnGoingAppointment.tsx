import React from 'react';
import { Avatar, Heading, Tags, Text } from '@/app/atoms';
import Tooltip from '@/app/molecules/Tooltip';
import IconDots from '@/app/atoms/customIcons/IconDots.svg';
import DropdownMenu from '@/app/molecules/DropdownMenu';
import IconWarning from '@/app/atoms/customIcons/IconWarning.svg';
import { APPOINTMENT_TRIAGE_OPTIONS } from '@/app/utils/constant';
import { Activity } from 'lucide-react';
import { Clock } from 'iconsax-react';
import AppointmentInfo from '@/app/molecules/AppointmentInfo';
import { EnumAppointmentStatus } from '@/app/types/appointment';
import { Call } from 'iconsax-react';

import Image from 'next/image';
import {
    appointmentStatusDetails,
    getClockTime,
    getInitials,
    getStatusLabel,
} from '@/app/utils/common';
import TimerToolTip from '@/app/molecules/TimerToolTip';

interface AppointmentDetailsProps {
    type: string;
    reason: string;
    providers: string[];
    weight: string;
    room: string;
}

interface OnGoingAppointmentProps {
    handleMenu: (action: string, id: string) => void;
    scheduleData: {
        time: string;
        timeSpent: Date;
        status: string;
        statusLabel: string;
        profile?: string;
        name?: string;
        breed?: string;
        triage?: string;
        appointmentDetails: AppointmentDetailsProps;
    };
    id: string;
    menuList: { id: string; label: string }[];
    onStepHandler: (id: string) => void;
    className?: string;
    onAppointmentClick: (id: string) => void;
}

const PriorityIcon: React.FC<{ triage?: string }> = ({ triage }) => {
    if (!triage) return null;
    return (
        <Tooltip content={triage} position="top">
            <IconWarning
                size={24}
                className={`-mt-0.5 ml-1 ${APPOINTMENT_TRIAGE_OPTIONS.find((item) => item.name === triage)?.icon}`}
            />
        </Tooltip>
    );
};

const OnGoingAppointment: React.FC<OnGoingAppointmentProps> = ({
    handleMenu,
    scheduleData,
    id,
    menuList,
    onStepHandler,
    className,
    onAppointmentClick,
}) => {
    const {
        time,
        statusLabel,
        profile,
        name,
        breed,
        triage,
        appointmentDetails,
        timeSpent,
    } = scheduleData;

    const { type, reason, providers, weight, room } = appointmentDetails;

    return (
        <div
            className={`bg-white rounded-2xl p-4 w-full cursor-pointer ${className}`}
        >
            <div className="flex items-center justify-between pb-4 border-b border-primary-50">
                <div
                    className="flex items-center gap-3"
                    onClick={() => onAppointmentClick(id)}
                >
                    {profile ? (
                        <Avatar
                            alt={name}
                            imgSrc={profile}
                            imgWidth={48}
                            imgHeight={48}
                        />
                    ) : (
                        <div
                            className="w-10 h-10 rounded-full flex items-center justify-center text-white text-2xl font-bold"
                            style={{ backgroundColor: '#A29A78' }}
                        >
                            {getInitials(name!)}
                        </div>
                    )}
                    <div className="mr-10">
                        <div className="flex justify-center items-center flex-row space-x-2">
                            <Heading
                                type="h3"
                                fontSize="text-lg"
                                className="flex items-center mb-1"
                                textColor="text-black"
                                fontWeight="font-medium"
                            >
                                {name}
                            </Heading>

                            <div className="min-w-5 min-h-5 bg-secondary-300 rounded-full overflow-hidden flex justify-center items-center">
                                <Tooltip
                                    content={
                                        <>
                                            <Text variant="bodySmall">
                                                {scheduleData?.owner}
                                            </Text>
                                            <Text
                                                variant="bodySmall"
                                                fontWeight="font-semibold"
                                            >
                                                {' '}
                                                {scheduleData?.ownerNumber}
                                            </Text>
                                        </>
                                    }
                                    position="top"
                                >
                                    <Call size="12" color="#ffffff" />
                                </Tooltip>
                            </div>

                            <PriorityIcon triage={triage} />
                        </div>
                        <Text
                            variant="caption"
                            textColor="text-primary-700"
                            className="capitalize"
                        >
                            {breed}
                        </Text>
                    </div>
                </div>
                <Heading
                    type="h5"
                    fontWeight="font-semibold"
                    textColor="text-neutral-900"
                >
                    {time}
                </Heading>
                <div className="flex items-center gap-4">
                    <TimerToolTip
                        defaultTime={timeSpent}
                        content={<div></div>}
                        position="top"
                        requireBeepAnimation={true}
                        status={scheduleData.status}
                    >
                        <Clock size="16" variant="Bold" className="-mr-2" />
                    </TimerToolTip>
                    <Tags
                        className="font-medium"
                        size="normal"
                        variant="status"
                        label={getStatusLabel(statusLabel)}
                    />
                    {appointmentStatusDetails[statusLabel] && (
                        <Tooltip
                            content={
                                appointmentStatusDetails[statusLabel].tooltip
                            }
                            position="top"
                        >
                            <div className="h-8 w-8 cursor-pointer flex justify-center items-center rounded-full bg-primary-900 flex-shrink-0">
                                <Image
                                    src={
                                        appointmentStatusDetails[statusLabel]
                                            .imageUrl
                                    }
                                    alt="beign-treatment"
                                    height={16}
                                    width={16}
                                    id="beginTreatment"
                                    onClick={() => onStepHandler(id)}
                                />
                            </div>
                        </Tooltip>
                    )}
                    <DropdownMenu
                        minWidth="min-w-[120px]"
                        menuDirection="rightAuto"
                        onMenuClick={(action) => handleMenu(action, id)}
                        menuList={menuList}
                    >
                        <div className="h-8 w-8 rounded-full hover:bg-primary-100 hover:text-primary-800 flex justify-center items-center cursor-pointer">
                            <IconDots
                                className="text-neutral-900 hover:text-secondary-900"
                                size={24}
                            />
                        </div>
                    </DropdownMenu>
                </div>
            </div>

            <div className="flex flex-wrap justify-between gap-4 pt-4">
                <AppointmentInfo label="Type" value={type} />
                <AppointmentInfo label="Reason" value={reason} />
                <AppointmentInfo
                    label="Providers"
                    value={
                        <Tooltip
                            content={providers.join(', ')}
                            position="bottom"
                        >
                            <Text
                                variant="bodySmall"
                                textColor="text-primary-900"
                            >
                                {providers.length > 1
                                    ? `${providers[0]} +${providers.length - 1} more`
                                    : providers[0]}
                            </Text>
                        </Tooltip>
                    }
                />
                <AppointmentInfo label="Weight" value={weight} />
                <AppointmentInfo label="Room" value={room} />
            </div>
        </div>
    );
};

export default OnGoingAppointment;
