import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { ExceptionsSection } from '@/app/molecules/hours/ExceptionsSection';
import ExceptionsModal from './ExceptionsModal';
import GoogleCalendarConnection from './GoogleCalendarConnection';
import {
    useGetClinicUserData,
    useUpdateWorkingHours,
    useGetUserExceptions,
    useCreateException,
    useUpdateException,
    useDeleteException,
} from '@/app/services/user.queries';
import WorkingHours from '../admin/clinic-details/WorkingHours/WorkingHours';
import moment from 'moment';
import ConfirmModal from '@/app/organisms/patient/ConfirmModal';
import { Tags } from '@/app/atoms';

// TimeSlot for working hours
type TimeSlot = {
    startTime: string;
    endTime: string;
    isWorkingDay: boolean;
};

// Define the ExceptionData type to match what ExceptionsModal expects
type ExceptionData = {
    id: string;
    startDate: string;
    type: string;
    endDate: string | null;
    isFullDay: boolean;
    times: { startTime: string; endTime: string; isWorkingDay?: boolean }[];
};

type WorkingHoursType = {
    [key: string]: TimeSlot[];
};

// Define form data type to avoid type errors
interface FormData {
    workingHours: WorkingHoursType;
    selectedDays: string[];
}

interface HoursTabProps {
    clinicUserId?: string;
}

// Define the ExceptionData type to match what ExceptionsSection expects
interface ExceptionSectionSlot {
    id: string;
    startDate: string;
    endDate: string | null;
    isFullDay: boolean;
    type: string;
    times?: { start: string; end: string }[];
}

const HoursTab: React.FC<HoursTabProps> = ({ clinicUserId }) => {
    // Days of the week
    const daysOfWeek = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
    ];

    // Default working hours as a fallback
    const defaultWorkingHours: WorkingHoursType = {
        monday: [{ startTime: '00:00', endTime: '23:59', isWorkingDay: true }],
        tuesday: [{ startTime: '00:00', endTime: '23:59', isWorkingDay: true }],
        wednesday: [
            { startTime: '00:00', endTime: '23:59', isWorkingDay: true },
        ],
        thursday: [
            { startTime: '00:00', endTime: '23:59', isWorkingDay: true },
        ],
        friday: [{ startTime: '00:00', endTime: '23:59', isWorkingDay: true }],
        saturday: [
            { startTime: '00:00', endTime: '23:59', isWorkingDay: true },
        ],
        sunday: [{ startTime: '00:00', endTime: '23:59', isWorkingDay: true }],
    };

    // Fetch clinic user data including working hours
    const { data: clinicUserData, isLoading } = useGetClinicUserData(
        clinicUserId || ''
    );
    const updateWorkingHoursMutation = useUpdateWorkingHours();

    // Fetch the exceptions for this user
    const {
        data: exceptionsData,
        isLoading: isLoadingExceptions,
        error: exceptionsError,
        refetch: refetchExceptions,
    } = useGetUserExceptions(
        clinicUserId || '',
        false // include history
    );

    // Exception API mutations
    const createExceptionMutation = useCreateException();
    const updateExceptionMutation = useUpdateException();
    const deleteExceptionMutation = useDeleteException();

    // State management
    const [isExceptionsModalOpen, setIsExceptionsModalOpen] = useState(false);
    const [activeTab, setActiveTab] = useState('out-of-office');
    const [editingExceptionId, setEditingExceptionId] = useState('');
    const [apiError, setApiError] = useState<string | null>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [deleteExceptionId, setDeleteExceptionId] = useState('');
    const [deleteError, setDeleteError] = useState<string | null>(null);

    // Form handling with react-hook-form
    const { watch, setValue } = useForm<FormData>({
        defaultValues: {
            workingHours: defaultWorkingHours,
            selectedDays: daysOfWeek.filter(
                (day) => defaultWorkingHours[day]?.[0]?.isWorkingDay
            ),
        },
    });

    // Update form when clinic user data is fetched
    useEffect(() => {
        if (clinicUserData?.data?.workingHours?.workingHours) {
            const userWorkingHours =
                clinicUserData.data.workingHours.workingHours;

            // Format working hours to match our component's format
            const formattedWorkingHours: WorkingHoursType = {};
            Object.entries(userWorkingHours).forEach(([day, hours]) => {
                if (Array.isArray(hours)) {
                    formattedWorkingHours[day] = hours.map((slot) => ({
                        startTime: slot.startTime || '',
                        endTime: slot.endTime || '',
                        isWorkingDay: !!slot.isWorkingDay,
                    }));
                }
            });

            // Update form values
            setValue('workingHours', formattedWorkingHours);

            // Update selected days based on the fetched data
            const selectedDays = daysOfWeek.filter(
                (day) => formattedWorkingHours[day]?.[0]?.isWorkingDay
            );
            setValue('selectedDays', selectedDays);
        }
    }, [clinicUserData, setValue]);

    // Clear error when modal is closed intentionally
    useEffect(() => {
        if (!isExceptionsModalOpen) {
            setApiError(null);
        }
    }, [isExceptionsModalOpen]);

    const workingHours = watch('workingHours');
    const selectedDays = watch('selectedDays');

    // Menu list for exception actions
    const menuList = [
        { id: 'edit', label: 'Edit' },
        { id: 'delete', label: 'Delete' },
    ];

    // Convert API exceptions to the format expected by ExceptionsSection
    const convertToExceptionSectionSlots = (
        apiExceptions: any[]
    ): ExceptionSectionSlot[] => {
        if (!apiExceptions || !Array.isArray(apiExceptions)) return [];

        return apiExceptions.map((exception) => ({
            id: exception.id,
            startDate: exception.startDate,
            endDate: exception.endDate,
            isFullDay: exception.isFullDay,
            type: exception.type,
            times: exception.times?.map((time: any) => ({
                start: time.startTime,
                end: time.endTime,
            })),
        }));
    };

    // Convert API exceptions to ExceptionData for ExceptionsModal
    const convertToExceptionData = (apiExceptions: any[]): ExceptionData[] => {
        if (!apiExceptions || !Array.isArray(apiExceptions)) return [];

        return apiExceptions.map((exception) => ({
            id: exception.id,
            startDate: exception.startDate,
            endDate: exception.endDate,
            isFullDay: exception.isFullDay,
            type: exception.type,
            times:
                exception.times?.map((time: any) => ({
                    startTime: time.startTime,
                    endTime: time.endTime,
                    start: time.startTime,
                    end: time.endTime,
                    isWorkingDay: true,
                })) || [],
        }));
    };

    // Convert UI format to API format for form submission
    const formatTimeForApi = (timeString: string): string => {
        // Log the input to debug
        console.log('formatTimeForApi input:', timeString);

        // Handle special 'full-day' value
        if (timeString === 'full-day') {
            return '00:00';
        }

        // Handle invalid inputs
        if (!timeString || timeString === 'Invalid date') {
            console.log('Invalid time detected, using default 09:00');
            return '09:00'; // Return a default time instead of empty string
        }

        // If already in 24h format (HH:MM), return as is
        if (/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeString)) {
            // Ensure proper 2-digit format
            const [hours, minutes] = timeString.split(':').map(Number);
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        try {
            // Convert from "9:30 AM" format to "09:30" format
            return moment(timeString, 'h:mm A').format('HH:mm');
        } catch (error) {
            console.error('Error formatting time:', error);
            return '09:00'; // Return default time instead of empty string on error
        }
    };

    // Format exception data for API submission
    const formatExceptionForApi = (exceptionData: any) => {
        // Format times while handling full day exceptions
        let formattedTimes = [];

        if (exceptionData.isFullDay) {
            // Full day exceptions cover the entire day
            formattedTimes = [
                {
                    startTime: '00:00',
                    endTime: '23:59',
                },
            ];
        } else {
            // Format time slots for partial day exceptions
            formattedTimes =
                exceptionData.times?.map((time: any) => ({
                    startTime: formatTimeForApi(
                        time.startTime || time.start || '09:00'
                    ),
                    endTime: formatTimeForApi(
                        time.endTime || time.end || '17:00'
                    ),
                })) || [];
        }

        // Format dates and prepare final data
        return {
            ...exceptionData,
            clinicUserId: clinicUserId,
            startDate: moment(exceptionData.startDate).format('YYYY-MM-DD'),
            endDate: exceptionData.endDate
                ? moment(exceptionData.endDate).format('YYYY-MM-DD')
                : null,
            times: formattedTimes,
        };
    };

    // Handle exception actions (edit, delete)
    const handleActionClick = (action: string, id: string) => {
        if (action === 'edit') {
            setEditingExceptionId(id);
            setIsExceptionsModalOpen(true);
        } else if (action === 'delete') {
            setDeleteExceptionId(id);
            setIsDeleteModalOpen(true);
        }
    };

    // Handle delete confirmation
    const handleDeleteConfirm = () => {
        if (deleteExceptionId) {
            // Clear any previous error
            setDeleteError(null);
            console.log('Deleting exception with ID:', deleteExceptionId);

            try {
                deleteExceptionMutation.mutate(deleteExceptionId, {
                    onSuccess: (data) => {
                        console.log('Delete success with data:', data);

                        // Check if data contains error (our API can return errors with status:false)
                        if (data && !data.status) {
                            console.log('API returned error data:', data);
                            // Use the actual error message from the API
                            const errorMessage =
                                data.errorMessage ||
                                data.message ||
                                'Failed to delete the exception';
                            console.log(
                                'Setting error from success callback:',
                                errorMessage
                            );
                            setDeleteError(errorMessage);
                            return;
                        }

                        // Success path
                        setDeleteError(null);
                        setIsDeleteModalOpen(false);
                        refetchExceptions();
                    },
                    onError: (error: any) => {
                        console.log('Delete error:', error);
                        // Extract error message from the API response
                        let errorMessage = 'Failed to delete the exception';

                        if (error.message) {
                            errorMessage = error.message;
                        }

                        // Check for nested error details
                        if (error.rawError && error.rawError.errors) {
                            errorMessage = error.rawError.errors;
                        } else if (error.rawError && error.rawError.message) {
                            errorMessage = error.rawError.message;
                        }

                        setDeleteError(errorMessage);
                    },
                });
            } catch (error) {
                console.error('Exception during delete mutation:', error);
                setDeleteError(
                    'An unexpected error occurred. Please try again.'
                );
            }
        }
    };

    // Handle exception form submission
    const handleExceptionSubmit = (data: any) => {
        // Extract form data
        const exceptionData = data.exceptions[0];

        // Format the data for API submission
        let formattedException = formatExceptionForApi(exceptionData);

        // If editing an existing exception
        if (editingExceptionId) {
            updateExceptionMutation.mutate(
                { id: editingExceptionId, data: formattedException },
                {
                    onSuccess: () => {
                        handleModalClose(); // Use the handleModalClose function here
                        refetchExceptions(); // Use the existing refetch function
                    },
                    onError: (error: any) => {
                        console.log('Full error object:', error);
                        console.error(
                            'Error updating exception:',
                            error?.errorMessage
                        );

                        // Extract specific error message from various possible sources
                        // Prioritize the 'errors' field which contains the detailed error message
                        const apiErrorMessage =
                            error?.rawError?.errors ||
                            error?.rawError?.message ||
                            error?.errorMessage ||
                            error?.message ||
                            'Failed to update exception. Please try again.';

                        setApiError(apiErrorMessage); // Set specific error message
                        // Don't close modal on error
                    },
                }
            );
        } else {
            // If creating a new exception
            createExceptionMutation.mutate(formattedException, {
                onSuccess: () => {
                    handleModalClose(); // Use the handleModalClose function here
                    refetchExceptions(); // Use the existing refetch function
                },
                onError: (error: any) => {
                    console.log('Full error object (create):', error);
                    console.error(
                        'Error creating exception:',
                        error?.errorMessage
                    );

                    // Extract specific error message from various possible sources
                    // Prioritize the 'errors' field which contains the detailed error message
                    const apiErrorMessage =
                        error?.rawError?.errors ||
                        error?.rawError?.message ||
                        error?.errorMessage ||
                        error?.message ||
                        'Failed to create exception. Please try again.';

                    setApiError(apiErrorMessage); // Set specific error message
                    // Don't close modal on error
                },
            });
        }
    };

    // Add a proper close handler that clears errors
    const handleModalClose = () => {
        setIsExceptionsModalOpen(false);
        setEditingExceptionId('');
        setApiError(null); // Clear any API errors when closing the modal
    };

    // Get exceptions for the current active tab
    const getFilteredExceptions = () => {
        if (!exceptionsData?.data) return [];

        return exceptionsData.data.filter(
            (exception: any) => exception.type === activeTab
        );
    };

    if (isLoading && !clinicUserId) {
        return (
            <div className="flex justify-center items-center h-full">
                Loading working hours...
            </div>
        );
    }

    if (isLoadingExceptions) {
        return (
            <div className="flex justify-center items-center h-full">
                Loading exceptions...
            </div>
        );
    }

    return (
        <div className="w-full h-full bg-white">
            <div className="grid grid-cols-2 gap-x-6 h-full mx-4">
                {/* Scheduled Work Hours Section */}
                <div className="mt-3">
                    <WorkingHours
                        title="Scheduled Work Hours"
                        daysOfWeek={daysOfWeek}
                        workingHours={workingHours}
                        onUpdate={(updatedHours) => {
                            // Format the updated hours to match the API structure
                            const formattedHours = {
                                workingHours: updatedHours,
                            };

                            if (clinicUserId) {
                                updateWorkingHoursMutation.mutate(
                                    {
                                        userId: clinicUserId,
                                        workingHours: formattedHours,
                                    },
                                    {
                                        onSuccess: () => {
                                            console.log(
                                                'Working hours updated successfully'
                                            );
                                        },
                                        onError: (error) => {
                                            console.error(
                                                'Error updating working hours:',
                                                error
                                            );
                                            setApiError(
                                                'Failed to update working hours'
                                            );
                                        },
                                    }
                                );
                            }
                        }}
                    />

                    <GoogleCalendarConnection clinicUserId={clinicUserId} />
                </div>

                {/* Exceptions Section */}
                <div>
                    <ExceptionsSection
                        menuList={menuList}
                        handleActionClick={handleActionClick}
                        onEdit={() => {
                            setEditingExceptionId('');
                            setIsExceptionsModalOpen(true);
                        }}
                        activeTab={activeTab}
                        setActiveTab={setActiveTab}
                        Slots={convertToExceptionSectionSlots(
                            getFilteredExceptions()
                        )}
                    />

                    <ExceptionsModal
                        isOpen={isExceptionsModalOpen}
                        onClose={handleModalClose}
                        activeTab={activeTab}
                        setActiveTab={setActiveTab}
                        editingExceptionId={editingExceptionId}
                        Slots={convertToExceptionData(
                            exceptionsData?.data || []
                        )}
                        onSubmit={handleExceptionSubmit}
                        apiError={apiError}
                    />

                    {/* Delete Confirmation Modal */}
                    <ConfirmModal
                        isOpen={isDeleteModalOpen}
                        onClose={() => {
                            setIsDeleteModalOpen(false);
                            setDeleteError(null);
                        }}
                        modalTitle="Delete Exception"
                        modalDescription={
                            'Are you sure you want to delete this exception? This action cannot be undone.'
                        }
                        alertType={deleteError ? 'warning' : 'delete'}
                        dataAutomation="delete-exception-modal"
                        primaryBtnProps={{
                            label: deleteError ? 'Ok' : 'Delete',
                            onClick: deleteError
                                ? () => {
                                      console.log(
                                          'Acknowledging error:',
                                          deleteError
                                      );
                                      setIsDeleteModalOpen(false);
                                      setDeleteError(null);
                                  }
                                : handleDeleteConfirm,
                            dataAutomation: deleteError
                                ? 'acknowledge-error'
                                : 'confirm-delete-exception',
                        }}
                        secondaryBtnProps={{
                            label: 'Cancel',
                            onClick: () => {
                                console.log('Canceling delete operation');
                                setIsDeleteModalOpen(false);
                                setDeleteError(null);
                            },
                            dataAutomation: 'cancel-delete-exception',
                        }}
                        primaryBtnDisabled={false}
                    >
                        {deleteError && (
                            <div className="px-4 pt-4 mb-4">
                                <Tags
                                    variant="error"
                                    isLight={true}
                                    size="small"
                                    className="text-[9px] p-1 w-full mt-2 whitespace-pre-line text-center"
                                    label={deleteError}
                                />
                            </div>
                        )}
                    </ConfirmModal>
                </div>
            </div>
        </div>
    );
};

export default HoursTab;
