import React, { useState, useEffect } from 'react';
import Text from '@/app/atoms/Text';
import RenderFields, { fieldsType } from '@/app/molecules/RenderFields';
import NextImage from 'next/image';
import {
    UseFormReturn,
    UseFormSetValue,
    UseFormGetValues,
    UseFormRegister,
    UseFormWatch,
    Control,
    UseFormSetError,
} from 'react-hook-form';
import { parsePhoneNumber } from 'react-phone-number-input';
import { Button, Alert } from '@/app/atoms';
import { Add, ArrowRight2, Repeat } from 'iconsax-react';
import TransferOwnershipModal from './TransferOwnershipModal';
import { searchPatientByPhone } from '@/app/services/patient.service';
import { uniqueId } from 'lodash';
import ConfirmModal from './ConfirmModal';
import {
    useGetOwnerPatients,
    useCreateOwner,
    useUpdateOwner,
    useRemoveOwnerFromPatient,
} from '@/app/services/owner.queries';
import { Tooltip } from '@/app/molecules';
import Tags from '@/app/atoms/Tags';

interface EditOwnerInfoProps {
    control: Control<any>;
    errors: any;
    setValue: UseFormSetValue<any>;
    setError: UseFormSetError<any>;
    getValues: UseFormGetValues<any>;
    register: UseFormRegister<any>;
    watch: UseFormWatch<any>;
    index: number;
    showSecondOwnersInfo: boolean;
    handleDeleteOwnersInfo: () => void;
    patientId: string | null;
    onClose: () => void;
    showPatientDetailsButton?: boolean;
    ownerId?: string; // Add ownerId prop for when editing from owner list
}

const EditOwnerInfo: React.FC<EditOwnerInfoProps> = ({
    control,
    errors,
    setValue,
    setError,
    getValues,
    register,
    watch,
    handleDeleteOwnersInfo,
    patientId,
    showSecondOwnersInfo,
    index = 0,
    onClose,
    showPatientDetailsButton = false,
}) => {
    const [transferOwnershipModal, setTransferOwnershipModal] = useState(false);
    const [transferPets, setTransferPets] = useState<
        Array<{ value: string; label: string }>
    >([]);
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');
    const [alertVariant, setAlertVariant] = useState<'success' | 'error'>(
        'success'
    );
    const [primaryOwnerPhone, setPrimaryOwnerPhone] = useState('');
    const [secondaryOwnerPhone, setSecondaryOwnerPhone] = useState('');
    const [secondaryOwnerData, setSecondaryOwnerData] = useState<{
        id?: string;
        firstName: string;
        lastName: string;
        email: string | null;
        address: string | null;
    } | null>(null);

    const commonPrefix = `patientOwners[${index}]`;
    const formData = getValues(commonPrefix);
    const patientOwners = getValues('patientOwners') || [];
    // Use the hook at component level
    const { data: ownerPetsData } = useGetOwnerPatients(patientOwners[0].id);

    // Use useEffect to update transferPets when ownerPetsData changes
    useEffect(() => {
        if (ownerPetsData?.data?.patients) {
            const petsData = ownerPetsData.data.patients.map((pet: any) => ({
                value: pet.id,
                label: pet.patientName,
            }));
            setTransferPets(petsData);
        }
    }, [ownerPetsData]);

    // Use useEffect to set owner phone numbers and secondary owner data when component mounts
    useEffect(() => {
        const patientOwners = getValues('patientOwners') || [];

        // Set primary owner phone
        if (patientOwners[0]) {
            const primaryCode = patientOwners[0].countryCode;
            const primaryPhone = patientOwners[0].phoneNumber;
            if (primaryCode && primaryPhone) {
                setPrimaryOwnerPhone(`+${primaryCode}${primaryPhone}`);
            }
        }

        // Set secondary owner phone and data if exists
        if (patientOwners[1]) {
            const secondaryCode = patientOwners[1].countryCode;
            const secondaryPhone = patientOwners[1].phoneNumber;
            if (secondaryCode && secondaryPhone) {
                setSecondaryOwnerPhone(`+${secondaryCode}${secondaryPhone}`);
                setSecondaryOwnerData({
                    id: patientOwners[1].id,
                    firstName: patientOwners[1].firstName,
                    lastName: patientOwners[1].lastName,
                    email: patientOwners[1].email,
                    address: patientOwners[1].address,
                });
            }
        }
    }, []);

    const getErrorMessage = (fieldName: string) => {
        if (
            errors.patientOwners &&
            errors.patientOwners &&
            errors.patientOwners[index] &&
            errors.patientOwners[index][fieldName]
        ) {
            return errors?.patientOwners[index][fieldName]?.message;
        }
        return undefined;
    };
    const fields: fieldsType[] = [
        {
            id: `${commonPrefix}.firstName`,
            name: `${commonPrefix}.firstName`,
            label: 'Owner first name',
            placeholder: 'Enter first name',
            defaultValue: formData?.firstName || '',
            value: getValues(`${commonPrefix}.firstName`),
            onChange: (e) =>
                setValue(`${commonPrefix}.firstName`, e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('firstName'),
        },
        {
            id: `${commonPrefix}.lastName`,
            name: `${commonPrefix}.lastName`,
            label: 'Owner last name',
            placeholder: 'Enter last name',
            defaultValue: formData?.lastName || '',
            value: getValues(`${commonPrefix}.lastName`),
            onChange: (e) =>
                setValue(`${commonPrefix}.lastName`, e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('lastName'),
        },
        {
            id: `${commonPrefix}.phoneNumber`,
            name: `${commonPrefix}.phoneNumber`,
            label: 'Phone number',
            placeholder: 'Enter phone number',
            defaultValue:
                `+${formData?.countryCode}${formData?.phoneNumber}` || '',
            value: `+${getValues(`${commonPrefix}.countryCode`)}${getValues(`${commonPrefix}.phoneNumber`)}`,
            onChange: (e) => {
                try {
                    const phoneNumberDetails = parsePhoneNumber(e);
                    if (phoneNumberDetails) {
                        // Check if this is a secondary owner and the phone number matches primary owner
                        if (index > 0) {
                            const primaryOwner = getValues('patientOwners')[0];
                            const primaryPhone = parsePhoneNumber(
                                `+${primaryOwner.countryCode}${primaryOwner.phoneNumber}`
                            )?.nationalNumber;
                            const currentPhone =
                                phoneNumberDetails.nationalNumber;

                            if (primaryPhone === currentPhone) {
                                setError(`${commonPrefix}.phoneNumber`, {
                                    message:
                                        "Phone number must be different from primary owner's",
                                });
                                return;
                            }
                        }

                        setValue(
                            `${commonPrefix}.countryCode`,
                            phoneNumberDetails.countryCallingCode,
                            {
                                shouldValidate: true,
                            }
                        );
                        setValue(
                            `${commonPrefix}.phoneNumber`,
                            phoneNumberDetails.nationalNumber,
                            {
                                shouldValidate: true,
                            }
                        );
                    }
                } catch (error) {
                    // Clear values if parsing fails
                    setValue(`${commonPrefix}.countryCode`, '', {
                        shouldValidate: true,
                    });
                    setValue(`${commonPrefix}.phoneNumber`, '', {
                        shouldValidate: true,
                    });
                }
            },
            type: 'number-with-country',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('phoneNumber'),
        },
        {
            id: `${commonPrefix}.email`,
            name: `${commonPrefix}.email`,
            label: 'E-mail',
            placeholder: 'Enter an email',
            defaultValue: formData?.email || '',
            value: getValues(`${commonPrefix}.email`),
            onChange: (e) =>
                setValue(`${commonPrefix}.email`, e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            errorMessage: getErrorMessage('email'),
        },
        {
            id: `${commonPrefix}.address`,
            name: `${commonPrefix}.address`,
            label: `Address`,
            placeholder: 'Enter address',
            type: 'text-input',
            defaultValue: formData?.address || '',
            value: getValues(`${commonPrefix}.address`),
            onChange: (e) =>
                setValue(`${commonPrefix}.address`, e.target?.value, {
                    shouldValidate: true,
                }),
            fieldSize: 'medium',
            column: 'col-span-2',
        },
    ];

    // Add create owner mutation
    const createOwnerMutation = useCreateOwner();

    // Function for adding secondary owner
    const handleAddSecondaryOwner = () => {
        const patientOwners = getValues('patientOwners') || [];
        const firstOwner = patientOwners[0];

        // Initialize empty secondary owner
        const secondaryOwner = {
            id: '', // This will be assigned when updating
            firstName: '',
            lastName: '',
            countryCode: firstOwner?.countryCode || '',
            phoneNumber: '',
            email: '',
            address: '',
            isPrimary: false, // Secondary owner is never primary
        };

        // Add to owners array
        const ownersData = [...patientOwners, secondaryOwner];
        setValue('patientOwners', ownersData, { shouldValidate: true });
    };

    // Add the owner update mutation hook
    const updateOwnerMutation = useUpdateOwner();

    // Add the remove owner mutation
    const removeOwnerMutation = useRemoveOwnerFromPatient();

    // Modified handleUpdate to handle owner updates
    const handleUpdate = async () => {
        // Validate phone number before updating
        if (!validatePhoneNumber()) {
            return;
        }

        try {
            // Get all current owners data
            const patientOwners = getValues('patientOwners') || [];

            // Update each owner
            for (const [idx, owner] of patientOwners.entries()) {
                const ownerData = {
                    firstName: getValues(`patientOwners[${idx}].firstName`),
                    lastName: getValues(`patientOwners[${idx}].lastName`),
                    phoneNumber: getValues(`patientOwners[${idx}].phoneNumber`),
                    countryCode: getValues(`patientOwners[${idx}].countryCode`),
                    email: getValues(`patientOwners[${idx}].email`),
                    address: getValues(`patientOwners[${idx}].address`),
                    isPrimary: idx === 0, // First owner is primary
                    ...(patientId && { patientId }), // Include patientId only if it exists
                };

                try {
                    let result;
                    if (!owner.id) {
                        // First search for existing owner by phone number
                        const searchResult = await searchPatientByPhone(
                            ownerData.phoneNumber
                        );

                        if (searchResult?.data?.owner?.id) {
                            // Owner exists, use their ID
                            result = await updateOwnerMutation.mutateAsync({
                                id: searchResult.data.owner.id,
                                ...ownerData,
                            });

                            // Update the owner's ID in the form state
                            const updatedPatientOwners = [...patientOwners];
                            updatedPatientOwners[idx] = {
                                ...updatedPatientOwners[idx],
                                id: searchResult.data.owner.id,
                            };
                            setValue('patientOwners', updatedPatientOwners);
                        } else {
                            // Create new owner if not found
                            result =
                                await createOwnerMutation.mutateAsync(
                                    ownerData
                                );

                            // Check for errors in creation result
                            if (result.data?.error) {
                                throw new Error(result.data.error);
                            }

                            const responseData = result as any;
                            let ownerId = responseData.data.id;
                            // Check if owner was created successfully and we have an ID
                            if (ownerId) {
                                // Update the owner's ID in the form state
                                const updatedPatientOwners = [...patientOwners];
                                updatedPatientOwners[idx] = {
                                    ...updatedPatientOwners[idx],
                                    id: ownerId,
                                };
                                setValue('patientOwners', updatedPatientOwners);

                                // Make a second call to update the owner to ensure relationship is properly established
                                // This is crucial for secondary owners
                                const updateResult =
                                    await updateOwnerMutation.mutateAsync({
                                        id: ownerId,
                                        ...ownerData,
                                    });

                                // Use the update result for subsequent error handling
                                result = updateResult;
                            } else {
                                throw new Error(
                                    'Could not extract brand owner ID from the response'
                                );
                            }
                        }

                        if (
                            result.data?.error ||
                            result.statusCode === 409 ||
                            result.errorMessage
                        ) {
                            // If there's a conflict with phone number
                            if (
                                result.statusCode === 409 ||
                                result.data?.statusCode === 409
                            ) {
                                setAlertVariant('error');
                                // Access nested error message from the response
                                const errorMsg =
                                    result.rawError?.errors ||
                                    result.errorMessage ||
                                    result.data?.error ||
                                    'Phone number is already in use';
                                setAlertMessage(errorMsg);
                                setShowAlert(true);
                                return;
                            }
                            throw new Error(
                                result.data?.error ||
                                    result.errorMessage ||
                                    'Update failed'
                            );
                        }
                    } else {
                        // Update existing owner
                        result = await updateOwnerMutation.mutateAsync({
                            id: owner.id,
                            ...ownerData,
                        });
                    }
                    if (
                        result.data?.error ||
                        result.statusCode === 409 ||
                        result.errorMessage
                    ) {
                        // If there's a conflict with phone number
                        if (
                            result.statusCode === 409 ||
                            result.data?.statusCode === 409
                        ) {
                            setAlertVariant('error');
                            // Access nested error message from the response
                            const errorMsg =
                                result.rawError?.errors ||
                                result.errorMessage ||
                                result.data?.error ||
                                'Phone number is already in use';
                            setAlertMessage(errorMsg);
                            setShowAlert(true);
                            return;
                        }
                        throw new Error(
                            result.data?.error ||
                                result.errorMessage ||
                                'Update failed'
                        );
                    }
                } catch (error: any) {
                    setAlertVariant('error');
                    // Handle axios error response and conflict cases
                    if (
                        error.response?.status === 409 ||
                        error.statusCode === 409
                    ) {
                        const errorMsg =
                            error.rawError?.errors ||
                            error.response?.data?.errors ||
                            error.response?.data?.message ||
                            error.errorMessage ||
                            'This number is already linked to another user. Transfer the Patient to them?';
                        setAlertMessage(errorMsg);
                    } else if (error.response?.data) {
                        setAlertMessage(
                            error.response.data.errors ||
                                error.response.data.message ||
                                'Failed to update owner'
                        );
                    } else {
                        setAlertMessage(
                            error.message || 'Failed to update owner'
                        );
                    }
                    setShowAlert(true);
                    return;
                }
            }

            // Update initial phone number after successful update
            setPrimaryOwnerPhone(
                `+${getValues(`${commonPrefix}.countryCode`)}${getValues(`${commonPrefix}.phoneNumber`)}`
            );

            setAlertVariant('success');
            setAlertMessage('Owner details updated successfully');
            setShowAlert(true);
            setTimeout(() => {
                setShowAlert(false);
                if (onClose) onClose();
            }, 2000);
        } catch (error: any) {
            console.error('Error updating owners:', error);
            setAlertVariant('error');
            setAlertMessage(error.message || 'Failed to update owners');
            setShowAlert(true);
        }
    };

    // Fix the validatePhoneNumber function to handle undefined and add primary/secondary validation
    const validatePhoneNumber = () => {
        const phoneNumber = getValues(`${commonPrefix}.phoneNumber`);
        if (!phoneNumber) {
            setError(`${commonPrefix}.phoneNumber`, {
                message: 'Phone number is required',
            });
            return false;
        }

        // First check for duplicate phone number with primary owner for secondary owners
        if (index > 0) {
            const primaryOwner = getValues('patientOwners')[0];
            if (primaryOwner) {
                try {
                    const primaryPhone = parsePhoneNumber(
                        `+${primaryOwner.countryCode}${primaryOwner.phoneNumber}`
                    )?.nationalNumber;
                    const currentPhone = parsePhoneNumber(
                        `+${getValues(`${commonPrefix}.countryCode`)}${phoneNumber}`
                    )?.nationalNumber;

                    if (primaryPhone === currentPhone) {
                        setError(`${commonPrefix}.phoneNumber`, {
                            message:
                                "Phone number must be different from primary owner's",
                        });
                        return false;
                    }
                } catch (error) {
                    // If parsing fails, continue to other validations
                    console.error('Error parsing phone numbers:', error);
                }
            }
        }

        if (
            phoneNumber.startsWith('+') &&
            phoneNumber.substring(1) ===
                getValues(`${commonPrefix}.countryCode`)
        ) {
            setError(`${commonPrefix}.phoneNumber`, {
                message: 'Phone number is required',
            });
            return false;
        }

        const parsedNumber = parsePhoneNumber(
            `+${getValues(`${commonPrefix}.countryCode`)}${phoneNumber}`
        );
        if (
            !parsedNumber?.nationalNumber ||
            parsedNumber.nationalNumber.length < 10
        ) {
            // Only set length error if we don't already have a duplicate error
            if (
                !errors?.patientOwners?.[index]?.phoneNumber?.message?.includes(
                    'different from primary owner'
                )
            ) {
                setError(`${commonPrefix}.phoneNumber`, {
                    message: 'Phone number must be at least 10 digits',
                });
            }
            return false;
        }

        return true;
    };

    const handleTransferPhoneChange = async (phone: string) => {
        try {
            const searchData = await searchPatientByPhone(phone);
            return searchData?.data;
        } catch (error) {
            console.error('Error searching for pets:', error);
        }
    };

    // Handle owner deletion
    const handleDeleteOwner = async () => {
        const owner = getValues(`patientOwners[${index}]`);
        if (!owner.id) {
            // If owner hasn't been created yet, just remove from form
            handleDeleteOwnersInfo();
            return;
        }

        // If we don't have a patientId (editing from owner list), we can't remove owner from patient
        if (!patientId) {
            setAlertVariant('error');
            setAlertMessage(
                'Cannot delete owner from this context. Please edit from patient details.'
            );
            setShowAlert(true);
            return;
        }

        try {
            const result = await removeOwnerMutation.mutateAsync({
                ownerId: owner.id,
                patientId,
            });

            if (result.data?.message) {
                setAlertVariant('success');
                setAlertMessage('Owner removed successfully');
                setShowAlert(true);
                handleDeleteOwnersInfo(); // Remove from form after successful deletion
                setTimeout(() => {
                    setShowAlert(false);
                }, 2000);
            }
        } catch (error: any) {
            console.error('Error removing owner:', error);
            setAlertVariant('error');
            setAlertMessage(
                error.response?.data?.message || 'Failed to remove owner'
            );
            setShowAlert(true);
        }
    };

    return (
        <div className="space-y-4">
            {/* Patient Details Button */}
            {showPatientDetailsButton && index === 0 && (
                <Tags
                    size="small"
                    shape="rounded"
                    isLight
                    variant="neutral"
                    label="Patient Details"
                    iconPosition="left"
                    iconSrc="/images/icons/arrow-left.svg"
                    iconHeight={8}
                    iconWidth={8}
                    className="hover:bg-neutral-100 cursor-pointer transition-colors mb-4"
                    onClick={onClose}
                />
            )}

            <div className="w-full mt-0">
                {/* Add section divider for secondary owner */}
                {index > 0 && (
                    <div className="flex justify-between items-center mb-6">
                        <Text variant="bodySmall" className="text-gray-500">
                            Add alternate contact information
                        </Text>
                        <NextImage
                            src="/images/icons/delete.svg"
                            alt="delete"
                            width={24}
                            height={24}
                            onClick={handleDeleteOwner}
                            className="cursor-pointer"
                            data-automation="delete-owner"
                        />
                    </div>
                )}

                <RenderFields
                    control={control}
                    errors={errors}
                    fields={fields}
                    setValue={setValue}
                    register={register}
                    watch={watch}
                    grid="grid-cols-2 gap-y-6"
                />

                {/* <div className="flex flex-col gap-4 mt-6">
                   
                    {index === 0 && getValues('patientOwners')?.length === 1 && (
                        <div className="flex justify-start items-center">
                            <Button
                                id="add-secondary-owner"
                                onClick={handleAddSecondaryOwner}
                                size="small"
                                variant="link"
                                iconPosition="left"
                                icon={<Add size={20} />}
                            >
                                Add alternate contact information
                            </Button>
                        </div>
                    )}
                </div> */}
            </div>

            {/* Only show Transfer Ownership and action buttons for the last owner section */}
            {index === getValues('patientOwners')?.length - 1 && (
                <>
                    <hr />
                    {/* Transfer Ownership Button */}
                    <div className="flex justify-start items-center mt-6">
                        <Button
                            id="transfer-ownership"
                            onClick={() => setTransferOwnershipModal(true)}
                            size="small"
                            variant="link"
                            iconPosition="right"
                            icon={<Repeat size={20} />}
                        >
                            Transfer Ownership
                        </Button>
                    </div>

                    {/* Footer Buttons */}
                    <div className="flex justify-end space-x-4 mt-6">
                        <Button
                            variant="secondary"
                            onClick={onClose}
                            id="cancel-edit-owner"
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            onClick={handleUpdate}
                            id="update-owner"
                            disabled={updateOwnerMutation.isPending}
                        >
                            {updateOwnerMutation.isPending
                                ? 'Updating...'
                                : 'Update'}
                        </Button>
                    </div>
                </>
            )}

            {transferOwnershipModal && (
                <TransferOwnershipModal
                    isOpen={transferOwnershipModal}
                    onClose={() => setTransferOwnershipModal(false)}
                    currentOwnerPets={transferPets}
                    onPhoneChange={handleTransferPhoneChange}
                    primaryOwnerPhone={primaryOwnerPhone}
                    secondaryOwnerPhone={secondaryOwnerPhone}
                    secondaryOwnerData={secondaryOwnerData || undefined}
                    parentOnClose={onClose}
                />
            )}

            {showAlert && (
                <Alert
                    className="fixed top-4 left-1/2 -translate-x-1/2 z-50"
                    variant={alertVariant}
                    label={alertMessage}
                    isLight={true}
                    onClose={() => setShowAlert(false)}
                />
            )}
        </div>
    );
};

export default EditOwnerInfo;
