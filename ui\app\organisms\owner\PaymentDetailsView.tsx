import React, { useState } from 'react';
import moment from 'moment';
import { Tags, Text, Button } from '@/app/atoms';
import InvoiceBasicDetail from '@/app/molecules/cart/InvoiceBasicDetail';
import { CREDIT_TYPES } from '@/app/utils/constant';
import { usePaymentDocumentMutation } from '@/app/services/payment-details.queries';
import ShareMultipleDocumentsModal from '@/app/organisms/ShareMultipleDocumentsModal';
import FileShareSuccessModal from '@/app/organisms/FileShareSuccessModal';
import NotFoundModal from '@/app/organisms/NotFoundModal';
import Modal from '@/app/molecules/Modal';
import { ArrowLeft, CircleAlert, Loader2 } from 'lucide-react';
import { useLastTabActivity } from '@/app/services/tab-activity.queries';

// Payment Details View Component
interface PaymentDetailsViewProps {
    payment: any;
    onBack?: () => void;
    ownerId?: string;
}

const PaymentDetailsView: React.FC<PaymentDetailsViewProps> = ({
    payment,
    onBack,
    ownerId,
}) => {
    // State for modals
    const [isShareModal, setIsShareModal] = useState(false);
    const [isFileShareSuccessModal, setIsFileShareSuccessModal] =
        useState(false);
    const [openNotFoundModal, setOpenNotFoundModal] = useState(false);
    const [isDownloadInProgress, setIsDownloadInProgress] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // Get payment document mutation hook
    const { paymentDocumentMutation } = usePaymentDocumentMutation();

    const patientIdForActivity = payment.patientId || payment.patient?.id || '';
    const activityReferenceId = payment.referenceAlphaId || payment.id || '';

    const tabActivityResult = useLastTabActivity(
        patientIdForActivity,
        'invoices', // Assuming 'payments' is the correct tab name for this context
        activityReferenceId.trim()
    );

    // Format amount with currency
    const formatAmount = (amount: number | string | undefined): string => {
        if (!amount) return '₹0';
        const value = typeof amount === 'string' ? parseFloat(amount) : amount;
        return `₹${Math.abs(value).toLocaleString('en-IN')}`;
    };

    // Get variant color based on payment type
    const getPaymentTypeVariant = (
        type: string
    ): 'success' | 'warning' | 'error' | 'neutral' => {
        switch (type) {
            case CREDIT_TYPES.Collect:
                return 'success';
            case CREDIT_TYPES.CreditNote:
                return 'warning';
            case CREDIT_TYPES.Return:
                return 'error';
            default:
                return 'neutral';
        }
    };

    // Navigate to patient details
    const handlePatientClick = (patientId: string) => {
        if (patientId) {
            window.location.href = `/patients/${patientId}/details`;
        }
    };

    // Get title and status based on payment type
    const getPaymentTitle = () => {
        console.log(payment);
        switch (payment.type) {
            case CREDIT_TYPES.Invoice:
                return 'Invoice #' + (payment.invoice.referenceAlphaId || '');
            case CREDIT_TYPES.CreditNote:
                return 'Credit Note  #' + (payment.invoice.referenceAlphaId || '');
            case CREDIT_TYPES.Collect:
                return 'Payment Receipt';
            case CREDIT_TYPES.Return:
                return 'Return Receipt';
            case CREDIT_TYPES.ReconcileInvoice:
                return 'Reconcile Invoice';
            case CREDIT_TYPES.BulkReconcileInvoice:
                return 'Bulk Reconcile Invoice';
            default:
                return 'Payment';
        }
    };

    // Get unique pet names for display
    const getUniquePetNames = () => {
        if (
            payment.type === CREDIT_TYPES.BulkReconcileInvoice &&
            payment.relatedPayments?.length > 0
        ) {
            const uniquePetNames = new Set(
                payment.relatedPayments
                    .filter((p: any) => p.patient?.patientName)
                    .map((p: any) => p.patient.patientName)
            );

            if (uniquePetNames.size > 0) {
                return Array.from(uniquePetNames);
            }
        }

        return payment.patient?.patientName
            ? [payment.patient.patientName]
            : [];
    };

    // Calculate total amount and other aggregates for bulk reconcile
    const getBulkReconcileStats = () => {
        if (
            payment.type !== CREDIT_TYPES.BulkReconcileInvoice ||
            !payment.relatedPayments?.length
        ) {
            return null;
        }

        const totalAmount = payment.relatedPayments.reduce(
            (sum: number, p: any) => sum + (p.amount || 0),
            0
        );
        const totalCreditsUsed = payment.relatedPayments.reduce(
            (sum: number, p: any) => sum + (p.creditAmountUsed || 0),
            0
        );
        const invoiceCount = new Set(
            payment.relatedPayments.map((p: any) => p.invoice?.id)
        ).size;
        const petCount = new Set(
            payment.relatedPayments.map((p: any) => p.patient?.id)
        ).size;

        return {
            totalAmount,
            totalCreditsUsed,
            invoiceCount,
            petCount,
        };
    };

    // Get reference IDs from related payments for navigation
    const getInvoiceReferenceInfo = (relatedPayment: any) => {
        if (!relatedPayment || !relatedPayment.invoice) return null;

        return {
            id: relatedPayment.invoice.referenceAlphaId || '',
            patientId: relatedPayment.patientId || '',
        };
    };

    // Navigate to invoice
    const navigateToInvoice = (referenceId: string, patientId: string) => {
        if (!referenceId || !patientId) return;

        window.location.href = `/patients/${patientId}/details?tab=invoices&activeItem=${referenceId}`;
    };

    // Handle share action
    const handleShare = () => setIsShareModal(true);

    // Handle download action
    const handleDownload = () => {
        setIsLoading(true);
        setIsDownloadInProgress(true);

        // Get reference ID for the payment
        const referenceAlphaId = payment.referenceAlphaId || '';

        if (!referenceAlphaId) {
            setIsLoading(false);
            setIsDownloadInProgress(false);
            setOpenNotFoundModal(true);
            return;
        }

        paymentDocumentMutation.mutate(
            {
                referenceAlphaId,
                documentType: 'payment-details',
                action: 'download',
                patientId: payment.patientId || '',
            },
            {
                onSuccess: () => {
                    if (tabActivityResult.refetch) {
                        tabActivityResult.refetch();
                    }
                },
                onSettled: () => {
                    setIsLoading(false);
                },
                onError: () => {
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Handle share document
    const handleShareDocument = async (data: any) => {
        setIsLoading(true);

        try {
            // Get reference ID for the payment
            const referenceAlphaId = payment.referenceAlphaId || '';

            if (!referenceAlphaId) {
                setIsLoading(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
                return;
            }

            // Determine sharing modes based on user selection
            let shareMethod: 'whatsapp' | 'email' | 'both' | undefined;
            if (data.shareViaEmail && data.shareViaWhatsapp) {
                shareMethod = 'both';
            } else if (data.shareViaEmail) {
                shareMethod = 'email';
            } else if (data.shareViaWhatsapp) {
                shareMethod = 'whatsapp';
            }

            if (!shareMethod) {
                setIsLoading(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
                return;
            }

            // For custom recipient, get email and phone number from form
            const emailValue =
                data.recipient === 'other' && data.shareViaEmail
                    ? data.email
                    : '';
            const phoneValue =
                data.recipient === 'other' && data.shareViaWhatsapp
                    ? data.number
                    : '';

            const response = await paymentDocumentMutation.mutateAsync({
                referenceAlphaId,
                documentType: 'payment-details',
                action: 'share',
                patientId: payment.patientId || '',
                shareMethod,
                recipient: data.recipient,
                email: emailValue,
                whatsapp: phoneValue,
            });

            if (response?.status) {
                setIsLoading(false);
                setIsShareModal(false);
                setIsFileShareSuccessModal(true);
                if (tabActivityResult.refetch) {
                    tabActivityResult.refetch();
                }
            } else {
                setIsLoading(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
            }
        } catch (error) {
            console.error('Error sharing document:', error);
            setIsLoading(false);
            setIsShareModal(false);
            setOpenNotFoundModal(true);
        }
    };

    const uniqueNames = getUniquePetNames();
    const primaryPatientName =
        uniqueNames.length > 0 ? uniqueNames[0] : undefined;

    return (
        <div className="flex flex-col h-[calc(100vh_-_(32px_+_33px))]  rounded-2xl w-full  p-4 bg-white">
            {/* Back button */}
            <div className="">
                {onBack && (
                    <Button
                        id="back-to-invoices-button"
                        variant="link"
                        size="small"
                        label={'Back to Payments'}
                        iconPosition="left"
                        icon={<ArrowLeft size={16} />}
                        className="cursor-pointer transition-colors !p-0"
                        onClick={onBack}
                    />
                )}
            </div>

            {/* Use InvoiceBasicDetail component similar to TabInvoices.tsx */}
            <InvoiceBasicDetail
                id={payment.id}
                title={`Receipt #${payment.referenceAlphaId || ''}`}
                visitType={getPaymentTitle()}
                source={payment.source}
                date={moment(payment.createdAt).format('DD MMM YYYY')}
                time={moment(payment.createdAt).format('hh:mm A')}
                handleOnShare={handleShare}
                handleDownload={handleDownload}
                latestActivity={tabActivityResult?.data}
                className="bg-white p-0"
                patientName={primaryPatientName}
            />

            {/* Payment Details with p-6 padding similar to TabInvoices */}
            <div className="p-4 h-[calc(100vh_-_(32px_+_33px))] overflow-auto bg-white">
                {/* Different content based on payment type */}
                {payment.type === CREDIT_TYPES.Collect && (
                    <div className="bg-neutral-10 p-4 rounded-xl">
                        <div className="flex items-center flex-wrap gap-2 mb-4">
                            {getUniquePetNames().map((petName, index) => (
                                <Tags
                                    key={`pet-${index}`}
                                    size="small"
                                    shape="rounded"
                                    variant="field"
                                    iconSrc="/images/icons/paws.svg"
                                    label={petName}
                                    iconPosition="left"
                                    iconWidth={14}
                                    iconHeight={14}
                                    className="text-basic-white hover:bg-secondary-400 cursor-pointer"
                                    onClick={() => {
                                        // Find the patient ID for this pet name
                                        const patientId =
                                            payment.relatedPayments?.find(
                                                (p: any) =>
                                                    p.patient?.patientName ===
                                                    petName
                                            )?.patient?.id ||
                                            payment.patient?.id;

                                        if (patientId) {
                                            handlePatientClick(patientId);
                                        }
                                    }}
                                />
                            ))}
                        </div>

                        <div className="space-y-3">
                            <div className="flex justify-between items-center">
                                <Text className="font-Inter text-sm">
                                    Payment Mode
                                </Text>
                                <Text className="font-Inter text-sm font-medium">
                                    {payment.paymentType || '-'}
                                </Text>
                            </div>
                            <div className="flex justify-between items-center">
                                <Text className="font-Inter text-sm">
                                    Created By
                                </Text>
                                <Text className="font-Inter text-sm font-medium">
                                    {payment.createdByName || '-'}
                                </Text>
                            </div>
                            {payment.invoice && (
                                <div className="flex justify-between items-center">
                                    <Text className="font-Inter text-sm">
                                        Related Invoice
                                    </Text>
                                    <Tags
                                        size="small"
                                        shape="rounded"
                                        isLight
                                        variant="neutral"
                                        label={`Invoice #${payment.invoice.referenceAlphaId || ''}`}
                                        className="hover:bg-neutral-100 cursor-pointer font-Inter text-[10px] leading-4 font-medium"
                                        onClick={() =>
                                            navigateToInvoice(
                                                payment.invoice
                                                    .referenceAlphaId,
                                                payment.patientId
                                            )
                                        }
                                    />
                                </div>
                            )}
                            {payment.creditAmountUsed > 0 && (
                                <div className="flex justify-between items-center">
                                    <Text className="font-Inter text-sm">
                                        Credits Used
                                    </Text>
                                    <Text className="font-Inter text-sm font-medium">
                                        {formatAmount(payment.creditAmountUsed)}
                                    </Text>
                                </div>
                            )}
                            {payment.creditAmountAdded > 0 && (
                                <div className="flex justify-between items-center">
                                    <Text className="font-Inter text-sm">
                                        Credits Added
                                    </Text>
                                    <Text className="font-Inter text-sm font-medium">
                                        {formatAmount(
                                            payment.creditAmountAdded
                                        )}
                                    </Text>
                                </div>
                            )}
                            <div className="flex justify-between items-center pt-3 border-t">
                                <Text className="font-Inter text-sm font-semibold">
                                    Amount Collected
                                </Text>
                                <Text className="font-Inter text-sm font-semibold">
                                    {formatAmount(payment.amount)}
                                </Text>
                            </div>
                        </div>
                    </div>
                )}

                {payment.type === CREDIT_TYPES.Return && (
                    <div className="bg-neutral-10 p-4 rounded-xl">
                        <div className="flex items-center flex-wrap gap-2 mb-4">
                            {getUniquePetNames().map((petName, index) => (
                                <Tags
                                    key={`pet-${index}`}
                                    size="small"
                                    shape="rounded"
                                    variant="field"
                                    iconSrc="/images/icons/paws.svg"
                                    label={petName}
                                    iconPosition="left"
                                    iconWidth={14}
                                    iconHeight={14}
                                    className="text-basic-white hover:bg-secondary-400 cursor-pointer"
                                    onClick={() => {
                                        const patientId =
                                            payment.relatedPayments?.find(
                                                (p: any) =>
                                                    p.patient?.patientName ===
                                                    petName
                                            )?.patient?.id ||
                                            payment.patient?.id;

                                        if (patientId) {
                                            handlePatientClick(patientId);
                                        }
                                    }}
                                />
                            ))}
                        </div>

                        <div className="space-y-3">
                            <div className="flex justify-between items-center">
                                <Text className="font-Inter text-sm">
                                    Return Mode
                                </Text>
                                <Text className="font-Inter text-sm font-medium">
                                    {payment.paymentType || '-'}
                                </Text>
                            </div>
                            <div className="flex justify-between items-center">
                                <Text className="font-Inter text-sm">
                                    Created By
                                </Text>
                                <Text className="font-Inter text-sm font-medium">
                                    {payment.createdByName || '-'}
                                </Text>
                            </div>
                            {payment.invoice && (
                                <div className="flex justify-between items-center">
                                    <Text className="font-Inter text-sm">
                                        Related Invoice
                                    </Text>
                                    <Tags
                                        size="small"
                                        shape="rounded"
                                        isLight
                                        variant="neutral"
                                        label={`Invoice #${payment.invoice.referenceAlphaId || ''}`}
                                        className="hover:bg-neutral-100 cursor-pointer font-Inter text-[10px] leading-4 font-medium"
                                        onClick={() =>
                                            navigateToInvoice(
                                                payment.invoice
                                                    .referenceAlphaId,
                                                payment.patientId
                                            )
                                        }
                                    />
                                </div>
                            )}
                            <div className="flex justify-between items-center pt-3 border-t">
                                <Text className="font-Inter text-sm font-semibold">
                                    Amount Returned
                                </Text>
                                <Text className="font-Inter text-sm font-semibold">
                                    {formatAmount(payment.amount)}
                                </Text>
                            </div>
                        </div>
                    </div>
                )}

                {(payment.type === CREDIT_TYPES.ReconcileInvoice ||
                    payment.type === CREDIT_TYPES.Invoice ||
                    payment.type === CREDIT_TYPES.CreditNote) && (
                    <div className="border p-4 border-neutral-400 rounded-2xl">
                        <div className="flex justify-between">
                            <Text className="font-inter text-sm leading-[22px] font-normal text-primary-700 border-gray-200">
                                {payment.invoice?.invoiceType === 'Refund'
                                    ? 'Reference Credit Note'
                                    : 'Reference Invoice'}
                            </Text>
                            <Tags
                                className="font-inter text-[10px] leading-4 hover:bg-neutral-100 cursor-pointer"
                                variant="neutral"
                                isLight={true}
                                label={`${
                                    payment.invoice?.invoiceType === 'Refund'
                                        ? 'Credit Note #'
                                        : 'Invoice #'
                                }
                                ${payment.invoice?.referenceAlphaId}`}
                                size="small"
                                onClick={() => {
                                    if (payment.invoice?.referenceAlphaId) {
                                        navigateToInvoice(
                                            payment.invoice.referenceAlphaId,
                                            payment.patientId
                                        );
                                    }
                                }}
                            />
                        </div>

                        <div className="my-4">
                            <Text
                                variant="caption"
                                fontWeight="font-normal"
                                className="border-b-[0.5px] border-primary-400 text-primary-700 font-inter text-[10px] leading-[100%] tracking-[5%] align-middle uppercase pb-1"
                            >
                                {moment(payment.createdAt).format(
                                    'DD[TH] MMM YYYY'
                                )}
                                <span> • </span>
                                {moment(payment.createdAt).format('hh:mm A')}
                                <span> • </span>
                                {payment.createdByName}
                            </Text>

                            {payment.amount > 0 && (
                                <div className="flex justify-between mt-2">
                                    <div className="flex gap-2 items-center">
                                        {payment.invoice?.invoiceType ===
                                        'Refund' ? (
                                            <Text
                                                textColor="text-primary-900"
                                                fontWeight="font-semibold"
                                            >
                                                Amount Returned
                                            </Text>
                                        ) : (
                                            <Text
                                                textColor="text-primary-900"
                                                fontWeight="font-semibold"
                                            >
                                                Amount Collected
                                            </Text>
                                        )}

                                        <span> • </span>

                                        <Text
                                            textColor="text-primary-900"
                                            fontWeight="font-semibold"
                                        >
                                            {payment.paymentType}
                                        </Text>
                                    </div>
                                    <Text fontWeight="font-medium">
                                        {formatAmount(payment.amount)}
                                    </Text>
                                </div>
                            )}

                            {payment.creditAmountUsed > 0 && (
                                <div className="flex justify-between mt-2">
                                    <div className="flex gap-2 items-center">
                                        <Text
                                            textColor="text-primary-900"
                                            fontWeight="font-semibold"
                                        >
                                            Credits Applied{' '}
                                        </Text>
                                        <span> • </span>
                                        <Text
                                            textColor="text-primary-900"
                                            fontWeight="font-semibold"
                                        >
                                            {payment.paymentType}
                                        </Text>
                                    </div>
                                    <Text fontWeight="font-medium">
                                        {formatAmount(payment.creditAmountUsed)}
                                    </Text>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Bulk Reconcile Invoice - special handling for multiple invoices/pets */}
                {payment.type === CREDIT_TYPES.BulkReconcileInvoice && (
                    <div className="border border-neutral-400 p-4 rounded-2xl">
                        <div className="my-2">
                            <Text
                                variant="caption"
                                className="border-b-[0.5px] border-b-primary-400 text-primary-700 font-inter text-[10px] leading-[100%] tracking-[0.05em] align-middle uppercase pb-1"
                            >
                                {moment(payment.createdAt).format(
                                    'DD[TH] MMM YYYY'
                                )}
                                <span> • </span>
                                {moment(payment.createdAt).format('hh:mm A')}
                                <span> • </span>
                                {payment.createdByName}
                            </Text>

                            {payment.amount > 0 && (
                                <div className="flex justify-between mt-2">
                                    <div className="flex gap-2 items-center">
                                        <Text
                                            textColor="text-primary-900"
                                            fontWeight="font-semibold"
                                            className="font-inter"
                                        >
                                            Amount collected
                                        </Text>

                                        <Text className="text-xs text-primary-600">
                                            {' '}
                                            •{' '}
                                        </Text>

                                        <Text
                                            textColor="text-primary-900"
                                            fontWeight="font-semibold"
                                            className="font-inter"
                                        >
                                            {payment.paymentType}
                                        </Text>
                                    </div>
                                    <Text
                                        fontWeight="font-medium"
                                        className="font-inter"
                                    >
                                        {formatAmount(payment.amount)}
                                    </Text>
                                </div>
                            )}

                            {payment.creditAmountUsed > 0 && (
                                <div className="flex justify-between mt-2">
                                    <div className="flex gap-2 items-center">
                                        <Text
                                            textColor="text-primary-900"
                                            className="font-inter"
                                        >
                                            Total credits applied
                                        </Text>
                                    </div>
                                    <Text
                                        fontWeight="font-medium"
                                        className="font-inter"
                                    >
                                        {formatAmount(payment.creditAmountUsed)}
                                    </Text>
                                </div>
                            )}

                            {payment.creditAmountAdded > 0 && (
                                <div className="flex justify-between mt-2">
                                    <div className="flex gap-2 items-center">
                                        <Text
                                            textColor="text-primary-900"
                                            className="font-inter"
                                        >
                                            Credit Added
                                        </Text>
                                    </div>
                                    <Text
                                        fontWeight="font-medium"
                                        className="font-inter"
                                    >
                                        {formatAmount(
                                            payment.creditAmountAdded
                                        )}
                                    </Text>
                                </div>
                            )}
                        </div>

                        {/* Related Payments Section for Bulk Reconcile */}
                        {payment.relatedPayments &&
                            payment.relatedPayments.length > 0 && (
                                <div className="mt-6">
                                    <Text className="font-Inter text-sm font-semibold leading-5 mb-4 border-t pt-4">
                                        Related Payments
                                    </Text>

                                    <div className="space-y-4">
                                        {payment.relatedPayments.map(
                                            (
                                                relatedPayment: any,
                                                index: number
                                            ) => (
                                                <div
                                                    key={`related-${index}`}
                                                    className="py-3 rounded-lg"
                                                >
                                                    {(relatedPayment.amount >
                                                        0 ||
                                                        (relatedPayment.isCreditUsed &&
                                                            relatedPayment.creditAmountUsed >
                                                                0)) && (
                                                        <Text
                                                            variant="caption"
                                                            className="border-b-[0.5px] border-b-primary-400 text-primary-700 font-inter text-[10px] leading-[100%] tracking-[0.05em] align-middle uppercase pb-1"
                                                        >
                                                            {
                                                                relatedPayment
                                                                    .patient
                                                                    ?.patientName
                                                            }
                                                        </Text>
                                                    )}

                                                    {relatedPayment.amount >
                                                        0 && (
                                                        <div className="flex justify-between mt-2">
                                                            <div className="flex items-center gap-2">
                                                                <Text className="font-inter text-[12px] leading-4 tracking-[0%] align-middle font-normal">
                                                                    Invoice
                                                                    amount
                                                                    cleared
                                                                </Text>
                                                                {relatedPayment.referenceAlphaId && (
                                                                    <div className="flex items-center text-xs gap-2">
                                                                        <div className="flex items-center gap-2 text-primary-600">
                                                                            •{' '}
                                                                            {
                                                                                relatedPayment.paymentType
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                )}
                                                                <Tags
                                                                    className="font-inter text-[10px] leading-4 hover:bg-neutral-100 cursor-pointer"
                                                                    variant="neutral"
                                                                    isLight={
                                                                        true
                                                                    }
                                                                    label={
                                                                        relatedPayment?.type ===
                                                                        'Collect'
                                                                            ? relatedPayment?.referenceAlphaId
                                                                                ? `Receipt #${relatedPayment?.referenceAlphaId}`
                                                                                : 'No ID Available'
                                                                            : relatedPayment
                                                                                  ?.invoice
                                                                                  ?.referenceAlphaId
                                                                    }
                                                                    size="small"
                                                                    onClick={() => {
                                                                        if (
                                                                            relatedPayment
                                                                                ?.invoice
                                                                                ?.referenceAlphaId
                                                                        ) {
                                                                            navigateToInvoice(
                                                                                relatedPayment
                                                                                    .invoice
                                                                                    .referenceAlphaId,
                                                                                relatedPayment.patientId
                                                                            );
                                                                        }
                                                                    }}
                                                                />
                                                            </div>
                                                            <Text
                                                                fontWeight="font-medium"
                                                                className="font-inter"
                                                            >
                                                                {formatAmount(
                                                                    relatedPayment.amount
                                                                )}
                                                            </Text>
                                                        </div>
                                                    )}

                                                    {relatedPayment.creditAmountUsed >
                                                        0 && (
                                                        <div className="flex justify-between mt-2">
                                                            <div className="flex items-center gap-2">
                                                                <Text className="font-inter text-[12px] leading-4 tracking-[0%] align-middle font-normal">
                                                                    Credits used
                                                                </Text>
                                                                {relatedPayment.referenceAlphaId && (
                                                                    <div className="flex items-center text-xs gap-2">
                                                                        <div className="flex items-center gap-2 text-primary-600">
                                                                            •{' '}
                                                                            {
                                                                                relatedPayment.paymentType
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                )}
                                                                <Tags
                                                                    className="font-inter text-[10px] leading-4 hover:bg-neutral-100 cursor-pointer"
                                                                    variant="neutral"
                                                                    isLight={
                                                                        true
                                                                    }
                                                                    label={
                                                                        relatedPayment?.type ===
                                                                        'Collect'
                                                                            ? relatedPayment?.referenceAlphaId
                                                                                ? `Receipt #${relatedPayment?.referenceAlphaId}`
                                                                                : 'No ID Available'
                                                                            : relatedPayment
                                                                                    ?.invoice
                                                                                    ?.referenceAlphaId
                                                                              ? `Invoice #${relatedPayment?.invoice?.referenceAlphaId}`
                                                                              : relatedPayment
                                                                                    ?.invoice
                                                                                    ?.referenceId
                                                                    }
                                                                    size="small"
                                                                    onClick={() => {
                                                                        if (
                                                                            relatedPayment
                                                                                ?.invoice
                                                                                ?.referenceAlphaId
                                                                        ) {
                                                                            navigateToInvoice(
                                                                                relatedPayment
                                                                                    .invoice
                                                                                    .referenceAlphaId,
                                                                                relatedPayment.patientId
                                                                            );
                                                                        }
                                                                    }}
                                                                />
                                                            </div>
                                                            <Text
                                                                fontWeight="font-medium"
                                                                className="font-inter"
                                                            >
                                                                {formatAmount(
                                                                    relatedPayment.creditAmountUsed
                                                                )}
                                                            </Text>
                                                        </div>
                                                    )}
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            )}
                    </div>
                )}

                {/* Notes section if available */}
                {payment.paymentNotes && (
                    <div
                        className={`mt-4 p-4 bg-neutral-10 rounded-xl ${payment.type === CREDIT_TYPES.ReconcileInvoice || payment.type === CREDIT_TYPES.BulkReconcileInvoice ? 'border border-t-0 border-neutral-400 rounded-t-none rounded-b-2xl' : ''}`}
                    >
                        <Text className="font-Inter text-sm font-semibold leading-5 mb-2">
                            Notes
                        </Text>
                        <Text className="font-Inter text-sm">
                            {payment.paymentNotes}
                        </Text>
                    </div>
                )}
            </div>

            {/* Share Modal */}
            {isShareModal && (
                <ShareMultipleDocumentsModal
                    isOpen={isShareModal}
                    onClose={() => setIsShareModal(false)}
                    handleCancel={() => setIsShareModal(false)}
                    handleShare={(data) => handleShareDocument(data)}
                    title={`Share Payment ${payment.referenceAlphaId || ''}`}
                    documentAvailability={{ invoices: true }}
                />
            )}

            {/* Success Modal */}
            {isFileShareSuccessModal && (
                <FileShareSuccessModal
                    isOpen={isFileShareSuccessModal}
                    onClose={() => setIsFileShareSuccessModal(false)}
                />
            )}

            {/* Not Found Modal */}
            {openNotFoundModal && (
                <NotFoundModal
                    isOpen={openNotFoundModal}
                    onClose={() => setOpenNotFoundModal(false)}
                />
            )}

            {/* Download in Progress Modal */}
            {isDownloadInProgress && (
                <Modal
                    isOpen={isDownloadInProgress}
                    onClose={() => setIsDownloadInProgress(false)}
                    icon={<CircleAlert size={26} color="#E99400" />}
                    modalTitle="Download in Progress"
                    modalWidth="max-w-[500px]"
                    childrenPt="pt-4"
                    childrenPr="pr-4"
                    isPaddingRequired={true}
                    modalFooter={
                        <div className="flex gap-2 justify-end">
                            <Button
                                id="cancel-download-details"
                                variant="secondary"
                                type="button"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                id="done-download-details"
                                variant="primary"
                                type="submit"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Done
                            </Button>
                        </div>
                    }
                >
                    <div className="mb-4">
                        <Text variant="body">
                            Your download is being prepared and will begin
                            shortly. You can continue working.
                        </Text>
                    </div>
                </Modal>
            )}
        </div>
    );
};

export default PaymentDetailsView;
