import React from 'react';
import { ArrowRight2 } from 'iconsax-react';
import UserAvatar from '@/app/molecules/UserAvatar';
import { formatBreed } from '@/app/utils/common';
import { getProfileImage } from '@/app/utils/common';
import Button from '@/app/atoms/Button';
import { Tooltip } from '@/app/molecules';
import { useRouter } from 'next/navigation';

interface PetListItemCardProps {
    id: string;
    name: string;
    breed: string;
    species: string;
    onClick?: () => void;
}

const PetListItemCard: React.FC<PetListItemCardProps> = ({
    id,
    name,
    breed,
    species,
    onClick,
}) => {
    const router = useRouter();

    const handleEditClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        router.push(`/patients/${id}/details`);
    };

    return (
        <div
            className="flex items-center justify-between p-3 bg-black/20 hover:bg-black/30 rounded-lg cursor-pointer transition-colors"
            onClick={onClick}
        >
            <div className="flex items-center gap-3">
                <UserAvatar
                    avatar={
                        getProfileImage({
                            species: species,
                            breedValue: breed,
                        }) || ''
                    }
                    name={name}
                    description={formatBreed(breed)}
                />
            </div>
            <div className="flex items-center gap-2">
                <Tooltip content={`View ${name}`} position="top">
                    <div className="h-8 w-8 rounded-full bg-primary-50 hover:bg-primary-100 flex justify-center items-center cursor-pointer">
                        <Button
                            id={`edit-pet-${name}`}
                            onlyIcon
                            size="mini"
                            onClick={handleEditClick}
                            variant="secondary"
                            icon={
                                <ArrowRight2
                                    size={16}
                                    className="text-gray-700"
                                />
                            }
                        />
                    </div>
                </Tooltip>
            </div>
        </div>
    );
};

export default PetListItemCard;
