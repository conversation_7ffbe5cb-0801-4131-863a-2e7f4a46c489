import React, {
    useMemo,
    useState,
    useEffect,
    useRef,
    useCallback,
} from 'react';
import { <PERSON><PERSON>, Tooltip } from '@/app/molecules';
import Table from '@/app/molecules/Table';
import InfiniteScrollTable from '@/app/molecules/InfiniteScrollTable';
import { Button, Text, Tags, Checkbox } from '@/app/atoms';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import {
    Download,
    Share2,
    RefreshCw,
    Loader2,
    CircleAlert,
    ChevronDown,
} from 'lucide-react';
import { OwnerInvoice } from '@/app/types/owner-invoice';
import { useEMRMutation } from '@/app/services/emr.queries';
import { useStatementDocumentMutation } from '@/app/services/statement.queries';
import StatementShareSuccessModal from '@/app/organisms/StatementShareSuccessModal';
import StatementDownloadSuccessModal from '@/app/organisms/StatementDownloadSuccessModal';
import ShareMultipleDocumentsModal from '../ShareMultipleDocumentsModal';
import FileShareSuccessModal from '@/app/organisms/FileShareSuccessModal';
import NotFoundModal from '@/app/organisms/NotFoundModal';
import ModalReconcile from '@/app/organisms/invoice/ModalReconcile';
import ReconcileOwnerBalanceModal from '@/app/organisms/ledger/ReconcileOwnerBalanceModal';

import Modal from '@/app/molecules/Modal';
import InvoicesFilterBar from '@/app/molecules/InvoicesFilterBar';
import IconShare from '@/app/atoms/customIcons/IconShare';
import IconDownload from '@/app/atoms/customIcons/IconDownload.svg';
import IconFilter from '@/app/atoms/customIcons/IconFilter.svg';
import IconActiveFilter from '@/app/atoms/customIcons/IconActiveFilter.svg';

// Define User and Status types for the filter bar (ideally move to a shared types file)
interface UserFilterItem {
    id: string;
    name: string;
}

interface StatusFilterItem {
    id: string;
    label: string;
}

interface InvoicesTabProps {
    invoicesData: OwnerInvoice[];
    ownerId?: string;
    ownerDetails?: {
        ownerBrand: {
            firstName: string;
            lastName: string;
            ownerBalance: number;
            ownerCredits: number;
            globalOwner: {
                phoneNumber: string;
                countryCode: string;
            };
        };
    };
    totalCount?: number;
    isLoading?: boolean;
    onLoadMore?: () => void;
    hasMore?: boolean;
    onSearch?: (searchTerm: string, filters?: any) => void;
    availableUsers?: UserFilterItem[]; // List of users from API response
    onReconcileSelectedInvoicesRequest?: (
        selectedInvoices: OwnerInvoice[]
    ) => void;
    onShowInvoiceDetailViewRequest?: (invoice: OwnerInvoice, action?: 'edit' | 'delete' | 'write-off') => void;
    // Add filter props to persist filter state
    invoiceFilters?: {
        selectedUsers?: UserFilterItem[];
        selectedStatuses?: StatusFilterItem[];
        startDate?: string;
        endDate?: string;
    };
}

// Add WithSelection type to extend OwnerInvoice
interface WithSelection extends OwnerInvoice {
    isSelected?: boolean;
    isHighlight?: 'active' | string;
    customTableWrapperClass: string;
    tableWrapper?: string;
    rowBorder?: boolean;
    headerSticky?: boolean;
}

// Define status options based on enum values
const AVAILABLE_STATUSES: StatusFilterItem[] = [
    { id: 'fully_paid', label: 'Paid' },
    { id: 'partially_paid', label: 'Partially Paid' },
    { id: 'pending', label: 'Pending' },
    { id: 'cancelled', label: 'Cancelled' },
    { id: 'written_off', label: 'Written Off' },
];

const InvoicesTab: React.FC<InvoicesTabProps> = ({
    invoicesData,
    ownerId,
    ownerDetails,
    totalCount = 0,
    isLoading = false,
    onLoadMore,
    hasMore = false,
    onSearch,
    availableUsers = [],
    onReconcileSelectedInvoicesRequest,
    onShowInvoiceDetailViewRequest,
    invoiceFilters,
}) => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
    const [selectAll, setSelectAll] = useState(false);
    const [activeRowId, setActiveRowId] = useState<string | null>(null);
    const [isShareModal, setIsShareModal] = useState(false);
    const [isStatementShare, setIsStatementShare] = useState(false);
    const [isDownloadModal, setIsDownloadModal] = useState(false);
    const [isStatementShareSuccessModal, setIsStatementShareSuccessModal] =
        useState(false);
    const [statementShareMethod, setStatementShareMethod] = useState<
        'email' | 'whatsapp' | 'both'
    >('both');
    const [statementRecipient, setStatementRecipient] = useState<
        'client' | 'other'
    >('client');
    const [
        isStatementDownloadSuccessModal,
        setIsStatementDownloadSuccessModal,
    ] = useState(false);
    const [openNotFoundModal, setOpenNotFoundModal] = useState(false);
    const [isActionLoading, setIsActionLoading] = useState(false);
    const [isReconcileModalOpen, setIsReconcileModalOpen] = useState(false);
    const [isReconcileOwnerBalanceModal, setIsReconcileOwnerBalanceModal] =
        useState(false);
    // Note: directToStep2 is used in the ReconcileOwnerBalanceModal
    const [directToStep2] = useState(false);
    const [isDownloadInProgress, setIsDownloadInProgress] = useState(false);
    const [isStatementDownloadInProgress, setIsStatementDownloadInProgress] =
        useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [showFilterBar, setShowFilterBar] = useState(false);
    const tableWrapperRef = useRef<HTMLDivElement>(null);
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Add missing success states for better user feedback
    const [isLedgerShareSuccess, setIsLedgerShareSuccess] = useState(false);
    const [ledgerShareMethod, setLedgerShareMethod] = useState<
        'email' | 'whatsapp' | 'both'
    >('both');
    const [ledgerShareRecipient, setLedgerShareRecipient] = useState<
        'client' | 'other'
    >('client');

    // --- START: Filter Bar State and Data ---
    // Get filter values from props if available, otherwise use default values
    const [selectedUsers, setSelectedUsers] = useState<UserFilterItem[]>(
        invoiceFilters?.selectedUsers || []
    );
    const [selectedStatuses, setSelectedStatuses] = useState<
        StatusFilterItem[]
    >(invoiceFilters?.selectedStatuses || []);
    const [selectedDateRange, setSelectedDateRange] = useState<{
        start: Date | null;
        end: Date | null;
    }>({
        start: invoiceFilters?.startDate
            ? new Date(invoiceFilters.startDate)
            : null,
        end: invoiceFilters?.endDate ? new Date(invoiceFilters.endDate) : null,
    });

    // Check if any filters are active
    const hasActiveFilters = useMemo(() => {
        return (
            selectedUsers.length > 0 ||
            selectedStatuses.length > 0 ||
            selectedDateRange.start !== null ||
            selectedDateRange.end !== null
        );
    }, [selectedUsers, selectedStatuses, selectedDateRange]);

    // Toggle filter bar visibility
    const toggleFilterBar = () => {
        setShowFilterBar(!showFilterBar);
    };

    // Use the available statuses constant instead of mock data
    const availableStatuses = AVAILABLE_STATUSES;

    // Get EMR mutations for bulk operations (similar to PaymentHistory)
    const { shareLedgerDocumentMutation, downloadLedgerDocumentMutation } =
        useEMRMutation();
    // Get statement document mutation hook
    const { requestStatementDocumentsMutation } =
        useStatementDocumentMutation();

    // Helper to get full OwnerInvoice objects for selected reconcilable invoices
    const getSelectedReconcilableInvoices = (): OwnerInvoice[] => {
        if (selectedRows.size === 0) return [];

        return Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter(
                (invoice): invoice is OwnerInvoice =>
                    !!invoice && isInvoiceReconcilable(invoice)
            );
    };

    // Handle search term changes with debounce
    const handleSearchChange = useCallback(
        (value: string) => {
            setSearchTerm(value);

            // Clear any existing timeout
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }

            // Set a new timeout for debouncing
            searchTimeoutRef.current = setTimeout(() => {
                if (onSearch) {
                    // Convert selected status objects to their IDs for API
                    const statusParams =
                        selectedStatuses.length > 0
                            ? selectedStatuses
                                  .map((status) => status.id)
                                  .join(',')
                            : undefined;

                    // Convert selected user objects to their IDs for API
                    const userIds =
                        selectedUsers.length > 0
                            ? selectedUsers.map((user) => user.id).join(',')
                            : undefined;

                    // Format dates for API if they exist
                    const startDate = selectedDateRange.start
                        ? selectedDateRange.start.toISOString().split('T')[0]
                        : undefined;

                    const endDate = selectedDateRange.end
                        ? selectedDateRange.end.toISOString().split('T')[0]
                        : undefined;

                    // Pass current filter state along with search term
                    onSearch(value, {
                        userId: userIds,
                        status: statusParams,
                        startDate,
                        endDate,
                    });
                }
            }, 500); // 500ms debounce
        },
        [onSearch, selectedUsers, selectedStatuses, selectedDateRange]
    );

    // Clean up search timeout on unmount
    useEffect(() => {
        return () => {
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
        };
    }, []);

    // Calculate the total balance of selected invoices
    const selectedInvoicesBalance = useMemo(() => {
        if (selectedRows.size === 0) {
            return 0;
        }
        return Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice)
            .reduce((sum, invoice) => sum + (invoice?.invoiceBalance || 0), 0);
    }, [selectedRows, invoicesData]);

    const columnHelper = createColumnHelper<WithSelection>();

    // Prepare data with isHighlight property
    const tableData = useMemo(() => {
        return invoicesData.map((invoice) => ({
            ...invoice,
            isHighlight: undefined,
            customTableWrapperClass: '',
            tableWrapper: undefined,
            rowBorder: undefined,
            headerSticky: undefined,
        }));
    }, [invoicesData, activeRowId]);

    // Function to check if an invoice is eligible for reconciliation (unpaid or partially paid)
    const isInvoiceReconcilable = (invoice: OwnerInvoice): boolean => {
        if (!invoice.status) {
            return false;
        }
        const lowerStatus = invoice.status.toLowerCase();

        // Exclude cancelled invoices from reconciliation
        if (lowerStatus === 'cancelled') {
            return false;
        }

        return (
            lowerStatus === 'unpaid' ||
            lowerStatus === 'pending' ||
            lowerStatus === 'partially paid'
        );
    };

    // Function to check if an invoice can be edited/deleted/written off (only pending invoices)
    const isInvoiceEditable = (invoice: OwnerInvoice): boolean => {
        if (!invoice.status) {
            return false;
        }
        const lowerStatus = invoice.status.toLowerCase();
        
        // Only pending invoices can be edited, deleted, or written off
        return lowerStatus === 'pending' || lowerStatus === 'unpaid';
    };

    // Get count of selected reconcilable invoices
    const getReconcilableSelectedCount = (): number => {
        return Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice && isInvoiceReconcilable(invoice))
            .length;
    };

    // Check if there are any reconcilable invoices selected
    const hasReconcilableSelected = (): boolean => {
        return getReconcilableSelectedCount() > 0;
    };

    // Handle row selection
    const handleRowSelect = (id: string) => {
        const newSelected = new Set(selectedRows);
        if (newSelected.has(id)) {
            newSelected.delete(id);
        } else {
            newSelected.add(id);
        }
        setSelectedRows(newSelected);
        setSelectAll(newSelected.size === invoicesData.length);
    };

    // Handle table row click for highlighting and showing details
    const handleTableRowClick = (row: any) => {
        if (onShowInvoiceDetailViewRequest) {
            // Pass undefined as action when clicking on row (normal detail view)
            onShowInvoiceDetailViewRequest(row.original, undefined);
        } else {
            console.warn(
                'onShowInvoiceDetailViewRequest prop not provided to InvoicesTab'
            );
        }
    };

    // Handle select all
    const handleSelectAll = () => {
        if (selectedRows.size > 0) {
            // If any rows are selected, the action is to deselect all.
            setSelectedRows(new Set());
            setSelectAll(false);
        } else {
            // If no rows are selected, the action is to select all.
            setSelectedRows(new Set(invoicesData.map((invoice) => invoice.id)));
            setSelectAll(invoicesData.length > 0); // Set to true if there are items to select
        }
    };

    // Handle download for selected invoices as ledger document
    const handleDownloadSelected = () => {
        if (selectedRows.size === 0) return;
        setIsDownloadModal(true);
    };

    // Handle download for statement document (entire ledger)
    const handleDownloadStatementSelected = () => {
        setIsDownloadModal(true);
    };

    // Handle share for selected invoices as ledger document
    const handleShareSelected = () => {
        if (selectedRows.size === 0) return;

        // Get all selected invoices for sharing
        const selectedInvoices = Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice !== undefined) as OwnerInvoice[];

        // Set the first invoice as the current selection (for UI purposes)
        if (selectedInvoices.length > 0) {
            setIsShareModal(true);
        }
    };

    // Handle share for statement document (entire ledger)
    const handleShareStatementSelected = () => {
        if (!ownerId) return;

        // Set statement share flag and open share modal
        setIsStatementShare(true);
        setIsShareModal(true);
    };

    // Handle download action for a single invoice
    const handleDownload = async (invoice: OwnerInvoice) => {
        // Select this invoice for download
        const newSelected = new Set<string>();
        newSelected.add(invoice.id);
        setSelectedRows(newSelected);

        // Directly download the invoice without showing modal
        if (!ownerId) {
            setOpenNotFoundModal(true);
            return;
        }

        const selectedReferenceIds = [
            invoice.invoiceNumber?.replace('#', '').trim(),
        ].filter((id) => id) as string[];

        if (selectedReferenceIds.length === 0) {
            setOpenNotFoundModal(true);
            return;
        }

        setIsDownloadInProgress(true);

        // Get current filter parameters for filtered download
        const filterParams = getCurrentFilterParams();

        downloadLedgerDocumentMutation.mutate(
            {
                paymentIds: selectedReferenceIds,
                ownerId: ownerId || '',
                filters: filterParams,
            },
            {
                onSuccess: (response) => {
                    console.log('Download started successfully', {
                        hash: response?.data?.hash,
                        referenceIds: selectedReferenceIds.length,
                    });
                    setTimeout(() => setIsDownloadInProgress(false), 3000);
                },
                onError: (error) => {
                    console.error('Error initiating download:', error);
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Handle share action for a single invoice
    const handleShare = (invoice: OwnerInvoice) => {
        // Select this invoice
        const newSelected = new Set<string>();
        newSelected.add(invoice.id);
        setSelectedRows(newSelected);

        // Open share modal
        setIsShareModal(true);
    };

    // Helper function to get current filter parameters
    const getCurrentFilterParams = () => {
        // Convert selected status objects to their IDs for API
        const statusParams =
            selectedStatuses.length > 0
                ? selectedStatuses.map((status) => status.id).join(',')
                : undefined;

        // Convert selected user objects to their IDs for API
        const userIds =
            selectedUsers.length > 0
                ? selectedUsers.map((user) => user.id).join(',')
                : undefined;

        // Format dates for API if they exist
        const startDate = selectedDateRange.start
            ? selectedDateRange.start.toISOString().split('T')[0]
            : undefined;

        const endDate = selectedDateRange.end
            ? selectedDateRange.end.toISOString().split('T')[0]
            : undefined;

        const searchTermValue = searchTerm || undefined;

        // Check if any filters are actually applied
        const hasAnyFilters =
            statusParams || userIds || startDate || endDate || searchTermValue;

        // Return undefined if no filters are applied, otherwise return filter object
        return hasAnyFilters
            ? {
                  userId: userIds,
                  status: statusParams,
                  startDate,
                  endDate,
                  searchTerm: searchTermValue,
              }
            : undefined;
    };

    // Share document handler for multiple invoices (ledger or statement)
    const handleShareDocument = async (data: any) => {
        setIsActionLoading(true);

        try {
            // Determine sharing modes based on user selection
            let shareMode: string[] = [];
            if (data.shareViaEmail) shareMode.push('email');
            if (data.shareViaWhatsapp) shareMode.push('whatsapp');

            if (shareMode.length === 0) {
                setIsActionLoading(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
                return;
            }

            // For custom recipient, get email and phone number from form
            const emailValue =
                data.recipient === 'other' && data.shareViaEmail
                    ? data.email
                    : '';
            const phoneValue =
                data.recipient === 'other' && data.shareViaWhatsapp
                    ? data.number
                    : '';

            // Route to correct API based on document type selection from modal
            if (data.documentType === 'statement') {
                // Use Statement API for statement documents
                if (!ownerId) {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                // Handle statement ID selection logic (same as invoices)
                let selectedStatementIds: string[] = [];

                if (selectedRows.size > 0) {
                    // Backward compatibility: specific statements selected (existing behavior)
                    const selectedInvoices = Array.from(selectedRows)
                        .map((id) => invoicesData.find((inv) => inv.id === id))
                        .filter(
                            (invoice) => invoice !== undefined
                        ) as OwnerInvoice[];

                    // Extract reference alpha IDs for the API call
                    selectedStatementIds = selectedInvoices
                        .map((invoice) =>
                            invoice.invoiceNumber?.replace('#', '').trim()
                        )
                        .filter((id) => id) as string[];

                    // Validate we have valid IDs for backward compatibility
                    if (selectedStatementIds.length === 0) {
                        setIsActionLoading(false);
                        setIsShareModal(false);
                        setOpenNotFoundModal(true);
                        return;
                    }
                } else {
                    // New functionality: No specific statements selected - use special "ALL" flag
                    // Backend will handle selecting all statements for this owner
                    selectedStatementIds = ['ALL_STATEMENTS'];
                }

                // Get current filter parameters for filtered statement sharing
                const filterParams = getCurrentFilterParams();

                // Use statement document mutation for statement sharing
                const response =
                    await requestStatementDocumentsMutation.mutateAsync({
                        ownerId,
                        types: ['invoice'], // Only invoice type for now
                        action: 'SHARE',
                        shareMethod:
                            shareMode.length === 2
                                ? 'both'
                                : (shareMode[0] as 'email' | 'whatsapp'),
                        recipient: data.recipient,
                        email: emailValue,
                        phoneNumber: phoneValue,
                        // Add statement IDs for selection support
                        statementIds: selectedStatementIds,
                        // Add filter parameters for filtered statement generation
                        filters: filterParams,
                    });

                if (response?.status) {
                    // Set the share method based on user selection
                    if (shareMode.length === 2) {
                        setStatementShareMethod('both');
                    } else if (shareMode.includes('email')) {
                        setStatementShareMethod('email');
                    } else if (shareMode.includes('whatsapp')) {
                        setStatementShareMethod('whatsapp');
                    }

                    // Set the recipient
                    setStatementRecipient(data.recipient as 'client' | 'other');

                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                    setIsStatementShareSuccessModal(true);
                } else {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                    setOpenNotFoundModal(true);
                }
            } else if (data.documentType === 'invoice') {
                // Use EMR API for individual invoice documents
                if (!ownerId) {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                let selectedReferenceIds: string[] = [];

                if (selectedRows.size > 0) {
                    // Backward compatibility: specific invoices selected (existing behavior)
                    const selectedInvoices = Array.from(selectedRows)
                        .map((id) => invoicesData.find((inv) => inv.id === id))
                        .filter(
                            (invoice) => invoice !== undefined
                        ) as OwnerInvoice[];

                    // Extract reference alpha IDs for the API call
                    selectedReferenceIds = selectedInvoices
                        .map((invoice) =>
                            invoice.invoiceNumber?.replace('#', '').trim()
                        )
                        .filter((id) => id) as string[];

                    // Validate we have valid IDs for backward compatibility
                    if (selectedReferenceIds.length === 0) {
                        setIsActionLoading(false);
                        setIsShareModal(false);
                        setOpenNotFoundModal(true);
                        return;
                    }
                } else {
                    // New functionality: No specific invoices selected - use special "ALL" flag
                    // Backend will handle selecting all invoices for this owner
                    selectedReferenceIds = ['ALL_INVOICES'];
                }

                // Get current filter parameters for filtered ledger sharing
                const filterParams = getCurrentFilterParams();

                // Use the EMR bulk share mutation
                const response = await shareLedgerDocumentMutation.mutateAsync({
                    paymentIds: selectedReferenceIds,
                    ownerId: ownerId || '',
                    sendParameter: shareMode,
                    type: data.recipient,
                    email: emailValue,
                    phoneNumber: phoneValue,
                    filters: filterParams,
                });

                if (response?.status) {
                    // Set share success states for proper user feedback
                    if (shareMode.length === 2) {
                        setLedgerShareMethod('both');
                    } else if (shareMode.includes('email')) {
                        setLedgerShareMethod('email');
                    } else if (shareMode.includes('whatsapp')) {
                        setLedgerShareMethod('whatsapp');
                    }

                    setLedgerShareRecipient(
                        data.recipient as 'client' | 'other'
                    );

                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setIsLedgerShareSuccess(true);
                } else {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                }
            } else {
                // Unknown document type
                setIsActionLoading(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
            }
        } catch (error) {
            console.error('Error sharing documents:', error);
            setIsActionLoading(false);
            setIsShareModal(false);
            setOpenNotFoundModal(true);
        }
    };

    // Handle download document for the modal
    const handleDownloadDocument = async (data: {
        documentType: 'statement' | 'invoice';
    }) => {
        setIsDownloadModal(false);

        // Route to correct API based on document type selection from modal
        if (data.documentType === 'statement') {
            // Use Statement API for statement documents
            if (!ownerId) {
                setOpenNotFoundModal(true);
                return;
            }

            // Handle statement ID selection logic (same as invoices)
            let selectedStatementIds: string[] = [];

            if (selectedRows.size > 0) {
                // Backward compatibility: specific statements selected (existing behavior)
                const selectedInvoices = Array.from(selectedRows)
                    .map((id) => invoicesData.find((inv) => inv.id === id))
                    .filter(
                        (invoice) => invoice !== undefined
                    ) as OwnerInvoice[];

                // Extract reference alpha IDs for the API call
                selectedStatementIds = selectedInvoices
                    .map((invoice) =>
                        invoice.invoiceNumber?.replace('#', '').trim()
                    )
                    .filter((id) => id) as string[];

                // Validate we have valid IDs for backward compatibility
                if (selectedStatementIds.length === 0) {
                    setOpenNotFoundModal(true);
                    return;
                }
            } else {
                // New functionality: No specific statements selected - use special "ALL" flag
                // Backend will handle selecting all statements for this owner
                selectedStatementIds = ['ALL_STATEMENTS'];
            }

            setIsStatementDownloadSuccessModal(true);

            // Get current filter parameters for filtered statement download
            const filterParams = getCurrentFilterParams();

            requestStatementDocumentsMutation.mutate(
                {
                    ownerId,
                    types: ['invoice'],
                    action: 'DOWNLOAD',
                    // Add statement IDs for selection support
                    statementIds: selectedStatementIds,
                    // Add filter parameters for filtered statement generation
                    filters: filterParams,
                },
                {
                    onSuccess: (response) => {
                        console.log('Statement download started successfully', {
                            requestId: response?.data?.requestId,
                            filters: filterParams,
                        });
                    },
                    onError: (error) => {
                        console.error(
                            'Error initiating statement download:',
                            error
                        );
                        setIsStatementDownloadSuccessModal(false);
                        setOpenNotFoundModal(true);
                    },
                }
            );
        } else if (data.documentType === 'invoice') {
            // Use EMR API for individual invoice documents
            if (!ownerId) {
                setOpenNotFoundModal(true);
                return;
            }

            let selectedReferenceIds: string[] = [];

            if (selectedRows.size > 0) {
                // Backward compatibility: specific invoices selected (existing behavior)
                const selectedInvoices = Array.from(selectedRows)
                    .map((id) => invoicesData.find((inv) => inv.id === id))
                    .filter(
                        (invoice) => invoice !== undefined
                    ) as OwnerInvoice[];

                // Extract reference alpha IDs for the API call
                selectedReferenceIds = selectedInvoices
                    .map((invoice) =>
                        invoice.invoiceNumber?.replace('#', '').trim()
                    )
                    .filter((id) => id) as string[];

                // Validate we have valid IDs for backward compatibility
                if (selectedReferenceIds.length === 0) {
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                    return;
                }
            } else {
                // New functionality: No specific invoices selected - use special "ALL" flag
                // Backend will handle selecting all invoices for this owner
                selectedReferenceIds = ['ALL_INVOICES'];
            }

            setIsDownloadInProgress(true);

            // Get current filter parameters for filtered download
            const filterParams = getCurrentFilterParams();

            downloadLedgerDocumentMutation.mutate(
                {
                    paymentIds: selectedReferenceIds,
                    ownerId: ownerId || '',
                    filters: filterParams,
                },
                {
                    onSuccess: (response) => {
                        console.log('Download started successfully', {
                            hash: response?.data?.hash,
                            referenceIds: selectedReferenceIds.length,
                        });
                        setTimeout(() => setIsDownloadInProgress(false), 3000);
                    },
                    onError: (error) => {
                        console.error('Error initiating download:', error);
                        setIsDownloadInProgress(false);
                        setOpenNotFoundModal(true);
                    },
                }
            );
        }
    };

    // Handle reconcile action for a single invoice
    const handleReconcile = (invoice: OwnerInvoice) => {
        // Only allow reconciliation for unpaid or partially paid invoices
        if (isInvoiceReconcilable(invoice)) {
            if (onReconcileSelectedInvoicesRequest) {
                onReconcileSelectedInvoicesRequest([invoice]); // Pass the single invoice in an array
            } else {
                // Fallback or alternative behavior if the prop isn't provided,
                // though in the current setup it should be.
                // For now, retain the old modal logic as a potential fallback if desired,
                // or remove if ReconcileOwnerBalanceView is the sole path.
                setIsReconcileModalOpen(true);
            }
        }
    };

    // Handle edit action for a single invoice
    const handleEditInvoice = (invoice: OwnerInvoice) => {
        if (isInvoiceEditable(invoice) && onShowInvoiceDetailViewRequest) {
            onShowInvoiceDetailViewRequest(invoice, 'edit');
        }
    };

    // Handle delete action for a single invoice
    const handleDeleteInvoice = (invoice: OwnerInvoice) => {
        if (isInvoiceEditable(invoice) && onShowInvoiceDetailViewRequest) {
            onShowInvoiceDetailViewRequest(invoice, 'delete');
        }
    };

    // Handle write-off action for a single invoice
    const handleWriteOffInvoice = (invoice: OwnerInvoice) => {
        if (isInvoiceEditable(invoice) && onShowInvoiceDetailViewRequest) {
            onShowInvoiceDetailViewRequest(invoice, 'write-off');
        }
    };

    // Handle reconcile for selected invoices
    const handleReconcileSelected = () => {
        // Ensure there are selected and reconcilable invoices
        if (selectedRows.size > 0 && hasReconcilableSelected()) {
            const selectedReconcilable = getSelectedReconcilableInvoices();
            if (
                onReconcileSelectedInvoicesRequest &&
                selectedReconcilable.length > 0
            ) {
                onReconcileSelectedInvoicesRequest(selectedReconcilable);
            }
            // setIsReconcileOwnerBalanceModal(true); // This will be handled by parent
            // setDirectToStep2(false); // This state might be specific to the modal, ensure it's still needed or remove
        }
    };

    // Note: We're not using a direct reconcile modal open function
    // as reconciliation is handled through onReconcileSelectedInvoicesRequest prop

    // Get only the reconcilable selected invoice data for ReconcileOwnerBalanceModal
    const getSelectedInvoicesData = () => {
        if (selectedRows.size === 0) return [];

        return Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice && isInvoiceReconcilable(invoice))
            .map((invoice) => ({
                id: invoice?.id || '',
                invoiceDate: invoice?.date || '',
                patientName: invoice?.patientName || '',
                patientId: invoice?.patientId || '', // Keep patientId for compatibility with reconcile modal
                invoiceReference:
                    invoice?.invoiceNumber?.replace('#', '').trim() || '',
                balanceDue: invoice?.invoiceBalance || 0,
                status:
                    invoice?.status?.toLowerCase().replace(' ', '_') ||
                    'pending',
            }));
    };

    const columns = useMemo(
        () => [
            // Checkbox column
            columnHelper.display({
                id: 'select',
                header: () => (
                    <div className="flex justify-center">
                        <Tags
                            label={
                                selectedRows.size > 0
                                    ? 'Deselect All'
                                    : 'Select All'
                            }
                            onClick={handleSelectAll}
                            variant="neutral"
                            size="small"
                            shape="rounded"
                            isLight
                            className="hover:bg-neutral-100 cursor-pointer transition-colors"
                        />
                    </div>
                ),
                cell: ({ row }) => (
                    <div
                        className="flex justify-center"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <Checkbox
                            checked={selectedRows.has(row.original.id)}
                            size="medium"
                            onChange={() => handleRowSelect(row.original.id)}
                            aria-label={`Select invoice ${row.original.invoiceNumber}`}
                        />
                    </div>
                ),
                meta: {
                    thAlign: 'text-center',
                    thClassName: 'w-min',
                    tdClassName: 'w-min cursor-pointer', // Added cursor-pointer
                },
            }),
            columnHelper.accessor('invoiceNumber', {
                header: 'Invoice Number',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('status', {
                header: 'Status',
                cell: (info: any) => {
                    let status = info.getValue();
                    let textColor = '';
                    if (status === 'Unpaid') {
                        status = 'Pending';
                    }
                    switch (status.toLowerCase()) {
                        case 'partially_paid':
                        case 'partially paid': // Corrected to lowercase
                            textColor = 'text-warning-100'; // Semantic/Warning_Base
                            break;
                        case 'fully_paid':
                        case 'paid': // Corrected to lowercase
                            textColor = 'text-success-100'; // Semantic/Success_Base
                            break;
                        case 'cancelled':
                            textColor = 'text-gray-500'; // Gray text for cancelled invoices
                            break;
                        case 'written off':
                            textColor = 'text-neutral-900'; // Gray text for cancelled invoices
                            break;
                        default:
                            textColor = 'red-error';
                            break;
                    }
                    return (
                        <Text
                            className={`!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle  ${textColor} uppercase !text-[10px] text-nowrap`}
                        >
                            {status}
                        </Text>
                    );
                },
                meta: {
                    tdClassName: 'uppercase cursor-pointer', // The text color is already applied in the cell render
                },
            }),
            columnHelper.accessor('date', {
                header: 'Date',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    tdClassName: 'whitespace-nowrap cursor-pointer', // Added cursor-pointer
                },
            }),
            columnHelper.accessor('patientName', {
                header: 'Patient',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('user', {
                header: 'User',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue() || '-'}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('invoiceAmount', {
                header: 'Invoice Amount',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('invoiceBalance', {
                header: 'Invoice Balance',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.display({
                id: 'actions',
                header: 'Actions',
                meta: {
                    thClassName: 'w-[5%]',
                    tdClassName:
                        'w-[5%] group-hover:bg-others-100 hover:bg-neutral-50 cursor-pointer',
                    thAlign: 'text-center',
                    actionOptions: ((row: { original: WithSelection }) => {
                        const actions = [
                            { id: 'download', label: 'Download' },
                            { id: 'share', label: 'Share' },
                        ];
                        
                        // Only add Reconcile if the invoice is reconcilable
                        if (isInvoiceReconcilable(row.original)) {
                            actions.push({
                                id: 'reconcile',
                                label: 'Reconcile',
                            });
                        }
                        
                        // Add Edit, Delete, and Write-off actions for pending invoices
                        if (isInvoiceEditable(row.original)) {
                            actions.push(
                                { id: 'edit', label: 'Edit Invoice' },
                                { id: 'delete', label: 'Cancel Invoice' },
                                { id: 'write-off', label: 'Write Off Invoice' }
                            );
                        }
                        
                        return actions;
                    }) as any,
                    onActionClick: ({
                        row,
                        action,
                    }: {
                        row: { original: WithSelection };
                        action: { id: string };
                    }) => {
                        switch (action.id) {
                            case 'download':
                                handleDownload(row.original);
                                break;
                            case 'share':
                                handleShare(row.original);
                                break;
                            case 'reconcile':
                                if (isInvoiceReconcilable(row.original)) {
                                    handleReconcile(row.original);
                                }
                                break;
                            case 'edit':
                                if (isInvoiceEditable(row.original)) {
                                    handleEditInvoice(row.original);
                                }
                                break;
                            case 'delete':
                                if (isInvoiceEditable(row.original)) {
                                    handleDeleteInvoice(row.original);
                                }
                                break;
                            case 'write-off':
                                if (isInvoiceEditable(row.original)) {
                                    handleWriteOffInvoice(row.original);
                                }
                                break;
                            default:
                                break;
                        }
                    },
                },
            }),
        ],
        [
            selectAll,
            selectedRows,
            isActionLoading,
            handleDownload,
            handleShare,
            handleReconcile,
            handleEditInvoice,
            handleDeleteInvoice,
            handleWriteOffInvoice,
            isInvoiceReconcilable,
            isInvoiceEditable,
        ]
    );

    // Convert the onLoadMore callback to a Promise-based function for InfiniteScrollTable
    const handleLoadMore = async () => {
        if (onLoadMore && !isLoading && hasMore) {
            onLoadMore();
        }
        // Return a resolved promise to ensure compatibility with InfiniteScrollTable
        return Promise.resolve();
    };

    // If we have a selected invoice and showDetailView is true, show the invoice details view
    // This entire block will be removed, as PetOwnerDetailsTemplate now handles this logic.
    // if (showDetailView && selectedInvoice) {
    //     return (
    //         <div className="h-[calc(100vh-140px)] overflow-auto">
    //             <div className="mb-1">
    //                 <Tags
    //                     size="small"
    //                     shape="rounded"
    //                     isLight
    //                     variant="neutral"
    //                     label={'Back to Invoices'}
    //                     iconPosition="left"
    //                     iconSrc="/images/icons/arrow-left.svg"
    //                     iconHeight={8}
    //                     iconWidth={8}
    //                     className="hover:bg-neutral-100 cursor-pointer transition-colors"
    //                     onClick={handleBackFromDetail}
    //                 />
    //             </div>
    //             <div className="h-[calc(100vh-170px)] overflow-auto">
    //                 <InvoiceDetailsView
    //                     invoice={selectedInvoice}
    //                     ownerId={ownerId}
    //                     ownerDetails={ownerDetails}
    //                 />
    //             </div>
    //         </div>
    //     );
    // }

    // Otherwise, show the invoices list view
    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center w-full space-x-2">
                    <div className="relative">
                        <Button
                            id="filter-toggle-button"
                            variant="secondary"
                            size="small"
                            onClick={toggleFilterBar}
                            className={`p-2 relative ${
                                hasActiveFilters && !showFilterBar
                                    ? 'bg-transparent hover:bg-transparent'
                                    : hasActiveFilters
                                      ? '!bg-primary-900 !text-white hover:!bg-primary-900'
                                      : 'bg-primary-50 text-neutral-900'
                            }`}
                            icon={
                                hasActiveFilters && !showFilterBar ? (
                                    <IconActiveFilter className="h-10 w-10" />
                                ) : (
                                    <IconFilter
                                        className={`h-5 w-5 ${
                                            hasActiveFilters
                                                ? 'text-white'
                                                : showFilterBar
                                                  ? 'text-primary-900'
                                                  : ''
                                        }`}
                                    />
                                )
                            }
                            onlyIcon
                        />
                    </div>
                    <div className="flex-1">
                        <Searchbar
                            id="invoice-search"
                            name="invoice-search"
                            placeholder="Search..."
                            onChange={handleSearchChange}
                            className="border w-full"
                        />
                    </div>
                </div>
                <div className="flex items-center ml-4 space-x-2">
                    {/* Statement buttons - always visible, work with both selected and all statements */}
                    <div className="flex flex-col">
                        <div className="flex items-center space-x-2">
                            <Button
                                id="share-statement-button"
                                variant="primary"
                                size="semiSmall"
                                onClick={handleShareStatementSelected}
                                className="p-2"
                                icon={
                                    <IconShare
                                        className="h-6 w-6 text-white"
                                        fill="white"
                                    />
                                }
                                onlyIcon
                            />
                            <Button
                                id="download-statement-button"
                                variant="primary"
                                size="semiSmall"
                                onClick={handleDownloadStatementSelected}
                                className="p-2"
                                disabled={isStatementDownloadSuccessModal} // Disable if download is in progress
                                icon={
                                    isStatementDownloadSuccessModal ? (
                                        <Loader2 className="h-5 w-5 animate-spin" />
                                    ) : (
                                        <IconDownload className="h-5 w-5" />
                                    )
                                }
                                onlyIcon
                            />
                        </div>
                    </div>

                    {/* Reconcile Selection button - only visible when rows are selected AND at least one has pending/partially paid status */}
                    {selectedRows.size > 0 && hasReconcilableSelected() && (
                        <Button
                            id="reconcile-selected-button"
                            variant="primary"
                            size="semiSmall"
                            onClick={handleReconcileSelected}
                            className="ml-4 w-full text-nowrap"
                        >
                            Reconcile Selection
                        </Button>
                    )}
                </div>
            </div>

            {showFilterBar && (
                <InvoicesFilterBar
                    availableUsers={availableUsers}
                    availableStatuses={availableStatuses}
                    initialSelectedUsers={selectedUsers}
                    initialSelectedStatus={selectedStatuses}
                    initialStartDate={selectedDateRange.start}
                    initialEndDate={selectedDateRange.end}
                    onFilterChange={(filters) => {
                        setSelectedUsers(filters.selectedUsers);
                        setSelectedStatuses(filters.selectedStatus);
                        setSelectedDateRange(filters.dateRange);

                        // Call onSearch immediately with current searchTerm and new filter values
                        if (onSearch) {
                            const statusParams =
                                filters.selectedStatus.length > 0
                                    ? filters.selectedStatus
                                          .map((status) => status.id)
                                          .join(',')
                                    : undefined;
                            const userIds =
                                filters.selectedUsers.length > 0
                                    ? filters.selectedUsers
                                          .map((user) => user.id)
                                          .join(',')
                                    : undefined;
                            const startDate = filters.dateRange.start
                                ? filters.dateRange.start
                                      .toISOString()
                                      .split('T')[0]
                                : undefined;
                            const endDate = filters.dateRange.end
                                ? filters.dateRange.end
                                      .toISOString()
                                      .split('T')[0]
                                : undefined;

                            // Use searchTerm from InvoicesTab state along with new filters
                            onSearch(searchTerm, {
                                userId: userIds,
                                status: statusParams,
                                startDate,
                                endDate,
                            });
                        }
                    }}
                    onClearAll={() => {
                        setSelectedUsers([]);
                        setSelectedStatuses([]);
                        setSelectedDateRange({ start: null, end: null });

                        // Call onSearch immediately with current searchTerm and cleared filters
                        if (onSearch) {
                            // Use searchTerm from InvoicesTab state and undefined for all filter values
                            onSearch(searchTerm, {
                                userId: undefined,
                                status: undefined,
                                startDate: undefined,
                                endDate: undefined,
                            });
                        }
                    }}
                />
            )}

            <div
                ref={tableWrapperRef}
                className={
                    showFilterBar
                        ? 'h-[calc(100vh-344px)]'
                        : 'h-[calc(100vh-280px)]'
                }
            >
                <InfiniteScrollTable
                    columns={columns}
                    tableData={tableData ?? []}
                    listLoadStatus={isLoading ? 'pending' : 'success'}
                    customTableWrapperClass=""
                    variant="small"
                    handleTableRowClick={handleTableRowClick}
                    headerSticky={true}
                    loadMoreData={handleLoadMore}
                    hasMore={hasMore}
                    emptyTableMessage="No invoices found"
                    subEmptyTableMessage="Clear any filters and confirm the selected owner."
                    emptyStateImageSrc="/images/dog-with-paper-mouth.png"
                    customHeight={
                        showFilterBar
                            ? 'h-[calc(100vh-344px)]'
                            : 'h-[calc(100vh-280px)]'
                    }
                />
            </div>
            {/*total stats */}
            <div className="flex flex-col justify-center border-t border-others-100 p-6">
                <div className="flex items-center gap-2">
                    <span className="font-inter font-medium text-[12px] leading-4 tracking-[0%] align-middle text-primary-900">
                        Total no. of invoices :
                    </span>
                    <span className="font-inter font-medium text-[12px] leading-4 tracking-[0%] align-middle text-primary-900">
                        {totalCount}
                    </span>
                </div>
            </div>
            {/* Share Modal */}
            <ShareMultipleDocumentsModal
                isOpen={isShareModal}
                onClose={() => {
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                }}
                handleCancel={() => {
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                }}
                handleShare={handleShareDocument}
                title={
                    isStatementShare
                        ? 'Share Document'
                        : selectedRows.size > 1
                          ? `Share Invoices (${selectedRows.size})`
                          : selectedRows.size === 1
                            ? `Share Invoice ${
                                  invoicesData.find(
                                      (inv) =>
                                          inv.id === Array.from(selectedRows)[0]
                                  )?.invoiceNumber || ''
                              }`
                            : 'Share Document' // Generic title for document selection
                }
                documentAvailability={{
                    invoices: true,
                }}
            />

            {/* Download Modal */}
            <ShareMultipleDocumentsModal
                isOpen={isDownloadModal}
                onClose={() => setIsDownloadModal(false)}
                handleCancel={() => setIsDownloadModal(false)}
                handleShare={handleDownloadDocument}
                handleDownload={handleDownloadDocument}
                isDownloadFlow={true}
                title={
                    selectedRows.size > 1
                        ? `Download Documents (${selectedRows.size} selected)`
                        : selectedRows.size === 1
                          ? `Download Document (${
                                invoicesData.find(
                                    (inv) =>
                                        inv.id === Array.from(selectedRows)[0]
                                )?.invoiceNumber || ''
                            } selected)`
                          : 'Download Document'
                }
                documentAvailability={{
                    invoices: true,
                }}
            />
            {/* Success Modal for ledger download in progress */}
            {isDownloadInProgress && (
                <Modal
                    isOpen={isDownloadInProgress}
                    onClose={() => setIsDownloadInProgress(false)}
                    icon={<CircleAlert size={26} color="#E99400" />}
                    modalTitle="Ledger Download in Progress"
                    modalWidth="max-w-[500px]"
                    childrenPt="pt-4"
                    childrenPr="pr-4"
                    isPaddingRequired={true}
                    modalFooter={
                        <div className="flex gap-2 justify-end">
                            <Button
                                id="cancel"
                                variant="secondary"
                                type="button"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                id="done"
                                variant="primary"
                                type="submit"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Done
                            </Button>
                        </div>
                    }
                >
                    <div className="mb-4">
                        <Text variant="body">
                            This action will take a few seconds. You can exit
                            this page while we take care of it.
                        </Text>
                    </div>
                </Modal>
            )}

            {/* Statement download now uses StatementDownloadSuccessModal */}

            {/* Success Modal for statement sharing */}
            <StatementShareSuccessModal
                isOpen={isStatementShareSuccessModal}
                onClose={() => setIsStatementShareSuccessModal(false)}
                statementType="invoice"
                shareMethod={statementShareMethod}
                recipient={statementRecipient}
            />

            {/* Success Modal for ledger document sharing */}
            <FileShareSuccessModal
                isOpen={isLedgerShareSuccess}
                onClose={() => setIsLedgerShareSuccess(false)}
                title="Invoices Successfully Shared!"
            />

            {/* Success Modal for statement download */}
            <StatementDownloadSuccessModal
                isOpen={isStatementDownloadSuccessModal}
                onClose={() => setIsStatementDownloadSuccessModal(false)}
                statementType="invoice"
            />
            {/* Not Found Modal */}
            <NotFoundModal
                isOpen={openNotFoundModal}
                onClose={() => setOpenNotFoundModal(false)}
            />
            {/* Single Invoice Reconcile Modal */}
            {isReconcileModalOpen &&
                invoicesData.find(
                    (inv) => inv.id === Array.from(selectedRows)[0]
                ) && (
                    <ModalReconcile
                        isOpen={{
                            invoice: {
                                id: invoicesData.find(
                                    (inv) =>
                                        inv.id === Array.from(selectedRows)[0]
                                )!.id,
                            },
                            ownerName: ownerDetails
                                ? `${ownerDetails.ownerBrand.firstName} ${ownerDetails.ownerBrand.lastName}`
                                : '',
                            ownerPhone: ownerDetails?.ownerBrand.globalOwner
                                ?.phoneNumber
                                ? `${ownerDetails.ownerBrand.globalOwner.countryCode} ${ownerDetails.ownerBrand.globalOwner.phoneNumber}`
                                : '',
                            ownerBalance: String(
                                ownerDetails?.ownerBrand.ownerBalance || 0
                            ),
                            ownerCredits: String(
                                ownerDetails?.ownerBrand.ownerCredits || 0
                            ),
                            amountPayable:
                                invoicesData
                                    .find(
                                        (inv) =>
                                            inv.id ===
                                            Array.from(selectedRows)[0]
                                    )!
                                    .invoiceAmount?.toString() || '0',
                            balanceDue:
                                invoicesData
                                    .find(
                                        (inv) =>
                                            inv.id ===
                                            Array.from(selectedRows)[0]
                                    )!
                                    .invoiceBalance?.toString() || '0',
                            petName:
                                invoicesData.find(
                                    (inv) =>
                                        inv.id === Array.from(selectedRows)[0]
                                )!.patientName || '',
                            ownerId: ownerId || '',
                            patientId:
                                invoicesData.find(
                                    (inv) =>
                                        inv.id === Array.from(selectedRows)[0]
                                )!.patientId || '',
                            id: invoicesData.find(
                                (inv) => inv.id === Array.from(selectedRows)[0]
                            )!.id,
                        }}
                        onClose={() => setIsReconcileModalOpen(false)}
                    />
                )}
            {/* Reconcile Owner Balance Modal */}
            {isReconcileOwnerBalanceModal && ownerDetails && (
                <ReconcileOwnerBalanceModal
                    isOpen={isReconcileOwnerBalanceModal}
                    onClose={(success?: boolean) => {
                        setIsReconcileOwnerBalanceModal(false);
                        if (success) {
                            // Refresh data or show success message
                            // Potentially clear selection
                            // setSelectedRows(new Set());
                            // setSelectAll(false);
                            // Trigger a refresh of invoices if onSearch is available and appropriate
                            if (onSearch) {
                                onSearch(searchTerm, {
                                    /* pass current filters */
                                });
                            }
                        }
                    }}
                    isRefetching={false}
                    paymentDetailsList={[]}
                    ownerDetails={{
                        id: ownerId || '',
                        firstName: ownerDetails.ownerBrand.firstName,
                        lastName: ownerDetails.ownerBrand.lastName,
                        phoneNumber:
                            ownerDetails.ownerBrand.globalOwner.phoneNumber,
                        countryCode:
                            ownerDetails.ownerBrand.globalOwner.countryCode,
                        ownerBalance: ownerDetails.ownerBrand.ownerBalance,
                        ownerCredits: ownerDetails.ownerBrand.ownerCredits,
                    }}
                    filteredPaymentDetails={[]}
                    isSelectStage={directToStep2}
                    selectedInvoices={getSelectedInvoicesData()}
                />
            )}
        </div>
    );
};

export default InvoicesTab;
