import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, Text, Tags, Checkbox } from '@/app/atoms';
import moment from 'moment';
import { useStatementDocumentMutation } from '@/app/services/statement.queries';
import { DATE_FORMAT } from '@/app/utils/constant';
import { createColumnHelper } from '@tanstack/react-table';
import { Download, Share2 } from 'lucide-react';
import { Modal, Searchbar, Tooltip } from '@/app/molecules';
import IconShare from '@/app/atoms/customIcons/IconShare';
import IconDownload from '@/app/atoms/customIcons/IconDownload.svg';
import { Loader2 } from 'lucide-react';
import LedgerFilterBar, { User } from '@/app/molecules/LedgerFilterBar';
import InfiniteScrollTable from '@/app/molecules/InfiniteScrollTable';
import StatementShareSuccessModal from '../../StatementShareSuccessModal';
import StatementDownloadSuccessModal from '../../StatementDownloadSuccessModal';
import { formatCurrency, getAmountColor } from '@/app/utils/common';
import NotFoundModal from '../../NotFoundModal';
import { format, parseISO } from 'date-fns';

// Define LedgerItem interface
interface LedgerItem {
    id: string;
    type: string;
    referenceAlphaId?: string;
    amount: number;
    credit: number;
    debit: number;
    runningBalance: number;
    clearedInvoices?: any;
    balanceDue?: number;
    totalCollected?: number;
    createdAt: string;
    createdBy: string;
    createdByName: string;
    patientId?: string;
    patientName?: string;
    notes?: string;
    comment?: string;
    status?: string;
    paymentType?: string;
    paymentMode?: string;
    isCreditUsed?: boolean;
    creditAmountUsed?: number;
    creditChange?: number;
    runningCredits?: number;
    source: 'payment' | 'invoice' | 'creditnote';
}

interface LedgerTabProps {
    ledgerData: LedgerItem[];
    ownerId: string;
    ownerDetails?: {
        ownerBrand: {
            firstName: string;
            lastName: string;
            ownerBalance: number;
            ownerCredits: number;
            globalOwner: {
                phoneNumber: string;
                countryCode: string;
            };
        };
    };
    isLoading?: boolean;
    availableUsers?: Array<{ id: string; name: string }>;
    totalCount?: number;
    onLoadMore?: () => void;
    hasMore?: boolean;
    onSearch?: (searchTerm: string, filters: any) => void;
    onFilterChange?: (filters: any) => void;
    ledgerFilters?: {
        selectedUsers?: Array<{ id: string; name: string }>;
        startDate?: Date | null;
        endDate?: Date | null;
    };
    summary?: {
        totalMonetaryDebits: number;
        totalMonetaryCredits: number;
        finalRunningBalance: number;
        totalProfileCreditsAdded: number;
        totalProfileCreditsUsed: number;
        finalRunningCredits: number;
    };
}

// Add WithSelection type to extend LedgerItem
interface WithSelection extends LedgerItem {
    isSelected?: boolean;
    isHighlight?: 'active' | string;
    customTableWrapperClass: string;
    tableWrapper?: string;
    rowBorder?: boolean;
    headerSticky?: boolean;
}

const LedgerTab: React.FC<LedgerTabProps> = ({
    ledgerData,
    ownerId,
    ownerDetails,
    isLoading = false,
    availableUsers = [],
    totalCount,
    onLoadMore,
    hasMore,
    onSearch,
    onFilterChange,
    ledgerFilters,
    summary,
}) => {
    // Determine loading state - consider actual loading, empty data, and test loading
    const isLoadingData = isLoading || (!isLoading && !ledgerData?.length);

    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
    const [selectAll, setSelectAll] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
    const [selectedDateRange, setSelectedDateRange] = useState<{
        start: Date | null;
        end: Date | null;
    }>({ start: null, end: null });
    const [isShareModal, setIsShareModal] = useState(false);
    const [isStatementShareSuccessModal, setIsStatementShareSuccessModal] =
        useState(false);
    const [statementShareMethod, setStatementShareMethod] = useState<
        'email' | 'whatsapp' | 'both'
    >('email');
    const [
        isStatementDownloadSuccessModal,
        setIsStatementDownloadSuccessModal,
    ] = useState(false);
    const [isDownloadInProgress, setIsDownloadInProgress] = useState(false);
    const [openNotFoundModal, setOpenNotFoundModal] = useState(false);
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Statement document mutation for downloading/sharing statements
    const { requestStatementDocumentsMutation: statementDocumentMutation } =
        useStatementDocumentMutation();

    // Handle search input change with debounce
    const handleSearchChange = useCallback((value: string) => {
        setSearchTerm(value);
    }, []);

    // Handle download statement
    const handleDownloadStatement = () => {
        setIsDownloadInProgress(true);

        statementDocumentMutation.mutate(
            {
                ownerId,
                types: ['statement'],
                action: 'DOWNLOAD',
            },
            {
                onSuccess: () => {
                    setIsDownloadInProgress(false);
                    setIsStatementDownloadSuccessModal(true);
                },
                onError: () => {
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Handle share statement
    const handleShareStatement = () => {
        setIsShareModal(true);
    };

    // Handle share statement confirmation
    const handleShareStatementConfirm = (
        shareMethod: 'email' | 'whatsapp' | 'both',
        recipient: 'client' | 'other',
        email?: string,
        phoneNumber?: string
    ) => {
        setIsShareModal(false);
        setStatementShareMethod(shareMethod);

        statementDocumentMutation.mutate(
            {
                ownerId,
                action: 'share',
                documentType: 'statement',
                shareMethod,
                recipient,
                email,
                phoneNumber,
            },
            {
                onSuccess: () => {
                    setIsStatementShareSuccessModal(true);
                },
                onError: () => {
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    const columnHelper = createColumnHelper<WithSelection>();

    // Define table columns
    const columns = useMemo(
        () => [
            columnHelper.accessor('createdAt', {
                header: 'Date',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {moment(info.getValue()).format('DD MMM YYYY')}
                    </Text>
                ),
                meta: {
                    tdClassName: 'whitespace-nowrap !p-5',
                },
            }),
            columnHelper.accessor('referenceAlphaId', {
                header: 'Reference ID',
                cell: (info) => {
                    const source = info.row.original.source;
                    const referenceId = info.getValue();

                    // Handle special cases for writeoff and cancellation entries
                    if (
                        referenceId &&
                        (referenceId.startsWith('WO-') ||
                            referenceId.startsWith('IC-'))
                    ) {
                        return (
                            <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                                {referenceId}
                            </Text>
                        );
                    }

                    // Only show reference ID for payment, invoice, or creditnote sources
                    if (['payment', 'invoice', 'creditnote'].includes(source)) {
                        let prefix = '';
                        switch (source) {
                            case 'payment':
                                prefix = 'PA';
                                break;
                            case 'invoice':
                                prefix = 'IN';
                                break;
                            case 'creditnote':
                                prefix = 'CN';
                                break;
                        }
                        return (
                            <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                                {prefix}
                                {referenceId ? `-${referenceId}` : '-'}
                            </Text>
                        );
                    }
                    return (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                            -
                        </Text>
                    );
                },
            }),
            columnHelper.accessor('type', {
                header: 'Type',
                cell: (info) => {
                    const clearedInvoices = info.row.original.clearedInvoices;

                    // Filter out cleared invoices that don't have valid invoice reference IDs
                    const validClearedInvoices = clearedInvoices?.filter(
                        (invoice: any) =>
                            invoice.invoiceReferenceAlphaId &&
                            invoice.invoiceReferenceAlphaId.trim() !== '' &&
                            invoice.invoiceId &&
                            invoice.invoiceId.trim() !== ''
                    );

                    return (
                        <div>
                            <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                                {info.getValue()}
                            </Text>
                            {validClearedInvoices &&
                                validClearedInvoices.length > 0 && (
                                    <Tooltip
                                        content={validClearedInvoices
                                            .map(
                                                (invoice: any) =>
                                                    invoice.invoiceReferenceAlphaId
                                            )
                                            .join(', ')}
                                        position="top"
                                    >
                                        <Text className="text-xs !text-blue-400 cursor-pointer">
                                            {validClearedInvoices.length > 2
                                                ? `${validClearedInvoices[0].invoiceReferenceAlphaId}, ${validClearedInvoices[1].invoiceReferenceAlphaId} +${validClearedInvoices.length - 2} more`
                                                : validClearedInvoices
                                                      .map(
                                                          (invoice: any) =>
                                                              invoice.invoiceReferenceAlphaId
                                                      )
                                                      .join(', ')}
                                        </Text>
                                    </Tooltip>
                                )}
                        </div>
                    );
                },
            }),
            columnHelper.accessor('patientName', {
                header: 'Patient',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue() || '-'}
                    </Text>
                ),
            }),
            columnHelper.accessor('paymentMode', {
                header: 'Payment Mode',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue() || '-'}
                    </Text>
                ),
            }),
            // columnHelper.accessor('createdByName', {
            //     header: 'User',
            //     cell: (info) => <Text>{info.getValue() || '-'}</Text>,
            // }),
            columnHelper.accessor('debit', {
                header: () => (
                    <div>
                        <div>Debit</div>
                        {summary && (
                            <div className="text-xs font-normal text-red-600">
                                Total:
                                {summary.totalMonetaryDebits.toLocaleString(
                                    'en-IN',
                                    {
                                        minimumFractionDigits: 2,
                                    }
                                )}
                            </div>
                        )}
                    </div>
                ),
                cell: (info) => {
                    const value = info.getValue();
                    return value > 0 ? (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right text-error-500">
                            {value.toLocaleString('en-IN', {
                                minimumFractionDigits: 2,
                            })}
                        </Text>
                    ) : (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right">
                            -
                        </Text>
                    );
                },
                meta: {
                    tdClassName: 'text-right',
                },
            }),
            columnHelper.accessor('credit', {
                header: () => (
                    <div>
                        <div>Credit</div>
                        {summary && (
                            <div className="text-xs font-normal text-green-600">
                                Total:
                                {summary.totalMonetaryCredits.toLocaleString(
                                    'en-IN',
                                    {
                                        minimumFractionDigits: 2,
                                    }
                                )}
                            </div>
                        )}
                    </div>
                ),
                cell: (info) => {
                    const value = info.getValue();
                    return value > 0 ? (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right text-success-500">
                            {value.toLocaleString('en-IN', {
                                minimumFractionDigits: 2,
                            })}
                        </Text>
                    ) : (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right">
                            -
                        </Text>
                    );
                },
                meta: {
                    tdClassName: 'text-right',
                },
            }),

            columnHelper.accessor('runningBalance', {
                header: () => (
                    <div>
                        <div>Running Balance</div>
                        {summary && (
                            <div
                                className="text-xs font-normal"
                                style={{
                                    color:
                                        summary.finalRunningBalance < 0
                                            ? '#dc2626'
                                            : '#16a34a',
                                }}
                            >
                                Final:
                                {Math.abs(
                                    summary.finalRunningBalance
                                ).toLocaleString('en-IN', {
                                    minimumFractionDigits: 2,
                                })}
                            </div>
                        )}
                    </div>
                ),
                cell: (info) => {
                    const value = info.getValue();
                    return (
                        <Text
                            className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle"
                            style={{
                                color:
                                    value < 0
                                        ? '#dc2626'
                                        : value == 0
                                          ? '#000'
                                          : '#16a34a',
                            }}
                        >
                            {Math.abs(value).toLocaleString('en-IN', {
                                minimumFractionDigits: 2,
                            })}
                        </Text>
                    );
                },
                meta: {
                    tdClassName: 'text-right',
                },
            }),

            columnHelper.accessor('creditChange', {
                header: 'Credit Change',
                cell: (info) => {
                    const value = info.getValue();
                    if (value === undefined || value === null || value === 0)
                        return (
                            <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right">
                                -
                            </Text>
                        );
                    const isPositive = value > 0;
                    return (
                        <Text
                            className={`!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right ${isPositive ? 'text-success-500' : 'text-error-500'}`}
                        >
                            {isPositive ? '+' : ''}
                            {value.toLocaleString('en-IN', {
                                minimumFractionDigits: 2,
                            })}
                        </Text>
                    );
                },
                meta: {
                    tdClassName: 'text-right',
                },
            }),
            columnHelper.accessor('runningCredits', {
                header: () => (
                    <div>
                        <div>Running Credits</div>
                        {summary && (
                            <div className="text-xs font-normal text-success-600">
                                Final:
                                {summary.finalRunningCredits.toLocaleString(
                                    'en-IN',
                                    {
                                        minimumFractionDigits: 2,
                                    }
                                )}
                            </div>
                        )}
                    </div>
                ),
                cell: (info) => {
                    const value = info.getValue();
                    if (value === undefined || value === null)
                        return (
                            <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle text-right">
                                -
                            </Text>
                        );
                    return (
                        <Text
                            className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle"
                            style={{
                                color:
                                    value < 0
                                        ? '#dc2626'
                                        : value == 0
                                          ? '#000'
                                          : '#16a34a',
                            }}
                        >
                            {Math.abs(value).toLocaleString('en-IN', {
                                minimumFractionDigits: 2,
                            })}
                        </Text>
                    );
                },
                meta: {
                    tdClassName: 'text-right',
                },
            }),
        ],

        [selectedRows, summary]
    );

    return (
        <div className="">
            <div className="flex items-center justify-between">
                <div className="flex-1">
                    {/* <Searchbar
                        id="ledger-search"
                        name="ledger-search"
                        placeholder="Search..."
                        onChange={handleSearchChange}
                        className="border" // Note: If InvoicesTab.tsx Searchbar has different styling, this class may need adjustment.
                    /> */}
                </div>
                {/* <div className="flex items-center space-x-2">
                    <Button
                        id="share-statement-btn"
                        variant="primary"
                        size="semiSmall"
                        onClick={handleShareStatement}
                        // disabled={isShareInProgress} // Consider adding a disabled state if applicable
                        onlyIcon
                        className="p-2"
                        aria-label="Share Statement"
                        icon={
                            <IconShare
                                className="h-6 w-6 text-white"
                                fill="white"
                            />
                        }
                    />
                    <Button
                        id="download-statement-btn"
                        variant="primary"
                        size="semiSmall"
                        onClick={handleDownloadStatement}
                        disabled={isDownloadInProgress}
                        onlyIcon
                        className="p-2"
                        aria-label="Download Statement"
                    >
                        {isDownloadInProgress ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <IconDownload className="h-5 w-5" />
                        )}
                    </Button>
                </div> */}
            </div>

            {/* <LedgerFilterBar
                availableUsers={availableUsers}
                initialSelectedUsers={selectedUsers}
                initialStartDate={selectedDateRange.start}
                initialEndDate={selectedDateRange.end}
                onFilterChange={(filters) => {
                    setSelectedUsers(filters.selectedUsers);
                    setSelectedDateRange(filters.dateRange);
                }}
                onClearAll={() => {
                    setSelectedUsers([]);
                    setSelectedDateRange({ start: null, end: null });
                }}
            /> */}

            {/* <div className="h-[calc(100vh-320px)]"> */}
            <InfiniteScrollTable
                columns={columns as any} // Adjust type as necessary
                tableData={ledgerData.map((item) => ({
                    ...item,
                    customTableWrapperClass: '',
                    rowBorder: true,
                    headerSticky: true,
                }))} // Ensure data matches WithSelection
                listLoadStatus={isLoadingData ? 'pending' : 'success'}
                variant="small"
                customTableWrapperClass=""
                headerSticky={true}
                className="!h-[calc(100vh-160px)]"
                loadMoreData={
                    onLoadMore ? async () => onLoadMore() : async () => {}
                } // Adjust onLoadMore
                hasMore={hasMore || false}
                // handleTableRowClick={handleTableRowClick} // Add if needed
                loadingIndicator={<div className="hidden"></div>}
                emptyTableMessage="No ledger entries found"
                subEmptyTableMessage="Clear any filters and confirm the selected owner."
                emptyStateImageSrc="/images/dog-with-paper-mouth.png" // Or a more relevant image
                toShowEmptyState={!isLoadingData} // Only show empty state when not loading
            />
            {/* </div> */}

            {/* Modals */}
            <Modal
                isOpen={isShareModal}
                onClose={() => setIsShareModal(false)}
                modalTitle="Share Statement"
            >
                <div className="flex flex-col gap-4 p-4">
                    <div className="flex gap-2">
                        <Button
                            id="share-email-btn"
                            variant="primary"
                            onClick={() =>
                                handleShareStatementConfirm('email', 'client')
                            }
                        >
                            Share via Email
                        </Button>
                        <Button
                            id="share-whatsapp-btn"
                            variant="secondary"
                            onClick={() =>
                                handleShareStatementConfirm(
                                    'whatsapp',
                                    'client'
                                )
                            }
                        >
                            Share via WhatsApp
                        </Button>
                    </div>
                </div>
            </Modal>

            <StatementShareSuccessModal
                isOpen={isStatementShareSuccessModal}
                onClose={() => setIsStatementShareSuccessModal(false)}
                shareMethod={statementShareMethod}
                statementType="payment-detail"
            />

            <StatementDownloadSuccessModal
                isOpen={isStatementDownloadSuccessModal}
                onClose={() => setIsStatementDownloadSuccessModal(false)}
                statementType="payment-detail"
            />

            <Modal
                isOpen={openNotFoundModal}
                onClose={() => setOpenNotFoundModal(false)}
                modalTitle="Document Not Found"
            >
                <div className="p-4">
                    <Text>
                        The requested document could not be found or generated.
                        Please try again later.
                    </Text>
                </div>
            </Modal>
        </div>
    );
};

export default LedgerTab;
