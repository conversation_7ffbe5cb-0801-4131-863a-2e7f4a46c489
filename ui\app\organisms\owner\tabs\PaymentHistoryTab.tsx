import React, {
    useMemo,
    useState,
    useEffect,
    useRef,
    useCallback,
} from 'react';

import InfiniteScrollTable from '@/app/molecules/InfiniteScrollTable';
import { Button, Text, Tags, Checkbox } from '@/app/atoms';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { <PERSON><PERSON>, Tooltip, Datepicker } from '@/app/molecules';

import {
    Download,
    Share2,
    Loader2,
    CircleAlert,
    ChevronDown,
} from 'lucide-react';
import IconShare from '@/app/atoms/customIcons/IconShare';
import IconDownload from '@/app/atoms/customIcons/IconDownload.svg';
import IconFilter from '@/app/atoms/customIcons/IconFilter.svg';
import IconActiveFilter from '@/app/atoms/customIcons/IconActiveFilter.svg';
import { usePaymentDocumentMutation } from '@/app/services/payment-details.queries';
import { useEMRMutation } from '@/app/services/emr.queries';
import { useStatementDocumentMutation } from '@/app/services/statement.queries';
import StatementShareSuccessModal from '@/app/organisms/StatementShareSuccessModal';
import StatementDownloadSuccessModal from '@/app/organisms/StatementDownloadSuccessModal';
import ShareMultipleDocumentsModal from '@/app/organisms/ShareMultipleDocumentsModal';
import FileShareSuccessModal from '@/app/organisms/FileShareSuccessModal';
import NotFoundModal from '@/app/organisms/NotFoundModal';
import Modal from '@/app/molecules/Modal';
import moment from 'moment';
import { CREDIT_TYPES } from '@/app/utils/constant';
import InvoiceBasicDetail from '@/app/molecules/cart/InvoiceBasicDetail';
import PaymentDetailsView from '../PaymentDetailsView';
import PaymentHistoryFilterBar, {
    User,
    PaymentMethod,
} from '@/app/molecules/PaymentHistoryFilterBar';

// Helper function to format currency
const formatCurrency = (amount: number | string | undefined): string => {
    if (!amount) return '₹0';
    const numericAmount =
        typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(numericAmount);
};

interface PaymentDetailsFilter {
    searchTerm: string;
    startDate: string;
    endDate: string;
    petName: string;
    paymentMode: string;
    paymentType: string;
    userIds: string;
    page: number;
    limit: number;
}

interface UserFilterItem {
    id: string;
    name: string;
}

interface PaymentMethodFilterItem {
    id: string;
    label: string;
}

interface PaymentDetailsWithTableProps extends WithSelection {
    isHighlight?: 'active' | string;
    customTableWrapperClass: string;
    tableWrapper?: string;
    rowBorder?: boolean;
    headerSticky?: boolean;
}

interface PaymentHistoryTabProps {
    paymentDetailsData: any[];
    ownerId: string;
    filters: PaymentDetailsFilter;
    onFilterChange: (filters: PaymentDetailsFilter) => void;
    totalCount: number;
    isLoading: boolean;
    onLoadMore: () => void;
    hasMore: boolean;
    availableUsers?: UserFilterItem[];
    onShowPaymentDetailViewRequest?: (payment: any) => void;
}

interface WithSelection {
    id: string;
    amount?: number;
    type?: string;
    paymentType?: string;
    createdAt?: string;
    createdByName?: string;
    referenceAlphaId?: string;
    patientId?: string;
    patientName?: string;
    isSelected?: boolean;
    formattedDate?: string;
    relatedPayments?: any[];
    [key: string]: any;
}

const AVAILABLE_PAYMENT_METHODS: PaymentMethodFilterItem[] = [
    { id: 'Cash', label: 'Cash' },
    { id: 'Card', label: 'Card' },
    { id: 'Cheque', label: 'Cheque' },
    { id: 'Wallet', label: 'Wallet' },
    { id: 'Credits', label: 'Credits' },
    { id: 'BankTransfer', label: 'Bank Transfer' },
];

const PaymentHistoryTab: React.FC<PaymentHistoryTabProps> = ({
    paymentDetailsData,
    ownerId,
    onFilterChange,
    totalCount,
    isLoading,
    onLoadMore,
    hasMore,
    filters,
    availableUsers = [],
    onShowPaymentDetailViewRequest,
}) => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
    const [selectAll, setSelectAll] = useState(false);
    const [isShareModal, setIsShareModal] = useState(false);
    const [isFileShareSuccessModal, setIsFileShareSuccessModal] =
        useState(false);
    const [openNotFoundModal, setOpenNotFoundModal] = useState(false);
    const [isActionLoading, setIsActionLoading] = useState(false);

    const [selectedPayment, setSelectedPayment] = useState<any | null>(null);
    const [localSearchTerm, setLocalSearchTerm] = useState(
        filters.searchTerm || ''
    );
    const [showDetailView, setShowDetailView] = useState(false);
    const [isDownloadInProgress, setIsDownloadInProgress] = useState(false);
    const [isStatementShare, setIsStatementShare] = useState(false);
    const [isStatementShareSuccessModal, setIsStatementShareSuccessModal] =
        useState(false);
    const [statementShareMethod, setStatementShareMethod] = useState<
        'email' | 'whatsapp' | 'both'
    >('both');
    const [statementRecipient, setStatementRecipient] = useState<
        'client' | 'other'
    >('client');
    const [
        isStatementDownloadSuccessModal,
        setIsStatementDownloadSuccessModal,
    ] = useState(false);
    const tableWrapperRef = useRef<HTMLDivElement>(null);
    const observerRef = useRef<IntersectionObserver | null>(null);
    const loadMoreRef = useRef<HTMLDivElement>(null);
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Initialize search term from filters
    const [searchTerm, setSearchTerm] = useState(filters.searchTerm || '');

    // Initialize selected users from filters.userIds
    const [selectedUsers, setSelectedUsersState] = useState<UserFilterItem[]>(
        filters.userIds && availableUsers
            ? availableUsers.filter((user) =>
                  filters.userIds.split(',').includes(user.id)
              )
            : []
    );

    // Initialize selected payment methods from filters.paymentMode
    const [selectedPaymentMethods, setSelectedPaymentMethodsState] = useState<
        PaymentMethodFilterItem[]
    >(
        filters.paymentMode
            ? AVAILABLE_PAYMENT_METHODS.filter((method) =>
                  filters.paymentMode.split(',').includes(method.id)
              )
            : []
    );

    // Initialize date range from filters
    const [selectedDateRange, setSelectedDateRangeState] = useState<{
        start: Date | null;
        end: Date | null;
    }>({
        start: filters.startDate ? new Date(filters.startDate) : null,
        end: filters.endDate ? new Date(filters.endDate) : null,
    });

    // Filter bar visibility state
    const [showFilterBar, setShowFilterBar] = useState(false);

    // Check if any filters are active
    const hasActiveFilters = useMemo(() => {
        return (
            selectedUsers.length > 0 ||
            selectedPaymentMethods.length > 0 ||
            selectedDateRange.start !== null ||
            selectedDateRange.end !== null
        );
    }, [selectedUsers, selectedPaymentMethods, selectedDateRange]);

    // Toggle filter bar visibility
    const toggleFilterBar = () => {
        setShowFilterBar(!showFilterBar);
    };

    const localAvailablePaymentMethods = AVAILABLE_PAYMENT_METHODS;
    const localAvailableUsers = useMemo(() => {
        return availableUsers || [];
    }, [availableUsers]);

    const selectedPaymentsTotalAmount = useMemo(() => {
        return Array.from(selectedRows).reduce((total, rowId) => {
            const payment = paymentDetailsData.find((p) => p.id === rowId);
            return total + (payment?.amount || 0);
        }, 0);
    }, [selectedRows, paymentDetailsData]);

    // Get payment document mutation hook
    const { paymentDocumentMutation } = usePaymentDocumentMutation();
    // Instantiate EMR mutations for payment receipts
    const { storePaymentReceiptsMutation, sharePaymentReceiptsMutation } =
        useEMRMutation();
    // Get statement document mutation hook
    const { requestStatementDocumentsMutation } =
        useStatementDocumentMutation();

    const columnHelper = createColumnHelper<PaymentDetailsWithTableProps>();

    // Clean up search timeout on unmount
    useEffect(() => {
        return () => {
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
        };
    }, []);

    const handleSearchInputChange = useCallback(
        (value: string) => {
            setSearchTerm(value);

            // Clear any existing timeout
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }

            // Set a new timeout for debouncing
            searchTimeoutRef.current = setTimeout(() => {
                const startDate = selectedDateRange.start
                    ? selectedDateRange.start.toISOString().split('T')[0]
                    : undefined;
                const endDate = selectedDateRange.end
                    ? selectedDateRange.end.toISOString().split('T')[0]
                    : undefined;

                const userIds =
                    selectedUsers.length > 0
                        ? selectedUsers.map((user) => user.id).join(',')
                        : undefined;

                const paymentMethodIds =
                    selectedPaymentMethods.length > 0
                        ? selectedPaymentMethods.map((pm) => pm.id).join(',')
                        : undefined;

                onFilterChange({
                    searchTerm: value,
                    startDate: startDate || '',
                    endDate: endDate || '',
                    userIds: userIds || '',
                    paymentMode: paymentMethodIds || '',
                    paymentType: '',
                    petName: '',
                    page: 1,
                    limit: filters.limit || 20,
                });
            }, 500); // 500ms debounce
        },
        [
            selectedDateRange,
            selectedUsers,
            selectedPaymentMethods,
            onFilterChange,
        ]
    );

    const handleFilterBarChange = (filters: {
        selectedUsers: UserFilterItem[];
        selectedPaymentMethods: PaymentMethodFilterItem[];
        dateRange: { start: Date | null; end: Date | null };
    }) => {
        setSelectedUsersState(filters.selectedUsers);
        setSelectedPaymentMethodsState(filters.selectedPaymentMethods);
        setSelectedDateRangeState(filters.dateRange);

        // Apply filters immediately
        const startDate = filters.dateRange.start
            ? filters.dateRange.start.toISOString().split('T')[0]
            : undefined;
        const endDate = filters.dateRange.end
            ? filters.dateRange.end.toISOString().split('T')[0]
            : undefined;

        const userIds =
            filters.selectedUsers.length > 0
                ? filters.selectedUsers.map((user) => user.id).join(',')
                : undefined;

        const paymentMethodIds =
            filters.selectedPaymentMethods.length > 0
                ? filters.selectedPaymentMethods.map((pm) => pm.id).join(',')
                : undefined;

        onFilterChange({
            searchTerm: searchTerm,
            startDate: startDate || '',
            endDate: endDate || '',
            userIds: userIds || '',
            paymentMode: paymentMethodIds || '',
            paymentType: '',
            petName: '',
            page: 1,
            limit: 20,
        });
    };

    const handleClearAllFiltersFromBar = () => {
        setSearchTerm('');
        setSelectedUsersState([]);
        setSelectedPaymentMethodsState([]);
        setSelectedDateRangeState({ start: null, end: null });

        // Apply cleared filters immediately
        onFilterChange({
            searchTerm: '',
            startDate: '',
            endDate: '',
            userIds: '',
            petName: '',
            paymentType: '',
            paymentMode: '',
            page: 1,
            limit: 20,
        });
    };

    useEffect(() => {
        const handleIntersection = (entries: IntersectionObserverEntry[]) => {
            const [entry] = entries;
            if (entry.isIntersecting && hasMore && !isLoading) {
                onLoadMore();
            }
        };

        const options = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1,
        };

        if (loadMoreRef.current) {
            observerRef.current = new IntersectionObserver(
                handleIntersection,
                options
            );
            observerRef.current.observe(loadMoreRef.current);
        }

        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }

            // Clear any pending search timeouts
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
        };
    }, [hasMore, isLoading, onLoadMore]);

    useEffect(() => {
        const checkIfScrollNeeded = () => {
            if (tableWrapperRef.current) {
                const wrapperHeight = tableWrapperRef.current.clientHeight;
                const contentHeight = tableWrapperRef.current.scrollHeight;

                // If we have space to scroll and we have more items
                if (contentHeight <= wrapperHeight && hasMore && !isLoading) {
                    onLoadMore();
                }
            }
        };

        // Check after render and whenever the data changes
        checkIfScrollNeeded();

        // Add resize listener to check when window size changes
        window.addEventListener('resize', checkIfScrollNeeded);

        return () => {
            window.removeEventListener('resize', checkIfScrollNeeded);
        };
    }, [paymentDetailsData, hasMore, isLoading, onLoadMore]);

    const tableData = useMemo(() => {
        return paymentDetailsData.map((payment: any) => {
            let patientNameDisplay = payment.patient?.patientName || 'N/A';
            let fullPatientList = patientNameDisplay; // For tooltip

            // If it's a bulk reconcile invoice with related payments, aggregate pet names
            if (
                payment.type === CREDIT_TYPES.BulkReconcileInvoice &&
                payment.relatedPayments?.length > 0
            ) {
                const uniquePetNamesArray = Array.from(
                    new Set(
                        payment.relatedPayments
                            .filter((p: any) => p.patient?.patientName)
                            .map((p: any) => p.patient.patientName)
                    )
                );

                if (uniquePetNamesArray.length > 0) {
                    fullPatientList = uniquePetNamesArray.join(', ');
                    if (uniquePetNamesArray.length > 2) {
                        patientNameDisplay = `${uniquePetNamesArray
                            .slice(0, 2)
                            .join(', ')}, +${uniquePetNamesArray.length - 2}`;
                    } else {
                        patientNameDisplay = uniquePetNamesArray.join(', ');
                    }
                }
            }

            return {
                ...payment,
                patientName: patientNameDisplay, // This will be used for display
                fullPatientList, // This will be used for the tooltip
                formattedDate: payment.createdAt
                    ? moment(payment.createdAt).format('DD MMM YYYY')
                    : '-',
                isHighlight: undefined,
                customTableWrapperClass: '',
                tableWrapper: undefined,
                rowBorder: undefined,
                headerSticky: undefined,
            };
        });
    }, [paymentDetailsData]);

    const handleRowSelect = (id: string) => {
        const newSelected = new Set(selectedRows);
        if (newSelected.has(id)) {
            newSelected.delete(id);
        } else {
            newSelected.add(id);
        }
        setSelectedRows(newSelected);
        setSelectAll(newSelected.size === paymentDetailsData.length);
    };

    const handleTableRowClick = (row: any) => {
        onShowPaymentDetailViewRequest?.(row.original);
    };

    const handleSelectAll = () => {
        if (selectAll) {
            setSelectedRows(new Set());
        } else {
            setSelectedRows(
                new Set(paymentDetailsData.map((payment) => payment.id))
            );
        }
        setSelectAll(!selectAll);
    };

    const handleDownload = (payment: PaymentDetailsWithTableProps) => {
        setIsActionLoading(true);
        setIsDownloadInProgress(true);

        const referenceAlphaId = payment.referenceAlphaId || '';

        if (!referenceAlphaId) {
            setIsActionLoading(false);
            setIsDownloadInProgress(false);
            setOpenNotFoundModal(true);
            return;
        }

        paymentDocumentMutation.mutate(
            {
                referenceAlphaId,
                documentType: 'payment-details',
                action: 'download',
                patientId: payment.patientId || '',
            },
            {
                onSettled: () => {
                    setIsActionLoading(false);
                },
                onError: () => {
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    const handleDownloadSelected = () => {
        if (selectedRows.size === 0) {
            console.log('No payments selected for download.');
            return;
        }

        setIsActionLoading(true);
        setIsDownloadInProgress(true);

        const selectedPaymentReferenceIds = Array.from(selectedRows)
            .map((id) => {
                const payment = paymentDetailsData.find((p) => p.id === id);
                return payment?.referenceAlphaId;
            })
            .filter((refId) => !!refId) as string[];

        if (selectedPaymentReferenceIds.length === 0) {
            console.warn(
                'No valid referenceAlphaIds found for selected payments.'
            );
            setIsActionLoading(false);
            setIsDownloadInProgress(false);
            setOpenNotFoundModal(true);
            return;
        }

        storePaymentReceiptsMutation.mutate(
            {
                paymentReferenceIds: selectedPaymentReferenceIds,
                patientId: ownerId,
            },
            {
                onSuccess: (response) => {
                    console.log(
                        'Store payment receipts request successful:',
                        response
                    );
                    // Polling will be initiated by the mutation's onSuccess handler in emr.queries.ts
                    // Potentially show a notification that download is being prepared.
                    setIsActionLoading(false);
                    setSelectedRows(new Set());
                    setSelectAll(false);
                },
                onError: (error) => {
                    console.error('Failed to store payment receipts:', error);
                    setIsActionLoading(false);
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    const handleShare = (payment: PaymentDetailsWithTableProps) => {
        setSelectedPayment(payment);
        setIsShareModal(true);
    };

    const handleShareSelected = () => {
        if (selectedRows.size === 0) {
            console.log('No payments selected for sharing.');
            return;
        }
        // For bulk sharing, just open the modal.
        // The modal will then call handleShareDocument which will use selectedRows.
        setIsShareModal(true);
    };

    // Helper function to get current filter parameters
    const getCurrentFilterParams = () => {
        const hasFilters =
            searchTerm ||
            selectedUsers.length > 0 ||
            selectedPaymentMethods.length > 0 ||
            selectedDateRange.start ||
            selectedDateRange.end;

        if (!hasFilters) return undefined;

        return {
            searchTerm: searchTerm || undefined,
            userId:
                selectedUsers.length > 0
                    ? selectedUsers.map((user) => user.id).join(',')
                    : undefined,
            paymentMode:
                selectedPaymentMethods.length > 0
                    ? selectedPaymentMethods.map((pm) => pm.id).join(',')
                    : undefined,
            startDate: selectedDateRange.start
                ? selectedDateRange.start.toISOString().split('T')[0]
                : undefined,
            endDate: selectedDateRange.end
                ? selectedDateRange.end.toISOString().split('T')[0]
                : undefined,
        };
    };

    // Handle download for statement document with 4-scenario logic
    const handleDownloadStatementSelected = () => {
        if (!ownerId) return;

        const filterParams = getCurrentFilterParams();
        const hasSelectedRows = selectedRows.size > 0;

        // Determine download logic based on selections and filters
        let statementIds: string[] | undefined;

        if (hasSelectedRows) {
            // Scenario 3 & 4: Specific statements selected (filters don't matter)
            statementIds = Array.from(selectedRows)
                .map((id) => {
                    const payment = paymentDetailsData.find((p) => p.id === id);
                    return payment?.referenceAlphaId;
                })
                .filter((refId) => !!refId) as string[];

            if (statementIds.length === 0) {
                setOpenNotFoundModal(true);
                return;
            }
        } else if (!filterParams) {
            // Scenario 1: Nothing selected + No filters = Download ALL
            statementIds = ['ALL_STATEMENTS'];
        } else {
            // Scenario 2: Nothing selected + Filters applied = Download filtered
            // statementIds will be undefined to let the API handle filtering
            statementIds = undefined;
        }

        // Show download success modal
        setIsStatementDownloadSuccessModal(true);

        // Use the statement document mutation
        requestStatementDocumentsMutation.mutate(
            {
                ownerId,
                types: ['payment-detail'], // Payment detail type for payment history
                action: 'DOWNLOAD',
                statementIds,
                filters: hasSelectedRows ? undefined : filterParams, // Only use filters when no selection
            },
            {
                onSuccess: (response) => {
                    console.log('Statement download started successfully', {
                        requestId: response?.data?.requestId,
                        scenario: hasSelectedRows
                            ? `Selected ${selectedRows.size} payments`
                            : filterParams
                              ? 'Filtered data'
                              : 'All data',
                        filters: hasSelectedRows ? undefined : filterParams,
                        statementIds: hasSelectedRows
                            ? statementIds
                            : undefined,
                    });
                    // Keep the success modal open for user to see
                    // It will be closed by the user
                },
                onError: (error) => {
                    console.error(
                        'Error initiating statement download:',
                        error
                    );
                    setIsStatementDownloadSuccessModal(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Handle share for statement document
    const handleShareStatementSelected = () => {
        if (!ownerId) return;

        // Set statement share flag and open share modal
        setIsStatementShare(true);
        setIsShareModal(true);
    };
    const handleLoadMoreForInfiniteScroll = async () => {
        if (onLoadMore && !isLoading && hasMore) {
            onLoadMore();
        }
        return Promise.resolve();
    };
    const handleShareDocument = async (data: any) => {
        setIsActionLoading(true);

        let shareMethod: 'email' | 'whatsapp' | 'both' = 'both';
        if (data.shareViaEmail && !data.shareViaWhatsapp) shareMethod = 'email';
        if (!data.shareViaEmail && data.shareViaWhatsapp)
            shareMethod = 'whatsapp';

        // For custom recipient, get email and phone number from form
        const emailValue =
            data.recipient === 'other' && data.shareViaEmail ? data.email : '';
        const phoneValue =
            data.recipient === 'other' && data.shareViaWhatsapp
                ? data.number
                : '';

        // Prepare share mode array
        const shareMode: string[] = [];
        if (data.shareViaEmail) shareMode.push('email');
        if (data.shareViaWhatsapp) shareMode.push('whatsapp');

        // Check if we're sharing as a statement
        if (isStatementShare && ownerId) {
            const filterParams = getCurrentFilterParams();
            const hasSelectedRows = selectedRows.size > 0;

            // Determine share logic based on selections and filters
            let statementIds: string[] | undefined;

            if (hasSelectedRows) {
                // Scenario 3 & 4: Specific statements selected (filters don't matter)
                statementIds = Array.from(selectedRows)
                    .map((id) => {
                        const payment = paymentDetailsData.find(
                            (p) => p.id === id
                        );
                        return payment?.referenceAlphaId;
                    })
                    .filter((refId) => !!refId) as string[];

                if (statementIds.length === 0) {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setIsStatementShare(false);
                    setOpenNotFoundModal(true);
                    return;
                }
            } else if (!filterParams) {
                // Scenario 1: Nothing selected + No filters = Share ALL
                statementIds = ['ALL_STATEMENTS'];
            } else {
                // Scenario 2: Nothing selected + Filters applied = Share filtered
                // statementIds will be undefined to let the API handle filtering
                statementIds = undefined;
            }

            // Use statement document mutation for statement sharing
            const response =
                await requestStatementDocumentsMutation.mutateAsync({
                    ownerId,
                    types: ['payment-detail'], // Payment detail type for payment history
                    action: 'SHARE',
                    shareMethod:
                        shareMode.length === 2
                            ? 'both'
                            : (shareMode[0] as 'email' | 'whatsapp'),
                    recipient: data.recipient,
                    email: emailValue,
                    phoneNumber: phoneValue,
                    statementIds,
                    filters: hasSelectedRows ? undefined : filterParams, // Only use filters when no selection
                });

            if (response?.status) {
                // Set the share method based on user selection
                if (shareMode.length === 2) {
                    setStatementShareMethod('both');
                } else if (shareMode.includes('email')) {
                    setStatementShareMethod('email');
                } else if (shareMode.includes('whatsapp')) {
                    setStatementShareMethod('whatsapp');
                }

                // Set the recipient
                setStatementRecipient(data.recipient as 'client' | 'other');

                setIsActionLoading(false);
                setIsShareModal(false);
                setIsStatementShare(false); // Reset statement share flag
                setIsStatementShareSuccessModal(true);
            } else {
                setIsActionLoading(false);
                setIsShareModal(false);
                setIsStatementShare(false); // Reset statement share flag
                setOpenNotFoundModal(true);
            }
            return;
        }

        // If we have a single selected payment (from handleShare)
        if (selectedPayment && !selectedRows.size) {
            const referenceAlphaId = selectedPayment.referenceAlphaId || '';

            if (!referenceAlphaId) {
                setIsActionLoading(false);
                setIsShareModal(false);
                return;
            }

            // Use paymentDocumentMutation for individual share
            paymentDocumentMutation.mutate(
                {
                    referenceAlphaId,
                    documentType: 'payment-details',
                    action: 'share',
                    patientId: selectedPayment.patientId || '',
                    shareMethod,
                    recipient: data.recipient,
                    email:
                        data.recipient === 'other' && data.shareViaEmail
                            ? data.email
                            : undefined,
                    whatsapp:
                        data.recipient === 'other' && data.shareViaWhatsapp
                            ? data.number
                            : undefined,
                },
                {
                    onSuccess: () => {
                        setIsActionLoading(false);
                        setIsShareModal(false);
                        setIsFileShareSuccessModal(true);
                    },
                    onError: () => {
                        setIsActionLoading(false);
                        setIsShareModal(false);
                        setOpenNotFoundModal(true);
                    },
                }
            );
            return;
        }

        // For bulk sharing (multiple payments selected)
        if (selectedRows.size === 0) {
            console.log(
                'No payments selected for sharing in handleShareDocument.'
            );
            setIsShareModal(false);
            return;
        }

        const selectedPaymentReferenceIds = Array.from(selectedRows)
            .map((id) => {
                const payment = paymentDetailsData.find((p) => p.id === id);
                return payment?.referenceAlphaId;
            })
            .filter((refId) => !!refId) as string[];

        if (selectedPaymentReferenceIds.length === 0) {
            console.warn(
                'No valid referenceAlphaIds found for selected payments to share.'
            );
            setIsActionLoading(false);
            setIsShareModal(false);
        }

        if (shareMode.length === 0) {
            console.warn('No share mode selected.');
            setIsActionLoading(false);
            setIsShareModal(false);
            return;
        }

        sharePaymentReceiptsMutation.mutate(
            {
                paymentReferenceIds: selectedPaymentReferenceIds,
                patientId: ownerId, // Using ownerId as patientId
                shareMode,
                type: data.recipient,
                email:
                    data.recipient === 'other' && data.shareViaEmail
                        ? data.email
                        : '',
                phoneNumber:
                    data.recipient === 'other' && data.shareViaWhatsapp
                        ? data.number
                        : '',
            },
            {
                onSuccess: (response) => {
                    console.log(
                        'Share payment receipts request successful:',
                        response
                    );
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setSelectedRows(new Set()); // Clear selection
                    setSelectAll(false);
                },
                onError: (error) => {
                    console.error('Failed to share payment receipts:', error);
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true); // Show error
                },
            }
        );
    };

    // Reset filters
    const handleResetFilters = () => {
        onFilterChange({
            searchTerm: '',
            startDate: '',
            endDate: '',
            paymentMode: '',
            petName: '',
            paymentType: '',
            userIds: '',
            page: 1,
            limit: 20,
        });
        setLocalSearchTerm('');
    };

    // Payment type options
    const paymentTypeOptions = [
        { value: '', label: 'All Types' },
        { value: 'Invoice', label: 'Invoice' },
        { value: 'Credit Note', label: 'Credit Note' },
        { value: 'Collect', label: 'Collect' },
        { value: 'Return', label: 'Return' },
        { value: 'ReconcileInvoice', label: 'Reconcile Invoice' },
        { value: 'BulkReconcileInvoice', label: 'Bulk Reconcile Invoice' },
    ];

    const columns = useMemo(
        () => [
            columnHelper.display({
                id: 'select',
                header: () => (
                    <div className="flex justify-center">
                        <Tags
                            label={selectAll ? 'Deselect All' : 'Select All'}
                            onClick={handleSelectAll}
                            variant="neutral"
                            size="small"
                            shape="rounded"
                            isLight
                            className="hover:bg-neutral-100 cursor-pointer transition-colors"
                        />
                    </div>
                ),
                cell: ({ row }) => (
                    <div
                        className="flex justify-center"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <Checkbox
                            checked={selectedRows.has(row.original.id)}
                            size="medium"
                            onChange={() => handleRowSelect(row.original.id)}
                            aria-label={`Select payment ${row.original.referenceAlphaId}`}
                        />
                    </div>
                ),
                meta: {
                    thAlign: 'text-center',
                    thClassName: 'w-min',
                    tdClassName: 'w-min cursor-pointer', // Added cursor-pointer
                },
            }),
            columnHelper.accessor('referenceAlphaId', {
                header: 'Reference ID',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        #{info.getValue() || '-'}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('type', {
                header: 'Type',
                cell: (info) => {
                    return (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                            {info.getValue()}
                        </Text>
                    );
                },
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('formattedDate', {
                header: 'Date',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    tdClassName: 'whitespace-nowrap cursor-pointer', // Added cursor-pointer
                },
            }),
            columnHelper.accessor('patientName', {
                header: 'Patient',
                cell: (info) => {
                    const rowData = info.row.original;
                    const displayValue = info.getValue();
                    const fullList = rowData.fullPatientList; // Get the full list

                    // Check if the display value was truncated (contains "+")
                    if (
                        typeof displayValue === 'string' &&
                        displayValue.includes('+')
                    ) {
                        return (
                            <Tooltip content={fullList} position="top">
                                <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                                    {displayValue}
                                </Text>
                            </Tooltip>
                        );
                    }
                    return (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                            {displayValue}
                        </Text>
                    );
                },
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('paymentType', {
                header: 'Payment Mode',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue() || '-'}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('createdByName', {
                header: 'Created By',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue() || '-'}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('amount', {
                header: 'Amount',
                cell: (info) => {
                    const row = info.row.original;
                    const amount = row.amount || 0;
                    const creditUsed = row.creditAmountUsed || 0;
                    const creditAdded = row.creditAmountAdded || 0;

                    return (
                        <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                            ₹{amount + creditUsed}
                        </Text>
                    );
                },
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('id', {
                id: 'actions',
                header: 'Actions',
                meta: {
                    thClassName: 'w-[10%] text-center',
                    tdClassName:
                        'w-[10%] group-hover:bg-others-100 hover:bg-neutral-50 cursor-pointer',
                    thAlign: 'text-center',
                    actionOptions: [
                        { id: 'download', label: 'Download' },
                        { id: 'share', label: 'Share' },
                    ],
                    onActionClick: ({
                        row,
                        action,
                    }: {
                        row: { original: PaymentDetailsWithTableProps };
                        action: { id: string };
                    }) => {
                        switch (action.id) {
                            case 'download':
                                handleDownload(row.original);
                                break;
                            case 'share':
                                handleShare(row.original);
                                break;
                            default:
                                break;
                        }
                    },
                },
            }),
        ],
        [
            selectAll,
            selectedRows,
            isActionLoading,
            handleDownload,
            handleShare,
            handleSelectAll,
            handleRowSelect,
        ]
    );

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center w-full space-x-2">
                    <div className="relative">
                        <Button
                            id="filter-toggle-button"
                            variant="secondary"
                            size="small"
                            onClick={toggleFilterBar}
                            className={`p-2 relative ${
                                hasActiveFilters && !showFilterBar
                                    ? 'bg-transparent hover:bg-transparent'
                                    : hasActiveFilters
                                      ? '!bg-primary-900 !text-white hover:!bg-primary-900'
                                      : 'bg-primary-50 text-neutral-900'
                            }`}
                            icon={
                                hasActiveFilters && !showFilterBar ? (
                                    <IconActiveFilter className="h-10 w-10" />
                                ) : (
                                    <IconFilter
                                        className={`h-5 w-5 ${
                                            hasActiveFilters
                                                ? 'text-white'
                                                : showFilterBar
                                                  ? 'text-primary-900'
                                                  : ''
                                        }`}
                                    />
                                )
                            }
                            onlyIcon
                        />
                    </div>
                    <div className="flex-1">
                        <Searchbar
                            id="payment-search"
                            name="payment-search"
                            placeholder="Search..."
                            onChange={handleSearchInputChange}
                            className="border w-full"
                        />
                    </div>
                </div>
                <div className="flex items-center ml-4 gap-2">
                    {/* Statement buttons - always visible for 4-scenario logic */}
                    {/* DEBUG: selectedRows.size = {selectedRows.size}, hasActiveFilters = {hasActiveFilters.toString()} */}
                    <div
                        className="flex flex-col"
                        style={{ display: 'flex', visibility: 'visible' }}
                    >
                        <div
                            className="flex items-center space-x-2"
                            style={{ display: 'flex', visibility: 'visible' }}
                        >
                            <Tooltip
                                content={
                                    selectedRows.size > 0
                                        ? `Share selected ${selectedRows.size} payments`
                                        : hasActiveFilters
                                          ? 'Share filtered payment history'
                                          : 'Share all payment history'
                                }
                                position="top"
                            >
                                <Button
                                    id="share-statement-button"
                                    variant="primary"
                                    size="semiSmall"
                                    onClick={handleShareStatementSelected}
                                    className="p-2"
                                    icon={
                                        <IconShare
                                            className="h-6 w-6 text-white"
                                            fill="white"
                                        />
                                    }
                                    onlyIcon
                                />
                            </Tooltip>
                            <Tooltip
                                content={
                                    selectedRows.size > 0
                                        ? `Download selected ${selectedRows.size} payments`
                                        : hasActiveFilters
                                          ? 'Download filtered payment history'
                                          : 'Download all payment history'
                                }
                                position="top"
                            >
                                <Button
                                    id="download-statement-button"
                                    variant="primary"
                                    size="semiSmall"
                                    onClick={handleDownloadStatementSelected}
                                    className="p-2"
                                    disabled={isStatementDownloadSuccessModal}
                                    icon={
                                        isStatementDownloadSuccessModal ? (
                                            <Loader2 className="h-5 w-5 animate-spin" />
                                        ) : (
                                            <IconDownload className="h-5 w-5" />
                                        )
                                    }
                                    onlyIcon
                                />
                            </Tooltip>
                        </div>
                    </div>

                    {/* Commented out ledger download/share buttons for selected rows
                {selectedRows.size > 0 ? (
                    <>
                        <Button
                            id="download-selected"
                            variant="secondary"
                            size="small"
                            onClick={handleDownloadSelected}
                            disabled={isActionLoading}
                        >
                            {isActionLoading ? (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                                <Download className="h-4 w-4 mr-2" />
                            )}
                            Download ({selectedRows.size})
                        </Button>
                        <Button
                            id="share-selected"
                            variant="secondary"
                            size="small"
                            onClick={handleShareSelected}
                        >
                            <Share2 className="h-4 w-4 mr-2" />
                            Share ({selectedRows.size})
                        </Button>
                    </>
                ) : null}
                */}
                </div>
            </div>

            {showFilterBar && (
                <PaymentHistoryFilterBar
                    availableUsers={localAvailableUsers.map((u) => ({
                        id: u.id,
                        name: u.name,
                    }))}
                    availablePaymentMethods={localAvailablePaymentMethods}
                    initialSelectedUsers={selectedUsers}
                    initialSelectedPaymentMethods={selectedPaymentMethods}
                    initialStartDate={selectedDateRange.start}
                    initialEndDate={selectedDateRange.end}
                    onFilterChange={handleFilterBarChange}
                    onClearAll={handleClearAllFiltersFromBar}
                />
            )}

            <div
                className={
                    showFilterBar
                        ? 'h-[calc(100vh-344px)]'
                        : 'h-[calc(100vh-280px)]'
                }
            >
                <InfiniteScrollTable
                    columns={columns}
                    tableData={tableData}
                    listLoadStatus={isLoading ? 'pending' : 'success'}
                    customTableWrapperClass=""
                    variant="small"
                    handleTableRowClick={handleTableRowClick}
                    headerSticky={true}
                    loadMoreData={handleLoadMoreForInfiniteScroll}
                    hasMore={hasMore}
                    emptyTableMessage="No payment history found"
                    subEmptyTableMessage="Clear any filters and confirm the selected owner."
                    emptyStateImageSrc="/images/dog-with-paper-mouth.png"
                    customHeight={
                        showFilterBar
                            ? 'h-[calc(100vh-344px)]'
                            : 'h-[calc(100vh-280px)]'
                    }
                />
            </div>
            <div className="flex flex-col justify-center border-t border-others-100 p-6">
                <div className="flex items-center gap-2">
                    <span className="font-inter font-medium text-[12px] leading-4 tracking-[0%] align-middle text-primary-900">
                        Total no. of payments :
                    </span>
                    <span className="font-inter font-medium text-[12px] leading-4 tracking-[0%] align-middle text-primary-900">
                        {totalCount}
                    </span>
                </div>
            </div>

            <ShareMultipleDocumentsModal
                isOpen={isShareModal}
                onClose={() => {
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                    setSelectedPayment(null); // Reset selected payment
                }}
                handleCancel={() => {
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                    setSelectedPayment(null); // Reset selected payment
                }}
                handleShare={(data) => handleShareDocument(data)}
                title={
                    isStatementShare
                        ? 'Share Statement'
                        : `Share Payment Receipt ${
                              selectedPayment?.referenceAlphaId || ''
                          }`
                }
                documentAvailability={{ paymentReceipt: true }}
            />

            {/* Success Modal for statement sharing */}
            <StatementShareSuccessModal
                isOpen={isStatementShareSuccessModal}
                onClose={() => setIsStatementShareSuccessModal(false)}
                statementType="payment-detail"
                shareMethod={statementShareMethod}
                recipient={statementRecipient}
            />

            {/* Success Modal for statement download */}
            <StatementDownloadSuccessModal
                isOpen={isStatementDownloadSuccessModal}
                onClose={() => setIsStatementDownloadSuccessModal(false)}
                statementType="payment-detail"
            />

            <NotFoundModal
                isOpen={openNotFoundModal}
                onClose={() => setOpenNotFoundModal(false)}
            />

            <FileShareSuccessModal
                isOpen={isFileShareSuccessModal}
                onClose={() => setIsFileShareSuccessModal(false)}
                title="Payment Receipt Successfully Shared!"
            />

            {isDownloadInProgress && (
                <Modal
                    isOpen={isDownloadInProgress}
                    onClose={() => setIsDownloadInProgress(false)}
                    icon={<CircleAlert size={26} color="#E99400" />}
                    modalTitle="Download in Progress"
                    modalWidth="max-w-[500px]"
                    childrenPt="pt-4"
                    childrenPr="pr-4"
                    isPaddingRequired={true}
                    modalFooter={
                        <div className="flex gap-2 justify-end">
                            <Button
                                id="cancel-download"
                                variant="secondary"
                                type="button"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                id="done-download"
                                variant="primary"
                                type="submit"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Done
                            </Button>
                        </div>
                    }
                >
                    <div className="mb-4">
                        <Text variant="body">
                            Your download is being prepared and will begin
                            shortly. You can continue working.
                        </Text>
                    </div>
                </Modal>
            )}

            {/* Statement download now uses StatementDownloadSuccessModal */}
        </div>
    );
};

export default PaymentHistoryTab;
