import React, {
    useMemo,
    useState,
    useEffect,
    useRef,
    useCallback,
} from 'react';
import { <PERSON>bar, Tooltip } from '@/app/molecules';
import Table from '@/app/molecules/Table';
import InfiniteScrollTable from '@/app/molecules/InfiniteScrollTable';
import { Button, Text, Tags, Checkbox } from '@/app/atoms';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import {
    Download,
    Share2,
    RefreshCw,
    Loader2,
    CircleAlert,
    ChevronDown,
} from 'lucide-react';
import { OwnerInvoice } from '@/app/types/owner-invoice';
import { useInvoiceDocumentMutation } from '@/app/services/invoice.queries';
import { useEMRMutation } from '@/app/services/emr.queries';
import { useStatementDocumentMutation } from '@/app/services/statement.queries';
import StatementShareSuccessModal from '@/app/organisms/StatementShareSuccessModal';
import StatementDownloadSuccessModal from '@/app/organisms/StatementDownloadSuccessModal';
import ShareMultipleDocumentsModal from '@/app/organisms/ShareMultipleDocumentsModal';
import FileShareSuccessModal from '@/app/organisms/FileShareSuccessModal';
import NotFoundModal from '@/app/organisms/NotFoundModal';
import ModalReconcile from '@/app/organisms/invoice/ModalReconcile';
import ReconcileOwnerBalanceModal from '@/app/organisms/ledger/ReconcileOwnerBalanceModal';
import RefundDetailsView from '../RefundDetailsView';
import Modal from '@/app/molecules/Modal';
import InvoicesFilterBar from '@/app/molecules/InvoicesFilterBar';
import IconShare from '@/app/atoms/customIcons/IconShare';
import IconDownload from '@/app/atoms/customIcons/IconDownload.svg';
import IconFilter from '@/app/atoms/customIcons/IconFilter.svg';
import IconActiveFilter from '@/app/atoms/customIcons/IconActiveFilter.svg';

// Define User and Status types for the filter bar (ideally move to a shared types file)
interface UserFilterItem {
    id: string;
    name: string;
}

interface StatusFilterItem {
    id: string;
    label: string;
}

interface RefundsTabProps {
    invoicesData: OwnerInvoice[];
    ownerId?: string;
    ownerDetails?: {
        ownerBrand: {
            firstName: string;
            lastName: string;
            ownerBalance: number;
            ownerCredits: number;
            globalOwner: {
                phoneNumber: string;
                countryCode: string;
            };
        };
    };
    totalCount?: number;
    isLoading?: boolean;
    onLoadMore?: () => void;
    hasMore?: boolean;
    onSearch?: (searchTerm: string, filters?: any) => void;
    availableUsers?: UserFilterItem[]; // List of users from API response
    // Add filter props to persist filter state
    refundFilters?: {
        selectedUsers?: UserFilterItem[];
        selectedStatuses?: StatusFilterItem[];
        startDate?: string;
        endDate?: string;
    };
    onShowRefundDetailViewRequest?: (refund: OwnerInvoice) => void;
}

// Add WithSelection type to extend OwnerInvoice
interface WithSelection extends OwnerInvoice {
    isSelected?: boolean;
    isHighlight?: 'active' | string;
    customTableWrapperClass: string;
    tableWrapper?: string;
    rowBorder?: boolean;
    headerSticky?: boolean;
}

const RefundsTab: React.FC<RefundsTabProps> = ({
    invoicesData,
    ownerId,
    ownerDetails,
    totalCount = 0,
    isLoading = false,
    onLoadMore,
    hasMore = false,
    onSearch,
    availableUsers = [],
    refundFilters,
    onShowRefundDetailViewRequest,
}) => {
    const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
    const [selectAll, setSelectAll] = useState(false);
    const [isShareModal, setIsShareModal] = useState(false);
    const [openNotFoundModal, setOpenNotFoundModal] = useState(false);
    const [isActionLoading, setIsActionLoading] = useState(false);
    const [isReconcileModalOpen, setIsReconcileModalOpen] = useState(false);
    const [isReconcileOwnerBalanceModal, setIsReconcileOwnerBalanceModal] =
        useState(false);
    const [directToStep2, setDirectToStep2] = useState(false);
    const [isDownloadInProgress, setIsDownloadInProgress] = useState(false);
    const [isStatementShare, setIsStatementShare] = useState(false);
    const [isStatementShareSuccessModal, setIsStatementShareSuccessModal] =
        useState(false);
    const [statementShareMethod, setStatementShareMethod] = useState<
        'email' | 'whatsapp' | 'both'
    >('both');
    const [statementRecipient, setStatementRecipient] = useState<
        'client' | 'other'
    >('client');
    const [
        isStatementDownloadSuccessModal,
        setIsStatementDownloadSuccessModal,
    ] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const tableWrapperRef = useRef<HTMLDivElement>(null);
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // --- START: Filter Bar State and Data ---
    // Initialize filter values from props if available
    const [selectedUsers, setSelectedUsers] = useState<UserFilterItem[]>(
        refundFilters?.selectedUsers || []
    );
    const [selectedStatuses, setSelectedStatuses] = useState<
        StatusFilterItem[]
    >(refundFilters?.selectedStatuses || []);
    const [selectedDateRange, setSelectedDateRange] = useState<{
        start: Date | null;
        end: Date | null;
    }>({
        start: refundFilters?.startDate
            ? new Date(refundFilters.startDate)
            : null,
        end: refundFilters?.endDate ? new Date(refundFilters.endDate) : null,
    });

    // Filter bar visibility state
    const [showFilterBar, setShowFilterBar] = useState(false);

    // Check if any filters are active
    const hasActiveFilters = useMemo(() => {
        return (
            selectedUsers.length > 0 ||
            selectedStatuses.length > 0 ||
            selectedDateRange.start !== null ||
            selectedDateRange.end !== null
        );
    }, [selectedUsers, selectedStatuses, selectedDateRange]);

    // Toggle filter bar visibility
    const toggleFilterBar = () => {
        setShowFilterBar(!showFilterBar);
    };

    // Get invoice document mutation hook
    const { invoiceDocumentMutation } = useInvoiceDocumentMutation();
    // Get EMR mutations for bulk operations (similar to PaymentHistory)
    const { shareLedgerDocumentMutation, downloadLedgerDocumentMutation } =
        useEMRMutation();
    // Get statement document mutation hook
    const { requestStatementDocumentsMutation } =
        useStatementDocumentMutation();

    // Handle search term changes with debounce
    const handleSearchChange = useCallback(
        (value: string) => {
            setSearchTerm(value);

            // Clear any existing timeout
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }

            // Set a new timeout for debouncing
            searchTimeoutRef.current = setTimeout(() => {
                if (onSearch) {
                    // Convert selected status objects to their IDs for API
                    const statusParams =
                        selectedStatuses.length > 0
                            ? selectedStatuses
                                  .map((status) => status.id)
                                  .join(',')
                            : undefined;

                    // Convert selected user objects to their IDs for API
                    const userIds =
                        selectedUsers.length > 0
                            ? selectedUsers.map((user) => user.id).join(',')
                            : undefined;

                    // Format dates for API if they exist
                    const startDate = selectedDateRange.start
                        ? selectedDateRange.start.toISOString().split('T')[0]
                        : undefined;

                    const endDate = selectedDateRange.end
                        ? selectedDateRange.end.toISOString().split('T')[0]
                        : undefined;

                    // Pass current filter state along with search term
                    onSearch(value, {
                        userId: userIds,
                        status: statusParams,
                        startDate,
                        endDate,
                    });
                }
            }, 500); // 500ms debounce
        },
        [onSearch, selectedUsers, selectedStatuses, selectedDateRange]
    );

    // Clean up search timeout on unmount
    useEffect(() => {
        return () => {
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
        };
    }, []);

    const columnHelper = createColumnHelper<WithSelection>();

    // Prepare data with isHighlight property
    const tableData = useMemo(() => {
        return invoicesData.map((invoice) => ({
            ...invoice,
            isHighlight: undefined,
            customTableWrapperClass: '',
            tableWrapper: undefined,
            rowBorder: undefined,
            headerSticky: undefined,
        }));
    }, [invoicesData]);

    // Function to check if an invoice is eligible for reconciliation (unpaid or partially paid)
    const isInvoiceReconcilable = (invoice: OwnerInvoice): boolean => {
        return (
            invoice.status === 'Unpaid' ||
            invoice.status === 'Partially Paid' ||
            invoice.status === 'PENDING' ||
            invoice.status === 'PARTIALLY PAID'
        );
    };

    // Get count of selected reconcilable invoices
    const getReconcilableSelectedCount = (): number => {
        return Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice && isInvoiceReconcilable(invoice))
            .length;
    };

    // Check if there are any reconcilable invoices selected
    const hasReconcilableSelected = (): boolean => {
        return getReconcilableSelectedCount() > 0;
    };

    // Handle row selection
    const handleRowSelect = (id: string) => {
        const newSelected = new Set(selectedRows);
        if (newSelected.has(id)) {
            newSelected.delete(id);
        } else {
            newSelected.add(id);
        }
        setSelectedRows(newSelected);
        setSelectAll(newSelected.size === invoicesData.length);
    };

    // Handle table row click for highlighting and showing details
    const handleTableRowClick = (row: any) => {
        onShowRefundDetailViewRequest?.(row.original);
    };

    // Handle select all
    const handleSelectAll = () => {
        if (selectedRows.size > 0) {
            // If any rows are selected, the action is to deselect all.
            setSelectedRows(new Set());
            setSelectAll(false);
        } else {
            // If no rows are selected, the action is to select all.
            setSelectedRows(new Set(invoicesData.map((invoice) => invoice.id)));
            setSelectAll(invoicesData.length > 0); // Set to true if there are items to select
        }
    };

    // Updated handle download for selected invoices
    const handleDownloadSelected = () => {
        if (selectedRows.size === 0) return;

        // Get the selected invoices' reference IDs
        const selectedInvoices = Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice !== undefined) as OwnerInvoice[];

        // Extract reference alpha IDs for the API call
        const selectedReferenceIds = selectedInvoices
            .map((invoice) => invoice.invoiceNumber?.replace('#', '').trim())
            .filter((id) => id) as string[];

        if (selectedReferenceIds.length === 0) {
            setOpenNotFoundModal(true);
            return;
        }

        // Show download in progress indicator
        setIsDownloadInProgress(true);

        // Use the bulk download mutation like in PaymentHistory
        downloadLedgerDocumentMutation.mutate(
            {
                paymentIds: selectedReferenceIds,
                patientId: selectedInvoices[0]?.patientId || '',
            },
            {
                onSuccess: (response) => {
                    console.log('Download started successfully', {
                        hash: response?.data?.hash,
                        referenceIds: selectedReferenceIds.length,
                    });
                    setTimeout(() => setIsDownloadInProgress(false), 3000);
                },
                onError: (error) => {
                    console.error('Error initiating download:', error);
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Updated share for selected invoices
    const handleShareSelected = () => {
        if (selectedRows.size === 0) return;

        // Get all selected invoices for sharing
        const selectedInvoices = Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice !== undefined) as OwnerInvoice[];

        // Set the first invoice as the current selection (for UI purposes)
        if (selectedInvoices.length > 0) {
            setIsShareModal(true);
        }
    };

    // Handle download action for a single invoice
    const handleDownload = (invoice: OwnerInvoice) => {
        setIsActionLoading(true);
        setIsDownloadInProgress(true);

        // Get reference ID for the invoice
        const referenceAlphaId =
            invoice.invoiceNumber?.replace('#', '').trim() || '';

        if (!referenceAlphaId) {
            setIsActionLoading(false);
            setIsDownloadInProgress(false);
            setOpenNotFoundModal(true);
            return;
        }

        invoiceDocumentMutation.mutate(
            {
                referenceAlphaId,
                action: 'download',
                patientId: invoice.patientId || '',
            },
            {
                onSettled: () => {
                    setIsActionLoading(false);
                },
                onError: () => {
                    setIsDownloadInProgress(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Handle share action
    const handleShare = (invoice: OwnerInvoice) => {
        const newSelectedRows = new Set<string>();
        newSelectedRows.add(invoice.id);
        setSelectedRows(newSelectedRows);
        setSelectAll(false); // Ensure selectAll is false for single share
        setIsShareModal(true);
    };

    // Updated share document handler for multiple invoices
    const handleShareDocument = async (data: any) => {
        if (selectedRows.size === 0 && !isStatementShare) return;

        setIsActionLoading(true);

        try {
            // Determine sharing modes based on user selection
            let shareMode: string[] = [];
            if (data.shareViaEmail) shareMode.push('email');
            if (data.shareViaWhatsapp) shareMode.push('whatsapp');

            if (shareMode.length === 0) {
                setIsActionLoading(false);
                setIsShareModal(false);
                setIsStatementShare(false);
                setOpenNotFoundModal(true);
                return;
            }

            // For custom recipient, get email and phone number from form
            const emailValue =
                data.recipient === 'other' && data.shareViaEmail
                    ? data.email
                    : '';
            const phoneValue =
                data.recipient === 'other' && data.shareViaWhatsapp
                    ? data.number
                    : '';

            // Check if we're sharing as a statement or ledger document
            if (isStatementShare && ownerId) {
                const filterParams = getCurrentFilterParams();
                const hasSelectedRows = selectedRows.size > 0;

                // Determine share logic based on selections and filters
                let statementIds: string[] | undefined;

                if (hasSelectedRows) {
                    // Scenario 3 & 4: Specific statements selected (filters don't matter)
                    statementIds = Array.from(selectedRows)
                        .map((id) => {
                            const invoice = invoicesData.find(
                                (inv) => inv.id === id
                            );
                            return invoice?.invoiceNumber
                                ?.replace('#', '')
                                .trim();
                        })
                        .filter((refId) => !!refId) as string[];

                    if (statementIds.length === 0) {
                        setIsActionLoading(false);
                        setIsShareModal(false);
                        setIsStatementShare(false);
                        setOpenNotFoundModal(true);
                        return;
                    }
                } else if (!filterParams) {
                    // Scenario 1: Nothing selected + No filters = Share ALL
                    statementIds = ['ALL_STATEMENTS'];
                } else {
                    // Scenario 2: Nothing selected + Filters applied = Share filtered
                    // statementIds will be undefined to let the API handle filtering
                    statementIds = undefined;
                }

                // Use statement document mutation for statement sharing
                const response =
                    await requestStatementDocumentsMutation.mutateAsync({
                        ownerId,
                        types: ['refund'], // Refund type for refunds tab
                        action: 'SHARE',
                        shareMethod:
                            shareMode.length === 2
                                ? 'both'
                                : (shareMode[0] as 'email' | 'whatsapp'),
                        recipient: data.recipient,
                        email: emailValue,
                        phoneNumber: phoneValue,
                        statementIds,
                        filters: hasSelectedRows ? undefined : filterParams, // Only use filters when no selection
                    });

                if (response?.status) {
                    // Set the share method based on user selection
                    if (shareMode.length === 2) {
                        setStatementShareMethod('both');
                    } else if (shareMode.includes('email')) {
                        setStatementShareMethod('email');
                    } else if (shareMode.includes('whatsapp')) {
                        setStatementShareMethod('whatsapp');
                    }

                    // Set the recipient
                    setStatementRecipient(data.recipient as 'client' | 'other');

                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                    setIsStatementShareSuccessModal(true);
                } else {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                    setOpenNotFoundModal(true);
                }
            } else if (selectedRows.size > 0) {
                // Get the selected invoices' reference IDs
                const selectedInvoices = Array.from(selectedRows)
                    .map((id) => invoicesData.find((inv) => inv.id === id))
                    .filter(
                        (invoice) => invoice !== undefined
                    ) as OwnerInvoice[];

                // Extract reference alpha IDs for the API call
                const selectedReferenceIds = selectedInvoices
                    .map((invoice) =>
                        invoice.invoiceNumber?.replace('#', '').trim()
                    )
                    .filter((id) => id) as string[];

                if (selectedReferenceIds.length === 0) {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                    return;
                }

                // Use the bulk share mutation like in PaymentHistory
                const response = await shareLedgerDocumentMutation.mutateAsync({
                    paymentIds: selectedReferenceIds,
                    patientId: selectedInvoices[0]?.patientId || '',
                    sendParameter: shareMode,
                    type: data.recipient,
                    email: emailValue,
                    phoneNumber: phoneValue,
                });

                if (response?.status) {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                } else {
                    setIsActionLoading(false);
                    setIsShareModal(false);
                    setOpenNotFoundModal(true);
                }
            } else {
                // No rows selected and not a statement share
                setIsActionLoading(false);
                setIsShareModal(false);
                setOpenNotFoundModal(true);
            }
        } catch (error) {
            console.error('Error sharing documents:', error);
            setIsActionLoading(false);
            setIsShareModal(false);
            setIsStatementShare(false);
            setOpenNotFoundModal(true);
        }
    };

    // Handle reconcile action for a single invoice
    const handleReconcile = (invoice: OwnerInvoice) => {
        // Only allow reconciliation for unpaid or partially paid invoices
        if (isInvoiceReconcilable(invoice)) {
            setIsReconcileModalOpen(true);
        }
    };

    // Handle reconcile for selected invoices
    const handleReconcileSelected = () => {
        if (!hasReconcilableSelected()) return;

        // Open the ReconcileOwnerBalanceModal directly to step 2 if there are reconcilable invoices selected
        setDirectToStep2(true);
        setIsReconcileOwnerBalanceModal(true);
    };

    // Helper function to get current filter parameters
    const getCurrentFilterParams = () => {
        const hasFilters =
            searchTerm ||
            selectedUsers.length > 0 ||
            selectedStatuses.length > 0 ||
            selectedDateRange.start ||
            selectedDateRange.end;

        if (!hasFilters) return undefined;

        return {
            searchTerm: searchTerm || undefined,
            userId:
                selectedUsers.length > 0
                    ? selectedUsers.map((user) => user.id).join(',')
                    : undefined,
            status:
                selectedStatuses.length > 0
                    ? selectedStatuses.map((status) => status.id).join(',')
                    : undefined,
            startDate: selectedDateRange.start
                ? selectedDateRange.start.toISOString().split('T')[0]
                : undefined,
            endDate: selectedDateRange.end
                ? selectedDateRange.end.toISOString().split('T')[0]
                : undefined,
        };
    };

    // Handle download for statement document with 4-scenario logic
    const handleDownloadStatementSelected = () => {
        if (!ownerId) return;

        const filterParams = getCurrentFilterParams();
        const hasSelectedRows = selectedRows.size > 0;

        // Determine download logic based on selections and filters
        let statementIds: string[] | undefined;

        if (hasSelectedRows) {
            // Scenario 3 & 4: Specific statements selected (filters don't matter)
            statementIds = Array.from(selectedRows)
                .map((id) => {
                    const invoice = invoicesData.find((inv) => inv.id === id);
                    return invoice?.invoiceNumber?.replace('#', '').trim();
                })
                .filter((refId) => !!refId) as string[];

            if (statementIds.length === 0) {
                setOpenNotFoundModal(true);
                return;
            }
        } else if (!filterParams) {
            // Scenario 1: Nothing selected + No filters = Download ALL
            statementIds = ['ALL_STATEMENTS'];
        } else {
            // Scenario 2: Nothing selected + Filters applied = Download filtered
            // statementIds will be undefined to let the API handle filtering
            statementIds = undefined;
        }

        // Show download success modal
        setIsStatementDownloadSuccessModal(true);

        // Use the statement document mutation
        requestStatementDocumentsMutation.mutate(
            {
                ownerId,
                types: ['refund'], // Refund type for refunds tab
                action: 'DOWNLOAD',
                statementIds,
                filters: hasSelectedRows ? undefined : filterParams, // Only use filters when no selection
            },
            {
                onSuccess: (response) => {
                    console.log('Statement download started successfully', {
                        requestId: response?.data?.requestId,
                        scenario: hasSelectedRows
                            ? `Selected ${selectedRows.size} credit notes`
                            : filterParams
                              ? 'Filtered data'
                              : 'All data',
                        filters: hasSelectedRows ? undefined : filterParams,
                        statementIds: hasSelectedRows
                            ? statementIds
                            : undefined,
                    });
                    // Keep the success modal open for user to see
                    // It will be closed by the user
                },
                onError: (error) => {
                    console.error(
                        'Error initiating statement download:',
                        error
                    );
                    setIsStatementDownloadSuccessModal(false);
                    setOpenNotFoundModal(true);
                },
            }
        );
    };

    // Handle share for statement document
    const handleShareStatementSelected = () => {
        if (!ownerId) return;

        // Set statement share flag and open share modal
        setIsStatementShare(true);
        setIsShareModal(true);
    };

    // Open ReconcileOwnerBalanceModal without selections (default button)
    const handleOpenReconcileModal = () => {
        setDirectToStep2(false);
        setIsReconcileOwnerBalanceModal(true);
    };

    // Get only the reconcilable selected invoice data for ReconcileOwnerBalanceModal
    const getSelectedInvoicesData = () => {
        if (selectedRows.size === 0) return [];

        return Array.from(selectedRows)
            .map((id) => invoicesData.find((inv) => inv.id === id))
            .filter((invoice) => invoice && isInvoiceReconcilable(invoice))
            .map((invoice) => ({
                id: invoice?.id || '',
                invoiceDate: invoice?.date || '',
                patientName: invoice?.patientName || '',
                patientId: invoice?.patientId || '',
                invoiceReference:
                    invoice?.invoiceNumber?.replace('#', '').trim() || '',
                balanceDue: invoice?.invoiceBalance || 0,
                status:
                    invoice?.status?.toLowerCase().replace(' ', '_') ||
                    'pending',
            }));
    };

    const columns = useMemo(
        () => [
            // Checkbox column
            columnHelper.display({
                id: 'select',
                header: () => (
                    <div className="flex justify-center">
                        <Tags
                            label={
                                selectedRows.size > 0
                                    ? 'Deselect All'
                                    : 'Select All'
                            }
                            onClick={handleSelectAll}
                            variant="neutral"
                            size="small"
                            shape="rounded"
                            isLight
                            className="hover:bg-neutral-100 cursor-pointer transition-colors"
                        />
                    </div>
                ),
                cell: ({ row }) => (
                    <div
                        className="flex justify-center"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <Checkbox
                            checked={selectedRows.has(row.original.id)}
                            size="medium"
                            onChange={() => handleRowSelect(row.original.id)}
                            aria-label={`Select invoice ${row.original.invoiceNumber}`}
                        />
                    </div>
                ),
                meta: {
                    thAlign: 'text-center',
                    thClassName: 'w-min',
                    tdClassName: 'w-min cursor-pointer', // Added cursor-pointer
                },
            }),
            columnHelper.accessor('invoiceNumber', {
                header: 'Credit Note',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    tdClassName: 'cursor-pointer',
                },
            }),

            columnHelper.accessor('date', {
                header: 'Date',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    tdClassName: 'whitespace-nowrap cursor-pointer', // Added cursor-pointer
                },
            }),
            columnHelper.accessor('patientName', {
                header: 'Patient',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('user', {
                header: 'User',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue() || '-'}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('originalInvoiceReferenceAlphaId', {
                header: 'Reference Invoice',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        # {info.getValue() || '-'}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),
            columnHelper.accessor('invoiceAmount', {
                header: 'Credit Note Amount',
                cell: (info) => (
                    <Text className="!font-inter !font-normal !text-[12px] !leading-4 !tracking-[0%] !align-middle !text-primary-900">
                        {info.getValue()}
                    </Text>
                ),
                meta: {
                    // Added meta
                    tdClassName: 'cursor-pointer',
                },
            }),

            columnHelper.display({
                id: 'actions',
                header: 'Actions',
                meta: {
                    thClassName: 'w-[10%]',
                    tdClassName:
                        'w-[10%] group-hover:bg-others-100 hover:bg-neutral-50 cursor-pointer',
                    thAlign: 'text-center',
                    actionOptions: (() => {
                        const actions = [
                            { id: 'download', label: 'Download' },
                            { id: 'share', label: 'Share' },
                        ];
                        return actions;
                    }) as any,
                    onActionClick: ({
                        row,
                        action,
                    }: {
                        row: { original: WithSelection };
                        action: { id: string };
                    }) => {
                        switch (action.id) {
                            case 'download':
                                handleDownload(row.original);
                                break;
                            case 'share':
                                handleShare(row.original);
                                break;
                            default:
                                break;
                        }
                    },
                },
            }),
        ],
        [selectAll, selectedRows, isActionLoading, handleDownload, handleShare]
    );

    // Convert the onLoadMore callback to a Promise-based function for InfiniteScrollTable
    const handleLoadMore = async () => {
        if (onLoadMore && !isLoading && hasMore) {
            onLoadMore();
        }
        // Return a resolved promise to ensure compatibility with InfiniteScrollTable
        return Promise.resolve();
    };

    // Otherwise, show the invoices list view
    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center w-full space-x-2">
                    <div className="relative">
                        <Button
                            id="filter-toggle-button"
                            variant="secondary"
                            size="small"
                            onClick={toggleFilterBar}
                            className={`p-2 relative ${
                                hasActiveFilters && !showFilterBar
                                    ? 'bg-transparent hover:bg-transparent'
                                    : hasActiveFilters
                                      ? '!bg-primary-900 !text-white hover:!bg-primary-900'
                                      : 'bg-primary-50 text-neutral-900'
                            }`}
                            icon={
                                hasActiveFilters && !showFilterBar ? (
                                    <IconActiveFilter className="h-10 w-10" />
                                ) : (
                                    <IconFilter
                                        className={`h-5 w-5 ${
                                            hasActiveFilters
                                                ? 'text-white'
                                                : showFilterBar
                                                  ? 'text-primary-900'
                                                  : ''
                                        }`}
                                    />
                                )
                            }
                            onlyIcon
                        />
                    </div>
                    <div className="flex-1">
                        <Searchbar
                            id="invoice-search"
                            name="invoice-search"
                            placeholder="Search..."
                            onChange={handleSearchChange}
                            className="border w-full"
                        />
                    </div>
                </div>
                <div className="flex items-center ml-4 space-x-2">
                    {/* Statement buttons - always visible for 4-scenario logic */}
                    {/* DEBUG: selectedRows.size = {selectedRows.size}, hasActiveFilters = {hasActiveFilters.toString()} */}
                    <div
                        className="flex flex-col"
                        style={{ display: 'flex', visibility: 'visible' }}
                    >
                        <div
                            className="flex items-center space-x-2"
                            style={{ display: 'flex', visibility: 'visible' }}
                        >
                            <Tooltip
                                content={
                                    selectedRows.size > 0
                                        ? `Share selected ${selectedRows.size} credit notes`
                                        : hasActiveFilters
                                          ? 'Share filtered credit notes'
                                          : 'Share all credit notes'
                                }
                                position="top"
                            >
                                <Button
                                    id="share-statement-button"
                                    variant="primary"
                                    size="semiSmall"
                                    onClick={handleShareStatementSelected}
                                    className="p-2"
                                    icon={
                                        <IconShare
                                            className="h-6 w-6 text-white"
                                            fill="white"
                                        />
                                    }
                                    onlyIcon
                                />
                            </Tooltip>
                            <Tooltip
                                content={
                                    selectedRows.size > 0
                                        ? `Download selected ${selectedRows.size} credit notes`
                                        : hasActiveFilters
                                          ? 'Download filtered credit notes'
                                          : 'Download all credit notes'
                                }
                                position="top"
                            >
                                <Button
                                    id="download-statement-button"
                                    variant="primary"
                                    size="semiSmall"
                                    onClick={handleDownloadStatementSelected}
                                    className="p-2"
                                    disabled={isStatementDownloadSuccessModal}
                                    icon={
                                        isStatementDownloadSuccessModal ? (
                                            <Loader2 className="h-5 w-5 animate-spin" />
                                        ) : (
                                            <IconDownload className="h-5 w-5" />
                                        )
                                    }
                                    onlyIcon
                                />
                            </Tooltip>
                        </div>
                    </div>

                    {/* Commented out ledger download/share buttons
                    {selectedRows.size > 0 && (
                        <>
                            <Button
                                id="share-selected-button"
                                variant="primary"
                                size="small"
                                onClick={handleShareSelected}
                                className="p-2"
                                icon={<Share2 className="h-4 w-4" />}
                            >
                                Share ({selectedRows.size})
                            </Button>
                            <Button
                                id="download-selected-button"
                                variant="primary"
                                size="small"
                                onClick={handleDownloadSelected}
                                className="p-2"
                                icon={<Download className="h-4 w-4" />}
                            >
                                Download ({selectedRows.size})
                            </Button>
                        </>
                    )}
                    */}
                </div>
            </div>

            {showFilterBar && (
                <InvoicesFilterBar
                    availableUsers={availableUsers}
                    availableStatuses={[]}
                    initialSelectedUsers={selectedUsers}
                    initialSelectedStatus={selectedStatuses}
                    initialStartDate={selectedDateRange.start}
                    initialEndDate={selectedDateRange.end}
                    onFilterChange={(filters) => {
                        setSelectedUsers(filters.selectedUsers);
                        setSelectedStatuses(filters.selectedStatus);
                        setSelectedDateRange(filters.dateRange);

                        // Call onSearch immediately with current searchTerm and new filter values
                        if (onSearch) {
                            const statusParams =
                                filters.selectedStatus.length > 0
                                    ? filters.selectedStatus
                                          .map((status) => status.id)
                                          .join(',')
                                    : undefined;
                            const userIds =
                                filters.selectedUsers.length > 0
                                    ? filters.selectedUsers
                                          .map((user) => user.id)
                                          .join(',')
                                    : undefined;
                            const startDate = filters.dateRange.start
                                ? filters.dateRange.start
                                      .toISOString()
                                      .split('T')[0]
                                : undefined;
                            const endDate = filters.dateRange.end
                                ? filters.dateRange.end
                                      .toISOString()
                                      .split('T')[0]
                                : undefined;

                            // Use searchTerm from RefundsTab state along with new filters
                            onSearch(searchTerm, {
                                userId: userIds,
                                status: statusParams,
                                startDate,
                                endDate,
                            });
                        }
                    }}
                    onClearAll={() => {
                        setSelectedUsers([]);
                        setSelectedStatuses([]);
                        setSelectedDateRange({ start: null, end: null });

                        // Call onSearch immediately with current searchTerm and cleared filters
                        if (onSearch) {
                            // Use searchTerm from RefundsTab state and undefined for all filter values
                            onSearch(searchTerm, {
                                userId: undefined,
                                status: undefined,
                                startDate: undefined,
                                endDate: undefined,
                            });
                        }
                    }}
                />
            )}

            <div
                ref={tableWrapperRef}
                className={
                    showFilterBar
                        ? 'h-[calc(100vh-344px)]'
                        : 'h-[calc(100vh-280px)]'
                }
            >
                <InfiniteScrollTable
                    columns={columns}
                    tableData={tableData ?? []}
                    listLoadStatus={isLoading ? 'pending' : 'success'}
                    customTableWrapperClass=""
                    variant="small"
                    handleTableRowClick={handleTableRowClick}
                    headerSticky={true}
                    loadMoreData={handleLoadMore}
                    hasMore={hasMore}
                    emptyTableMessage="No credit notes found"
                    subEmptyTableMessage="Clear any filters and confirm the selected owner."
                    emptyStateImageSrc="/images/dog-with-paper-mouth.png"
                    customHeight={
                        showFilterBar
                            ? 'h-[calc(100vh-344px)]'
                            : 'h-[calc(100vh-280px)]'
                    }
                />
            </div>
            {/*total stats */}
            <div className="flex flex-col justify-center border-t border-others-100 p-6">
                <div className="flex items-center gap-2">
                    <span className="font-inter font-medium text-[12px] leading-4 tracking-[0%] align-middle text-primary-900">
                        Total no. of credit notes :
                    </span>
                    <span className="font-inter font-medium text-[12px] leading-4 tracking-[0%] align-middle text-primary-900">
                        {totalCount}
                    </span>
                </div>
            </div>
            {/* Share Modal */}
            <ShareMultipleDocumentsModal
                isOpen={isShareModal}
                onClose={() => {
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                }}
                handleCancel={() => {
                    setIsShareModal(false);
                    setIsStatementShare(false); // Reset statement share flag
                }}
                handleShare={(data) => handleShareDocument(data)}
                title={
                    isStatementShare
                        ? 'Share Statement'
                        : selectedRows.size > 1
                          ? `Share Credit Notes (${selectedRows.size})`
                          : selectedRows.size === 1
                            ? `Share Credit Note ${
                                  invoicesData.find(
                                      (inv) =>
                                          inv.id === Array.from(selectedRows)[0]
                                  )?.invoiceNumber || ''
                              }`
                            : 'Share Credit Note' // Fallback
                }
                documentAvailability={{ invoices: true }}
            />
            {/* Success Modal for download in progress */}
            {isDownloadInProgress && (
                <Modal
                    isOpen={isDownloadInProgress}
                    onClose={() => setIsDownloadInProgress(false)}
                    icon={<CircleAlert size={26} color="#E99400" />}
                    modalTitle="Download in Progress"
                    modalWidth="max-w-[500px]"
                    childrenPt="pt-4"
                    childrenPr="pr-4"
                    isPaddingRequired={true}
                    modalFooter={
                        <div className="flex gap-2 justify-end">
                            <Button
                                id="cancel"
                                variant="secondary"
                                type="button"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                id="done"
                                variant="primary"
                                type="submit"
                                size="mini"
                                onClick={() => setIsDownloadInProgress(false)}
                            >
                                Done
                            </Button>
                        </div>
                    }
                >
                    <div className="mb-4">
                        <Text variant="body">
                            This action will take a few seconds. You can exit
                            this page while we take care of it.
                        </Text>
                    </div>
                </Modal>
            )}

            {/* Statement download now uses StatementDownloadSuccessModal */}

            {/* Success Modal for statement sharing */}
            <StatementShareSuccessModal
                isOpen={isStatementShareSuccessModal}
                onClose={() => setIsStatementShareSuccessModal(false)}
                statementType="refund"
                shareMethod={statementShareMethod}
                recipient={statementRecipient}
            />

            {/* Success Modal for statement download */}
            <StatementDownloadSuccessModal
                isOpen={isStatementDownloadSuccessModal}
                onClose={() => setIsStatementDownloadSuccessModal(false)}
                statementType="refund"
            />
            {/* Not Found Modal */}
            <NotFoundModal
                isOpen={openNotFoundModal}
                onClose={() => setOpenNotFoundModal(false)}
            />
            {/* Single Invoice Reconcile Modal - Removed as selectedInvoice is no longer available and reconcile for refunds is unusual here */}
            {/*
            {selectedInvoice && isReconcileModalOpen && (
                <ModalReconcile
                    isOpen={{
                        invoice: { id: selectedInvoice.id },
                        ownerName: ownerDetails
                            ? `${ownerDetails.ownerBrand.firstName} ${ownerDetails.ownerBrand.lastName}`
                            : '',
                        ownerPhone: ownerDetails?.ownerBrand.globalOwner
                            ?.phoneNumber
                            ? `${ownerDetails.ownerBrand.globalOwner.countryCode} ${ownerDetails.ownerBrand.globalOwner.phoneNumber}`
                            : '',
                        ownerBalance: String(
                            ownerDetails?.ownerBrand.ownerBalance || 0
                        ),
                        ownerCredits: String(
                            ownerDetails?.ownerBrand.ownerCredits || 0
                        ),
                        amountPayable:
                            selectedInvoice.invoiceAmount?.toString() || '0',
                        balanceDue:
                            selectedInvoice.invoiceBalance?.toString() || '0',
                        petName: selectedInvoice.patientName || '',
                        ownerId: ownerId || '',
                        patientId: selectedInvoice.patientId || '',
                        id: selectedInvoice.id,
                    }}
                    onClose={() => setIsReconcileModalOpen(false)}
                />
            )}
            */}
            {/* Reconcile Owner Balance Modal */}
            {isReconcileOwnerBalanceModal && (
                <ReconcileOwnerBalanceModal
                    isOpen={isReconcileOwnerBalanceModal}
                    onClose={() => setIsReconcileOwnerBalanceModal(false)}
                    isRefetching={false}
                    paymentDetailsList={[]}
                    ownerDetails={
                        ownerDetails
                            ? {
                                  id: ownerId || '',
                                  firstName: ownerDetails.ownerBrand.firstName,
                                  lastName: ownerDetails.ownerBrand.lastName,
                                  phoneNumber:
                                      ownerDetails.ownerBrand.globalOwner
                                          .phoneNumber,
                                  countryCode:
                                      ownerDetails.ownerBrand.globalOwner
                                          .countryCode,
                                  ownerBalance:
                                      ownerDetails.ownerBrand.ownerBalance,
                                  ownerCredits:
                                      ownerDetails.ownerBrand.ownerCredits,
                              }
                            : undefined
                    }
                    filteredPaymentDetails={[]}
                    isSelectStage={directToStep2}
                    selectedInvoices={getSelectedInvoicesData()}
                />
            )}
        </div>
    );
};

export default RefundsTab;
