import React from 'react';
import Text from '@/app/atoms/Text';
import RenderFields, { fieldsType } from '@/app/molecules/RenderFields';
import NextImage from 'next/image';
import { UseFormReturn } from 'react-hook-form';
import { parsePhoneNumber } from 'react-phone-number-input';

interface OwnersInfoProps {
    control: any;
    errors: any;
    setValue: UseFormReturn['setValue'];
    getValues: any;
    register: any;
    watch: (name: string) => any;
    handleDeleteOwnersInfo: () => void;
    showSecondOwnersInfo: boolean;
    index: number;
}

const OwnersInfo: React.FC<OwnersInfoProps> = ({
    control,
    errors,
    setValue,
    getValues,
    register,
    watch,
    handleDeleteOwnersInfo,
    showSecondOwnersInfo,
    index = 0,
}) => {
    const commonPrefix = `patientOwners[${index}]`;
    const formData = getValues(commonPrefix);
    const getErrorMessage = (fieldName: string) => {
        if (
            errors.patientOwners &&
            errors.patientOwners &&
            errors.patientOwners[index] &&
            errors.patientOwners[index][fieldName]
        ) {
            return errors?.patientOwners[index][fieldName]?.message;
        }
        return undefined;
    };
    const fields: fieldsType[] = [
        {
            id: `${commonPrefix}.firstName`,
            name: `${commonPrefix}.firstName`,
            label: 'Owner first name',
            placeholder: 'Enter first name',
            defaultValue: formData?.firstName || '',
            value: getValues(`${commonPrefix}.firstName`),
            onChange: (e) =>
                setValue(`${commonPrefix}.firstName`, e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('firstName'),
        },
        {
            id: `${commonPrefix}.lastName`,
            name: `${commonPrefix}.lastName`,
            label: 'Owner last name',
            placeholder: 'Enter last name',
            defaultValue: formData?.lastName || '',
            value: getValues(`${commonPrefix}.lastName`),
            onChange: (e) =>
                setValue(`${commonPrefix}.lastName`, e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('lastName'),
        },
        {
            id: `${commonPrefix}.phoneNumber`,
            name: `${commonPrefix}.phoneNumber`,
            label: 'Phone number',
            placeholder: 'Enter phone number',
            defaultValue:
                `+${formData?.countryCode}${formData?.phoneNumber}` || '',
            value: `+${getValues(`${commonPrefix}.countryCode`)}${getValues(`${commonPrefix}.phoneNumber`)}`,
            onChange: (e) => {
                try {
                    const phoneNumberDetails = parsePhoneNumber(e);
                    if (phoneNumberDetails) {
                        setValue(`${commonPrefix}.countryCode`,
                            phoneNumberDetails.countryCallingCode, {
                            shouldValidate: true,
                        });
                        setValue(`${commonPrefix}.phoneNumber`,
                            phoneNumberDetails.nationalNumber, {
                            shouldValidate: true,
                        });
                    }
                } catch (error) {
                    // Clear values if parsing fails
                    setValue(`${commonPrefix}.countryCode`, '', {
                        shouldValidate: true,
                    });
                    setValue(`${commonPrefix}.phoneNumber`, '', {
                        shouldValidate: true,
                    });
                }
            },
            type: 'number-with-country',
            fieldSize: 'medium',
            required: true,
            errorMessage: getErrorMessage('phoneNumber'),
            disabled: index === 0,
        },
        {
            id: `${commonPrefix}.email`,
            name: `${commonPrefix}.email`,
            label: 'E-mail',
            placeholder: 'Enter an email',
            defaultValue: formData?.email || '',
            value: getValues(`${commonPrefix}.email`),
            onChange: (e) =>
                setValue(`${commonPrefix}.email`, e.target?.value, {
                    shouldValidate: true,
                }),
            type: 'text-input',
            fieldSize: 'medium',
            errorMessage: getErrorMessage('email'),
        },
        {
            id: `${commonPrefix}.address`,
            name: `${commonPrefix}.address`,
            label: `Address`,
            placeholder: 'Enter address',
            type: 'text-input',
            defaultValue: formData?.address || '',
            value: getValues(`${commonPrefix}.address`),
            onChange: (e) =>
                setValue(`${commonPrefix}.address`, e.target?.value, {
                    shouldValidate: true,
                }),
            fieldSize: 'medium',
            column: 'col-span-2',
        },
    ];

    return (
        <>
            <div className="border border-dashed p-4 rounded-lg my-6">
                <div className="w-full pb-6 flex flex-row justify-between items-center">
                    <Text
                        variant="body"
                        textColor="text-neutral-800"
                        fontWeight="font-bold"
                    >
                        {index !== 0 ? "Second owner's info" : 'Owner info'}
                    </Text>

                    {index > 0 && (
                        <NextImage
                            src="/images/icons/delete.svg"
                            alt="delete"
                            width={24}
                            height={24}
                            onClick={handleDeleteOwnersInfo}
                            className="cursor-pointer"
                            data-automation="delete-owner"
                        />
                    )}
                </div>

                <div className="w-full mt-0">
                    <RenderFields
                        control={control}
                        errors={errors}
                        fields={fields}
                        setValue={setValue}
                        register={register}
                        watch={watch}
                        grid="grid-cols-2 gap-y-6"
                    />
                </div>
            </div>
        </>
    );
};

export default OwnersInfo;
