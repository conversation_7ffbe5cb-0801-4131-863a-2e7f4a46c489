// PersonalTab.tsx
import React from 'react';
import {
    Control,
    FieldErrors,
    UseFormGetValues,
    UseFormSetValue,
    UseFormWatch,
} from 'react-hook-form';
import { PersonalInfoSection } from '@/app/molecules/personal/PersonalInfoSection';
import { ClinicSection } from '@/app/molecules/personal/ClinicSection';
import { SignatureSection } from '@/app/molecules/personal/SignatureSection';
import EditBasicDetailsModal from '@/app/molecules/personal/EditBasicDetailsModal';
import EditDigitalSignatureModal from '@/app/molecules/personal/EditDigitalSignatureModal';
import { UserDetails } from '@/app/template/profile/Personal/PersonalTemplate';

interface Clinic {
    name: string;
    location: string;
    isActive: boolean;
}

interface PersonalInfo {
    firstName: string;
    lastName: string;
    mobileNumber: string;
    countryCode: string;
    alternateMobileNumber:string;
    alternateCountryCode: string;
    licenseNumber: string;
    clinics: Clinic[];
    digitalSignature: string;
}

interface PersonalTabProps {
    control: Control<PersonalInfo>;
    errors: FieldErrors<PersonalInfo>;
    watch: UseFormWatch<PersonalInfo>;
    setValue: UseFormSetValue<PersonalInfo>;
    getValues: UseFormGetValues<PersonalInfo>;
    isEditBasicDetailsModal: boolean;
    setEditBasicDetailsModal: (value: boolean) => void;
    isEditDigitalSignatureModal: boolean;
    setEditDigitalSignatureModal: (value: boolean) => void;
    register: any;
    userDetails: UserDetails;
}

const PersonalTab: React.FC<PersonalTabProps> = ({
    control,
    errors,
    getValues,
    setValue,
    watch,
    register,
    isEditBasicDetailsModal,
    setEditBasicDetailsModal,
    isEditDigitalSignatureModal,
    setEditDigitalSignatureModal,
    userDetails
}) => {
    const firstName = userDetails?.firstName || watch('firstName');
    const lastName = userDetails?.lastName || watch('lastName');
    const mobileNumber = userDetails?.mobileNumber || watch('mobileNumber');
    const countryCode = userDetails?.countryCode || watch('countryCode');
    const alternateMobileNumber = userDetails?.alternateMobileNumber || watch('alternateMobileNumber');
    const alternateCountryCode = userDetails?.alternateCountryCode || watch('alternateCountryCode');
    const licenseNumber = userDetails?.licenseNumber || watch('licenseNumber');
    const digitalSignature = userDetails?.digitalSignature || watch('digitalSignature');
    const clinics = watch('clinics')
    const role = userDetails?.role || '';
    const isReceptionistOrLabTech =
        role === 'receptionist' || role === 'lab_technician';
    
    const formattedClinics = userDetails?.clinics?.map(clinic => ({
        name: clinic.name,
        location: [clinic.city, clinic.state, clinic.country].filter(Boolean).join(', '),
        isActive: true
    })) || [];


    const phoneNumber = (countryCode !== '' ? '+' : '') + countryCode + ' ' + mobileNumber;
    const alternatePhoneNumber = (alternateCountryCode !== '' ? '+' : '') + alternateCountryCode + ' ' + alternateMobileNumber;
    return (
        <div className="w-full h-full bg-white">
            <div className="grid grid-cols-1 h-full">
                <div className="border-r border-gray-200">
                    <PersonalInfoSection
                        firstName={firstName}
                        lastName={lastName}
                        mobileNumber={phoneNumber}
                        alternateMobileNumber={alternatePhoneNumber}
                        licenseNumber={isReceptionistOrLabTech ? undefined : licenseNumber}
                        onEdit={() => setEditBasicDetailsModal(true)}
                    />
                    <ClinicSection clinics={formattedClinics} />
                </div>
            </div>
            <EditBasicDetailsModal
                control={control}
                register={register}
                errors={errors}
                getValues={getValues}
                setValue={setValue}
                watch={watch}
                isOpen={isEditBasicDetailsModal}
                onClose={() => setEditBasicDetailsModal(false)}
                userDetails={userDetails}
            />
        </div>
    );
};

export default PersonalTab;
