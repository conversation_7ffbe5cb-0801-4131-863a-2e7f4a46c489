import React, { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Button from '../../atoms/Button';
import Link from 'next/link';
import Text from '@/app/atoms/Text';
import RenderFields from '@/app/molecules/RenderFields';
import Image from 'next/image';

const validationSchema = yup.object().shape({
    pin: yup
        .string()
        .required('OTP is required')
        .matches(/^\d{4}$/, 'OTP must be a 4-digit number'),
});

interface PinSignInFormProps {
    onSubmit: (data: { pin: string }) => void;
    isSubmitting: boolean;
    alert: {
        variant: 'info' | 'error' | 'success' | 'warning';
        label: string;
        isOpen: boolean;
        payload?: any;
    };
    status?: string;
}

const PinSignInForm: React.FC<PinSignInFormProps> = ({
    onSubmit,
    isSubmitting,
    alert,
    status,
}) => {
    const {
        handleSubmit,
        setValue,
        register,
        control,
        watch,
        setError,
        formState: { errors },
    } = useForm({
        resolver: yupResolver(validationSchema),
        mode: 'onSubmit',
    });

    const handleFormSubmit = (data: { pin: string }) => {
        onSubmit(data);
    };

    useEffect(() => {
        if (alert?.payload === 'wrongPin') {
            setError('pin', { message: 'Wrong PIN' });
        }
    }, [alert?.payload]);

    return (
        <div className="bg-login-animals bg-cover fixed w-full h-full flex flex-col items-center justify-center top-0 left-0 z-50">
            <form
                onSubmit={handleSubmit(handleFormSubmit)}
                className="text-center rounded-2xl backdrop-blur-[15px] p-9 max-w-[464px] w-full"
                aria-labelledby="signIn"
            >
                {/* <Heading
                    id="signInNidana"
                    type='h1'
                    className='uppercase'
                    fontWeight='font-light'
                    textColor='text-basic-white'
                >
                    Nidana
                </Heading> */}

                <div className="w-full flex items-center justify-center">
                    <div className="relative w-[11.375rem] h-[2.813rem] flex justify-center items-center">
                        <Image
                            src={'/images/nidana-logo.png'}
                            alt="logo"
                            layout="fill"
                        />
                    </div>
                </div>

                <div className="my-12">
                    <div className="mb-2">
                        <Text
                            variant="bodySmall"
                            fontWeight="font-normal"
                            textColor="text-neutral-50"
                            className="uppercase"
                        >
                            {status
                                ? 'Please enter the unique code that has been sent to registered email address'
                                : 'ENTER UNIQUE 4-DIGIT PIN'}
                        </Text>
                    </div>

                    <RenderFields
                        className="max-w-[140px] mx-auto"
                        fields={[
                            {
                                id: 'pin',
                                name: 'pin',
                                placeholder: 'Enter the code',
                                type: 'password-input',
                                isHideMaxLengthIndicator: true,
                                centerAlign: true,
                                maxLength: 4,
                                onChange: (change: any) => {
                                    const val = change.target.value;
                                    const formattedVal = val.replace(/\D/g, '');
                                    setValue('pin', formattedVal, {
                                        shouldValidate: false,
                                    });
                                },
                            },
                        ]}
                        control={control}
                        register={register}
                        errors={errors}
                        setValue={setValue}
                        watch={watch}
                    />
                </div>

                <Button
                    id="sign-up-button"
                    type="submit"
                    variant="primary"
                    size="small"
                    label={isSubmitting ? 'Signing in...' : 'Sign in'}
                    disabled={isSubmitting || watch('pin')?.length !== 4}
                />

                <div className="mt-12">
                    <Link href="/signin/forgot-password">
                        <Text
                            variant="bodySmall"
                            fontWeight="font-extralight"
                            className="uppercase hover:underline tracking-widest"
                            textColor="text-neutral-50"
                        >
                            Forgot Password?
                        </Text>
                    </Link>
                </div>
            </form>
        </div>
    );
};

export default PinSignInForm;
