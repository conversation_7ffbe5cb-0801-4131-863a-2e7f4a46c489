import React, { useState, useCallback, useEffect } from 'react';
import AsyncReactSelectTable from '@/app/molecules/AsyncReactSelectTable';
import IconAdd from '@/app/atoms/customIcons/IconAdd.svg';
import { UseFormReturn, useWatch } from 'react-hook-form';
import moment from 'moment';
import DatePicker from '@/app/molecules/DatePicker';
import { parse } from 'date-fns';

type Vital = {
    time: string;
    weight: string;
    temperature: string;
    heartRate: string;
    respRate: string;
    attitude: string;
    painScore: string;
    mucousMembrane: string;
    capillaryRefill: string;
    hydrationStatus: string;
    bcs: string;
    bp: string;
    map: string;
};

type VITAL_FIELD = {
    title: string;
    key: keyof Vital | any;
    type: 'text' | 'select' | any;
    selectOptions?: string[] | any;
};

type TreatmentVitalProps = {
    initialVitals: Vital[];
    fieldsConfig: VITAL_FIELD[];
    getValue: UseFormReturn['getValues'];
    setValue: UseFormReturn['setValue'];
    watch?: UseFormReturn['watch'];
    control: UseFormReturn['control'];
    onObjectiveBlur?: (value: string) => void;
};

const truncateText = (text: string, maxLength: number) => {
    if (text && text.length > maxLength) {
        return text.substring(0, maxLength) + '...';
    }
    return text;
};

const TreatmentVital: React.FC<TreatmentVitalProps> = ({
    initialVitals,
    fieldsConfig,
    setValue,
    getValue,
    watch,
    control,
    onObjectiveBlur,
}) => {
    const [render, setRender] = useState(0);
    
    // Force re-render when component mounts to ensure weight is displayed
    useEffect(() => {
        setRender(prev => prev + 1);
    }, []);
    
    const handleAddColumn = () => {
        const vitals = getValue('objective.vitals');
        const newVitals = [
            ...vitals,
            {
                time: moment().format('h:mm a'),
                weight: '',
                temperature: '',
                heartRate: '',
                respRate: '',
                attitude: '',
                painScore: '',
                mucousMembrane: '',
                capillaryRefill: '',
                // hydrationStatus: '',
                bcs: '',
                bp: '',
                map: '',
            },
        ];
        // console.log('vitals', vitals);
        // console.log('vitals', newVitals);
        setValue('objective.vitals', newVitals, { shouldValidate: true });
        setRender(render + 1);
    };
    const objectiveVitals = useWatch({
        name: 'objective.vitals',
        control: control, // Ensure 'control' is passed correctly
    });
    useEffect(() => {
        setRender(prev => prev + 1);
    }, [objectiveVitals]);

    
    const handleChange = (
        index: number,
        key: keyof Vital,
        value: string | null
    ) => {
        if (key === 'painScore') {
            value = value?.replace(/\D/g, '') as string;
            if (Number(value) > 10) {
                value = value?.split('')[0];
            }
            if (Number(value) === 0) value = '';
        }
        if (key == 'bp' || key === 'map') {
            if (Number(value) > 300) {
                value = '300';
            }
        }
        if (key === 'heartRate' || key === 'respRate' || key === 'temperature' || key === 'weight' || key === 'capillaryRefill') {
            if (Number(value) > 999) {
                value = '999'
            }
        }
        if (key === 'bcs') {
            if (Number(value) > 5) {
                value = '5'
            }
        }
        const vitals = getValue('objective.vitals');

        if (value === 'delete') {
            if (index === 0) {
                setValue(
                    'objective.vitals',
                    [
                        {
                            time: moment().format('h:mm a'),
                            weight: '',
                            temperature: '',
                            heartRate: '',
                            respRate: '',
                            attitude: '',
                            painScore: '',
                            mucousMembrane: '',
                            capillaryRefill: '',
                            // hydrationStatus: '',
                            bcs: '',
                            bp: '',
                            map: '',
                        },
                    ],
                    {
                        shouldValidate: true,
                    }
                );
            } else {
                const filteredVitals = vitals.filter((_: any, i: number) => i !== index);
                setValue('objective.vitals', filteredVitals, {
                    shouldValidate: true,
                });
            }
        } else {
            const updatedVitals = vitals.map((vital: any, i: number) =>
                i === index
                    ? { ...vital, [key]: value === null ? '' : value }
                    : vital
            );
            setValue('objective.vitals', updatedVitals, {
                shouldValidate: true,
            });
        }
        setRender(render + 1);
    };

    const loadOptions = useCallback(
        (inputValue: string, key: keyof Vital, addDelete: boolean) => {
            const fieldConfig = fieldsConfig.find((field) => field.key === key);
            const options = fieldConfig?.selectOptions || [];

            const filteredOptions = options
                .filter((option: string) =>
                    option.toLowerCase().includes(inputValue.toLowerCase())
                )
                .map((option: any) => ({ label: option, value: option }));

            const deleteOption = { label: 'Delete', value: 'delete' };

            return Promise.resolve({
                options: !addDelete
                    ? [...filteredOptions]
                    : [...filteredOptions, deleteOption],
            });
        },
        [fieldsConfig]
    );

    const handleBlur = useCallback(() => {
        const vitals = getValue('objective.vitals');
        if (onObjectiveBlur) {
            console.log('vitals', vitals);
            onObjectiveBlur(JSON.stringify(vitals));
        }
    }, [getValue, onObjectiveBlur]);

    // useEffect(() => {
    //     if (isVitalsEmpty('objective.vitals')) {
    //         console.log('jere');
    //     }
    // }, [watch('objective.vitals')]);

    return (
        <div className="flex flex-row overflow-auto border border-other-100 rounded-3xl bg-white w-[calc(100vw-45rem)]">
            <div
                className={`${getValue('objective.vitals').length === 0 ? '' : ''} overflow-x-auto `}
            >
                <table className="min-w-full border-collapse rounded-3xl">
                    <thead>
                        <tr>
                            <th className="pinnedColumn pinnedColumn--left text-secondary-900 text-xs font-normal px-4 py-2 whitespace-nowrap min-w-[200px] max-w-[200px] bg-white z-[0]">
                                {/* Empty header cell for sticky column */}
                            </th>
                            {getValue('objective.vitals').map((_: any, index: React.Key | null | undefined) => (
                                <th
                                    key={index}
                                    className="px-4 py-1 text-left text-xs font-medium text-gray-500 max-w-[200px] min-w-[200px] border border-other-100 border-t-0 bg-white"
                                >
                                    <div className="relative bg-white">
                                        <AsyncReactSelectTable
                                            id={`value-${index}`}
                                            name={`value-${index}`}
                                            value={
                                                null
                                                // //    'Value'
                                                // {
                                                //     label: 'Value',
                                                //     value: 'Value',
                                                // }
                                            }
                                            onChange={(selectedOption) =>
                                                handleChange(
                                                    index,
                                                    'time',
                                                    selectedOption?.value ||
                                                    null
                                                )
                                            }
                                            loadOptions={(inputValue) =>
                                                loadOptions(
                                                    inputValue,
                                                    'time',
                                                    true
                                                )
                                            }
                                            placeholder="Value"
                                            placeholderVariant="dark"
                                        />
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white">
                        {fieldsConfig.map(
                            ({
                                title,
                                key,
                                type,
                                max,
                                input,
                                range,
                                selectOptions,
                            }) => (
                                <tr
                                    key={key}
                                    className="border border-other-100 border-l-0 last:border-b-0 bg-white"
                                >
                                    <td className="pinnedColumn pinnedColumn--left text-secondary-900 text-xs font-normal px-4 py-2 whitespace-nowrap min-w-[155px] max-w-[155px] border border-other-100 border-l-0  !border-b-0 last:border-b-0 bg-white z-[1]">
                                        {title}
                                    </td>
                                    {getValue('objective.vitals').map(
                                        (vital: { [x: string]: string; }, index: React.Key | null | undefined) => (
                                            <td
                                                key={index}
                                                className="max-w-[145px] min-w-[145px] border-x border-other-100 px-4 bg-white"
                                            >
                                                {type === 'select' && (
                                                    <AsyncReactSelectTable
                                                        key={`select-${index}-${key}-${vital[key]}`} 
                                                        id={`select-${index}-${key}`}
                                                        name={`select-${index}-${key}`}
                                                        value={
                                                            vital[key]
                                                                ? {
                                                                    label: vital[
                                                                        key
                                                                    ],
                                                                    value: vital[
                                                                        key
                                                                    ],
                                                                }
                                                                : null
                                                        }
                                                        onChange={(
                                                            selectedOption
                                                        ) =>
                                                            handleChange(
                                                                index,
                                                                key,
                                                                selectedOption?.value ||
                                                                null
                                                            )
                                                        }
                                                        defaultValue={
                                                            vital[key] && {
                                                                label: vital[
                                                                    key
                                                                ],
                                                                value: vital[
                                                                    key
                                                                ],
                                                            }
                                                        }
                                                        loadOptions={(
                                                            inputValue
                                                        ) =>
                                                            loadOptions(
                                                                inputValue,
                                                                key,
                                                                false
                                                            )
                                                        }
                                                        placeholder="Select"
                                                    />
                                                )}
                                                {type === 'text' && (
                                                    <input
                                                        type={'text'}
                                                        value={truncateText(
                                                            vital[key],
                                                            15
                                                        )}
                                                        maxLength={input === 'number' ? max + 3 : max}
                                                        pattern={
                                                            input === 'number'
                                                                ? range
                                                                    ? `[1-${range}]{1}`
                                                                    : '[0-9]*\.?[0-9]{0,2}'
                                                                : ''
                                                        }
                                                        max={range}
                                                        onChange={(e) =>
                                                            handleChange(
                                                                index,
                                                                key,
                                                                input ===
                                                                    'number'
                                                                    ? range
                                                                        ? e.target.value.replace(
                                                                            new RegExp(
                                                                                `[^${1}-${range}]`,
                                                                                'g'
                                                                            ),
                                                                            ''
                                                                        )
                                                                        : e.target.value
                                                                            .replace(/[^0-9.]/g, '')
                                                                            .replace(/(\.\d{2})\d+/g, '$1')
                                                                    : e.target
                                                                        .value
                                                            )
                                                        }
                                                        onBlur={handleBlur}
                                                        className="text-xs min-h-4 leading-4 text-neutral-900 w-full appearance-none border-none outline-none"
                                                    // {...(range && {
                                                    //     min: 1,
                                                    //     max: range,
                                                    // })}
                                                    />
                                                )}
                                                {type === 'date' && (
                                                    <DatePicker
                                                        showTimeSelect
                                                        value={String(
                                                            parse(
                                                                vital[key],
                                                                'hh:mm a',
                                                                new Date()
                                                            )
                                                        )}
                                                        onDateChange={(
                                                            date
                                                        ) => {
                                                            const time =
                                                                moment(
                                                                    date
                                                                ).format(
                                                                    'hh:mm a'
                                                                );
                                                            handleChange(
                                                                index,
                                                                key,
                                                                time
                                                            );
                                                        }}
                                                        showTimeSelectOnly
                                                        timeIntervals={15}
                                                        timeCaption="Time"
                                                        dateFormat="h:mm aa"
                                                        watch={() => { }}
                                                        id="date"
                                                        name="time"
                                                        variant="flatField"
                                                    />
                                                )}
                                            </td>
                                        )
                                    )}
                                </tr>
                            )
                        )}
                    </tbody>
                </table>
            </div>

            <div className="flex-none flex items-center justify-center border-dotted border-r border-other-100 px-4 mr-6">
                <div
                    id="addNewRecord"
                    className="cursor-pointer min-h-8 min-w-8 rounded-full flex items-center justify-center bg-primary-900 hover:bg-secondary-800"
                    onClick={handleAddColumn}
                >
                    <IconAdd size={16} className="text-basic-white" />
                </div>
            </div>
        </div>
    );
};

export default TreatmentVital;
