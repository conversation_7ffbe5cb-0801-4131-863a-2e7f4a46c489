'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PetOwnerDetailsTemplate from '@/app/template/PetOwnerDetailsTemplate';
import { BreadcrumbItem } from '@/app/molecules/Breadcrumbs';
import { useParams } from 'next/navigation';
import { useGetOwnerPatients } from '@/app/services/owner.queries';
import {
    useGetOwnerInvoicesWithPayments,
    useGetPaymentDetailsForAnOwner,
    useGetOwnerLedger,
} from '@/app/services/payment-details.queries';
import { useGetOwnerCreditTransactions } from '@/app/services/credits.queries';
import CustomLoader from '@/app/atoms/CustomLoader';
import moment from 'moment';
import { InvoiceAuditLogInfo, OwnerInvoice } from '@/app/types/owner-invoice';
import { useGetPlanList } from '@/app/services/appointment.queries';
import { getAuth } from '@/app/services/identity.service';

// Define interfaces for the types we're working with
interface OwnerPatient {
    id: string;
    patientName: string;
    breed?: string;
    species?: string;
    balance?: number;
    countryCode?: string;
    address?: string;
    email?: string;
}

const AVAILABLE_TRANSACTION_TYPES_FOR_CREDITS_PAGE = [
    { id: 'CREDITS_ADDED', label: 'Credits Added' },
    { id: 'CREDITS_USED', label: 'Credits Used' },
    { id: 'CREDITS_RETURNED', label: 'Credits Returned' },
    { id: 'EXCESS_PAYMENT', label: 'Excess Payment' },
    { id: 'UNKNOWN', label: 'Unknown' },
];

// Define the Invoice interface based on the API response
interface ApiInvoice {
    id: string;
    referenceAlphaId?: string;
    invoiceAmount: number;
    totalCollected: number;
    totalprice: number;
    discount: number;
    discountAmount: number;
    balanceDue: number;
    status: string;
    createdAt: string;
    createdBy: string;
    createdByName?: string;
    cartId?: string;
    patientId?: string;
    patientName?: string;
    appointmentId?: string;
    comment?: string;
    originalInvoiceReferenceAlphaId?: string;
    metadata: Record<string, any>;
    payments: Array<{
        id: string;
        amount: number;
        paymentType: string;
        paymentMode: string;
        createdAt: string;
        createdBy: string;
        createdByName?: string;
        isCreditUsed: boolean;
        creditAmountUsed: number;
        referenceAlphaId?: string;
    }>;
    details?: any[];
    creditNotes?: any[];
    auditLogs?: InvoiceAuditLogInfo[];
}

const PAYMENTS_PER_PAGE = 20;
const INVOICES_PER_PAGE = 20;
const CREDITNOTES_PER_PAGE = 20; // Add constant for credit notes
const CREDITS_PER_PAGE = 20; // Add constant for credit transactions

const PetOwnerDetailsPage = () => {
    const params = useParams();
    const ownerId = params.ownerId as string;

    // Define the type for payment detail filters
    interface PaymentFiltersState {
        searchTerm: string;
        startDate: string;
        endDate: string;
        petName: string;
        paymentMode: string;
        paymentType: string;
        userIds: string;
        page: number;
        limit: number;
    }

    // State for payment detail filters
    const [paymentFilters, setPaymentFilters] = useState<PaymentFiltersState>({
        searchTerm: '',
        startDate: '',
        endDate: '',
        petName: '',
        paymentMode: '',
        paymentType: '',
        userIds: '',
        page: 1,
        limit: PAYMENTS_PER_PAGE,
    });

    // Define the type for invoice filters
    interface InvoiceFiltersState {
        searchTerm: string;
        startDate: string;
        endDate: string;
        petName: string;
        status: string;
        userId: string;
        page: number;
        limit: number;
        selectedUsers: Array<{ id: string; name: string }>;
        selectedStatuses: Array<{ id: string; label: string }>;
    }

    // State for invoice filters
    const [invoiceFilters, setInvoiceFilters] = useState<InvoiceFiltersState>({
        searchTerm: '',
        startDate: '',
        endDate: '',
        petName: '',
        status: '',
        userId: '',
        page: 1,
        limit: INVOICES_PER_PAGE,
        // Add additional fields to store the selected filter objects for persistence
        selectedUsers: [],
        selectedStatuses: [],
    });

    // State for credit note filters
    const [creditNoteFilters, setCreditNoteFilters] = useState({
        searchTerm: '',
        startDate: '',
        endDate: '',
        petName: '',
        status: '',
        userId: '',
        page: 1,
        limit: CREDITNOTES_PER_PAGE,
        invoiceType: 'Refund', // Specify credit note type
    });

    // Define the type for credit transaction filters
    interface CreditTransactionFiltersState {
        searchTerm: string;
        startDate: string;
        endDate: string;
        derivedTransactionTypes: string;
        userId: string;
        page: number;
        limit: number;
        selectedTransactionTypes: Array<{ id: string; label: string }>;
        selectedUsers: Array<{ id: string; name: string }>;
    }

    // State for credit transactions filters
    const [creditTransactionFilters, setCreditTransactionFilters] =
        useState<CreditTransactionFiltersState>({
            searchTerm: '',
            startDate: '',
            endDate: '',
            derivedTransactionTypes: '',
            userId: '',
            page: 1,
            limit: CREDITS_PER_PAGE,
            // Add fields for the selected filter objects
            selectedTransactionTypes: [],
            selectedUsers: [],
        });

    // Track payment data loading state
    const [isLoadingMorePayments, setIsLoadingMorePayments] = useState(false);
    // Track invoice data loading state
    const [isLoadingMoreInvoices, setIsLoadingMoreInvoices] = useState(false);
    // Track credit note data loading state
    const [isLoadingMoreCreditNotes, setIsLoadingMoreCreditNotes] =
        useState(false);
    // Track credit transactions data loading state
    const [
        isLoadingMoreCreditTransactions,
        setIsLoadingMoreCreditTransactions,
    ] = useState(false);

    // Fetch owner data and pet list
    const {
        data: ownerPetsData,
        isLoading: isLoadingOwnerPets,
        error: ownerPetsError,
    } = useGetOwnerPatients(ownerId);

    // Fetch owner invoices with filters
    const {
        data: ownerInvoicesData,
        isLoading: isLoadingInvoices,
        error: invoicesError,
        refetch: refetchInvoices,
    } = useGetOwnerInvoicesWithPayments(
        ownerId,
        invoiceFilters.page,
        invoiceFilters.limit,
        {
            searchTerm: invoiceFilters.searchTerm,
            startDate: invoiceFilters.startDate,
            endDate: invoiceFilters.endDate,
            petName: invoiceFilters.petName,
            status: invoiceFilters.status,
            userId: invoiceFilters.userId,
            invoiceType: 'Invoice', // Explicitly set to Invoice
        },
        !!ownerId // Only enable if ownerId exists
    );

    // Fetch owner credit notes with filters
    const {
        data: ownerCreditNotesData,
        isLoading: isLoadingCreditNotes,
        error: creditNotesError,
        refetch: refetchCreditNotes,
    } = useGetOwnerInvoicesWithPayments(
        ownerId,
        creditNoteFilters.page,
        creditNoteFilters.limit,
        {
            searchTerm: creditNoteFilters.searchTerm,
            startDate: creditNoteFilters.startDate,
            endDate: creditNoteFilters.endDate,
            petName: creditNoteFilters.petName,
            status: creditNoteFilters.status,
            userId: creditNoteFilters.userId,
            invoiceType: creditNoteFilters.invoiceType,
        },
        !!ownerId // Only enable if ownerId exists
    );

    // Fetch owner payment details with filters
    const {
        data: ownerPaymentDetailsData,
        isLoading: isLoadingPaymentDetails,
        error: paymentDetailsError,
        refetch: refetchPaymentDetails,
    } = useGetPaymentDetailsForAnOwner(ownerId, paymentFilters);

    // Fetch owner credit transactions with filters
    const {
        data: ownerCreditTransactionsData,
        isLoading: isLoadingCreditTransactions,
        error: creditTransactionsError,
        refetch: refetchCreditTransactions,
    } = useGetOwnerCreditTransactions(ownerId, creditTransactionFilters, {
        enabled: !!ownerId,
    });

    // Fetch owner ledger data (all payment details with debit, credit, and running balance)
    const { data: ownerLedgerData, isLoading: isLoadingLedger } =
        useGetOwnerLedger(ownerId);

    // Fetch clinic inventory data for add product functionality
    const { data: cartOptions } = useGetPlanList('', '', getAuth()?.clinicId);

    // Extract ledger data from the response - handle different possible response structures
    let ledgerData = ownerLedgerData?.data?.ledgerItems;
    let ledgerSummary = ownerLedgerData?.data?.summary;

    // State to store all loaded payment details
    const [allPaymentDetails, setAllPaymentDetails] = useState<any[]>([]);

    // State to store all loaded invoices
    const [allInvoices, setAllInvoices] = useState<any[]>([]);

    // State to store all loaded credit notes
    const [allCreditNotes, setAllCreditNotes] = useState<any[]>([]);

    // State to store all loaded credit transactions
    const [allCreditTransactions, setAllCreditTransactions] = useState<any[]>(
        []
    );

    // State to store owner details (balance, credits) separately from invoice data
    const [ownerDetails, setOwnerDetails] = useState<{
        balance: number;
        credits: number;
    }>({ balance: 0, credits: 0 });

    // State to store total counts separately from pagination data
    const [totalCounts, setTotalCounts] = useState<{
        invoices: number;
        payments: number;
        creditNotes: number;
        creditTransactions: number;
    }>({ invoices: 0, payments: 0, creditNotes: 0, creditTransactions: 0 });

    const uniqueUsers = useMemo(() => {
        if (!ownerInvoicesData?.data?.uniqueUsers) return [];
        return ownerInvoicesData.data.uniqueUsers;
    }, [ownerInvoicesData?.data?.uniqueUsers]);

    // Use the same uniqueUsers for credit notes
    const uniqueCreditNoteUsers = useMemo(() => {
        if (!ownerCreditNotesData?.data?.uniqueUsers) return uniqueUsers;
        return ownerCreditNotesData.data.uniqueUsers;
    }, [ownerCreditNotesData?.data?.uniqueUsers, uniqueUsers]);

    // Get unique users from payment details
    const uniquePaymentUsers = useMemo(() => {
        if (!ownerPaymentDetailsData?.data?.uniqueUsers) return [];
        return ownerPaymentDetailsData.data.uniqueUsers;
    }, [ownerPaymentDetailsData?.data?.uniqueUsers]);

    // Get unique users from credit transactions
    const uniqueCreditTransactionUsers = useMemo(() => {
        if (!ownerCreditTransactionsData?.uniqueUsers) return [];
        return ownerCreditTransactionsData.uniqueUsers;
    }, [ownerCreditTransactionsData?.uniqueUsers]);

    // Update allPaymentDetails when data is loaded
    useEffect(() => {
        if (ownerPaymentDetailsData?.data?.paymentDetails) {
            if (paymentFilters.page === 1) {
                // For first page, replace the data
                setAllPaymentDetails(
                    ownerPaymentDetailsData.data.paymentDetails
                );
            } else {
                // For subsequent pages, append the data
                setAllPaymentDetails((prev) => {
                    // Create a Set of IDs to avoid duplicates
                    const existingIds = new Set(prev.map((item) => item.id));

                    // Filter out duplicates and append new items
                    const newItems =
                        ownerPaymentDetailsData.data.paymentDetails.filter(
                            (item: any) => !existingIds.has(item.id)
                        );

                    return [...prev, ...newItems];
                });
            }
            setIsLoadingMorePayments(false);
        }

        // Update payment total count only when it actually changes
        if (ownerPaymentDetailsData?.data?.total !== undefined) {
            const newPaymentTotal = ownerPaymentDetailsData.data.total;

            setTotalCounts((prev) => {
                if (prev.payments !== newPaymentTotal) {
                    return { ...prev, payments: newPaymentTotal };
                }
                return prev;
            });
        }
    }, [ownerPaymentDetailsData, paymentFilters.page]);

    // Update allInvoices when data is loaded
    useEffect(() => {
        if (ownerInvoicesData?.data?.invoices) {
            if (invoiceFilters.page === 1) {
                // For first page, replace the data
                setAllInvoices(ownerInvoicesData.data.invoices);
            } else {
                // For subsequent pages, append the data
                setAllInvoices((prev) => {
                    // Create a Set of IDs to avoid duplicates
                    const existingIds = new Set(prev.map((item) => item.id));

                    // Filter out duplicates and append new items
                    const newItems = ownerInvoicesData.data.invoices.filter(
                        (item: any) => !existingIds.has(item.id)
                    );

                    return [...prev, ...newItems];
                });
            }
            setIsLoadingMoreInvoices(false);
        }

        // Update owner details only when they actually change
        if (ownerInvoicesData?.data?.ownerDetails) {
            const newBalance = ownerInvoicesData.data.ownerDetails.balance || 0;
            const newCredits = ownerInvoicesData.data.ownerDetails.credits || 0;

            setOwnerDetails((prev) => {
                // Only update if values actually changed
                if (
                    prev.balance !== newBalance ||
                    prev.credits !== newCredits
                ) {
                    return { balance: newBalance, credits: newCredits };
                }
                return prev;
            });
        }

        // Update invoice total count only when it actually changes
        if (ownerInvoicesData?.data?.pagination?.total !== undefined) {
            const newInvoiceTotal = ownerInvoicesData.data.pagination.total;

            setTotalCounts((prev) => {
                if (prev.invoices !== newInvoiceTotal) {
                    return { ...prev, invoices: newInvoiceTotal };
                }
                return prev;
            });
        }
    }, [ownerInvoicesData, invoiceFilters.page]);

    // Update allCreditNotes when data is loaded
    useEffect(() => {
        if (ownerCreditNotesData?.data?.invoices) {
            if (creditNoteFilters.page === 1) {
                // For first page, replace the data
                setAllCreditNotes(ownerCreditNotesData.data.invoices);
            } else {
                // For subsequent pages, append the data
                setAllCreditNotes((prev) => {
                    // Create a Set of IDs to avoid duplicates
                    const existingIds = new Set(prev.map((item) => item.id));

                    // Filter out duplicates and append new items
                    const newItems = ownerCreditNotesData.data.invoices.filter(
                        (item: any) => !existingIds.has(item.id)
                    );

                    return [...prev, ...newItems];
                });
            }
            setIsLoadingMoreCreditNotes(false);
        }

        // Update credit note total count only when it actually changes
        if (ownerCreditNotesData?.data?.pagination?.total !== undefined) {
            const newCreditNoteTotal =
                ownerCreditNotesData.data.pagination.total;

            setTotalCounts((prev) => {
                if (prev.creditNotes !== newCreditNoteTotal) {
                    return { ...prev, creditNotes: newCreditNoteTotal };
                }
                return prev;
            });
        }
    }, [ownerCreditNotesData, creditNoteFilters.page]);

    // Update allCreditTransactions when data is loaded
    useEffect(() => {
        if (ownerCreditTransactionsData?.transactions) {
            if (creditTransactionFilters.page === 1) {
                // For first page, replace the data
                setAllCreditTransactions(
                    ownerCreditTransactionsData.transactions
                );
            } else {
                // For subsequent pages, append the data
                setAllCreditTransactions((prev) => {
                    // Create a Set of IDs to avoid duplicates
                    const existingIds = new Set(prev.map((item) => item.id));

                    // Filter out duplicates and append new items
                    const newItems =
                        ownerCreditTransactionsData.transactions.filter(
                            (item: any) => !existingIds.has(item.id)
                        );

                    return [...prev, ...newItems];
                });
            }
            setIsLoadingMoreCreditTransactions(false);
        }

        // Update credit transaction total count only when it actually changes
        if (ownerCreditTransactionsData?.pagination?.total !== undefined) {
            const newCreditTransactionTotal =
                ownerCreditTransactionsData.pagination.total;

            setTotalCounts((prev) => {
                if (prev.creditTransactions !== newCreditTransactionTotal) {
                    return {
                        ...prev,
                        creditTransactions: newCreditTransactionTotal,
                    };
                }
                return prev;
            });
        }
    }, [ownerCreditTransactionsData, creditTransactionFilters.page]);

    // Handle payment filter changes
    const handlePaymentFilterChange = useCallback(
        (
            newFilters: Partial<{
                searchTerm: string;
                startDate: string;
                endDate: string;
                petName: string;
                paymentMode: string;
                paymentType: string;
                page: number;
                limit: number;
            }>
        ) => {
            setPaymentFilters((prev) => ({
                ...prev,
                ...newFilters,
            }));
        },
        []
    );

    // Handle invoice filter changes
    const handleInvoiceFilterChange = useCallback(
        (
            newFilters: Partial<{
                searchTerm: string;
                startDate: string;
                endDate: string;
                petName: string;
                status: string;
                userId?: string;
                page: number;
                limit: number;
                // Add fields for the selected filter objects
                selectedUsers?: Array<{ id: string; name: string }>;
                selectedStatuses?: Array<{ id: string; label: string }>;
            }>
        ) => {
            setInvoiceFilters((prev) => {
                // If we're getting filter values from InvoicesTab, store the selected objects
                if (newFilters.userId && uniqueUsers) {
                    // Convert comma-separated userIds to an array of user objects
                    const userIds = newFilters.userId.split(',');
                    const selectedUsers = uniqueUsers.filter(
                        (user: { id: string; name: string }) =>
                            userIds.includes(user.id)
                    );
                    newFilters.selectedUsers = selectedUsers;
                }

                if (newFilters.status) {
                    // Convert status string to status object
                    const statusLabel =
                        newFilters.status === 'fully_paid'
                            ? 'Paid'
                            : newFilters.status === 'partially_paid'
                              ? 'Partially Paid'
                              : newFilters.status === 'pending'
                                ? 'Pending'
                                : 'Unknown';

                    newFilters.selectedStatuses = [
                        {
                            id: newFilters.status,
                            label: statusLabel,
                        },
                    ];
                }

                return {
                    ...prev,
                    ...newFilters,
                };
            });
        },
        [uniqueUsers]
    );

    // Handle credit note filter changes
    const handleCreditNoteFilterChange = useCallback(
        (
            newFilters: Partial<{
                searchTerm: string;
                startDate: string;
                endDate: string;
                petName: string;
                status: string;
                userId?: string;
                page: number;
                limit: number;
            }>
        ) => {
            setCreditNoteFilters((prev) => ({
                ...prev,
                ...newFilters,
            }));
        },
        []
    );

    const handleCreditTransactionFilterChange = useCallback(
        (
            newFilters: Partial<{
                searchTerm: string;
                startDate: string;
                endDate: string;
                derivedTransactionTypes: string; // Comma-separated string
                userId?: string; // Comma-separated string
                page: number;
                limit: number;
                // Arrays of objects for UI state persistence
                selectedTransactionTypes?: Array<{ id: string; label: string }>;
                selectedUsers?: Array<{ id: string; name: string }>;
            }>
        ) => {
            setCreditTransactionFilters((prev) => {
                const hasActualFilterChanges =
                    (newFilters.searchTerm !== undefined &&
                        newFilters.searchTerm !== prev.searchTerm) ||
                    (newFilters.startDate !== undefined &&
                        newFilters.startDate !== prev.startDate) ||
                    (newFilters.endDate !== undefined &&
                        newFilters.endDate !== prev.endDate) ||
                    (newFilters.derivedTransactionTypes !== undefined &&
                        newFilters.derivedTransactionTypes !==
                            prev.derivedTransactionTypes) ||
                    (newFilters.userId !== undefined &&
                        newFilters.userId !== prev.userId);

                let newSelectedUsers = prev.selectedUsers;
                if (newFilters.selectedUsers) {
                    newSelectedUsers = newFilters.selectedUsers;
                } else if (newFilters.userId && uniqueCreditTransactionUsers) {
                    const userIds = newFilters.userId.split(',');
                    newSelectedUsers = uniqueCreditTransactionUsers.filter(
                        (user: { id: string; name: string }) =>
                            userIds.includes(user.id)
                    );
                }

                let newSelectedTransactionTypes = prev.selectedTransactionTypes;
                if (newFilters.selectedTransactionTypes) {
                    newSelectedTransactionTypes =
                        newFilters.selectedTransactionTypes;
                } else if (newFilters.derivedTransactionTypes) {
                    const typeIds =
                        newFilters.derivedTransactionTypes.split(',');
                    newSelectedTransactionTypes =
                        AVAILABLE_TRANSACTION_TYPES_FOR_CREDITS_PAGE.filter(
                            (type) => typeIds.includes(type.id)
                        );
                }

                return {
                    ...prev,
                    searchTerm:
                        newFilters.searchTerm !== undefined
                            ? newFilters.searchTerm
                            : prev.searchTerm,
                    startDate:
                        newFilters.startDate !== undefined
                            ? newFilters.startDate
                            : prev.startDate,
                    endDate:
                        newFilters.endDate !== undefined
                            ? newFilters.endDate
                            : prev.endDate,
                    derivedTransactionTypes:
                        newFilters.derivedTransactionTypes !== undefined
                            ? newFilters.derivedTransactionTypes
                            : prev.derivedTransactionTypes,
                    userId:
                        newFilters.userId !== undefined
                            ? newFilters.userId
                            : prev.userId,
                    page: hasActualFilterChanges
                        ? 1
                        : newFilters.page || prev.page,
                    limit: newFilters.limit || prev.limit,
                    selectedTransactionTypes: newSelectedTransactionTypes,
                    selectedUsers: newSelectedUsers,
                };
            });
        },
        [uniqueCreditTransactionUsers] // Add AVAILABLE_TRANSACTION_TYPES_FOR_CREDITS_PAGE if it's not static
    );

    // Handle loading more payment details
    const handleLoadMorePayments = useCallback(() => {
        if (isLoadingPaymentDetails || isLoadingMorePayments) return;

        setIsLoadingMorePayments(true);
        handlePaymentFilterChange({
            page: paymentFilters.page + 1,
        });
    }, [
        paymentFilters.page,
        isLoadingPaymentDetails,
        isLoadingMorePayments,
        handlePaymentFilterChange,
    ]);

    // Handle loading more invoices or refetching current invoices
    const handleLoadMoreInvoices = useCallback(
        (shouldRefetch = false) => {
            // If called without parameters, it's a request to load more
            if (isLoadingInvoices || isLoadingMoreInvoices) return;

            if (shouldRefetch) {
                // Refetch current data without changing page
                refetchInvoices();
            } else {
                // Load more data by incrementing page
                setIsLoadingMoreInvoices(true);
                handleInvoiceFilterChange({
                    page: invoiceFilters.page + 1,
                });
            }
        },
        [
            invoiceFilters.page,
            isLoadingInvoices,
            isLoadingMoreInvoices,
            handleInvoiceFilterChange,
            refetchInvoices,
        ]
    );

    // Handle loading more credit notes
    const handleLoadMoreCreditNotes = useCallback(() => {
        if (isLoadingCreditNotes || isLoadingMoreCreditNotes) return;

        setIsLoadingMoreCreditNotes(true);
        handleCreditNoteFilterChange({
            page: creditNoteFilters.page + 1,
        });
    }, [
        creditNoteFilters.page,
        isLoadingCreditNotes,
        isLoadingMoreCreditNotes,
        handleCreditNoteFilterChange,
    ]);

    // Handle loading more credit transactions
    const handleLoadMoreCreditTransactions = useCallback(() => {
        if (isLoadingCreditTransactions || isLoadingMoreCreditTransactions)
            return;

        setIsLoadingMoreCreditTransactions(true);
        handleCreditTransactionFilterChange({
            page: creditTransactionFilters.page + 1,
        });
    }, [
        creditTransactionFilters.page,
        isLoadingCreditTransactions,
        isLoadingMoreCreditTransactions,
        handleCreditTransactionFilterChange,
    ]);

    // Calculate if there are more payments to load
    const hasMorePayments = useMemo(() => {
        if (!ownerPaymentDetailsData?.data) return false;

        const totalCount = ownerPaymentDetailsData.data.total || 0;
        return allPaymentDetails.length < totalCount;
    }, [allPaymentDetails.length, ownerPaymentDetailsData?.data]);

    // Calculate if there are more invoices to load
    const hasMoreInvoices = useMemo(() => {
        if (!ownerInvoicesData?.data?.pagination) return false;

        const totalCount = ownerInvoicesData.data.pagination.total || 0;
        return allInvoices.length < totalCount;
    }, [allInvoices.length, ownerInvoicesData?.data?.pagination]);

    // Calculate if there are more credit notes to load
    const hasMoreCreditNotes = useMemo(() => {
        if (!ownerCreditNotesData?.data?.pagination) return false;

        const totalCount = ownerCreditNotesData.data.pagination.total || 0;
        return allCreditNotes.length < totalCount;
    }, [allCreditNotes.length, ownerCreditNotesData?.data?.pagination]);

    // Calculate if there are more credit transactions to load
    const hasMoreCreditTransactions = useMemo(() => {
        if (!ownerCreditTransactionsData?.pagination) return false;

        const totalCount = ownerCreditTransactionsData.pagination.total || 0;
        return allCreditTransactions.length < totalCount;
    }, [allCreditTransactions.length, ownerCreditTransactionsData?.pagination]);

    // Loading state - only show on initial load, not when filters are applied
    const isInitialInvoiceLoad =
        isLoadingInvoices &&
        invoiceFilters.page === 1 &&
        invoiceFilters.userId === '' &&
        invoiceFilters.searchTerm === '' &&
        invoiceFilters.petName === '' &&
        invoiceFilters.status === '';

    const isInitialCreditNoteLoad =
        isLoadingCreditNotes &&
        creditNoteFilters.page === 1 &&
        creditNoteFilters.userId === '' &&
        creditNoteFilters.searchTerm === '' &&
        creditNoteFilters.petName === '' &&
        creditNoteFilters.status === '';

    const isInitialPaymentLoad =
        isLoadingPaymentDetails &&
        paymentFilters.page === 1 &&
        paymentFilters.searchTerm === '' &&
        paymentFilters.startDate === '' &&
        paymentFilters.endDate === '' &&
        paymentFilters.paymentMode === '' &&
        paymentFilters.userIds === '';

    const isInitialCreditTransactionLoad =
        isLoadingCreditTransactions &&
        creditTransactionFilters.page === 1 &&
        creditTransactionFilters.searchTerm === '' &&
        creditTransactionFilters.startDate === '' &&
        creditTransactionFilters.endDate === '' &&
        creditTransactionFilters.derivedTransactionTypes === '' &&
        creditTransactionFilters.userId === '';

    // Format pet list for the template
    const petList = useMemo(
        () =>
            ownerPetsData?.data?.patients?.map((pet: OwnerPatient) => ({
                id: pet.id,
                name: pet.patientName,
                breed: pet.breed || '',
                species: pet.species || '',
            })) || [],
        [ownerPetsData?.data?.patients]
    );

    // Map the invoices to the format expected by InvoicesTab
    const invoicesData: OwnerInvoice[] = useMemo(
        () =>
            allInvoices.map(
                (invoice: ApiInvoice) => {
                    // Format the status to match what InvoicesTab expects
                    let status;
                    if (invoice.status === 'fully_paid') {
                        status = 'Paid';
                    } else if (invoice.status === 'partially_paid') {
                        status = 'Partially Paid';
                    } else if (invoice.status === 'cancelled') {
                        status = 'Cancelled';
                    } else if (invoice.status === 'written_off') {
                        status = 'Written Off';
                    } else if (invoice.status === 'unknown') {
                        status = '';
                    } else {
                        status = 'Pending';
                    }
        
                    // Format date
                    const formattedDate = moment(invoice.createdAt).format(
                        'DD MMM YYYY'
                    );
                    const formattedTime = moment(invoice.createdAt).format(
                        'hh:mm A'
                    );
        
                    return {
                        id: invoice.id,
                        invoiceNumber: `# ${invoice.referenceAlphaId || ''}`,
                        status,
                        date: formattedDate,
                        time:formattedTime,
                        patientName: invoice.patientName || 'Unknown',
                        user: invoice.createdByName || '',
                        comment: invoice.comment || '',
                        invoiceAmount: invoice.invoiceAmount || 0,
                        amountCleared: invoice.totalCollected || 0,
                        invoiceBalance: invoice.balanceDue || 0,
                        totalprice: invoice.totalprice || 0,
                        discount: invoice.discount || 0,
                        discountAmount: invoice.discountAmount || 0,
                        patientId: invoice.patientId || '',
                        cartId: invoice.cartId || '',
                        appointmentId: invoice.appointmentId || '',
                        metadata: invoice.metadata || {},
                        creditNotes: invoice.creditNotes || [],
                        ownerId: ownerId, // Add owner ID to each invoice for reconciliation
                        // Cast payments to correct type
                        payments:
                            invoice.payments?.map((payment) => ({
                                id: payment.id,
                                amount: Number(payment.amount || 0),
                                paymentType: payment.paymentType || '',
                                paymentMode: payment.paymentMode || '',
                                createdAt: payment.createdAt || '',
                                createdBy: payment.createdBy || '',
                                createdByName: payment.createdByName || '',
                                isCreditUsed: Boolean(payment.isCreditUsed),
                                creditAmountUsed: Number(payment.creditAmountUsed || 0),
                                referenceAlphaId: payment.referenceAlphaId || '',
                            })) || [],
                        details: invoice.details || [], // Include invoice details/line items
                        auditLogs: invoice.auditLogs || [],
                    };
                }
            ),
        [allInvoices, ownerId]
    );

    // Map the credit notes to the format expected by RefundsTab
    const creditNotesData: OwnerInvoice[] = useMemo(
        () =>
            allCreditNotes.map((creditNote: ApiInvoice) => {
                // Format the status to match what RefundsTab expects
                let status;
                if (creditNote.status === 'fully_paid') {
                    status = 'Paid';
                } else if (creditNote.status === 'partially_paid') {
                    status = 'Partially Paid';
                } else if (creditNote.status === 'unknown') {
                    status = '';
                } else {
                    status = 'Pending';
                }

                // Format date
                const formattedDate = moment(creditNote.createdAt).format(
                    'DD MMM YYYY'
                );
                const formattedTime = moment(creditNote.createdAt).format(
                    'hh:mm A'
                );

                return {
                    id: creditNote.id,
                    invoiceNumber: `#${creditNote.referenceAlphaId || ''}`,
                    status,
                    date: formattedDate,
                    time:formattedTime,
                    patientName: creditNote.patientName || 'Unknown',
                    user: creditNote.createdByName || '',
                    comment: creditNote.comment || '',
                    invoiceAmount: creditNote.invoiceAmount || 0,
                    amountCleared: creditNote.totalCollected || 0,
                    invoiceBalance: creditNote.balanceDue || 0,
                    originalInvoiceReferenceAlphaId:
                        creditNote.originalInvoiceReferenceAlphaId || '',
                    patientId: creditNote.patientId || '',
                    ownerId: ownerId, // Add owner ID to each credit note for reconciliation
                    // Cast payments to correct type
                    payments:
                        creditNote.payments?.map((payment) => ({
                            id: payment.id,
                            amount: Number(payment.amount || 0),
                            paymentType: payment.paymentType || '',
                            paymentMode: payment.paymentMode || '',
                            createdAt: payment.createdAt || '',
                            createdBy: payment.createdBy || '',
                            createdByName: payment.createdByName || '',
                            isCreditUsed: Boolean(payment.isCreditUsed),
                            creditAmountUsed: Number(
                                payment.creditAmountUsed || 0
                            ),
                            referenceAlphaId: payment.referenceAlphaId || '',
                        })) || [],
                    details: creditNote.details || [], // Include credit note details/line items
                };
            }),
        [allCreditNotes, ownerId]
    );

    // Memoize ownerData to prevent unnecessary re-renders
    const ownerData = useMemo(
        () => ({
            id: ownerId,
            name: `${ownerPetsData?.data?.owner.firstName} ${ownerPetsData?.data?.owner.lastName}`.trim(),
            avatarChar: ownerPetsData?.data?.owner.firstName?.[0] || 'O',
            petCount: ownerPetsData?.data?.patients?.length || 0,
            mobile: ownerPetsData?.data?.owner.phoneNumber || '',
            email: ownerPetsData?.data?.owner.email || '',
            countryCode: ownerPetsData?.data?.owner.countryCode || '',
            address: ownerPetsData?.data?.owner.address || '',
            balance: ownerDetails.balance,
            credits: ownerDetails.credits,
        }),
        [ownerId, ownerPetsData, ownerDetails]
    );

    const breadcrumbList: BreadcrumbItem[] = useMemo(
        () => [
            { id: 'pet-owner-breadcrumb', name: 'Pet Owner', path: '/owners' },
            {
                id: 'pet-owner-details-breadcrumb',
                name: `${ownerData.name}`,
                path: `/owners/${ownerId}/details`,
            },
        ],
        [ownerData.name, ownerId]
    );

    if (
        isLoadingOwnerPets ||
        isInitialInvoiceLoad ||
        isInitialCreditNoteLoad ||
        isInitialPaymentLoad ||
        isInitialCreditTransactionLoad
    ) {
        return (
            <div className="h-screen flex items-center justify-center">
                <CustomLoader
                    wrapperWidth="w-full"
                    wrapperHeight="h-full"
                    loaderWidth="80px"
                    loaderHeight="32px"
                />
            </div>
        );
    }

    // Use total counts from state to prevent unnecessary re-renders
    const paymentTotalCount = totalCounts.payments;
    const invoiceTotalCount = totalCounts.invoices;
    const creditNoteTotalCount = totalCounts.creditNotes;

    return (
        <PetOwnerDetailsTemplate
            ownerData={ownerData}
            petList={petList}
            invoicesData={invoicesData}
            paymentDetailsData={allPaymentDetails}
            creditNotesData={creditNotesData} // Pass credit notes data
            creditTransactionsData={allCreditTransactions} // Pass credit transactions data
            ledgerData={ledgerData} // Pass ledger data
            ledgerSummary={ledgerSummary} // Pass ledger summary
            breadcrumbList={breadcrumbList}
            ownerId={ownerId}
            paymentFilters={paymentFilters}
            onPaymentFilterChange={handlePaymentFilterChange}
            paymentTotalCount={paymentTotalCount}
            isPaymentLoading={isLoadingPaymentDetails || isLoadingMorePayments}
            onLoadMorePayments={handleLoadMorePayments}
            hasMorePayments={hasMorePayments}
            paymentUserFilters={uniquePaymentUsers}
            isLedgerLoading={isLoadingLedger}
            invoiceFilters={invoiceFilters}
            onInvoiceFilterChange={handleInvoiceFilterChange}
            invoiceTotalCount={invoiceTotalCount}
            isInvoiceLoading={isLoadingInvoices || isLoadingMoreInvoices}
            onLoadMoreInvoices={handleLoadMoreInvoices}
            hasMoreInvoices={hasMoreInvoices}
            invoiceUserFilters={uniqueUsers}
            creditNoteFilters={creditNoteFilters} // Pass credit note filters
            onCreditNoteFilterChange={handleCreditNoteFilterChange}
            creditNoteTotalCount={creditNoteTotalCount}
            isCreditNoteLoading={
                isLoadingCreditNotes || isLoadingMoreCreditNotes
            }
            onLoadMoreCreditNotes={handleLoadMoreCreditNotes}
            hasMoreCreditNotes={hasMoreCreditNotes}
            creditNoteUserFilters={uniqueCreditNoteUsers}
            creditTransactionFilters={creditTransactionFilters} // Pass credit transaction filters
            onCreditTransactionFilterChange={
                handleCreditTransactionFilterChange
            }
            creditTransactionTotalCount={totalCounts.creditTransactions}
            isCreditTransactionLoading={
                isLoadingCreditTransactions || isLoadingMoreCreditTransactions
            }
            onLoadMoreCreditTransactions={handleLoadMoreCreditTransactions}
            hasMoreCreditTransactions={hasMoreCreditTransactions}
            creditTransactionUserFilters={uniqueCreditTransactionUsers}
            cartOptions={cartOptions}
        />
    );
};

export default PetOwnerDetailsPage;
