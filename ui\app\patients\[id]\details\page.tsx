'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation';
import { usePatientDetails, usePatients } from '@/app/services/patient.queries';
import {
    useGetAssessmentList,
    useGetLabReports,
    useGetLongTermMedicationForPatient,
    useGetPlanList,
    useGetPrescriptionList,
    useLatestPatientAppointments,
} from '@/app/services/appointment.queries';
import { useGetPatientAlerts } from '@/app/services/patientAlert.queries';
import PatientDetailsTemplate from '@/app/template/PatientDetailsTemplate';
import moment from 'moment';
import { Breadcrumbs } from '@/app/molecules';
import { CART_TYPES, CREDIT_TYPES, DATE_FORMAT } from '@/app/utils/constant';
import { useGetClinicAlerts } from '@/app/services/clinicAlerts.queries';
import { list } from 'postcss';
import { useGetLabReportForPatient } from '@/app/services/lab-report.queries';
import { usePatientVaccinationMutation } from '@/app/services/patientVaccination.queries';
import { useGetCartListForAnAppointment } from '@/app/services/cart.queries';
import { useNetworkStatus } from '@/app/hooks/useNetworkStatus';
import NetworkOverlay from '@/app/components/NetworkOverlay';

import { GetDoctorsType } from '@/app/types/provider';
import {
    useClinicDoctors,
    useClinicProviders,
} from '@/app/services/providers.queries';
import { useClinicRooms } from '@/app/services/clinic.queries';
import { EnumAppointmentStatus } from '@/app/types/appointment';
import { getAuth } from '@/app/services/identity.service';
import { 
    useGetPaymentDetailsForAPatient,
    useGetPaymentReceiptsForAPatient,
    useGetInvoicesForAPatient
} from '@/app/services/payment-details.queries';
import { getLatestWeight, getTreatmentAppointment } from '@/app/utils/patient-details-utils/appointment-details-utils';
import { getProfileImage } from '@/app/utils/common';
import { bulkInsertCartItems, getCartListForAnAppointment } from '@/app/services/cart.service';
import { useDocumentLibraryMutation } from '@/app/services/documentLibrary.queries';
import { useDiagnosticNotesForPatients } from '@/app/services/diagnostic-notes.queries';
import {useGetClinicDetails} from "@/app/services/clinic.queries"
import { PaymentStatus } from '@/app/types/payment-status';

const PatientDetail = () => {
    const isLongTermRequestMade = useRef(false);
    const { isOffline } = useNetworkStatus();

    const router = useRouter()
    const urlAppointment = useSearchParams().get('appointment');

    const urlStatus = useSearchParams().get('status');

    const auth = getAuth();
    const CLINIC_ID = auth?.clinicId;

    const [customRule, setCustomRule] = useState({ patientLastNameAsOwnerLastName: false });
    
    const { data: clinicDetails, status: clinicDetailsStatus } = useGetClinicDetails(CLINIC_ID);
    
    useEffect(() => {
        
        if (clinicDetails?.data?.customRule) {
            setCustomRule(clinicDetails.data.customRule);
        }
    }, [clinicDetails, clinicDetailsStatus]);

    const params = useParams();
    const pathName = usePathname();
    const { getPatientVaccinationMutation } = usePatientVaccinationMutation();
    const [cartAppointmentId, setCartAppointmentId] = useState(urlAppointment ? urlAppointment : null);

    // Update cartAppointmentId when urlAppointment changes
    useEffect(() => {
        if (urlAppointment && cartAppointmentId !== urlAppointment) {
            setCartAppointmentId(urlAppointment);
        }
    }, [urlAppointment]);
    const patientId: string = params.id as string;
    const providerQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 50,
        page: 1,
        orderBy: 'ASC',
    };
    const doctorQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 50,
        page: 1,
        orderBy: 'DESC',
        // role: 'doctor',
    };

    const [patientParams, setPatientParams] = useState({
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        search: '',
    });

    const { data: doctorData, status: doctorStauts } =
        useClinicDoctors(doctorQueryParams);

    const { data: providerData, status: providerStatus } =
        useClinicProviders(providerQueryParams);

    const { data: patientDetailsData, status: patientDetailsStatus } =
        usePatientDetails(patientId);

    const { data: patientAlertsData, status: patientAlertStatus } =
        useGetPatientAlerts(patientId);

    const { data: clinicAlertsData, status: clinicAlertsListStatus } =
        useGetClinicAlerts(CLINIC_ID);


    const { data: patientDiagnosticReportData, status: patientDiagnosticReportStatus, refetch: patientDiagnosticReportRefetch } = useGetLabReportForPatient(patientId);
    const { data: patientDiagnosticNotesData, status: patientDiagnosticNotesStatus, refetch: patientDiagnosticNotesRefetch } = useDiagnosticNotesForPatients(patientId);
    const { data: patientVaccinationData, status: patientVaccinationStatus } =
        getPatientVaccinationMutation(patientId);
    const {
        getDocumentLibraryForPatientMutation
    } = useDocumentLibraryMutation();

    const { data: patientInvoicesData, status: patientInvoiceStatus } = useGetPaymentDetailsForAPatient(patientId);


    // Payment Details - Comprehensive payment receipts
    const { data: patientReceiptsData, status: patientReceiptsStatus } = useGetPaymentReceiptsForAPatient(patientId);
    
    // Invoices - All invoices for the patient
    const { data: newPatientInvoicesData, status: newPatientInvoicesStatus } = useGetInvoicesForAPatient(patientId, {
        invoiceType: 'Invoice'
    });
    
    // Refunds - Credit notes (same API as invoices, filtered by type)
    const { data: patientRefundsData, status: patientRefundsStatus } = useGetInvoicesForAPatient(patientId, {
        invoiceType: 'Refund'
    });

    // Fetch appointment data
    const {
        data: appointmentsData,
        isLoading,
        status: appointmentStatus,
        error,
    } = useLatestPatientAppointments(patientId);

    const { data: labReportsData, status: labReportDataStatus } =
        useGetLabReports('', getAuth().clinicId);

    const { data: assessmentData, status: assessmentDataStatus } =
        useGetAssessmentList('');
    const { data: plansData, status: plansDataStatus } = useGetPlanList(
        '',
        '',
        getAuth()?.clinicId
    );

    const { data: medicationsData, status: medicationDataStatus } =
        useGetPrescriptionList('', getAuth().clinicId, true);
    const { data: cartListData, status: cartListDataStatus } =
        useGetCartListForAnAppointment(cartAppointmentId);

    const { data: cartOptions, status: cartOptionsStatus } = useGetPlanList(
        '',
        '',
        getAuth()?.clinicId
    );

    const { data: clinicRoomsData, status: clinicRoomsStatus } = useClinicRooms(
        {
            clinicId: CLINIC_ID,
        }
    );


    const { data: longTermMedicationResponse, status: longTermMediacationStatus } =
        useGetLongTermMedicationForPatient(patientId);

    // const prescriptionDetails = appointmentsData?.data
    //     ?.map((prescription: any) => {
    //         if (prescription.appointmentDetails?.details) {
    //             return {
    //                 createdAt: prescription.appointmentDetails.createdAt,
    //                 prescriptionList:
    //                     prescription.appointmentDetails.details.prescriptions,
    //             };
    //         }
    //         return null;
    //     })
    //     .filter((item: any) => item !== null);

    // console.log('prescriptionDetails', prescriptionDetails);

    // const problemListData = appointmentsData?.data?.reduce(
    //     (acc: Array<any>, appointment: any) => {
    //         const { problemList }: { problemList: [] } =
    //             appointment.appointmentDetails?.details ?? [];
    //         if (Array.isArray(problemList)) {
    //             acc.push(
    //                 ...problemList.map((x: any) => ({
    //                     ...x,
    //                     provider: appointment.providers,
    //                 }))
    //             );
    //         }
    //         return acc;
    //     },
    //     []
    // );
    const prescriptionsList = appointmentsData?.data ?
        .filter((item: { status: EnumAppointmentStatus; }) => item.status === EnumAppointmentStatus.Completed)
            ?.flatMap((appointment: any) => {
                return appointment.appointmentDetails?.details?.prescription.list.map(
                    (med: { name: any; isLongTerm: any; isRestricted: any; prescriptionId: any; }) => {
                        return {
                            date: moment(appointment.date).format(DATE_FORMAT),
                            name: med.name,
                            termText: med.isLongTerm ? 'Long term' : null,
                            isRestricted: med.isRestricted,
                            medId: med.prescriptionId,
                        };
                    }
                );
            });

    const prescriptionDetails = prescriptionsList?.map((pres: { medId: any; }) => {
        const medicationInfo = medicationsData?.data?.find(
            (item: { id: any; }) => item?.id === pres?.medId
        );
        return {
            ...pres,
            drug: medicationInfo?.drug,
            unit: medicationInfo?.unit,
            form: medicationInfo?.form,
            strength: medicationInfo?.strength,
        };
    });

    // appointmentsData?.data
    //     ?.flatMap((appointment: any) => {
    //         const prescriptionData =
    //             appointment.appointmentDetails?.details?.prescription.list;

    //         if (prescriptionData) {
    //             console.log('prescr', prescriptionData);

    //             const mediactionInfo = medicationsData?.data.find(
    //                 (item) => item?.id === prescriptionData?.medicationId
    //             );
    //             console.log('mediactoinInfo', mediactionInfo);
    //             const prescDetails = [];
    //             return {
    //                 date: moment(prescription.date).format('DD-MMM-YY'),
    //                 name: prescriptionData.list
    //                     .map((pd: any) => pd.name)
    //                     .join(','),
    //                 termText: prescriptionData?.list[0]?.isLongTerm
    //                     ? 'Long term'
    //                     : '',
    //                 additionalInfo: '',
    //                 unit: '',
    //                 form: '',
    //                 strength: '',
    //             };
    //         }
    //         return null;
    //     })
    //     .filter((item: any) => item !== null);

    const problemListData = appointmentsData?.data?.reduce(
        (acc: Array<any>, appointment: any) => {
            // Check if the appointment status is 'completed'
            if (appointment.status === EnumAppointmentStatus.Completed) {
                const { list }: { list: [] } =
                    appointment.appointmentDetails?.details?.assessment ?? [];

                if (Array.isArray(list) && list.length > 0) {
                    acc.push({
                        date: moment(appointment.date).format('DD-MMM-YY'),
                        problemDetail: list.map((x: any) => x.label),
                        doctors: appointment.appointmentDoctors.map(
                            (ad: { doctor: { firstName: any; lastName: any; }; }) =>
                                `${ad.doctor.firstName} ${ad.doctor.lastName}`
                        ),
                    });
                }
            }
            return acc;
        },
        []
    );

    const vitalListData = appointmentsData?.data?.reduce(
        (acc: Array<any>, appointment: any) => {
            if (appointment.status === EnumAppointmentStatus.Completed) {
                const { vitals }: { vitals: [] } =
                    appointment.appointmentDetails.details?.objective ?? [];
                if (Array.isArray(vitals) && vitals.length > 0) {
                    acc.push(...vitals.map((x: any, i: number) => ({
                        ...x,
                        weight: i === vitals.length-1 && (!x.weight || x.weight === '') && appointment.weight
                            ? appointment.weight + ' Kgs' 
                            : x.weight,
                        appointmentDate: moment(appointment.date).format(DATE_FORMAT)
                    })));
                } else if (appointment.weight) {
                    // Only add weight if no vitals exist for this appointment
                    acc.push({
                        attitude: "",
                        bcs: "",
                        bp: "",
                        capillaryRefill: "",
                        heartRate: "",
                        map: "",
                        mucousMembrane: "",
                        painScore: "",
                        respRate: "",
                        temperature: '',
                        time: moment(appointment.createdAt).format('hh:mm A'),
                        weight: appointment.weight,
                        appointmentDate: moment(appointment.date).format(DATE_FORMAT)
                    });
                }
            } else if (appointment.weight) {
                // For non-completed appointments, add weight if available
                acc.push({
                    attitude: "",
                    bcs: "",
                    bp: "",
                    capillaryRefill: "",
                    heartRate: "",
                    map: "",
                    mucousMembrane: "",
                    painScore: "",
                    respRate: "",
                    temperature: '',
                    time: moment(appointment.createdAt).format('hh:mm A'),
                    weight: appointment.weight,
                    appointmentDate: moment(appointment.date).format(DATE_FORMAT)
                });
            }
            return acc;
        },
        []
    );

    const clinicAlertsList = (clinicAlertsData?.data || []).map(
        (list: {
            id: string;
            clinicId: string;
            alertName: string;
            severity: string;
        }) => {
            return {
                id: list?.id,
                clinicId: list?.clinicId,
                name: list?.alertName,
                severity: list?.severity,
            };
        }
    );

    const filteredClinicAlert = (clinicAlertsList || []).filter(
        (list: { name: any; }) =>
            !(patientAlertsData?.data || []).find(
                (alert: { name: any; }) => alert.name === list.name
            )
    );

    const breadcrumbList = [
        { id: 1, name: 'Patients', path: '/patients' },
        {
            id: 2,
            name: 'Patients Details',
            path: `/patients/${patientId}/details`,
        },
    ];

    useEffect(() => {
        if (appointmentStatus === 'success') {
            const treatmentAppointment = getTreatmentDefaultData(appointmentsData?.data)

            // Only redirect if there's no appointment in URL and no treatment appointment found
            if (!treatmentAppointment && urlStatus === 'show-cart' && !urlAppointment) {
                router.push(`/patients/${patientId}/details`)
            }

            if (treatmentAppointment?.status === EnumAppointmentStatus.Checkedout) {
                //        setCartAppointmentId(treatmentAppointment.id)
                router.push(`/patients/${treatmentAppointment.patientId}/details?appointment=${treatmentAppointment.id}&status=show-cart`)
            } 
        }

    }, [appointmentStatus])


    const handleLognTermMedication = async () => {
        // console.log(appointmentsData);

        if (urlAppointment) {
            const cartItems = await getCartListForAnAppointment(urlAppointment)
            if (cartItems.data && cartItems.data.length === 0) {
                const prescriptionIds = longTermMedicationResponse?.data?.map(
                    (item: any) => item?.medicationId
                );
                if (prescriptionIds.length > 0) {
                    let response;
                    let updatedPrescriptionData = [];
                    response = await bulkInsertCartItems({
                        appointmentId: urlAppointment,
                        prescriptionIds: prescriptionIds,
                        addedFrom: 'LognTerm',
                    });
                }

            }
        }

    }
    useEffect(() => {
        if (longTermMediacationStatus === 'success' && urlStatus === 'begin-treatment' && !isLongTermRequestMade.current) {
            isLongTermRequestMade.current = true;
            handleLognTermMedication()
        }
    }, [longTermMediacationStatus])
    const getTreatmentDefaultData = (appointmentData: any[]) => {

        return getTreatmentAppointment(appointmentData)
    }
    if (
        isLoading ||
        patientAlertStatus === 'pending' ||
        patientDetailsStatus === 'pending' ||
        labReportDataStatus === 'pending' ||
        medicationDataStatus === 'pending' ||
        plansDataStatus === 'pending' ||
        assessmentDataStatus === 'pending' ||
        patientDiagnosticReportStatus === 'pending' ||
        longTermMediacationStatus === 'pending' ||
        patientVaccinationStatus === 'pending' || patientInvoiceStatus === 'pending' || (urlAppointment && cartListDataStatus === 'pending')
    )
        return <div>Loading patient data...</div>;
    if (error) return <div>Error loading patient data: {error.message}</div>;
    // Prepare data for PatientDetailsTemplate
    const templateProps = {
        patientSidebar: {
            patientData: {
                patientName: customRule?.patientLastNameAsOwnerLastName === true && patientDetailsData?.data?.patientOwners?.[0]?.ownerBrand?.lastName
                    ? `${patientDetailsData?.data?.patientName} ${patientDetailsData?.data?.patientOwners[0].ownerBrand.lastName}`
                    : patientDetailsData?.data?.patientName || '--',
                patientProfile: getProfileImage({
                    species: patientDetailsData?.data.species,
                    breedValue: patientDetailsData?.data.breed,
                }),
                breed: patientDetailsData?.data?.breed,
                weight: getLatestWeight(patientDetailsData.data, appointmentsData?.data).latestWeight,
                //patientDetailsData?.datapatientDetailsData?.data?.weight
                // ? `${patientDetailsData?.data?.weight} Kgs`
                // : '--',
                age: `${patientDetailsData?.data?.age}`,
                gender: patientDetailsData?.data?.gender,
                weightOnSpecificDate: getLatestWeight(patientDetailsData.data, appointmentsData?.data).latestTimestamp,
                //  patientDetailsData?.data?.createdAt
                //     ? moment(patientDetailsData?.data?.createdAt).format(
                //         "On DD MMM' YY"
                //     )
                //     : '',
                reproductiveStatus:
                    patientDetailsData?.data?.reproductiveStatus || '--',
                identification:
                    patientDetailsData?.data?.identification || '--',
                species: patientDetailsData?.data?.species || '--',
                microchipId: patientDetailsData?.data?.microchipId || '--',
                allergies: [patientDetailsData?.data?.allergies] || [],
                patientOwners: patientDetailsData?.data?.patientOwners || [],
                isDataImported: patientDetailsData?.data?.dummyData || null,
                balance: patientDetailsData?.data?.patientOwners[0].ownerBrand.ownerBalance || 0,
                credits : patientDetailsData?.data?.patientOwners[0].ownerBrand.ownerCredits || 0,
            },
            patientId: patientId,
            alertsList: filteredClinicAlert || [],
            problemList: problemListData || [],
            medicationList: prescriptionDetails || [],
            selectedAlertTagList: patientAlertsData?.data || [],
            vitalListData: vitalListData || [],
            balance: patientDetailsData?.data?.patientOwners[0].ownerBrand.ownerBalance,
            credits : patientDetailsData?.data?.patientOwners[0].ownerBrand.ownerCredits || 0,
            paymentDetailsList: patientInvoicesData?.data.paymentDetails,
            ownerDetails: patientInvoicesData?.data.ownerDetails,
            plansData: plansData?.data,
        },
        appointmentsData: appointmentsData,
        labReportsData: labReportsData?.data,
        assessmentData: assessmentData?.data,
        medicationData: medicationsData?.data,
        plansData: plansData?.data,
        patientDiagnosticReportData: patientDiagnosticReportData,
        patientDiagnosticReportRefetch:patientDiagnosticReportRefetch,
        patientDiagnosticNotesData:patientDiagnosticNotesData,
        patientDiagnosticNotesRefetch:patientDiagnosticNotesRefetch,
        patientVaccinationData: patientVaccinationData,
        cartOptions: cartOptions,
        cartListData: cartListData,
        setCartAppointmentId: setCartAppointmentId,
        cartAppointmentId: cartAppointmentId,
        clinicRoomsData,
        doctorData,
        providerData,
        patientPaymentDetailsData : {
            patientReceiptsData,
            patientReceiptsStatus,
            newPatientInvoicesData,
            newPatientInvoicesStatus,
            patientRefundsData,
            patientRefundsStatus
        },
        paymentDetailsList: patientInvoicesData?.data?.paymentDetails.map((item: any) => ({
            createdAt: item.createdAt,
            date: moment(item.createdAt).format(DATE_FORMAT),
            time: moment.utc(item.createdAt).local().format('hh:mm A'),
            paymentNotes: item.paymentNotes,
            id: ['Invoice', 'Credit Note'].includes(item.type) ? item?.invoice?.referenceId : item.referenceId ?? '', // This is just temporary
            type: item.type,
            paymentType: item.paymentType,
            receipt: item.receiptDetail,
            receiptAlphaId: item.referenceAlphaId,
            amount: item.amount,
            amountPayable:
                item.type == CREDIT_TYPES.Invoice
                    ? item.amountPayable
                    : null,
            invoice: item.invoice,
            referenceAlphaId: item?.invoice?.referenceAlphaId || item?.referenceAlphaId,
            showInInvoice: item.showInInvoice,
            payments: item.payments,
            relatedPayments: item.relatedPayments ? item.relatedPayments : [],
            createdByName: item.createdByName,
            balanceDue: item?.invoice?.balanceDue,
            creditAmountUsed : item?.creditAmountUsed,
            isCreditUsed : item?.isCreditUsed,
            isCreditsAdded : item?.isCreditsAdded,
            creditAmountAdded : item?.creditAmountAdded,
            status: (() => {
                const rawStatus = item?.invoice?.status;
                const status = rawStatus?.toLowerCase();
               
                
                if (status === 'partially_paid') {
                    return PaymentStatus.PARTIALLY_PAID;
                }
                if (status === 'fully_paid') {
                    return PaymentStatus.PAID;
                }
                if (status === 'cancelled') {
                    return PaymentStatus.CANCELLED;
                }
                if (status === 'written_off') {
                    return PaymentStatus.WRITTEN_OFF;
                }
                if (status === 'unknown' || !rawStatus) {
                    return PaymentStatus.UNKNOWN;
                }
                return PaymentStatus.PENDING;
            })(),
        })),
        longTermMedication: longTermMedicationResponse?.data
    };

    return (
        <>
            <PatientDetailsTemplate
                {...templateProps}
                defaultTreatmentData={getTreatmentDefaultData(appointmentsData?.data)}
            />
            <NetworkOverlay isVisible={isOffline} />
        </>
    );
};

export default PatientDetail;
