'use client';

import Image from 'next/image';
import PatientLayout from '../components/Layout/PatientLayout';

import {
    useClinicPatients,
    useCreateOrUpdatePatient,
    useSearchPatientByPhone,
} from '../services/patient.queries';
// import Table, { PaginationType } from "../organisms/Table";
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import BasicInfo from '../molecules/BasicInfo';
import { UserProfile } from '../molecules';
import IconDots from '../atoms/customIcons/IconDots.svg';
import { PatientListParams, PatientT } from '../types/patient';
import moment from 'moment';
import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import AddPatient from '../organisms/patient/AddPatient';
import Table, { PaginationType } from '../molecules/Table';
import PatientList from '../organisms/PatientList';
import Patients, { PatientsType } from '../template/Patients';
import { validationSchema } from '../utils/validation-schema/patientValidation';
import { DATE_FORMAT } from '../utils/constant';
import { PhoneNumber, parsePhoneNumber } from 'react-phone-number-input';
import { searchPatientByPhone } from '../services/patient.service';
import { Alert } from '../atoms';
import classNames from 'classnames';
import _ from 'lodash';
import useDebounce from '../hooks/useDebounce';
import CreateAppointment from '../template/CreateAppointment';
import { getAuth } from '../services/identity.service';
import { getProfileImage } from '../utils/common';
import { debounce } from 'lodash';
import CustomLoader from '../atoms/CustomLoader';
import {
    useGetClinic,
    useGetClinicDetails,
} from '@/app/services/clinic.queries';

interface AlertT {
    isOpen: boolean;
    label: string;
    variant: 'success' | 'error' | 'warning' | 'info';
    isLight?: boolean;
}

export default function Home() {
    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors },
        watch,
        trigger,
        clearErrors,
    } = useForm({
        resolver: yupResolver(validationSchema),
        mode: 'onChange', // This will trigger validation on change,
        defaultValues: {},
    });

    const CLINIC_ID = getAuth()?.clinicId;
    const [customRule, setCustomRule] = useState({
        patientLastNameAsOwnerLastName: false,
    });

    const { data: clinicDetails } = useGetClinicDetails(CLINIC_ID);

    useEffect(() => {
        if (clinicDetails?.data?.customRule) {
            setCustomRule(clinicDetails.data.customRule);
        }
    }, [clinicDetails]);

    const {
        data: clinicData,
        isLoading,
        error,
        refetch,
    } = useGetClinic(CLINIC_ID);
    const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
    const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const [patientData, setPatientData] = useState({});

    // const [currentPage, setCurrentPage] = useState(1);
    const [currentStep, setCurrentStep] = useState(1);

    const limit = 10;
    const [pagination, setPagination] = useState<PatientListParams>({
        pageIndex: 0,
        pageSize: limit,
        searchTerm: '',
    });

    const [totalPages, setTotalPages] = useState(1);
    const [showGlobalAlert, setShowGlobalAlert] = useState<AlertT>({
        isOpen: false,
        label: 'Appointment created successfully',
        variant: 'success',
        isLight: true,
    });
    const [showModalAlert, setShowModalAlert] = useState<AlertT>({
        isOpen: false,
        label: '',
        variant: 'error',
        isLight: true,
    });

    const { status, data } = useClinicPatients(
        pagination.pageIndex + 1,
        limit,
        pagination.searchTerm || '',
        'false'
    );

    // Commented out as pending payment tab has been removed
    // const { status: statusWithBalance, data: dataWithBalance } =
    //     useClinicPatients(
    //         pagination.pageIndex + 1,
    //         limit,
    //         pagination.searchTerm || '',
    //         'true'
    //     );

    const createPatientMutation = useCreateOrUpdatePatient();

    const columnHelper = createColumnHelper<PatientT>();

    const columns: ColumnDef<PatientT, any>[] = [
        columnHelper.accessor('patientName', {
            header: 'Patient Name',
            cell: (info) => (
                <UserProfile
                    avatar={info.row.original?.profile ?? ''}
                    name={info.row.original?.patientName}
                    description={info.row.original?.breed ?? ''}
                />
            ),
        }),
        columnHelper.accessor((row) => row.owners[0]?.name, {
            id: 'ownerName',
            size: 20,
            meta: {
                tdClassName: 'max-w-[223px]',
                thClassName: 'max-w-[223px] ',
            },
            header: () => <span>Owner Name</span>,
            cell: (info) => (
                <BasicInfo
                    label={info.getValue() || '--'}
                    value={info.row.original.owners[1]?.name || ''}
                />
            ),
        }),
        columnHelper.accessor((row) => row.owners[0]?.email, {
            id: 'ownerEmail',
            header: 'Email',
            size: 20,
            meta: {
                tdClassName: 'max-w-[223px]',
                thClassName: 'max-w-[223px] ',
            },
            cell: (info) => (
                <BasicInfo
                    label={info.getValue() || '--'}
                    value={info.row.original.owners[1]?.email || ''}
                    labelClass="text-truncation"
                />
            ),
        }),
        columnHelper.accessor((row) => row.owners[0]?.phoneNumber, {
            id: 'ownerPhone',
            header: 'Phone Number',
            cell: (info) => (
                <BasicInfo
                    label={info.getValue() || '--'}
                    value={info.row.original.owners[1]?.phoneNumber || ''}
                />
            ),
        }),
        columnHelper.accessor('lastVisit', {
            header: 'Last Visit',
            cell: (info) => (
                <BasicInfo
                    label={`${info.row.original.symptom || '--'} | ${moment(info.getValue()).format('DD MMM YYYY')}`}
                    value={
                        info.row.original.primaryDoctor
                            ? `Consulted by Dr. ${info.row.original.primaryDoctor}`
                            : '--'
                    }
                />
            ),
        }),
        {
            id: 'action',
            header: 'Action',
            meta: {
                actionOptions: [
                    {
                        id: 'edit',
                        label: 'Edit',
                    },
                    {
                        id: 'delete',
                        label: 'Delete',
                    },
                ],
            },
        },
    ];

    // useEffect(() => {
    //   setTotalPages(
    //     status === "pending"
    //       ? totalPages
    //       : data?.data?.total
    //         ? Math.ceil(data?.data?.total / limit)
    //         : 1
    //   );
    // }, [data]);

    const transformPatientData = (formValues: any, ownerId: string) => {
        const ownersData = formValues.patientOwners.map(
            (item: any, index: number) => {
                const phone = parsePhoneNumber(item.phoneNumber);
                const baseOwnerData = {
                    firstName: item.firstName,
                    lastName: item.lastName,
                    email: item.email !== '' ? item.email : null,
                    isPrimary: index === 0,
                    ...(index === 0 ? { id: ownerId } : {}), // Only add id for owner at index 0.
                };

                if (item.phoneNumber.startsWith('+')) {
                    return {
                        ...baseOwnerData,
                        phoneNumber: phone?.nationalNumber,
                        countryCode: phone?.countryCallingCode,
                    };
                } else {
                    return {
                        ...baseOwnerData,
                        phoneNumber: item.phoneNumber,
                        countryCode: item.countryCode || '',
                    };
                }
            }
        );

        return {
            patientName: formValues.patientName,
            ownersData,
            species: formValues.species?.value || undefined,
            breed: formValues.breed?.value || undefined,
            age: formValues.age
                ? moment(formValues.age).format(DATE_FORMAT)
                : undefined,
            gender: formValues.gender?.value || undefined,
            reproductiveStatus:
                formValues.reproductiveStatus?.value || undefined,
            microchipId: formValues.microchipId || undefined,
            identification: formValues.identification || undefined,
            allergies: formValues.allergies || undefined,
            markDeceased: formValues.markDeceased?.markDeceased || false,
        };
    };

    const validatePatientOwners = (patientOwners: any[]): boolean => {
        let hasError = false;

        patientOwners.forEach((owner, index) => {
            if (
                owner.phoneNumber.startsWith('+') &&
                owner.phoneNumber.substring(1) === owner.countryCode
            ) {
                setError(
                    `patientOwners.${index}.phoneNumber`,
                    {
                        message: 'Phone number is required',
                    },
                    { shouldFocus: true }
                );
                hasError = true;
                return;
            }

            if (
                owner.phoneNumber.length < 10 ||
                parsePhoneNumber(owner.phoneNumber)?.nationalNumber?.length < 10
            ) {
                setError(
                    `patientOwners.${index}.phoneNumber`,
                    {
                        message: 'Phone number is required',
                    },
                    { shouldFocus: true }
                );
                hasError = true;
                return;
            }
        });

        return !hasError;
    };

    const handleSave = async (data, bookAppointment = false) => {
        const formValues = getValues();
        const patientId = getValues('patientId');
        const ownerId = getValues('ownerId');
        console.log(formValues.age);

        const { patientOwners, patientName, breed, species } = formValues;
        setPatientData({
            patientOwners: [
                {
                    ownerBrand: {
                        firstName: formValues.patientOwners?.[0].firstName,
                        lastName: formValues.patientOwners?.[0].lastName,
                        globalOwner: {
                            phoneNumber:
                                formValues.patientOwners?.[0].phoneNumber,
                        },
                    },
                },
            ],
            id: patientId,
            patientName,
            breed: breed?.label,
            species: species?.value,
            profileImage: getProfileImage({
                species: species?.value,
                breedValue: breed?.value,
            }),
        });
        if (!validatePatientOwners(patientOwners)) {
            return;
        }
        const transformedData = transformPatientData(formValues, ownerId);

        try {
            await createPatientMutation.mutateAsync({
                type: 'update',
                data: { ...transformedData, id: patientId },
            });
            reset();
            if (bookAppointment) {
                handleBookAppointment(patientId);
            }
            handleCloseModal();
            setShowGlobalAlert({
                isOpen: true,
                label: 'Patient updated successfully',
                variant: 'success',
                isLight: true,
            });
        } catch (error: any) {
            setShowModalAlert({
                isOpen: true,
                label:
                    error.message || 'An error occurred while updating patient',
                variant: 'error',
                isLight: true,
            });
        }
    };
    const handlePatientCreation = async () => {
        const isValid = await trigger([
            'ownerFirstName',
            'ownerLastName',
            'patientName',
            'phoneNumber',
        ]);
        if (!isValid) {
            return false;
        }

        const formData = getValues();

        try {
            const patientData = await createPatientMutation.mutateAsync({
                type: 'create',
                data: {
                    patientName: formData.patientName,
                    clinicId: CLINIC_ID,
                    ownersData: [
                        {
                            firstName: formData.ownerFirstName,
                            lastName: formData.ownerLastName,
                            phoneNumber: formData.phoneNumberNational,
                            countryCode: formData.countryCode,
                            isPrimary: true,
                        },
                    ],
                },
            });
            let patientOwner = null;
            if (formData.patientOwners && formData.patientOwners.length > 0) {
                patientOwner = {
                    ...formData.patientOwners[0],
                    phoneNumber: formData.phoneNumberNational,
                    countryCode: formData.countryCode,
                    isPrimary: true,
                };
            } else {
                patientOwner = {
                    firstName: formData.ownerFirstName,
                    lastName: formData.ownerLastName,
                    email: '',
                    address: '',
                    phoneNumber: formData.phoneNumberNational,
                    countryCode: formData.countryCode,
                    isPrimary: true,
                };
            }

            setValue('patientOwners', [patientOwner]);
            setValue('patientId', patientData.data.id);
            setValue(
                'ownerId',
                patientData.data.patientOwners[0].ownerBrand.id
            );
            setShowGlobalAlert({
                isOpen: true,
                label: 'Patient created successfully',
                variant: 'success',
                isLight: true,
            });
            return true;
        } catch (error) {
            console.error('Error creating patient:', error);
            return false;
        }
    };

    const handleAddPatientClick = () => {
        reset();
        setIsAddPatientModalOpen(true);
        setCurrentStep(1);
    };
    const handleNext = debounce(async () => {
        console.log('clicked');
        const searchData = await searchPatientByPhone(
            getValues('phoneNumberNational')
        );

        if (currentStep === 1) {
            const isValid = await trigger('phoneNumber');
            if (!isValid) {
                console.log('Phone number validation failed');
                return;
            }

            if (searchData && searchData.data) {
                if (
                    searchData.data.message === 'Owner not found' ||
                    searchData.data.patients.length === 0
                ) {
                    console.log('Owner not found, moving to step 3');
                    setCurrentStep(3);
                } else if (searchData.data.owner && searchData.data.patients) {
                    const { owner, patients } = searchData.data;
                    setPatientData({
                        patientOwners: [{ owner }],
                        id: patients[0]?.id,
                        patientName: patients[0]?.patientName,
                        breed: patients[0]?.breed,
                        species: patients[0]?.species,
                        profileImage: getProfileImage({
                            species: patients[0]?.species,
                            breedValue: patients[0]?.breed,
                        }),
                    });
                    setValue('owner', owner);
                    setValue('patientOwners', [owner]);
                    setValue('patients', patients);
                    setCurrentStep(2);
                } else {
                    console.log('Unexpected data structure', searchData.data);
                    // Handle unexpected data structure
                }
            } else {
                console.log('No search data available');
                // Handle case when no search data is available
            }
        } else if (currentStep === 2) {
            setCurrentStep(3);
        } else if (currentStep === 3) {
            const patientCreated = await handlePatientCreation();
            if (patientCreated) {
                setCurrentStep(4);
            }
        } else if (currentStep === 4) {
            handleCloseModal();
        }
    }, 500);

    const handleCloseModal = () => {
        reset();
        setIsAddPatientModalOpen(false);
    };

    const handleBookAppointment = (patientId: string) => {
        // Find the patient data from the patients array
        const formValues = getValues();
        const patients = (formValues.patients || []) as PatientT[];
        const selectedPatient = patients.find((p) => p.id === patientId);

        if (selectedPatient) {
            const patientData = {
                patientOwners: [
                    {
                        ownerBrand: {
                            firstName: formValues.patientOwners?.[0].firstName,
                            lastName: formValues.patientOwners?.[0].lastName,
                            globalOwner: {
                                phoneNumber:
                                    formValues.patientOwners?.[0].phoneNumber,
                            },
                        },
                    },
                ],
                id: selectedPatient.id,
                patientName: selectedPatient.patientName,
                breed: selectedPatient.breed,
                species: selectedPatient.species,
                profileImage:
                    selectedPatient.species && selectedPatient.breed
                        ? getProfileImage({
                              species: selectedPatient.species.toLowerCase(),
                              breedValue: selectedPatient.breed,
                          })
                        : undefined,
            };
            setPatientData(patientData);
        }
        setShowAppointmentModal(true);
        setIsAddPatientModalOpen(false);
    };

    const [isDataLoading, setIsDataLoading] = useState(false);

    // Add after query declarations
    useEffect(() => {
        setIsDataLoading(status === 'pending');
    }, [status]);

    const getPatientsData = () => {
        return {
            pagination: pagination,
            listLoadStatus: status,
            setPagination,
            tableData: data?.data?.patients ?? [],
            totalPages: data?.data?.total
                ? Math.ceil(data?.data?.total / limit)
                : 1,
            isLoading: isDataLoading,
        };
    };

    const getPendingPaymentPatientsData = () => {
        return {
            pagination: pagination,
            listLoadStatus: status,
            setPagination,
            tableData: [], // Empty array since tab is removed
            totalPages: 1,
            isLoading: isDataLoading,
        };
    };

    useEffect(() => {
        if (showGlobalAlert.isOpen) {
            const timer = setTimeout(() => {
                setShowGlobalAlert((prevState) => ({
                    ...prevState,
                    isOpen: false,
                }));
            }, 5000);

            return () => clearTimeout(timer);
        }
    }, [showGlobalAlert.isOpen]);

    const handleSearch = (e) => {
        setPagination({ ...pagination, searchTerm: e, pageIndex: 0 });
    };

    const formattedWorkingHours = {
        Monday: [],
        Tuesday: [],
        Wednesday: [],
        Thursday: [],
        Friday: [],
        Saturday: [],
        Sunday: [],
    };

    if (clinicData?.data?.working_hours?.workingHours) {
        Object.entries(clinicData.data.working_hours.workingHours).forEach(
            ([day, hours]) => {
                const capitalizedDay =
                    day.charAt(0).toUpperCase() + day.slice(1);
                if (Array.isArray(hours)) {
                    formattedWorkingHours[capitalizedDay] = hours.map(
                        (slot) => ({
                            startTime: slot.startTime,
                            endTime: slot.endTime,
                            isWorkingDay: slot.isWorkingDay,
                        })
                    );
                }
            }
        );
    }
    return (
        <main className="">
            <>
                <Alert
                    className={classNames(
                        'font-semibold fixed top-4 left-1/2 -translate-x-1/2 z-50 !w-[560px] transition-opacity',
                        showGlobalAlert.isOpen
                            ? 'opacity-100 scale-100'
                            : 'opacity-0 scale-0'
                    )}
                    {...showGlobalAlert}
                    onClose={() => {
                        setShowGlobalAlert({
                            isOpen: false,
                            variant: 'success',
                            label: 'Patient created succesfully',
                            isLight: true,
                        });
                    }}
                />
                {isDataLoading && <CustomLoader />}
                <Patients
                    patientsTab={getPatientsData()}
                    pendingPaymentTab={getPendingPaymentPatientsData()}
                    onAddPatient={handleAddPatientClick}
                    control={control}
                    register={register}
                    setValue={setValue}
                    resetField={resetField}
                    watch={watch}
                    getValues={getValues}
                    handleSubmit={handleSubmit}
                    errors={errors}
                    reset={reset}
                    clearErrors={clearErrors}
                    handleSearch={handleSearch}
                    pageIndex={pagination.pageIndex + 1}
                    limit={limit}
                    searchTerm={pagination.searchTerm}
                    customRule={customRule}
                />
                {showAppointmentModal && (
                    <CreateAppointment
                        workinghours={formattedWorkingHours}
                        showAppointmentModal={showAppointmentModal}
                        setShowAppointmentModal={setShowAppointmentModal}
                        patientData={patientData}
                        setPatientData={setPatientData}
                    />
                )}
                <AddPatient
                    isOpen={isAddPatientModalOpen}
                    onClose={handleCloseModal}
                    modalTitle="Add Patient"
                    currentStep={currentStep}
                    handleNext={handleNext}
                    handleSave={handleSave}
                    handleBookAppointment={handleBookAppointment}
                    control={control}
                    register={register}
                    setValue={setValue}
                    resetField={resetField}
                    watch={watch}
                    getValues={getValues}
                    errors={errors}
                    setCurrentStep={setCurrentStep}
                    handleSubmit={handleSubmit}
                    showAlert={showModalAlert}
                    setShowAlert={setShowModalAlert}
                />
            </>
        </main>
    );
}
