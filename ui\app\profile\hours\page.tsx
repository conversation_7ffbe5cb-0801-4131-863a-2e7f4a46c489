'use client';

import HoursTemplate from '@/app/template/profile/hours/HoursTemplate';
import React from 'react';
import { getAuth } from '@/app/services/identity.service';
import { useGetUserDetails } from '@/app/services/user.queries';
import GoogleCalendarAuthModal from '@/app/molecules/GoogleCalendarAuthModal';

const Hours = () => {
    const breadcrumbList = [{ id: 1, name: 'Personal', path: '/' }];
    const userId = getAuth()?.userId;

    return (
        <>
            <HoursTemplate
                breadcrumbList={breadcrumbList}
                clinicUserId={userId}
            />
            <GoogleCalendarAuthModal />
        </>
    );
};

export default Hours;
