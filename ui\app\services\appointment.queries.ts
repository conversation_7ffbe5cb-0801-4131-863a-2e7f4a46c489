import {
    useMutation,
    useQuery,
    useQueryClient,
    useInfiniteQuery,
} from '@tanstack/react-query';
import {
    AppointmentParams,
    CreateAppointmentType,
    EnumAppointmentStatus,
} from '../types/appointment';
import {
    createPatientAppointment,
    getAppointmentDetails,
    getAssessmentList,
    getClinicAppointments,
    getLabReports,
    getLatestPatientAppointments,
    getPlanList,
    getPrescriptionList,
    createtNewAssessmentData,
    updateAppointmentDetails,
    updateAppointmentFeilds,
    updateAppointmentStatus,
    deleteAppointment,
    createtNewprescriptionData,
    addtLongTermprescriptionData,
    deleteLongTermPrescriptionData,
    getLongTermPrescriptionData,
    deleteLabReportFile,
} from './appointment.service';
import { UpdateAppointemntDetails } from '../types/update-appointment';
import { CreateAssessment } from '../types/create-assessment';
import { CreatePrescription } from '../types/create-prescription';
import { LongTermPrescription } from '../types/long-term-prescription';
import { ApiResponse } from './http.service';

export const useAppointmentMutation = (
    appointmentParams: AppointmentParams,
    setShowAppointmentModal: any,
    patientId: string
) => {
    const queryClient = useQueryClient();

    const createAppointmentMutation = useMutation({
        mutationFn: (appointDetails: CreateAppointmentType) =>
            createPatientAppointment(appointDetails),
        onSuccess: () => {
            queryClient.resetQueries({
                queryKey: ['latestPatientAppointments'],
                exact: false,
            });
            appointmentParams &&
                queryClient
                    .invalidateQueries({
                        queryKey: ['appointments', { ...appointmentParams }],
                    })
                    .then((res) => setShowAppointmentModal(false));
            patientId &&
                queryClient.invalidateQueries({
                    queryKey: ['latestPatientAppointments', patientId],
                });
            queryClient
                .invalidateQueries({
                    queryKey: ['GET_CLINIC_DOCTORS_AVAILABILITY'],
                })
                .then((res) => setShowAppointmentModal(false));
        },
    });

    return {
        createAppointmentMutation,
    };
};

export const useClinicAppointments = (appointmentParams: AppointmentParams) =>
    useInfiniteQuery({
        queryKey: ['appointments', { ...appointmentParams }],
        queryFn: async ({ pageParam = 1 }) => {
            const result = await getClinicAppointments({
                page: pageParam, //appointmentParams.p,
                limit: appointmentParams.limit,
                orderBy: appointmentParams.orderBy,
                date: appointmentParams.date,
                search: appointmentParams.search,
                doctors: appointmentParams.doctors,
                status: appointmentParams.status,
                onlyPrimary: false,
                includeGoogleEvents: true, // Always include Google events in calendar view
            });

            return result;
        },
        getNextPageParam: (lastPage, allPages) => {
            const totalAppointments = (lastPage as any)?.data?.total;
            const currentPage = allPages.length;
            const totalPages = Math.ceil(totalAppointments / 10);

            if (currentPage < totalPages) {
                return currentPage + 1;
            }
            return undefined;
        },
        initialPageParam: 1,
        keepPreviousData: true,
    });

export const useDoctorAppointments = (appointmentParams: AppointmentParams) =>
    useQuery({
        queryKey: ['doctor-appointments', { ...appointmentParams }],
        queryFn: async () => {
            return getClinicAppointments({
                page: appointmentParams.page,
                limit: appointmentParams.limit,
                orderBy: appointmentParams.orderBy,
                date: appointmentParams.date,
                search: appointmentParams.search,
                doctors: appointmentParams.doctors,
                status: appointmentParams.status,
                onlyPrimary: false,
            });
        },
    });
//treatment details
export const useUpdateAppointmentDetailsMutation = (
    // appointmentParams: AppointmentParams,
    patientId: string,
    pathName: string
) => {
    const queryClient = useQueryClient();

    const updateAppointmentDetailsMutation = useMutation({
        mutationFn: (details: UpdateAppointemntDetails) =>
            updateAppointmentDetails(details.appointmentId, details.data),
        onSuccess: async (response, variables) => {
            await queryClient.invalidateQueries({
                queryKey: ['appointmentDetails', variables.appointmentId],
            });
            await queryClient.invalidateQueries({
                queryKey: ['latestPatientAppointments', patientId],
                refetchType: 'all',
            });
        },

        onError: (error) => {
            console.log('Something went wrong = ', error);
        },
    });

    return {
        updateAppointmentDetailsMutation,
    };
};

export const useUpdateAppointmentDetails = () => {
    const queryClient = useQueryClient();

    return useMutation<
        any,
        Error,
        {
            appointmentId: string;
            data: any;
            isUseEffect?: boolean;
            callSiteId?: string;
        }
    >({
        mutationFn: ({ appointmentId, data }) =>
            updateAppointmentDetails(appointmentId, data),
        onSuccess: (response, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['appointmentDetails', variables.appointmentId],
            });
            queryClient.invalidateQueries({
                queryKey: ['latestPatientAppointments'],
            });
            queryClient.invalidateQueries({ queryKey: ['labReports'] });
            queryClient.resetQueries({
                queryKey: ['latestPatientAppointments'],
                exact: false,
            });
            queryClient.resetQueries({
                queryKey: ['appointments'],
                exact: false,
            });
        },
        onError: (error) => {
            console.error('Error updating appointment details:', error);
        },
    });
};

export const useLatestPatientAppointments = (patientId: string) =>
    useQuery({
        queryKey: ['latestPatientAppointments', patientId],
        queryFn: () => getLatestPatientAppointments(patientId),
        refetchOnMount: true,
        refetchOnWindowFocus: true,
        // enabled: !!patientId,
    });

export const useAppointmentDetails = (appointmentId: string) =>
    useQuery({
        queryKey: ['appointmentDetails', appointmentId],
        queryFn: () => getAppointmentDetails(appointmentId),
        enabled: !!appointmentId,
    });

export const useUpdateAppointmentStatus = (
    appointmentParams: AppointmentParams,
    patientId: string,
    doctorParams: AppointmentParams,
    userId: string
) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            appointmentId,
            status,
            soapPending = false,
            userId,
        }: {
            appointmentId: string;
            status: EnumAppointmentStatus;
            soapPending?: boolean;
            userId: string;
        }) =>
            updateAppointmentStatus(appointmentId, status, soapPending, userId),
        onSuccess: () => {
            patientId &&
                queryClient.invalidateQueries({
                    queryKey: ['latestPatientAppointments', patientId],
                });
            // : queryClient.resetQueries({
            //       queryKey: ['latestPatientAppointments'],
            //       exact: false,
            //   });
            appointmentParams &&
                queryClient.invalidateQueries({
                    queryKey: ['appointments', { ...appointmentParams }],
                });
            // : queryClient.resetQueries({
            //       queryKey: ['appointments'],
            //       exact: false,
            //   });
            doctorParams &&
                queryClient.invalidateQueries({
                    queryKey: ['doctor-appointments', { ...doctorParams }],
                });
        },
    });
};

export const useGetLabReports = (searchString: string, clinicId: string) => {
    return useQuery({
        queryFn: () => getLabReports(searchString, clinicId),
        queryKey: ['labReports', searchString],
    });
};

export const useCreateAssessmentMutation = () => {
    const createNewAssessmentMutation = useMutation({
        mutationFn: (details: CreateAssessment) =>
            createtNewAssessmentData(details.data),
        onSuccess: async (response) => {
            console.log('useCreateAssessmentMutation on Success', response);
        },
        onError: (error) => {
            console.log(
                'useCreateAssessmentMutation Something went wrong = ',
                error
            );
        },
    });

    return {
        createNewAssessmentMutation,
    };
};

export const useGetAssessmentList = (searchString: string) => {
    return useQuery({
        queryFn: () => getAssessmentList(searchString),
        queryKey: ['assessmentList', searchString],
    });
};

export const useGetPlanList = (
    searchString: string,
    exclude: string,
    clinicId: string
) => {
    return useQuery({
        queryFn: () => getPlanList(searchString, exclude, clinicId),
        queryKey: ['planList', searchString, exclude],
    });
};

export const useGetPrescriptionList = (
    searchString: string,
    clinicId: string,
    all: boolean = false
) => {
    return useQuery({
        queryFn: () => getPrescriptionList(searchString, clinicId, all),
        queryKey: ['prescriptionList', searchString],
    });
};

export const useUpdateAppointmentFeilds = (
    appointmentParams: AppointmentParams,
    patientId: string,
    doctorParams: AppointmentParams
) => {
    const queryClient = useQueryClient();

    const updateAppointmentMutation = useMutation({
        mutationFn: ({
            appointmentId,
            body,
        }: {
            appointmentId: string;
            body: any;
        }) => updateAppointmentFeilds(appointmentId, body),
        onSuccess: ({ data: { appointmentId } }) => {
            appointmentParams
                ? queryClient.invalidateQueries({
                      queryKey: ['appointments', { ...appointmentParams }],
                  })
                : queryClient.resetQueries({
                      queryKey: ['appointments'],
                      exact: false,
                  });
            patientId
                ? queryClient.invalidateQueries({
                      queryKey: ['latestPatientAppointments', patientId],
                  })
                : queryClient.resetQueries({
                      queryKey: ['latestPatientAppointments'],
                      exact: false,
                  });
            doctorParams &&
                queryClient.invalidateQueries({
                    queryKey: ['doctor-appointments', { ...appointmentParams }],
                });
            queryClient.invalidateQueries({
                queryKey: ['GET_CLINIC_DOCTORS_AVAILABILITY'],
            });
        },
    });
    return {
        updateAppointmentMutation,
    };
};

export const useDeleteAppointment = (
    appointmentParams?: AppointmentParams,
    isDashboard: boolean = false
) => {
    const queryClient = useQueryClient();

    const deleteAppointmentMutation = useMutation({
        mutationFn: (appointmentId: string) => deleteAppointment(appointmentId),
        onSuccess: ({ data: { appointmentId } }) => {
            isDashboard
                ? queryClient.invalidateQueries({
                      queryKey: [
                          'doctor-appointments',
                          { ...appointmentParams },
                      ],
                  })
                : queryClient.invalidateQueries({
                      queryKey: ['appointments', { ...appointmentParams }],
                  });
            queryClient.invalidateQueries({
                queryKey: ['GET_CLINIC_DOCTORS_AVAILABILITY'],
            });
            queryClient.resetQueries({
                queryKey: ['latestPatientAppointments'],
                exact: false,
            });
        },
    });

    return { deleteAppointmentMutation };
};

export const useCreatePrescriptionMutation = () => {
    const createNewPrescriptionMutation = useMutation({
        mutationFn: (details: CreatePrescription) =>
            createtNewprescriptionData(details.data),
        onSuccess: async (response) => {
            console.log('useCreatePrescriptionMutation on Success', response);
        },
        onError: (error) => {
            console.log(
                'useCreatePrescriptionMutation Something went wrong = ',
                error
            );
        },
    });

    return {
        createNewPrescriptionMutation,
    };
};
export const useAddLongTermPrescriptionMutation = () => {
    const queryClient = useQueryClient();
    const addLongTermPrescriptionMutation = useMutation({
        mutationFn: (details: LongTermPrescription) =>
            addtLongTermprescriptionData(details.data),
        onSuccess: async (response) => {
            queryClient.invalidateQueries({
                queryKey: ['getLongTermMedication', response.data.patientId],
            });
        },
        onError: (error) => {
            console.log(
                'useAddLongTermPrescriptionMutation Something went wrong = ',
                error
            );
        },
    });

    return {
        addLongTermPrescriptionMutation,
    };
};

export const useDeleteLongTermPrescriptionMutation = () => {
    const deleteLongTermPrescriptionMutation = useMutation({
        mutationFn: (details: LongTermPrescription) =>
            deleteLongTermPrescriptionData(details.data),
        onSuccess: async (response) => {
            console.log(
                'useRemoveLongTermPrescriptionMutation on Success',
                response
            );
        },
        onError: (error) => {
            console.log(
                'useRemoveLongTermPrescriptionMutation Something went wrong = ',
                error
            );
        },
    });

    return {
        deleteLongTermPrescriptionMutation,
    };
};

export const useGetLongTermMedicationForPatient = (patientId: string) => {
    return useQuery({
        queryFn: () => getLongTermPrescriptionData(patientId),
        queryKey: ['getLongTermMedication', patientId],
    });
};

export const useDeleteLabReportFile = () => {
    const queryClient = useQueryClient();

    return useMutation<
        ApiResponse<any>,
        Error,
        { labReportId: string; fileId: string; lineItemId: string }
    >({
        mutationFn: ({ labReportId, fileId, lineItemId }) =>
            deleteLabReportFile(labReportId, fileId, lineItemId),
        onSuccess: async (response, variables) => {
            queryClient.invalidateQueries({ queryKey: ['labReports'] });
            queryClient.invalidateQueries({ queryKey: ['patientLabReports'] });
            queryClient.invalidateQueries({ queryKey: ['appointmentDetails'] });

            // Invalidate specific appointment details and latest patient appointments
            if (response?.data?.appointmentId) {
                await queryClient.invalidateQueries({
                    queryKey: [
                        'appointmentDetails',
                        response.data.appointmentId,
                    ],
                });
            }
            if (response?.data?.patientId) {
                await queryClient.invalidateQueries({
                    queryKey: [
                        'latestPatientAppointments',
                        response.data.patientId,
                    ],
                    refetchType: 'all',
                });
            }
        },
    });
};
