import {
    AppointmentParams,
    CreateAppointmentType,
    EnumAppointmentStatus,
} from '../types/appointment';
import * as Http from './http.service';
import { getAuth } from './identity.service';
import {
    CREATE_APPOINTMENT,
    GET_APPOINTMENT_DETAILS,
    GET_APPOINTMENTS,
    UPDATE_APPOINTMENT_DETAILS,
    GET_LATEST_PATIENT_APPOINTMENTS,
    UPDATE_APPOINTMENT_STATUS,
    GET_LAB_REPORTS,
    GET_ASSESSMENT_LIST,
    GET_PLAN_LIST,
    GET_PRESCRIPTION_LIST,
    CREATE_NEW_ASSESSMENT,
    UPDATE_APPOINTMENT_FEILDS,
    DELETE_APPOINTMENT,
    CREATE_NEW_PRESCRIPTION,
    ADD_LONG_TERM_PRESCRIPTION,
    DELETE_LONG_TERM_PRESCRIPTION,
    GET_LONG_TERM_PRESCRIPTION,
    DELETE_LAB_REPORT_FILE,
    CHECK_PATIENT_ONGOING_APPOINTMENT,
    DOWNLOAD_TODAYS_APPOINTMENT,
} from './url.service';
export const createPatientAppointment = (
    appontmentDetails: CreateAppointmentType
) => {
    return Http.postWithAuth(CREATE_APPOINTMENT(), { ...appontmentDetails });
};

export const getClinicAppointments = ({
    page,
    limit,
    orderBy,
    date,
    search,
    doctors,
    status,
    onlyPrimary,
    includeGoogleEvents,
}: AppointmentParams & { includeGoogleEvents?: boolean }) => {
    return Http.getWithAuth(GET_APPOINTMENTS({
        page,
        limit,
        orderBy,
        date,
        search,
        doctors,
        status,
        onlyPrimary,
        includeGoogleEvents
    }));
};
//patient appointment

export const getLatestPatientAppointments = (patientId: string) => {
    return Http.getWithAuth(GET_LATEST_PATIENT_APPOINTMENTS(patientId));
};

export const checkOnGoingAppointmentService = (patientId: string) => {
    return Http.getWithAuth(CHECK_PATIENT_ONGOING_APPOINTMENT(patientId));
};

// Update appointment details
export const updateAppointmentDetails = (appointmentId: string, data: any) => {
    return Http.putWithAuth(UPDATE_APPOINTMENT_DETAILS(appointmentId), data);
};

export const getAppointmentDetails = (appointmentId: string) => {
    return Http.getWithAuth(GET_APPOINTMENT_DETAILS(appointmentId));
};

export const updateAppointmentStatus = (
    appointmentId: string,
    status: EnumAppointmentStatus,
    soapPending: boolean,
    userId: string
) => {
    return Http.putWithAuth(UPDATE_APPOINTMENT_STATUS(appointmentId), {
        status,
        soapPending,
        userId: userId,
    });
};

export const getLabReports = (search: string, clinicId: string) => {
    return Http.getWithAuth(GET_LAB_REPORTS(search, clinicId));
};

export const getAssessmentList = (search: string) => {
    return Http.getWithAuth(GET_ASSESSMENT_LIST(search));
};

export const getPlanList = (
    search: string,
    exclude: string,
    clinicId: string
) => {
    return Http.getWithAuth(GET_PLAN_LIST(search, exclude, clinicId));
};

export const getPrescriptionList = (
    search: string,
    clinicId: string,
    all: boolean = false
) => {
    return Http.getWithAuth(GET_PRESCRIPTION_LIST(search, clinicId, all));
};

// This is to create a new value for assessment other than the predefined list
export const createtNewAssessmentData = (data: any) => {
    return Http.postWithAuth(CREATE_NEW_ASSESSMENT(), data);
};

export const updateAppointmentFeilds = (appointmentId: string, data: any) => {
    return Http.putWithAuth(UPDATE_APPOINTMENT_FEILDS(appointmentId), data);
};

export const deleteAppointment = (appointmentId: string) => {
    return Http.deleteWithAuth(DELETE_APPOINTMENT(appointmentId));
};

// This is to create a new value for medication/prescription other than the predefined list
export const createtNewprescriptionData = (data: any) => {
    return Http.postWithAuth(CREATE_NEW_PRESCRIPTION(), data);
};

// This is to add a entry for long term medication/prescription
export const addtLongTermprescriptionData = (data: any) => {
    return Http.postWithAuth(ADD_LONG_TERM_PRESCRIPTION(), data);
};

// This is to delete a entry for long term medication/prescription
export const deleteLongTermPrescriptionData = (data: any) => {
    return Http.deleteWithAuth(
        DELETE_LONG_TERM_PRESCRIPTION(data.patientId, data.prescriptionId)
    );
};

// This is to get a entry for long term medication/prescription
export const getLongTermPrescriptionData = (patientId: string) => {
    return Http.getWithAuth(GET_LONG_TERM_PRESCRIPTION(patientId));
};

export const deleteLabReportFile = (labReportId: string, fileKey: string, lineItemId: string) => {
    return Http.deleteWithAuth(DELETE_LAB_REPORT_FILE(labReportId, fileKey, lineItemId));
};

export const downloadTodaysAppointment = (date: string, clinicId: string) => Http.getWithAuth(DOWNLOAD_TODAYS_APPOINTMENT(date, clinicId),'pdf')