import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    addUserToClinic,
    createClinicConsumable,
    createClinicDiagnostic,
    createClinicMedication,
    createClinicProduct,
    createClinicRoom,
    createClinicService,
    createClinicUser,
    createClinicVaccination,
    createNewClinic,
    deactivateClinic,
    deleteClinicConsumable,
    deleteClinicDiagnostic,
    deleteClinicMedication,
    deleteClinicProduct,
    deleteClinicService,
    deleteClinicVaccination,
    deleteInventoryItem,
    deleteRoom,
    downloadLatestInventory,
    editClinic,
    getAllClinics,
    getClinic,
    getClinicConsumables,
    getClinicDetails,
    getClinicDiagnostics,
    getClinicMedications,
    getClinicProducts,
    getClinicRooms,
    getClinicServices,
    getClinicVaccinations,
    getClinicWorkingHours,
    searchUsersAcrossClinics,
    updateClinicConsumable,
    updateClinicDetails,
    updateClinicDiagnostic,
    updateClinicMedication,
    updateClinicProduct,
    updateClinicRoom,
    updateClinicService,
    updateClinicVaccination,
    updateClinicWorkingHours,
    uploadClinicExcel,
    getClientBookingSettings,
    updateClientBookingSettings,
} from './clinic.service';
import { ClinicFormData } from '../brands/page';
import {
    ClientBookingDayScheduleDto,
    ClientBookingWorkingHoursDto,
    DoctorDto,
} from '@/lib/api';

// Interface for UpdateClinicDto
interface ClinicCustomRule {
    patientLastNameAsOwnerLastName: boolean;
}

export interface UpdateClinicDto {
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    pin?: string;
    state?: string;
    country?: string;
    email?: string;
    website?: string;
    logoUrl?: string;
    drugLicenseNumber?: string;
    phoneNumbers?: string[];
    customRule?: ClinicCustomRule;
}

export interface ClinicResponse {
    id: string;
    name: string;
    customRule: ClinicCustomRule;
    // ... other fields
}

export interface EditClinicDto {
    name?: string;
    adminFirstName?: string;
    adminLastName?: string;
    adminEmail?: string;
    adminMobile?: string;
}

export interface UserFormData {
    firstName: string;
    lastName: string;
    role: 'Doctor' | 'Receptionist' | 'Lab Technician';
    email: string;
}

export interface CreateClinicRoomDto {
    name: string;
    clinicId: string;
}

export interface UpdateClinicRoomDto {
    name?: string;
    description?: string;
    clinicId?: string;
}
interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    status: boolean;
}

interface UpdateClinicItemDto {
    id: string;
    data: any;
}

export const useClinicRooms = ({ clinicId }: { clinicId: string }) =>
    useQuery({
        queryKey: ['clinic-rooms', clinicId],
        queryFn: () => getClinicRooms(clinicId),
    });

export const useCreateClinicRoom = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, CreateClinicRoomDto>({
        mutationFn: (createClinicRoomDto) =>
            createClinicRoom(createClinicRoomDto),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinic-rooms'] });
        },
        onError: (error) => {
            console.error('Error creating clinic room:', error);
        },
    });
};

export const useUpdateClinicRoom = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, { id: string; data: UpdateClinicRoomDto }>({
        mutationFn: ({ id, data }) => updateClinicRoom(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinic-rooms'] });
        },
        onError: (error) => {
            console.error('Error updating clinic room:', error);
        },
    });
};

export const useDeleteRoom = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (roomId: string) => deleteRoom(roomId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({ queryKey: ['clinic-rooms'] });
        },
        onError: (error) => {
            console.error('Error deleting room:', error);
        },
    });
};

export const useUpdateClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            clinicId,
            data,
        }: {
            clinicId: string;
            data: UpdateClinicDto;
        }) => updateClinicDetails(clinicId, data),
        onSuccess: (response, variables) => {
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
            queryClient.invalidateQueries({
                queryKey: ['clinicsDetails', variables.clinicId],
            });
        },
        onError: (error) => {
            console.error('Failed to update clinic details: ', error);
        },
    });
};
export const useEditClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: EditClinicDto }) =>
            editClinic(id, data),
        onSuccess: (data) => {
            if (data.status === true) {
                queryClient.invalidateQueries({ queryKey: ['clinics'] });
            }
        },
        onError: (error) => {
            console.error('Error creating brand:', error);
        },
    });
};

export const useCreateClinicUserMutation = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            clinicId,
            brandId,
            userData,
        }: {
            clinicId: string;
            brandId: string;
            userData: UserFormData;
        }) => createClinicUser(clinicId, brandId, userData),
        onSuccess: () => {
            // Invalidate and refetch
            //
            queryClient.invalidateQueries({ queryKey: ['clinicUsers'] });
        },
        onError: (error) => {
            console.error('Failed to create clinic users: ', error);
        },
    });
};

export const useSearchUsersAcrossClinics = () => {
    return useMutation({
        mutationFn: ({
            brandId,
            searchTerm,
            excludeClinicId,
        }: {
            brandId: string;
            searchTerm: string;
            excludeClinicId: string;
        }) => searchUsersAcrossClinics(brandId, searchTerm, excludeClinicId),
    });
};

export const useAddUserToClinic = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            userId,
            clinicId,
            brandId,
        }: {
            userId: string;
            clinicId: string;
            brandId: string;
        }) => addUserToClinic(userId, clinicId, brandId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: ['clinicUsers'],
            });
        },
    });
};

export const useExcelUpload = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            file,
            clinicId,
            brandId,
        }: {
            file: File;
            clinicId: string;
            brandId: string;
        }) => uploadClinicExcel(file, clinicId, brandId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: ['clinicConsumables'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicProducts'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicMedications'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicVaccinations'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicServices'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicDiagnostics'],
            });
        },
    });
};

export const useClinicConsumables = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicConsumables', clinicId, page, limit],
        queryFn: () => getClinicConsumables(clinicId, page, limit),
    });

export const useClinicProducts = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicProducts', clinicId, page, limit],
        queryFn: () => getClinicProducts(clinicId, page, limit),
    });

export const useClinicMedications = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicMedications', clinicId, page, limit],
        queryFn: () => getClinicMedications(clinicId, page, limit),
    });

export const useClinicVaccinations = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicVaccinations', clinicId, page, limit],
        queryFn: () => getClinicVaccinations(clinicId, page, limit),
    });

export const useClinicServices = (
    clinicId: string,
    page: number,
    limit: number
) =>
    useQuery({
        queryKey: ['clinicServices', clinicId, page, limit],
        queryFn: () => getClinicServices(clinicId, page, limit),
    });

export const useClinicDiagnostics = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) =>
    useQuery({
        queryKey: ['clinicDiagnostics', clinicId, page, limit],
        queryFn: () => getClinicDiagnostics(clinicId, page, limit),
    });

export const useCreateClinicConsumable = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicConsumable,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicConsumables'] });
        },
    });
};

export const useCreateClinicProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicProduct,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicProducts'] });
        },
    });
};

export const useCreateClinicMedication = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicMedication,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicMedications'] });
        },
    });
};

export const useCreateClinicVaccination = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicVaccination,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicVaccinations'] });
        },
    });
};

export const useCreateClinicService = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicService,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicServices'] });
        },
    });
};

export const useCreateClinicDiagnostic = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createClinicDiagnostic,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicDiagnostics'] });
        },
    });
};

// Update clinic inventory
export const useUpdateClinicConsumable = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicConsumable(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicConsumables'] });
        },
        onError: (error) => {
            console.error('Error updating clinic consumable:', error);
        },
    });
};
export const useUpdateClinicProduct = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicProduct(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicProducts'] });
        },
        onError: (error) => {
            console.error('Error updating clinic product:', error);
        },
    });
};

export const useUpdateClinicMedication = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicMedication(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicMedications'] });
        },
        onError: (error) => {
            console.error('Error updating clinic medication:', error);
        },
    });
};

export const useUpdateClinicVaccination = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicVaccination(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicVaccinations'] });
        },
        onError: (error) => {
            console.error('Error updating clinic vaccination:', error);
        },
    });
};

export const useUpdateClinicService = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicService(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicServices'] });
        },
        onError: (error) => {
            console.error('Error updating clinic service:', error);
        },
    });
};

export const useUpdateClinicDiagnostic = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, UpdateClinicItemDto>({
        mutationFn: ({ id, data }) => updateClinicDiagnostic(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicDiagnostics'] });
        },
        onError: (error) => {
            console.error('Error updating clinic diagnostic:', error);
        },
    });
};

// Delete clinic inventory
export const useDeleteClinicConsumable = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicConsumable(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicConsumables'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicProduct = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicProduct(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicProducts'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicMedication = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicMedication(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicMedications'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicVaccination = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicVaccination(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicVaccinations'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicService = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicService(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicServices'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDeleteClinicDiagnostic = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => deleteClinicDiagnostic(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinicDiagnostics'] });
        },
        onError: (error) => {
            /* handle error */
        },
    });
};

export const useDownloadLatestInventory = () => {
    return useMutation({
        mutationFn: ({ clinicId }: { clinicId: string }) =>
            downloadLatestInventory(clinicId),
    });
};

export const useDeleteInventoryItem = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: ({
            itemType,
            itemId,
        }: {
            itemType: string;
            itemId: string;
        }) => deleteInventoryItem(itemType, itemId),
        onSuccess: async () => {
            await queryClient.invalidateQueries({
                queryKey: ['clinicConsumables'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicProducts'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicMedications'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicVaccinations'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicServices'],
            });
            await queryClient.invalidateQueries({
                queryKey: ['clinicDiagnostics'],
            });
        },
    });
};
export const useCreateClinic = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (clinicData: ClinicFormData) => createNewClinic(clinicData),
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate the clinics query to refresh data after success
                queryClient.invalidateQueries({ queryKey: ['clinics'] });
            }
        },
        onError: (error: any) => {
            if (error?.response?.data?.message) {
                // If there's a specific error message from the backend
                console.error(
                    'Failed to create clinic:',
                    error.response.data.message
                );
            } else {
                // General fallback error message
                console.error(
                    'An unexpected error occurred while creating the clinic.'
                );
            }
        },
    });
};
export const useGetAllClinics = () => {
    return useQuery({
        queryKey: ['clinics'],
        queryFn: () => getAllClinics(),
    });
};

export const useGetClinic = (id: string) => {
    return useQuery({
        queryFn: () => getClinic(id),
        queryKey: ['clinicsDetails', id],
    });
};
export const useDeactivateClinicMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => {
            return deactivateClinic(id);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['clinics'] });
        },
        onError: (error) => {
            console.error('Failed to deactivate clinic: ', error);
        },
    });
};

export const useGetClinicWorkingHours = (clinicId: string) =>
    useQuery({
        queryKey: ['clinicWorkingHours', clinicId],
        queryFn: () => getClinicWorkingHours(clinicId),
    });

export const useUpdateClinicWorkingHours = () => {
    const queryClient = useQueryClient();

    return useMutation<any, Error, { clinicId: string; data: any }>({
        mutationFn: ({ clinicId, data }) =>
            updateClinicWorkingHours(clinicId, data),
        onSuccess: (data, variables) => {
            // Invalidate relevant queries
            queryClient.invalidateQueries({
                queryKey: ['clinicWorkingHours', variables.clinicId],
            });
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
        },
        onError: (error) => {
            console.error('Error updating clinic working hours:', error);
        },
    });
};

export const useGetClinicDetails = (clinicId: string) =>
    useQuery({
        queryKey: ['clinicDetails', clinicId],
        queryFn: async () => {
            const response = await getClinicDetails(clinicId);
            return response;
        },
    });

// Time duration interface for granular time settings
export interface TimeDuration {
    days?: number | null;
    hours?: number | null;
    minutes?: number | null;
}

export interface UpdateClientBookingSettingsDto {
    isEnabled?: boolean;
    workingHours?: ClientBookingWorkingHoursDto;
    allowedDoctorIds?: string[] | null;
    allowAllDoctors?: boolean;

    // New time duration fields
    minBookingLeadTime?: TimeDuration | null;
    modificationDeadlineTime?: TimeDuration | null;
    maxAdvanceBookingTime?: TimeDuration | null;

    // Legacy fields for backward compatibility
    minBookingLeadHours?: number | null;
    modificationDeadlineHours?: number | null;
}

// Added DoctorInfo type based on API response and backend DTO
interface DoctorInfo {
    id: string;
    name: string;
}

export interface ClientBookingSettingsDto
    extends Omit<
        // Use Omit if Update DTO still has fields GET doesn't, otherwise just extend
        UpdateClientBookingSettingsDto,
        'minBookingLeadHours' | 'modificationDeadlineHours' // Example: Omit fields if GET structure differs significantly
    > {
    // Explicitly define all fields expected from the GET response
    isEnabled: boolean;
    workingHours?: ClientBookingWorkingHoursDto | null;
    allowedDoctorIds?: string[] | null;
    allowAllDoctors?: boolean;
    allowedDoctorsInfo?: DoctorInfo[] | null; // Added based on API response

    // New time duration fields
    minBookingLeadTime?: TimeDuration | null;
    modificationDeadlineTime?: TimeDuration | null;
    maxAdvanceBookingTime?: TimeDuration | null;

    // Legacy fields for backward compatibility
    minBookingLeadHours?: number | null;
    modificationDeadlineHours?: number | null;
}

// Query hook to get client booking settings
export const useGetClientBookingSettings = (clinicId: string) => {
    return useQuery<{
        data: ClientBookingSettingsDto;
        message: string;
        status: boolean;
    }>({
        // Specify the expected return type
        queryKey: ['clientBookingSettings', clinicId],
        queryFn: () => getClientBookingSettings(clinicId),
        enabled: !!clinicId, // Only run query if clinicId is truthy
        staleTime: 5 * 60 * 1000, // Optional: Cache for 5 minutes
        // Add error handling or other options as needed
    });
};

// Mutation hook to update client booking settings
export const useUpdateClientBookingSettingsMutation = () => {
    const queryClient = useQueryClient();

    return useMutation<
        any, // Adjust success response type if needed
        Error,
        { clinicId: string; data: UpdateClientBookingSettingsDto } // Variables type
    >({
        mutationFn: ({ clinicId, data }) =>
            updateClientBookingSettings(clinicId, data),
        onSuccess: (data, variables) => {
            // Invalidate the query for client booking settings to refetch
            queryClient.invalidateQueries({
                queryKey: ['clientBookingSettings', variables.clinicId],
            });
            // Optionally invalidate the general clinic details query too
            queryClient.invalidateQueries({
                queryKey: ['clinicDetails', variables.clinicId],
            });
            // Optionally add success notification/toast
        },
        onError: (error) => {
            console.error('Error updating client booking settings:', error);
            // Optionally add error notification/toast
        },
    });
};

// Define the structure based on your API DTOs
// Export DoctorDto if defined here, or ensure it's imported and re-exported
// If DoctorDto is defined within useGetClinicUsers, it needs to be moved out and exported.
// Assuming DoctorDto is meant to be shared:
// Corrected DoctorDto to represent a single doctor object
export interface DoctorDto {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    isActive: boolean;
    createdAt: string;
    role: {
        id: string;
        name: string;
        description: string;
    };
    userId: string;
    workingHours?: any;
}
