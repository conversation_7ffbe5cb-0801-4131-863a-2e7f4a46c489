import * as Http from './http.service';
import {
    ADD_USER_TO_CLINIC,
    CREATE_CLINIC,
    CREATE_C<PERSON>IN<PERSON>_CONSUMABLES,
    CREATE_<PERSON>LINIC_DIAGNOSTICS,
    CREATE_<PERSON>LINIC_MEDICATIONS,
    CREATE_<PERSON><PERSON><PERSON><PERSON>_PRODUCTS,
    CREATE_C<PERSON>IN<PERSON>_ROOM,
    CREATE_C<PERSON>INIC_SERVICES,
    CREATE_C<PERSON>IN<PERSON>_USER,
    CREATE_C<PERSON><PERSON>IC_VACCINATIONS,
    DEAC<PERSON>VATE_CLINIC,
    DELETE_CLINIC_CONSUMABLES,
    <PERSON>LETE_CLINIC_DIAGNOSTICS,
    <PERSON>LETE_<PERSON><PERSON>IN<PERSON>_MEDICATIONS,
    DELETE_CLINIC_PRODUCTS,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON>IN<PERSON>_SERVICES,
    <PERSON>LETE_CLINIC_VACCINATIONS,
    DELETE_INVENTORY_ITEM,
    <PERSON>LETE_ROOM,
    DOWNLOAD_LATEST_INVENTORY,
    EDIT_CLINIC_DETAILS,
    GET_ALL_CLINICS,
    GET_<PERSON>LIN<PERSON>,
    GET_CLINIC_CONSUMABLES,
    GET_<PERSON><PERSON>IN<PERSON>_DETAILS,
    GET_<PERSON><PERSON>IN<PERSON>_DIAGNOSTICS,
    GET_CLINIC_MEDICATIONS,
    GET_CLINIC_PRODUCTS,
    GET_CLINIC_ROOMS,
    GET_CLINIC_SERVICES,
    GET_CLINIC_VACCINATIONS,
    GET_CLINIC_WORKING_HOURS,
    SEARCH_USERS_ACROSS_CLINICS,
    UPDATE_CLINIC_CONSUMABLES,
    UPDATE_CLINIC_DETAILS,
    UPDATE_CLINIC_DIAGNOSTICS,
    UPDATE_CLINIC_MEDICATIONS,
    UPDATE_CLINIC_PRODUCTS,
    UPDATE_CLINIC_ROOM,
    UPDATE_CLINIC_SERVICES,
    UPDATE_CLINIC_VACCINATIONS,
    UPDATE_CLINIC_WORKING_HOURS,
    UPLOAD_CLINIC_EXCEL,
    GET_CLIENT_BOOKING_SETTINGS,
    UPDATE_CLIENT_BOOKING_SETTINGS,
} from './url.service';
import {
    CreateClinicRoomDto,
    EditClinicDto,
    UpdateClinicDto,
    UpdateClinicRoomDto,
    UserFormData,
    UpdateClientBookingSettingsDto,
} from './clinic.queries';
import { ClinicFormData } from '../brands/page';

export const getClinicRooms = (clinicId: string) => {
    return Http.getWithAuth(GET_CLINIC_ROOMS(clinicId));
};

export const createClinicRoom = (createClinicRoomDto: CreateClinicRoomDto) => {
    return Http.postWithAuth(CREATE_CLINIC_ROOM(), createClinicRoomDto);
};

export const updateClinicRoom = (
    id: string,
    updateClinicRoomDto: UpdateClinicRoomDto
) => {
    return Http.putWithAuth(UPDATE_CLINIC_ROOM(id), updateClinicRoomDto);
};

export const deleteRoom = (roomId: string) => {
    return Http.deleteWithAuth(DELETE_ROOM(roomId));
};

export const updateClinicDetails = async (
    clinicId: string,
    data: UpdateClinicDto
) => {
    const response = await Http.putWithAuth(
        UPDATE_CLINIC_DETAILS(clinicId),
        data
    );
    return response;
};

export const createClinicUser = (
    clinicId: string,
    brandId: string,
    userData: UserFormData
) => {
    return Http.postWithAuth(CREATE_CLINIC_USER(clinicId, brandId), userData);
};

export const searchUsersAcrossClinics = (
    brandId: string,
    searchTerm: string,
    excludeClinicId: string
) => {
    return Http.getWithAuth(
        SEARCH_USERS_ACROSS_CLINICS(brandId, searchTerm, excludeClinicId)
    );
};

export const addUserToClinic = (
    userId: string,
    clinicId: string,
    brandId: string
) => {
    return Http.postWithAuth(ADD_USER_TO_CLINIC(userId, clinicId, brandId), {});
};

export const uploadClinicExcel = (
    file: File,
    clinicId: string,
    brandId: string
) => {
    const formData = new FormData();
    formData.append('file', file);
    return Http.postWithAuth(UPLOAD_CLINIC_EXCEL(clinicId, brandId), formData);
};

export const getClinicConsumables = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_CONSUMABLES(clinicId, page, limit));
};

export const getClinicProducts = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_PRODUCTS(clinicId, page, limit));
};

export const getClinicMedications = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_MEDICATIONS(clinicId, page, limit));
};

export const getClinicVaccinations = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_VACCINATIONS(clinicId, page, limit));
};

export const getClinicServices = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_SERVICES(clinicId, page, limit));
};

export const getClinicDiagnostics = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => {
    return Http.getWithAuth(GET_CLINIC_DIAGNOSTICS(clinicId, page, limit));
};

export const createClinicConsumable = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_CONSUMABLES(), body);
};

export const createClinicProduct = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_PRODUCTS(), body);
};

export const createClinicMedication = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_MEDICATIONS(), body);
};

export const createClinicVaccination = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_VACCINATIONS(), body);
};

export const createClinicService = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_SERVICES(), body);
};

export const createClinicDiagnostic = (body: any) => {
    return Http.postWithAuth(CREATE_CLINIC_DIAGNOSTICS(), body);
};

// Update Functions for Consumables, Products, Medications, Vaccinations, Services, Diagnostics

export const updateClinicConsumable = (
    id: string,
    updateConsumableDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_CONSUMABLES(id),
        updateConsumableDto
    );
};

export const updateClinicProduct = (id: string, updateProductDto: any) => {
    return Http.patchWithAuth(UPDATE_CLINIC_PRODUCTS(id), updateProductDto);
};

export const updateClinicMedication = (
    id: string,
    updateMedicationDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_MEDICATIONS(id),
        updateMedicationDto
    );
};

export const updateClinicVaccination = (
    id: string,
    updateVaccinationDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_VACCINATIONS(id),
        updateVaccinationDto
    );
};

export const updateClinicService = (id: string, updateServiceDto: any) => {
    return Http.patchWithAuth(UPDATE_CLINIC_SERVICES(id), updateServiceDto);
};

export const updateClinicDiagnostic = (
    id: string,
    updateDiagnosticDto: any
) => {
    return Http.patchWithAuth(
        UPDATE_CLINIC_DIAGNOSTICS(id),
        updateDiagnosticDto
    );
};

// Delete Functions for Consumables, Products, Medications, Vaccinations, Services, Diagnostics

export const deleteClinicConsumable = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_CONSUMABLES(id));
};

export const deleteClinicProduct = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_PRODUCTS(id));
};

export const deleteClinicMedication = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_MEDICATIONS(id));
};

export const deleteClinicVaccination = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_VACCINATIONS(id));
};

export const deleteClinicService = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_SERVICES(id));
};

export const deleteClinicDiagnostic = (id: string) => {
    return Http.deleteWithAuth(DELETE_CLINIC_DIAGNOSTICS(id));
};

export const downloadLatestInventory = async (clinicId: string) => {
    const response = await Http.getWithAuthBlob(
        DOWNLOAD_LATEST_INVENTORY(clinicId)
    );
    return response;
};

export const deleteInventoryItem = async (
    itemType: string,
    itemId: string
): Promise<void> => {
    await Http.deleteWithAuth(DELETE_INVENTORY_ITEM(itemType, itemId));
};
export const createNewClinic = (clinicData: ClinicFormData) => {
    return Http.postWithAuth(CREATE_CLINIC(), clinicData);
};

export const getAllClinics = () => {
    return Http.getWithAuth(GET_ALL_CLINICS());
};

export const getClinic = async (id: string) => {
    const response = await Http.getWithAuth(GET_CLINIC(id));
    return response;
};

export const editClinic = (id: string, data: EditClinicDto) => {
    return Http.putWithAuth(EDIT_CLINIC_DETAILS(id), data);
};

export const deactivateClinic = (id: string) => {
    return Http.putWithAuth(DEACTIVATE_CLINIC(id), {});
};
export const getClinicWorkingHours = (clinicId: string) => {
    return Http.getWithAuth(GET_CLINIC_WORKING_HOURS(clinicId));
};

export const updateClinicWorkingHours = (
    clinicId: string,
    workingHours: any
) => {
    return Http.putWithAuth(
        UPDATE_CLINIC_WORKING_HOURS(clinicId),
        workingHours
    );
};

// Client Booking Settings Service Functions
/**
 * Fetches the client booking settings for a specific clinic.
 * @param clinicId - The ID of the clinic.
 * @returns Promise resolving to the client booking settings.
 */
export const getClientBookingSettings = (clinicId: string) => {
    if (!clinicId) {
        // Or handle this case appropriately
        return Promise.reject(new Error('Clinic ID is required'));
    }
    return Http.getWithAuth(GET_CLIENT_BOOKING_SETTINGS(clinicId));
};

/**
 * Updates the client booking settings for a specific clinic.
 * @param clinicId - The ID of the clinic.
 * @param data - The updated settings data (UpdateClientBookingSettingsDto).
 * @returns Promise resolving to the updated clinic data.
 */
export const updateClientBookingSettings = (
    clinicId: string,
    data: UpdateClientBookingSettingsDto // Use the defined DTO
) => {
    if (!clinicId) {
        // Or handle this case appropriately
        return Promise.reject(new Error('Clinic ID is required'));
    }
    return Http.putWithAuth(UPDATE_CLIENT_BOOKING_SETTINGS(clinicId), data);
};
// End Client Booking Settings Service Functions

export const getClinicDetails = async (clinicId: string) => {
    const url = GET_CLINIC_DETAILS(clinicId);
    return Http.getWithAuth(url);
};
