import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
    getGoogleCalendarStatus,
    getGoogleCalendarAuthUrl,
    getGoogleCalendars,
    connectGoogleCalendar,
    disconnectGoogleCalendar,
} from './google-calendar.service';

/**
 * Hook to get Google Calendar connection status
 */
export const useGoogleCalendarStatus = () => {
    return useQuery({
        queryKey: ['google-calendar-status'],
        queryFn: getGoogleCalendarStatus,
        refetchInterval: (data) => {
            // If connected successfully, reduce refetch frequency
            if (data?.data?.syncStatus === 'SUCCESS' && data?.data?.isConnected) {
                return 5 * 60 * 1000; // 5 minutes when stable
            }
            // If there's an error or pending state, check more frequently but not too aggressive
            return 60 * 1000; // 1 minute for pending/error states
        },
        staleTime: 30 * 1000, // Consider data fresh for 30 seconds
        retry: 2,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    });
};

/**
 * Hook to get Google OAuth authorization URL
 */
export const useGoogleCalendarAuthUrl = () => {
    return useQuery({
        queryKey: ['google-calendar-auth-url'],
        queryFn: getGoogleCalendarAuthUrl,
        enabled: false, // Only fetch when explicitly called
    });
};

/**
 * Hook to get user's Google calendars
 */
export const useGoogleCalendars = (
    enabled: boolean = false,
    onSuccess?: (data: Awaited<ReturnType<typeof getGoogleCalendars>>) => void,
) => {
    return useQuery({
        queryKey: ['google-calendars'],
        queryFn: getGoogleCalendars,
        enabled,
        retry: 2,
        onSuccess,
    });
};

/**
 * Hook to connect to Google Calendar
 */
export const useConnectGoogleCalendar = () => {
    const queryClient = useQueryClient();
    
    return useMutation({
        mutationFn: connectGoogleCalendar,
        onSuccess: () => {
            // Invalidate related queries to refetch fresh data
            queryClient.invalidateQueries({ queryKey: ['google-calendar-status'] });
            queryClient.invalidateQueries({ queryKey: ['google-calendars'] });
        },
    });
};

/**
 * Hook to disconnect from Google Calendar
 */
export const useDisconnectGoogleCalendar = () => {
    const queryClient = useQueryClient();
    
    return useMutation({
        mutationFn: disconnectGoogleCalendar,
        onSuccess: () => {
            // Invalidate related queries to refetch fresh data
            queryClient.invalidateQueries({ queryKey: ['google-calendar-status'] });
            queryClient.invalidateQueries({ queryKey: ['google-calendars'] });
        },
    });
}; 