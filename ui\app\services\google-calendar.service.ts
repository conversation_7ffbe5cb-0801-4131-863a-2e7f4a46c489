import * as Http from './http.service';
import {
    GET_GOOGLE_CALENDAR_STATUS,
    GET_GOOGLE_CALENDAR_AUTH_URL,
    GET_GOOGLE_CALENDARS,
    CONNECT_GOOGLE_CALENDAR,
    DISCONNECT_GOOGLE_CALENDAR,
} from './url.service';

/**
 * Get the current Google Calendar connection status for the user
 */
export const getGoogleCalendarStatus = () => {
    return Http.getWithAuth(GET_GOOGLE_CALENDAR_STATUS());
};

/**
 * Get the Google OAuth authorization URL
 */
export const getGoogleCalendarAuthUrl = () => {
    return Http.getWithAuth(GET_GOOGLE_CALENDAR_AUTH_URL());
};

/**
 * Get the list of user's Google calendars
 */
export const getGoogleCalendars = () => {
    return Http.getWithAuth(GET_GOOGLE_CALENDARS());
};

/**
 * Connect to a specific Google calendar
 */
export const connectGoogleCalendar = (calendarId: string) => {
    return Http.postWithAuth(CONNECT_GOOGLE_CALENDAR(), { calendarId });
};

/**
 * Disconnect from Google Calendar
 */
export const disconnectGoogleCalendar = () => {
    return Http.deleteWithAuth(DISCONNECT_GOOGLE_CALENDAR());
}; 