import axios, { AxiosResponse, AxiosError } from 'axios';
import { getAuth } from './identity.service';
import { checkForTokenExpiredError } from '../utils/common';
import { refreshTokenAndSetAuth } from './user.service';

export interface ApiResponse<T = any> {
    isAdmin?: any;
    isFirstLogin?: any;
    status: boolean;
    message?: string;
    errorMessage?: string;
    statusCode?: number;
    data?: T;
    rawError?: any;
}

export const postWithAuth = async (
    url: string,
    entity: any,
    contentType?: string
): Promise<ApiResponse> => {
    const auth = getAuth();
    const accessToken = auth?.token;
    const headers: Record<string, string> = {
        ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
    };

    if (entity instanceof FormData) {
        headers['Content-Type'] = 'multipart/form-data';
    } else {
        headers['Content-Type'] = contentType ?? 'application/json';
    }
    return new Promise((resolve, reject) => {
        axios
            .post(url, entity, { headers })
            .then((response: AxiosResponse) => {
                if (response.data) {
                    resolve({ status: true, data: response.data });
                }
                if (response?.data === '') {
                    resolve({ status: true, statusCode: response.status });
                }
            })
            .catch((ex: AxiosError) => {
                if (checkForTokenExpiredError(ex)) {
                    const callback = () => postWithAuth(url, entity);
                    refreshTokenAndSetAuth(callback)
                        .then((data: any) => resolve({ status: true, data }))
                        .catch((refreshError: any) => reject(refreshError));
                    return;
                }

                console.log('API Error Response (POST):', ex.response?.data);
                const responseData = ex.response?.data as Record<string, any>;
                resolve({
                    status: false,
                    message: ex.message,
                    errorMessage:
                        responseData?.message ||
                        responseData?.errors ||
                        responseData?.error ||
                        ex.message,
                    statusCode: ex.response?.status,
                    rawError: responseData,
                });
            });
    });
};

export const putWithAuth = async (
    url: string,
    entity: any
): Promise<ApiResponse> => {
    const auth = getAuth();
    const accessToken = auth?.token;

    const headers = {
        'content-type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
    };
    return new Promise((resolve, reject) => {
        axios
            .put(url, entity, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                if (checkForTokenExpiredError(ex)) {
                    const callback = () => putWithAuth(url, entity);
                    refreshTokenAndSetAuth(callback)
                        .then((data: any) => resolve({ status: true, data }))
                        .catch((refreshError: any) => reject(refreshError));
                    return;
                }
                console.log('API Error Response:', ex.response?.data);
                const responseData = ex.response?.data as Record<string, any>;
                resolve({
                    status: false,
                    message: ex.message,
                    errorMessage:
                        responseData?.message ||
                        responseData?.errors ||
                        responseData?.error ||
                        ex.message,
                    statusCode: ex.response?.status,
                    rawError: responseData,
                });
            });
    });
};

export const getWithAuth = async (
    url: string,
    responseType = ''
): Promise<ApiResponse> => {
    const auth = getAuth();
    const accessToken = auth?.token;

    const headers = {
        'content-type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
    };
    return new Promise((resolve) => {
        axios
            .get(url, {
                headers,
                ...(responseType === 'pdf' ? { responseType: 'blob' } : {}),
            })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                if (checkForTokenExpiredError(ex)) {
                    const callback = () => getWithAuth(url);
                    refreshTokenAndSetAuth(callback).then((data: any) =>
                        resolve({ status: true, data })
                    );
                    return;
                }
                resolve({
                    status: false,
                    message: ex.message,
                    // errorMessage: ex.response?.data?.message,
                    statusCode: ex.response?.status,
                });
            });
    });
};

export const getWithOutAuth = (url: string): Promise<ApiResponse> => {
    return new Promise((resolve) => {
        const headers = {
            'content-type': 'application/json',
        };
        axios
            .get(url, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                resolve({
                    status: false,
                    message: ex.message,
                    // errorMessage: ex.response?.data?.message ,
                    statusCode: ex.response?.status,
                });
            });
    });
};

export const postWithOutAuth = (
    url: string,
    entity: any,
    contentType?: any
): Promise<ApiResponse> =>
    new Promise((resolve) => {
        // const headers = {
        //     'content-type': 'application/json',
        // };

        let headers: Record<string, string> = {};
        if (!(entity instanceof FormData)) {
            headers['Content-Type'] = contentType || 'application/json';
        }
        axios
            .post(url, entity, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                resolve({
                    status: false,
                    message: ex.message,
                    // errorMessage: ex.response?.data?.message,
                    statusCode: ex.response?.status,
                });
            });
    });

export const putWithOutAuth = (
    url: string,
    entity: any
): Promise<ApiResponse> =>
    new Promise((resolve) => {
        const headers = {
            'content-type': 'application/json',
        };
        axios
            .put(url, entity, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                resolve({
                    status: false,
                    message: ex.message,
                    // errorMessage: ex.response?.data?.message,
                    statusCode: ex.response?.status,
                });
            });
    });

export const deleteWithOutAuth = (url: string): Promise<ApiResponse> => {
    return new Promise((resolve) => {
        const headers = {
            'content-type': 'application/json',
        };
        axios
            .delete(url, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                resolve({
                    status: false,
                    message: ex.message,
                    // errorMessage: ex.response?.data?.message,
                    statusCode: ex.response?.status,
                });
            });
    });
};

export const deleteWithAuth = (url: string): Promise<ApiResponse> => {
    const auth = getAuth();
    const accessToken = auth?.token;
    return new Promise((resolve, reject) => {
        const headers = {
            'content-type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
        };
        axios
            .delete(url, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                if (checkForTokenExpiredError(ex)) {
                    const callback = () => deleteWithAuth(url);
                    refreshTokenAndSetAuth(callback)
                        .then((data: any) => resolve({ status: true, data }))
                        .catch((refreshError: any) => reject(refreshError));
                    return;
                }

                console.log('API Error Response (DELETE):', ex.response?.data);

                if (ex.response?.data) {
                    // Ensure we're capturing all the error details properly
                    const responseData = ex.response.data as Record<
                        string,
                        any
                    >;

                    resolve({
                        status: false,
                        message: ex.message,
                        errorMessage:
                            responseData.message ||
                            responseData.errors ||
                            responseData.error ||
                            ex.message,
                        statusCode: ex.response.status,
                        rawError: responseData,
                    });
                } else {
                    // Handle case where response data is missing
                    resolve({
                        status: false,
                        message: ex.message,
                        errorMessage: ex.message,
                        statusCode: ex.response?.status,
                    });
                }
            });
    });
};

export const getWithAuthBlob = async (url: string): Promise<Blob> => {
    const auth = await getAuth();
    const accessToken = auth?.token;
    const headers = {
        'content-type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
    };

    try {
        const response = await axios.get(url, {
            headers,
            responseType: 'blob',
        });
        return response.data;
    } catch (error) {
        // Handle error appropriately
        console.error('Error in getWithAuthBlob:', error);
        throw error;
    }
};

export const patchWithAuth = (url: string, data: any): Promise<any> => {
    return new Promise((resolve) => {
        const auth = getAuth();
        const accessToken = auth?.token;
        const headers = {
            'content-type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
        };
        axios
            .patch(url, data, { headers })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                resolve({
                    status: false,
                    message: ex.message,
                    statusCode: ex.response?.status,
                });
            });
    });
};

export const deleteWithAuthAndBody = (
    url: string,
    data: any
): Promise<ApiResponse> => {
    const auth = getAuth();
    const accessToken = auth?.token;
    return new Promise((resolve, reject) => {
        const headers = {
            'content-type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
        };
        axios
            .delete(url, { headers, data })
            .then((response: AxiosResponse) => {
                resolve({ status: true, data: response.data });
            })
            .catch((ex: AxiosError) => {
                if (checkForTokenExpiredError(ex)) {
                    const callback = () => deleteWithAuthAndBody(url, data);
                    refreshTokenAndSetAuth(callback)
                        .then((data: any) => resolve({ status: true, data }))
                        .catch((refreshError: any) => reject(refreshError));
                    return;
                }

                console.log(
                    'API Error Response (DELETE with body):',
                    ex.response?.data
                );

                if (ex.response?.data) {
                    const responseData = ex.response.data as Record<
                        string,
                        any
                    >;

                    resolve({
                        status: false,
                        message: ex.message,
                        errorMessage:
                            responseData.message ||
                            responseData.errors ||
                            responseData.error ||
                            ex.message,
                        statusCode: ex.response.status,
                        rawError: responseData,
                    });
                } else {
                    resolve({
                        status: false,
                        message: ex.message,
                        errorMessage: ex.message,
                        statusCode: ex.response?.status,
                    });
                }
            });
    });
};
