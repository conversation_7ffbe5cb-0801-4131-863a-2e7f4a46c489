import {
    useMutation,
    UseMutationResult,
    useQuery,
    useQueryClient,
} from '@tanstack/react-query';
import {
    completePatientReminder,
    createGlobalReminder,
    createPatient,
    CreatePatientDto,
    createPatientReminder,
    deleteGlobalReminder,
    deletePatientReminder,
    getGlobalReminders,
    getPatientDetails,
    getPatientReminders,
    getPatients,
    getPatientsWithSearch,
    markReminderIncomplete,
    overriddenPatientReminder,
    searchPatientByPhone,
    updateGlobalReminder,
    updatePatient,
    UpdatePatientDto,
    updateReminder,
} from './patient.service';
import { ApiResponse } from './http.service';
import { GetPatientsT } from '../types/patient';

type CreateOrUpdatePatientDto =
    | { type: 'create'; data: CreatePatientDto }
    | { type: 'update'; data: UpdatePatientDto };

export const useClinicPatients = (
    page: number,
    limit: number,
    searchTerm: string,
    withBalance: string
) => {
    return useQuery({
        queryKey: ['patients', page, limit, searchTerm, withBalance],
        queryFn: () => getPatients(page, limit, searchTerm, withBalance),

        // keepPreviousData: true,
    });
};

export const useSearchPatientByPhone = (phoneNumber: string) => {
    const { data, isLoading, isError, status, refetch } = useQuery({
        queryKey: ['searchPatient', phoneNumber],
        queryFn: () => searchPatientByPhone(phoneNumber),
        enabled: !!phoneNumber,
    });

    return { data, isLoading, isError, status, refetch };
};

export const useCreateOrUpdatePatient = (): UseMutationResult<
    ApiResponse<any>,
    Error,
    CreateOrUpdatePatientDto,
    unknown
> => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: async (patientData: CreateOrUpdatePatientDto) => {
            if (patientData.type === 'create') {
                const response = await createPatient(patientData.data);
                return response;
            } else {
                const response = await updatePatient(patientData.data);
                return response;
            }
        },
        onError: (error: Error) => {
            console.error('Error occurred queries:', error.message);
        },
        onSuccess: (data, variables) => {
            queryClient.invalidateQueries({ queryKey: ['patients'] });
            if (variables.type === 'update') {
                queryClient.invalidateQueries({
                    queryKey: ['patientDetails', variables.data.id],
                });
            }
            queryClient.invalidateQueries({
                queryKey: ['patientReminders'],
                exact: false,
            });
            queryClient.invalidateQueries({
                queryKey: ['getOwnerPatients'],
                exact: false,
            });
            queryClient.invalidateQueries({
                queryKey: ['ownerPatients'],
                exact: false,
            });
            queryClient.invalidateQueries({
                queryKey: ['getPaymentDetailsForAPatient'],
                exact: false,
            });
            queryClient.invalidateQueries({
                queryKey: ['patientDetails'],
                exact: false,
            });
        },
    });
};

export const usePatients = (details: GetPatientsT) => {
    return useQuery({
        queryKey: ['patients', { ...details }],
        queryFn: () => getPatientsWithSearch(details),
    });
};

export const usePatientDetails = (id: any) => {
    return useQuery({
        queryFn: () => getPatientDetails(id),
        queryKey: ['patientDetails', id],
    });
};

// Patients Queries
export const useCreatePatientReminderMutation = (patientId: string) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: any) => createPatientReminder(patientId, data),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['patientReminders', patientId],
            });
            queryClient.invalidateQueries({
                queryKey: ['patientDetails', patientId],
            });
        },
        onError: (error) => {
            console.error('Error creating reminder:', error);
        },
    });
};

export const useGetPatientReminders = (
    patientId: string,
    page: number = 1,
    limit: number = 10
) => {
    return useQuery({
        queryKey: ['patientReminders', patientId, page, limit],
        queryFn: () => getPatientReminders(patientId, page, limit),
        enabled: !!patientId,
        // select: (data) => ({
        //     pending: data?.data?.filter((reminder: any) => reminder.status === 'PENDING') || [],
        //     completed: data?.data?.filter((reminder: any) => reminder.status === 'COMPLETED') || []
        // })
    });
};

export const useDeleteReminderMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            patientId,
            reminderId,
        }: {
            patientId: string;
            reminderId: string;
        }) => deletePatientReminder(patientId, reminderId),
        onSuccess: (_, { patientId }) => {
            queryClient.invalidateQueries({
                queryKey: ['patientReminders', patientId],
            });
        },
    });
};

export const useCompleteReminderMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            patientId,
            reminderId,
        }: {
            patientId: string;
            reminderId: string;
        }) => completePatientReminder(patientId, reminderId),
        onSuccess: (_, { patientId }) => {
            queryClient.invalidateQueries({
                queryKey: ['patientReminders', patientId],
            });
        },
    });
};

export const useOverriddenReminderMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            patientId,
            reminderId,
        }: {
            patientId: string;
            reminderId: string;
        }) => overriddenPatientReminder(patientId, reminderId),
        onSuccess: (_, { patientId }) => {
            queryClient.invalidateQueries({
                queryKey: ['patientReminders', patientId],
            });
        },
    });
};

export const useUpdateReminderMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: updateReminder,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['patientReminders'] });
        },
    });
};

export const useMarkReminderIncompleteMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            patientId,
            reminderId,
        }: {
            patientId: string;
            reminderId: string;
        }) => markReminderIncomplete(patientId, reminderId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['patientReminders'] });
        },
    });
};

export const useGetGlobalReminders = (
    clinicId: string,
    page: number = 1,
    limit: number = 10,
    search: string = ''
) => {
    return useQuery({
        queryKey: ['globalReminders', clinicId, page, limit, search],
        queryFn: () => getGlobalReminders(clinicId, page, limit, search),
    });
};

export const useCreateGlobalReminder = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: any) => createGlobalReminder(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['globalReminders'] });
        },
    });
};

export const useUpdateGlobalReminder = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({
            reminderId,
            data,
        }: {
            reminderId: string;
            data: Partial<any>;
        }) => updateGlobalReminder(reminderId, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['globalReminders'] });
        },
    });
};

export const useDeleteGlobalReminder = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (reminderId: string) => deleteGlobalReminder(reminderId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['globalReminders'] });
        },
    });
};
