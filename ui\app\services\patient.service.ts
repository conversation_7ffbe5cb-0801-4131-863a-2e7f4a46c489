import { GetPatientsT } from '../types/patient';
import * as HttpService from './http.service';
import {
    COMPLETE_PATIENT_REMINDER,
    CREATE_GLOBAL_REMINDER,
    CREATE_PATIENT,
    CREATE_PATIENT_REMINDER,
    DELETE_GLOBAL_REMINDER,
    DELETE_PATIENT_REMINDER,
    GET_CLINIC_PATIENTS,
    GET_GLOBAL_REMINDERS,
    GET_PATIENT_DETAILS,
    GET_PATIENT_REMINDERS,
    GET_PATIENTS,
    MARK_REMINDER_INCOMPLETE,
    OVERRIDDEN_PATIENT_REMINDER,
    SEARCH_PATIENT_BY_PHONE,
    UPDATE_GLOBAL_REMINDER,
    UPDATE_PATIENT,
    UPDATE_REMINDER,
} from './url.service';

export const getPatients = (
    page: number = 1,
    limit: number = 10,
    searchTerm: string,
    withBalance: string = 'false'
) => {
    return HttpService.getWithAuth(
        GET_CLINIC_PATIENTS(page, limit, searchTerm, withBalance)
    );
};

export const searchPatientByPhone = (phoneNumber: string) => {
    return HttpService.getWithAuth(SEARCH_PATIENT_BY_PHONE(phoneNumber));
};

export const getPatientsWithSearch = (getPatientParams: GetPatientsT) => {
    return HttpService.getWithAuth(GET_PATIENTS({ ...getPatientParams }));
};

export const getPatientDetails = (id: any) => {
    return HttpService.getWithAuth(GET_PATIENT_DETAILS(id));
};

export interface CreatePatientDto {
    patientName: string;
    // balance: number;
    ownersData: {
        firstName: string;
        lastName: string;
        phoneNumber: string;
        countryCode: string;
    }[];
}

export interface UpdatePatientDto {
    id: string;
    patientName?: string;
    ownersData?: {
        id?: string;
        firstName?: string;
        lastName?: string;
        phoneNumberNational?: string;
        countryCode?: string;
        email?: string;
        address?: string;
    }[];
    species?: string;
    breed?: string;
    age?: string;
    gender?: string;
    reproductiveStatus?: string;
    microchipId?: string;
    identification?: string;
    allergies?: string;
    balance: number;
}

export const createPatient = (patientData: CreatePatientDto) => {
    console.log(CREATE_PATIENT());
    return HttpService.postWithAuth(CREATE_PATIENT(), patientData);
};

export const updatePatient = (patientData: UpdatePatientDto) => {
    return HttpService.putWithAuth(
        UPDATE_PATIENT(patientData.id),
        patientData
    ).catch((error) => {
        if (error.response?.status === 409) {
            throw new Error(error.response.data.message || 'This phone number is already in use');
        }
        throw error;
    });
};

// Patients Reminder

// Add with other service functions
export const createPatientReminder = (patientId: string, reminderData: any) => {
    return HttpService.postWithAuth(
        CREATE_PATIENT_REMINDER(patientId),
        reminderData
    );
};

export const getPatientReminders = (
    patientId: string,
    page: number = 1,
    limit: number = 10
) => {
    return HttpService.getWithAuth(
        GET_PATIENT_REMINDERS(patientId, page, limit)
    );
};
export const deletePatientReminder = (
    patientId: string,
    reminderId: string
) => {
    return HttpService.deleteWithAuth(
        DELETE_PATIENT_REMINDER(patientId, reminderId)
    );
};

export const completePatientReminder = (
    patientId: string,
    reminderId: string
) => {
    return HttpService.putWithAuth(
        COMPLETE_PATIENT_REMINDER(patientId, reminderId),
        {}
    );
};

export const overriddenPatientReminder = (
    patientId: string,
    reminderId: string
) => {
    return HttpService.putWithAuth(
        OVERRIDDEN_PATIENT_REMINDER(patientId, reminderId),
        {}
    );
};

interface UpdateReminderParams {
    patientId: string;
    reminderId: string;
    data: any;
}

export const updateReminder = ({
    patientId,
    reminderId,
    data,
}: UpdateReminderParams) => {
    console.log('Update Reminder Data:', { data, patientId, reminderId });
    return HttpService.putWithAuth(
        UPDATE_REMINDER(patientId, reminderId),
        data
    );
};

export const markReminderIncomplete = (
    patientId: string,
    reminderId: string
) => {
    return HttpService.putWithAuth(
        MARK_REMINDER_INCOMPLETE(patientId, reminderId),
        {}
    );
};

export const createGlobalReminder = (reminderData: any) => {
    return HttpService.postWithAuth(CREATE_GLOBAL_REMINDER(), reminderData);
};

export const getGlobalReminders = (
    clinicId: string,
    page: number = 1,
    limit: number = 10,
    search: string = ''
) => {
    return HttpService.getWithAuth(
        GET_GLOBAL_REMINDERS(clinicId, page, limit, search)
    );
};

export const updateGlobalReminder = (reminderId: string, data: any) => {
    return HttpService.putWithAuth(UPDATE_GLOBAL_REMINDER(reminderId), data);
};

export const deleteGlobalReminder = (reminderId: string) => {
    return HttpService.deleteWithAuth(DELETE_GLOBAL_REMINDER(reminderId));
};
