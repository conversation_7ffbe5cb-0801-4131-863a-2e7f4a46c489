import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    createPaymentDetails,
    getPaymentDetailsForAPatient,
    getPaymentDetailsForAnOwner,
    getPaymentReceiptsForAPatient,
    getInvoicesForAPatient,
    getOwnerInvoicesWithPayments,
    getOwnerPendingInvoices,
    getOwnerLedger,
    createBulkPaymentDetails,
    handlePaymentDocument,
    downloadFileFromUrl,
    checkPaymentDocumentStatus,
} from './payment-details.service';
import { CreatePaymentDetails } from '../types/create-payment-details';
import { CREDIT_TYPES, DATE_FORMAT } from '../utils/constant';
import moment from 'moment';

/**
 * Type for bulk payment details request
 */
export interface BulkPaymentDetails {
    data: {
        invoiceIds: string[];
        ownerId: string;
        patientId?: string;
        cashAmount: number;
        isCreditUsed: boolean;
        creditAmountUsed?: number;
        paymentType: string;
        description?: string;
    };
}

//  Create payment details mutation - Credit collect, Credit return, Invoice
export const useCreatePaymentDetailsMutation = (
    pageIndex: number,
    limit: number,
    searchTerm: string,
    withBalance: string
) => {
    const queryClient = useQueryClient();

    const createPaymentDetailsMutation = useMutation({
        mutationFn: (details: CreatePaymentDetails) =>
            createPaymentDetails(details.data),
        onSuccess: async (response) => {
            // Invalidate payment details for patient
            queryClient.invalidateQueries({
                queryKey: ['getPaymentDetailsForAPatient'],
                refetchType: 'all',
            });
            // Invalidate payment details for owner
            queryClient.invalidateQueries({
                queryKey: ['getPaymentDetailsForAnOwner'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getInvoicesForAPatient'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getPaymentReceiptsForAPatient'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getOwnerLedger'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['ownerCreditTransactions'],
                refetchType: 'all',
            });
            // Invalidate owner invoices with payments
            queryClient.invalidateQueries({
                queryKey: ['getOwnerInvoicesWithPayments'],
                refetchType: 'all',
            });
            // Invalidate owner pending invoices
            queryClient.invalidateQueries({
                queryKey: ['getOwnerPendingInvoices'],
                refetchType: 'all',
            });
            // Invalidate patient details to get updated balance
            queryClient.invalidateQueries({
                queryKey: ['patientDetails'],
                refetchType: 'all',
            });
            // Invalidate patients list with both balance filters
            queryClient.invalidateQueries({
                queryKey: [
                    'patients',
                    pageIndex,
                    limit,
                    searchTerm,
                    withBalance === 'true' ? withBalance : 'false',
                ],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'patients',
                    pageIndex,
                    limit,
                    searchTerm,
                    withBalance === 'false' ? withBalance : 'false',
                ],
                refetchType: 'all',
            });
        },
        onError: (error) => {
            console.log(
                'useCreatePaymentDetailsMutation Something went wrong = ',
                error
            );
        },
    });

    return {
        createPaymentDetailsMutation,
    };
};

// Create bulk payment details mutation for processing multiple invoices at once
export const useCreateBulkPaymentDetailsMutation = (
    pageIndex: number,
    limit: number,
    searchTerm: string,
    withBalance: string
) => {
    const queryClient = useQueryClient();

    const createBulkPaymentDetailsMutation = useMutation({
        mutationFn: (details: BulkPaymentDetails) =>
            createBulkPaymentDetails(details.data),
        onSuccess: async (response) => {
            // Invalidate the same queries as single payment to update all related data
            queryClient.invalidateQueries({
                queryKey: ['getPaymentDetailsForAPatient'],
                refetchType: 'all',
            });
            // Invalidate payment details for owner
            queryClient.invalidateQueries({
                queryKey: ['getPaymentDetailsForAnOwner'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getInvoicesForAPatient'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getPaymentReceiptsForAPatient'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getOwnerInvoicesWithPayments'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['getOwnerPendingInvoices'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: ['patientDetails'],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'patients',
                    pageIndex,
                    limit,
                    searchTerm,
                    withBalance === 'true' ? withBalance : 'false',
                ],
                refetchType: 'all',
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'patients',
                    pageIndex,
                    limit,
                    searchTerm,
                    withBalance === 'false' ? withBalance : 'false',
                ],
                refetchType: 'all',
            });
            // Owner ledger
            queryClient.invalidateQueries({
                queryKey: ['getOwnerLedger'],
                refetchType: 'all',
            });
            // Owner credit transactions
            queryClient.invalidateQueries({
                queryKey: ['ownerCreditTransactions'],
                refetchType: 'all',
            });
        },
        onError: (error) => {
            console.log(
                'useCreateBulkPaymentDetailsMutation Something went wrong = ',
                error
            );
        },
    });

    return {
        createBulkPaymentDetailsMutation,
    };
};

// Get payment details list for a patient
export const useGetPaymentDetailsForAPatient = (patientId: string) => {
    return useQuery({
        queryFn: () => getPaymentDetailsForAPatient(patientId),
        queryKey: ['getPaymentDetailsForAPatient', patientId],
        enabled: !!patientId,
    });
};

// Get payment details list for an owner
export const useGetPaymentDetailsForAnOwner = (
    ownerId: string,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        paymentMode?: string;
        paymentType?: string;
        userIds?: string;
        searchTerm?: string;
        page?: number;
        limit?: number;
    }
) => {
    return useQuery({
        queryFn: () => getPaymentDetailsForAnOwner(ownerId, filters),
        queryKey: ['getPaymentDetailsForAnOwner', ownerId, filters],
        enabled: !!ownerId,
    });
};

// Get payment receipts list for a patient with comprehensive processing
export const useGetPaymentReceiptsForAPatient = (
    patientId: string,
    filters?: {
        search?: string;
        page?: number;
        limit?: number;
    }
) => {
    return useQuery({
        queryFn: () => getPaymentReceiptsForAPatient(patientId, filters),
        queryKey: ['getPaymentReceiptsForAPatient', patientId, filters],
        enabled: !!patientId,
    });
};

// Get invoices list for a patient with comprehensive processing
export const useGetInvoicesForAPatient = (
    patientId: string,
    filters?: {
        search?: string;
        page?: number;
        limit?: number;
        invoiceType?: string;
    }
) => {
    return useQuery({
        queryFn: () => getInvoicesForAPatient(patientId, filters),
        queryKey: ['getInvoicesForAPatient', patientId, filters],
        enabled: !!patientId,
    });
};

// Get owner invoices with payments
export const useGetOwnerInvoicesWithPayments = (
    ownerId: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        status?: string;
        paymentMode?: string;
        searchTerm?: string;
        userId?: string;
        invoiceType?: string;
    },
    enabled: boolean = true
) => {
    return useQuery({
        queryFn: () =>
            getOwnerInvoicesWithPayments(ownerId, page, limit, filters),
        queryKey: [
            'getOwnerInvoicesWithPayments',
            ownerId,
            page,
            limit,
            filters,
        ],
        enabled: !!ownerId && enabled,
    });
};

// Get owner pending invoices
export const useGetOwnerPendingInvoices = (
    ownerId: string,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        searchTerm?: string;
    },
    enabled: boolean = true
) => {
    return useQuery({
        queryFn: () => getOwnerPendingInvoices(ownerId, filters),
        queryKey: ['getOwnerPendingInvoices', ownerId, filters],
        enabled: !!ownerId && enabled,
    });
};

// Get owner ledger (combined invoices and payments chronologically)
export const useGetOwnerLedger = (ownerId: string, enabled: boolean = true) => {
    return useQuery({
        queryFn: () => getOwnerLedger(ownerId),
        queryKey: ['getOwnerLedger', ownerId],
        enabled: !!ownerId && enabled,
    });
};

/**
 * Hook for handling payment document operations (credit notes, receipts, etc.)
 */
export const usePaymentDocumentMutation = () => {
    const queryClient = useQueryClient();

    const paymentDocumentMutation = useMutation({
        mutationFn: ({
            referenceAlphaId,
            documentType,
            action,
            patientId,
            shareMethod,
            recipient,
            email,
            whatsapp,
        }: {
            referenceAlphaId: string;
            documentType: 'creditnote' | 'payment-details';
            action: 'share' | 'download';
            patientId: string;
            shareMethod?: 'whatsapp' | 'email' | 'both';
            recipient?: 'client' | 'other';
            email?: string;
            whatsapp?: string;
        }) =>
            handlePaymentDocument(
                referenceAlphaId,
                documentType,
                action,
                patientId,
                shareMethod,
                recipient,
                email,
                whatsapp
            ),
        onSuccess: (response, variables) => {
            const { action } = variables;

            console.log('payment document response', response);

            // Handle the response according to its structure
            if (response.status === true) {
                // New response structure with direct download URL
                if (
                    response.data?.status === 'success' &&
                    response.data?.data?.downloadUrl &&
                    response.data?.data?.fileName &&
                    action === 'download'
                ) {
                    console.log(
                        'Payment document available for direct download'
                    );
                    const { downloadUrl, fileName } = response.data.data;
                    downloadFileFromUrl(downloadUrl, fileName);
                    return;
                }

                // Handle processing status for new response structure
                if (
                    response.data?.status === 'processing' &&
                    response.data?.data?.referenceAlphaId
                ) {
                    const referenceAlphaId =
                        response.data.data.referenceAlphaId;
                    console.log(
                        `Payment document generation started. referenceAlphaId: ${referenceAlphaId}`
                    );

                    // Start polling for download action
                    if (action === 'download') {
                        pollDocumentStatus(referenceAlphaId);
                    }
                    return;
                }
            }

            // Legacy response handling
            const responseData = response.data;

            if (
                responseData?.status === 'processing' &&
                responseData?.data?.referenceAlphaId
            ) {
                const referenceAlphaId = responseData.data.referenceAlphaId;
                console.log(
                    `Payment document generation started. referenceAlphaId: ${referenceAlphaId}`
                );

                // Start polling only for 'download' action
                if (action === 'download') {
                    pollDocumentStatus(referenceAlphaId);
                }
            }
            // Handle immediate download if document is already available
            else if (
                responseData?.data?.url &&
                responseData?.data?.fileName &&
                action === 'download'
            ) {
                console.log(
                    'Payment document available immediately, downloading...'
                );
                const { url, fileName } = responseData.data;
                downloadFileFromUrl(url, fileName);
            } else {
                console.log(
                    'No polling required or unexpected response structure',
                    response
                );
            }
        },
        onError: (error) => {
            console.error('usePaymentDocumentMutation Error:', error);
        },
    });

    // Helper function to poll for document status
    const pollDocumentStatus = async (referenceAlphaId: string) => {
        const maxAttempts = 15; // Approximately 30 seconds with 2-second intervals
        let attempts = 0;

        const poll = async () => {
            if (attempts >= maxAttempts) {
                console.log(`Polling timed out after ${maxAttempts} attempts`);
                return;
            }
            attempts++;
            console.log(`Polling attempt ${attempts}/${maxAttempts}`);

            try {
                const statusResponse =
                    await checkPaymentDocumentStatus(referenceAlphaId);
                const statusData =
                    statusResponse?.data?.data || statusResponse?.data;

                if (statusData?.isReady) {
                    console.log('Payment document is ready, downloading...');
                    const { url, fileName } = statusData;
                    if (url && fileName) {
                        downloadFileFromUrl(url, fileName);
                    } else {
                        console.error(
                            'URL or fileName missing in response',
                            statusData
                        );
                    }
                } else {
                    setTimeout(poll, 2000); // Poll again after 2 seconds
                }
            } catch (error) {
                console.error(
                    'Error polling for payment document status:',
                    error
                );
            }
        };

        poll(); // Start polling
    };

    return {
        paymentDocumentMutation,
    };
};
