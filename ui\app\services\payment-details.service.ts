import {
    CREATE_PAYMENT_DETAILS,
    DELETE_LEDGER_DOCUMENT_FILEKEY,
    GET_INDIVIDUAL_PAYEMENT_DETAIL,
    GET_PAYMENT_DETAILS_LIST_FOR_A_PATIENT,
    GET_PAYMENT_DETAILS_LIST_FOR_A_OWNER,
    GET_PAYMENT_RECEIPTS_LIST_FOR_A_PATIENT,
    GET_INVOICES_LIST_FOR_A_PATIENT,
    GET_OWNER_INVOICES_WITH_PAYMENTS,
    GET_OWNER_PENDING_INVOICES,
    GET_OWNER_LEDGER,
    CREATE_BULK_PAYMENT_DETAILS,
    PAYMENT_DOCUMENT_URL,
    PAYMENT_DOCUMENT_STATUS_URL,
} from './url.service';
import * as Http from './http.service';

// Create invoice
export const createPaymentDetails = (data: any) => {
    return Http.postWithAuth(CREATE_PAYMENT_DETAILS(), data);
};

// Create bulk payment for multiple invoices
export const createBulkPaymentDetails = (data: any) => {
    return Http.postWithAuth(CREATE_BULK_PAYMENT_DETAILS(), data);
};

// This is to get payment details list for for an appointment
export const getPaymentDetailsForAPatient = (patientId: string) => {
    return Http.getWithAuth(GET_PAYMENT_DETAILS_LIST_FOR_A_PATIENT(patientId));
};

// This is to get payment details list for for an owner
export const getPaymentDetailsForAnOwner = (
    ownerId: string,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        paymentMode?: string;
        paymentType?: string;
        userIds?: string;
        searchTerm?: string;
        page?: number;
        limit?: number;
    }
) => {
    // Build query params
    let url = GET_PAYMENT_DETAILS_LIST_FOR_A_OWNER(ownerId);

    if (filters) {
        const queryParams: string[] = [];

        if (filters.startDate)
            queryParams.push(
                `startDate=${encodeURIComponent(filters.startDate)}`
            );
        if (filters.endDate)
            queryParams.push(`endDate=${encodeURIComponent(filters.endDate)}`);
        if (filters.petName)
            queryParams.push(`petName=${encodeURIComponent(filters.petName)}`);
        if (filters.paymentMode)
            queryParams.push(
                `paymentMode=${encodeURIComponent(filters.paymentMode)}`
            );
        if (filters.paymentType)
            queryParams.push(
                `paymentType=${encodeURIComponent(filters.paymentType)}`
            );
        if (filters.userIds) {
            queryParams.push(`userId=${encodeURIComponent(filters.userIds)}`);
        }
        if (filters.searchTerm)
            queryParams.push(
                `searchTerm=${encodeURIComponent(filters.searchTerm)}`
            );
        // Add pagination parameters
        if (filters.page !== undefined)
            queryParams.push(`page=${filters.page}`);
        if (filters.limit !== undefined)
            queryParams.push(`limit=${filters.limit}`);

        if (queryParams.length > 0) {
            url += `?${queryParams.join('&')}`;
        }
    }

    return Http.getWithAuth(url);
};

// Get payment receipts list for a patient with comprehensive processing
export const getPaymentReceiptsForAPatient = (
    patientId: string,
    filters?: {
        search?: string;
        page?: number;
        limit?: number;
    }
) => {
    // Build query params
    let url = GET_PAYMENT_RECEIPTS_LIST_FOR_A_PATIENT(patientId);

    if (filters) {
        const queryParams: string[] = [];

        if (filters.search)
            queryParams.push(`search=${encodeURIComponent(filters.search)}`);
        // Add pagination parameters
        if (filters.page !== undefined)
            queryParams.push(`page=${filters.page}`);
        if (filters.limit !== undefined)
            queryParams.push(`limit=${filters.limit}`);

        if (queryParams.length > 0) {
            url += `?${queryParams.join('&')}`;
        }
    }

    return Http.getWithAuth(url);
};

// Get invoices list for a patient with comprehensive processing
export const getInvoicesForAPatient = (
    patientId: string,
    filters?: {
        search?: string;
        page?: number;
        limit?: number;
        invoiceType?: string;
    }
) => {
    // Build query params
    let url = GET_INVOICES_LIST_FOR_A_PATIENT(patientId);

    if (filters) {
        const queryParams: string[] = [];

        if (filters.search)
            queryParams.push(`search=${encodeURIComponent(filters.search)}`);
        // Add pagination parameters
        if (filters.page !== undefined)
            queryParams.push(`page=${filters.page}`);
        if (filters.limit !== undefined)
            queryParams.push(`limit=${filters.limit}`);
        if (filters.invoiceType)
            queryParams.push(`invoiceType=${encodeURIComponent(filters.invoiceType)}`);

        if (queryParams.length > 0) {
            url += `?${queryParams.join('&')}`;
        }
    }

    return Http.getWithAuth(url);
};

export const getIndividualPaymentDetail = (id: string) =>
    Http.getWithAuth(GET_INDIVIDUAL_PAYEMENT_DETAIL(id));

export const deleteLedgerDocumentFileKey = (id: string) =>
    Http.putWithAuth(DELETE_LEDGER_DOCUMENT_FILEKEY(id), {});

// Get owner invoices with payments (with filtering options)
export const getOwnerInvoicesWithPayments = (
    ownerId: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        status?: string;
        paymentMode?: string;
        searchTerm?: string;
        userId?: string;
        invoiceType?: string;
    }
) => {
    return Http.getWithAuth(
        GET_OWNER_INVOICES_WITH_PAYMENTS(ownerId, page, limit, filters)
    );
};

// Get pending invoices for an owner (with filtering options)
export const getOwnerPendingInvoices = (
    ownerId: string,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        searchTerm?: string;
    }
) => {
    return Http.getWithAuth(GET_OWNER_PENDING_INVOICES(ownerId, filters));
};

// Get owner ledger (combined invoices and payments chronologically)
export const getOwnerLedger = (ownerId: string) => {
    return Http.getWithAuth(GET_OWNER_LEDGER(ownerId));
};

/**
 * Handle payment related document operations (credit notes, receipts, etc.)
 * @param referenceAlphaId Reference alpha ID of the document
 * @param documentType Type of document (creditnote or payment-details)
 * @param action Whether to share or download the document
 * @param patientId The patient ID related to the payment
 * @param shareMethod If sharing, which method to use (email, whatsapp, or both)
 * @param recipient Optional recipient type ('client' or 'other')
 * @param email Optional email address when sending to custom email
 * @param whatsapp Optional phone number when sending to custom whatsapp
 * @returns API response
 */
export const handlePaymentDocument = async (
    referenceAlphaId: string,
    documentType: 'creditnote' | 'payment-details',
    action: 'share' | 'download',
    patientId: string,
    shareMethod?: 'whatsapp' | 'email' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    whatsapp?: string
) => {
    try {
        if (action === 'share' && shareMethod) {
            // For sharing, use the new API endpoint with share action
            return await Http.getWithAuth(
                PAYMENT_DOCUMENT_URL(
                    referenceAlphaId,
                    documentType,
                    'share',
                    patientId,
                    shareMethod,
                    recipient,
                    email,
                    whatsapp
                )
            );
        } else if (action === 'download') {
            // For downloading, use the new API endpoint with download action
            return await Http.getWithAuth(
                PAYMENT_DOCUMENT_URL(
                    referenceAlphaId,
                    documentType,
                    'download',
                    patientId
                )
            );
        }

        throw new Error('Invalid action specified');
    } catch (error) {
        console.error('Error handling payment document:', error);
        throw error;
    }
};

/**
 * Check the status of a payment document generation
 * @param referenceAlphaId The reference alpha ID of the payment document
 * @returns API response with document status and URL if ready
 */
export const checkPaymentDocumentStatus = async (referenceAlphaId: string) => {
    try {
        return await Http.getWithAuth(
            PAYMENT_DOCUMENT_STATUS_URL(referenceAlphaId)
        );
    } catch (error) {
        console.error('Error checking payment document status:', error);
        throw error;
    }
};

/**
 * Trigger file download using the URL from API response
 * @param url The signed URL from the API response
 * @param fileName Optional custom filename for the downloaded file
 */
export const downloadFileFromUrl = (url: string, fileName?: string) => {
    try {
        // Use fetch to get the file content as a blob
        fetch(url)
            .then((response) => response.blob())
            .then((blob) => {
                // Create a blob URL from the file content
                const blobUrl = window.URL.createObjectURL(blob);

                // Create an invisible anchor element
                const link = document.createElement('a');
                link.href = blobUrl;

                // Set download attribute with proper filename
                link.setAttribute('download', fileName || 'receipt.pdf');

                // Append to body, click to trigger download, then remove
                document.body.appendChild(link);
                link.click();

                // Clean up
                document.body.removeChild(link);
                window.URL.revokeObjectURL(blobUrl);
            })
            .catch((error) => {
                console.error('Error fetching file:', error);
                // Fallback to direct open if fetch fails
                window.open(url, '_blank');
            });
    } catch (error) {
        console.error('Error downloading file:', error);
        // Fallback to direct open
        window.open(url, '_blank');
    }
};
