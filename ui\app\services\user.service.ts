import * as Http from './http.service';
import {
    getAuth,
    getRefreshToken,
    removeAuth,
    removeRT,
    setAuth,
} from './identity.service';
import {
    COMPLETE_STAFF_DETAILS,
    GET_ALL_ROLES,
    GET_CLINIC_USER_DATA,
    GET_USER_CLINICS,
    GET_USER_DETAILS,
    GET_USERS,
    UPDATE_USER_DETAILS,
    UPDATE_USER_STATUS,
    UPDATE_WORKING_HOURS,
    GET_USER_EXCEPTIONS,
    GET_EXCEPTION_DETAILS,
    CREATE_EXCEPTION,
    UPDATE_EXCEPTION,
    DELETE_EXCEPTION,
    GET_CALENDAR_WORKING_HOURS,
    GET_ALL_CLINIC_DOCTORS,
    VERIFY_PIN_URL,
} from './url.service';
import { redirect } from 'next/navigation';

export const refreshUserToken = (userId: string, refreshToken: string) => {
    return Http.postWithOutAuth('some-url', { userId, refreshToken });
};

export const refreshTokenAndSetAuth = async (callback: Function) => {
    try {
        if (typeof window !== 'undefined') {
            // Client-side only code
            window.location.href = '/signin/pin';
            return;
        } else {
            // Server-side code
            redirect('/signin/pin');
        }
        const auth = getAuth();
        const refreshToken = getRefreshToken();
        const response = await refreshUserToken(auth.id, refreshToken);
        if (response.status) {
            const data = response.data.entity;
            const user = {
                id: data.id,
                roleId: data.roleId,
                name: data.name,
                access_token: data.access_token,
                role: data.Role.name,
            };
            setAuth(user);

            const res = await callback();
            return res;
        }
        if (
            !response.status &&
            response.errorMessage === 'Failed to get new access tokens!'
        ) {
            removeRT();
            removeAuth();
            Router.push('/login');
        }
    } catch (error) {
        console.log(error);
    }
};

export const getClinicUsers = (
    clinicId: string,
    page: number,
    limit: number
) => {
    return Http.getWithAuth(GET_USERS(clinicId, page, limit));
};

export const updateClinicUserStatus = (userId: string, isActive: boolean) => {
    return Http.putWithAuth(UPDATE_USER_STATUS(userId), { isActive });
};

export const getUserDetails = (userId: string) => {
    return Http.getWithAuth(GET_USER_DETAILS(userId));
};

export const updateUserDetails = (userId: string, userData: any) => {
    return Http.putWithAuth(UPDATE_USER_DETAILS(userId), userData);
};

export const completeStaffDetails = (globalUserId: string, userData: any) => {
    return Http.putWithAuth(COMPLETE_STAFF_DETAILS(globalUserId), userData);
};

export const updateWorkingHours = (userId: string, workingHours: any) => {
    return Http.putWithAuth(UPDATE_WORKING_HOURS(userId), { workingHours });
};

export const getAllRoles = () => {
    return Http.getWithAuth(GET_ALL_ROLES());
};

export const getClinicUserData = (userId: string) => {
    return Http.getWithAuth(GET_CLINIC_USER_DATA(userId));
};

export const getUserClinics = (userId: string) => {
    return Http.getWithAuth(GET_USER_CLINICS(userId));
};

// Exception related methods
export const getUserExceptions = (
    clinicUserId: string,
    includeHistory: boolean = false
) => {
    return Http.getWithAuth(GET_USER_EXCEPTIONS(clinicUserId, includeHistory));
};

export const getExceptionDetails = (id: string) => {
    return Http.getWithAuth(GET_EXCEPTION_DETAILS(id));
};

export const createException = (exceptionData: any) => {
    return Http.postWithAuth(CREATE_EXCEPTION(), exceptionData);
};

export const updateException = (id: string, exceptionData: any) => {
    return Http.putWithAuth(UPDATE_EXCEPTION(id), exceptionData);
};

export const deleteException = (id: string) => {
    return Http.deleteWithAuth(DELETE_EXCEPTION(id));
};

export const getCalendarWorkingHours = (date: string, clinicId: string) => {
    return Http.getWithAuth(GET_CALENDAR_WORKING_HOURS(date, clinicId));
};

// Fetch all doctors and admins for a specific clinic
export const getAllClinicDoctors = (clinicId: string) => {
    return Http.getWithAuth(GET_ALL_CLINIC_DOCTORS(clinicId));
};

// Verify user PIN for authorization
export const verifyUserPin = (pin: string) => {
    return Http.postWithAuth(VERIFY_PIN_URL(), { pin });
};
