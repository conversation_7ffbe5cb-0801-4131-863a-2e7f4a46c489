import React, {
    ChangeEvent,
    Dispatch,
    SetStateAction,
    useCallback,
    useEffect,
    useState,
    useMemo,
} from 'react';
import BeginTreatment, {
    BeginTreatmentProps,
} from '../organisms/patientDetail/BeginTreatment';
import {
    Breadcrumbs,
    Modal,
    Pagination,
    Searchbar,
    Tooltip,
} from '../molecules';
import AppDetailSidebar, {
    AppDetailSidebarT,
} from '../organisms/AppointmentDetails/AppDetailSidebar';
import { Button, Heading, Text } from '../atoms';
import { Add, Eye, EyeSlash } from 'iconsax-react';
import DateNavigator from '../molecules/DateNavigator';
import Tabs from '../molecules/Tabs';
import { TabItemType } from '../atoms/HorizontalTabs';
import IconCalender from '../atoms/customIcons/IconCalender';
import IconMenu from '../atoms/customIcons/IconMenu';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import moment from 'moment';
import { useInView } from 'react-intersection-observer';
import CreateAppointment from '../organisms/appointment/CreateAppointment';
import {
    APPOINTMENT_TRIAGE_OPTIONS,
    APPOINTMENT_TYPE,
    DATE_FORMAT,
} from '../utils/constant';
import {
    AppointmentParams,
    CreateAppointmentType,
    EnumAppointmentStatus,
} from '../types/appointment';
import {
    useAppointmentMutation,
    useDeleteAppointment,
    useUpdateAppointmentFeilds,
    useUpdateAppointmentStatus,
} from '../services/appointment.queries';
import CreateAppointmentStaff, {
    AppointmentStaffStepT,
    AppStaffStepData,
} from '../organisms/appointment/CreateAppointmentStaff';
import { ResponseStatus } from '../molecules/PatientDropdown';
import { PatientT } from '../types/patient';
import { createAppointmentValidationSchema } from '../utils/validation-schema/createAppointmentValidation';
import { MenuListType } from '../molecules/DropdownMenu';
import AddPatient from '../organisms/patient/AddPatient';
import { useCreateOrUpdatePatient } from '../services/patient.queries';
import { searchPatientByPhone } from '../services/patient.service';
import _, { debounce } from 'lodash';
import { parsePhoneNumber } from 'react-phone-number-input';
import { validationSchema } from '../utils/validation-schema/patientValidation';
import Calendar from '../organisms/calendar/Calendar';
import { useDate } from '@/context/date-provider';
import {
    checkOnGoingAppointmentService,
    downloadTodaysAppointment,
} from '../services/appointment.service';
import IconWarning from '../atoms/customIcons/IconWarning.svg';
import { useRouter } from 'next/navigation';
import EmptyState from '@/app/molecules/EmptyState';
import { getAuth } from '../services/identity.service';
import { formatBreed, getProfileImage } from '../utils/common';
import { Download } from 'lucide-react';
import { useClinicDoctorsAvailability } from '../services/providers.queries';
import {
    getReasonOptions,
    concatDateTime,
} from '@/app/utils/patient-details-utils/create-appointment-utils';
import { deduplicateAppointments } from '../utils/appointment-utils';
import { useCalendarSocket } from '../hooks/useCalendarSocket';

const breadcrumbList = [
    { id: 1, name: 'Dashboard', path: '/dashboard' },
    { id: 2, name: 'Appointment', path: '/appointment' },
];
interface DaySchedule {
    startTime: string;
    endTime: string;
    isWorkingDay: boolean;
}
interface WeeklySchedule {
    Monday: DaySchedule[];
    Tuesday: DaySchedule[];
    Wednesday: DaySchedule[];
    Thursday: DaySchedule[];
    Friday: DaySchedule[];
    Saturday: DaySchedule[];
    Sunday: DaySchedule[];
}

interface AlertT {
    isOpen: boolean;
    label: string;
    variant: 'success' | 'error' | 'warning' | 'info';
    isLight?: boolean;
}

interface DoctorAvailabilityParams {
    clinicId: string;
    date?: string;
    startTime?: string;
    endTime?: string;
    role?: string;
    search?: string;
    orderBy?: string;
}
interface AppointmentDetailsT {
    appSidebarProps: AppDetailSidebarT;
    appointmentList: BeginTreatmentProps[];
    totalAppointments: number;
    listLoadStatus?: 'error' | 'success' | 'pending';
    fetchNextPage: Function;
    hasNextPage: boolean;
    clinicRoomsData: object;
    doctorData: object;
    patientsData: object;
    providerData: object;
    pageCount?: number;
    pagination: number;
    currentDate: string;
    setPagination: Dispatch<SetStateAction<number>>;
    setAppointmentParams: Dispatch<SetStateAction<AppointmentParams>>;
    appointmentParams: AppointmentParams;
    setPatientParams: Dispatch<SetStateAction<any>>;
    patientStatus: string;
    setShowAlert: Dispatch<SetStateAction<any>>;
    clinicDetails: any;
    workinghours: WeeklySchedule;
    customRule?: { patientLastNameAsOwnerLastName: boolean };
}
interface AvailabilityResponse {
    isAvailable: boolean;
    nextAvailableTime?: string;
}

const AppointmentDetails = (props: AppointmentDetailsT) => {
    // Enable real-time calendar updates with clinic ID
    const auth = getAuth();
    const CLINIC_ID = auth?.clinicId;
    useCalendarSocket(CLINIC_ID);

    const {
        setSelectedDate,
        setEvents,
        setSelectedUser,
        setAllDoctors,
        setClinicDetails,
    } = useDate();
    const {
        appSidebarProps,
        appointmentList,
        listLoadStatus,
        fetchNextPage,
        clinicRoomsData,
        doctorData,
        patientsData,
        providerData,
        currentDate,
        totalAppointments,
        setPatientParams,
        patientStatus,
        setShowAlert,
        clinicDetails,
        hasNextPage,
        workinghours,
        customRule,
    } = props;
    const router = useRouter();
    setAllDoctors(
        doctorData?.data?.users ? (doctorData?.data.users as any[]) : []
    );
    setClinicDetails(clinicDetails);
    const [selectedDoctors, setSelectedDoctors] = useState([]);

    const [selectedStatus, setSelectedStatus] = useState([]);

    const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const [showPatientDropdown, setShowPatientDropdown] = useState(false);
    const [showDoctorDropdown, setShowDoctorDropdown] = useState(false);
    const [listStatus, setListStatus] = useState<ResponseStatus>('pending');
    const [selectedPatient, setSelectedPatient] = useState<null | PatientT>(
        null
    );
    const [showOngoingAppointmentWarning, setShowOngoingAppointmentWarning] =
        useState(false);
    const [ongoingAppointmentPatientId, setOngoingAppointmentPatientId] =
        useState(null);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteAppointmentId, setDeleteAppointmentId] = useState('');
    const [patientList, setPatientList] = useState(patientsData?.patients);
    const [patientData, setPatientData] = useState({});
    const [viewMode, setViewMode] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [appointmentsViewMode, setAppointmentsViewMode] =
        useState('list-view');
    // const [patientParams, setPatientParams] = useState({
    //     clinicId: CLINIC_ID,
    //     limit: 10,
    //     page: 1,
    //     search: '',
    // });
    // Modal-specific alert for errors within the appointment modal
    const [showModalAlert, setShowModalAlert] = useState<AlertT>({
        isOpen: false,
        label: '',
        variant: 'error',
        isLight: true,
    });
    const [step, setStep] = useState<AppointmentStaffStepT>('');
    const [appointmentId, setAppointmentId] = useState('');
    const [selectedAppointmentData, setSelectedAppointmentData] = useState({});
    const [valuesChanged, setValuesChanged] = useState(false);
    const [confirmModal, setConfirmModal] = useState<{
        isOpen: boolean;
        type: 'cancelAppointment' | 'PatientAddedSuccessfully';
    }>({
        isOpen: false,
        type: 'cancelAppointment',
    });

    const [currentStep, setCurrentStep] = useState(1);
    const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);

    const { ref, inView } = useInView();
    const { deleteAppointmentMutation } = useDeleteAppointment(
        props.appointmentParams,
        false
    );
    const dateValidationSchema = yup.object().shape({
        appDate: yup.string(),
        appFilterOption: yup.string(),
        // appointmentDate: yup.date().required('Date is required'),
        // Add other fields validations here
    });
    const [defaultDoctorOptions, setDefaultDoctorOptions] = useState([]);
    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors, isDirty, dirtyFields },
        watch,
        trigger,
    } = useForm({
        resolver: yupResolver(createAppointmentValidationSchema),
        mode: 'onChange',
    });

    const [transformedAppointments, setTransformedAppointments] = useState([]);

    // Memoised list without Google events – used only for normal list view
    const listViewAppointments = useMemo(
        () =>
            transformedAppointments?.filter(
                (app: any) =>
                    !(
                        typeof app.id === 'string' &&
                        app.id.startsWith('google_')
                    )
            ) || [],
        [transformedAppointments]
    );

    const {
        register: dateRegister,
        getValues: dateGetValues,
        setValue: dateSetValue,
        control: dateControl,
        formState: { errors: dateErrors },
        watch: dateWatch,
    } = useForm({
        resolver: yupResolver(dateValidationSchema),
        mode: 'onChange',
        defaultValues: {
            appDate: new Date(),
            appFilterOption: 'all',
        },
    });

    const {
        register: addPatientRegister,
        handleSubmit: addPatientHandleSubmit,
        getValues: addPatientGetValues,
        setValue: addPatientSetValue,
        setError: addPatientSetError,
        control: addPatientControl,
        reset: addPatientReset,
        resetField: addPatientResetField,
        formState: { errors: addPatientErrors },
        watch: addPatientWatch,
        trigger: addPatientTrigger,
        clearErrors,
    } = useForm({
        resolver: yupResolver(validationSchema),
        mode: 'onChange', // This will trigger validation on change,
        defaultValues: {},
    });

    const selectedDate = dateGetValues('appDate');

    const appSidebarData = {
        className: '',
        control: dateControl,
        errors: dateErrors,
        register: dateRegister,
        setValue: dateSetValue,
        getValues: dateGetValues,
        watch: dateWatch,
        loadOptionsDoctors: [],
        selectedDoctors: selectedDoctors,
        selectedStatus: selectedStatus,
        setSelectedDoctors: setSelectedDoctors,
        setSelectedStatus: setSelectedStatus,
    };

    // Add watch for time fields
    const startTime = watch('startTime');
    const endTime = watch('endTime');

    // Memoize doctor options to avoid unnecessary recalculations
    const [doctorOptions, setDoctorOptions] = useState([]);

    // Update doctor options when times change
    useEffect(() => {
        console.log(doctorData);
        if (doctorData?.data.users && startTime && endTime) {
            const options = doctorData.data.users
                .filter(
                    (user: any) =>
                        user.role.name === 'admin' ||
                        user.role.name === 'doctor'
                )
                .map((item: any) => {
                    return {
                        value: item.id,
                        label: `${item.firstName} ${item.lastName}`,
                        availability: availabilityData?.data?.users?.find(
                            (u) => u.doctorId === item.id
                        )?.availability || {
                            isAvailable: false,
                            nextAvailableSlot: undefined,
                        },
                    };
                });
            setDoctorOptions(options);
        }
    }, [startTime, endTime, doctorData]);

    const dateChangeHandler = (date: Date) => {
        setTransformedAppointments([]);
        props.setAppointmentParams({
            ...props.appointmentParams,
            date: moment(date).format('YYYY-MM-DD').toString(),
        });
        setSelectedDate(moment(date));
        appSidebarData.setValue('appDate', date, { shouldValidate: true });
    };

    const onChangeDoctors = (data: any) => {
        setSelectedUser(data);
        let doctors = data?.length
            ? data.map((item: object) => item?.value)
            : [];
        props.setAppointmentParams({
            ...props.appointmentParams,
            doctors,
        });
    };
    const onChangeStatus = (data: any) => {
        setSelectedStatus(data);
        let status = data?.length
            ? data.map((item: object) => item?.value)
            : [];
        props.setAppointmentParams({
            ...props.appointmentParams,
            status,
        });
    };

    const viewModeList: TabItemType[] = [
        {
            id: 'list-view',
            label: (
                <IconMenu className="text-inherit cursor-pointer" size={24} />
            ),
        },
        {
            id: 'calender-view',
            label: (
                <IconCalender
                    className="text-inherit cursor-pointer"
                    size={24}
                />
            ),
        },
    ];
    const routeToPatientDetails = (patientId: string) => {
        return router.push(`/patients/${patientId}/details`);
    };
    const setDefaultValue = (
        data: any,
        setValue: Function,
        setPatientData: Function,
        step: any,
        viewMode: boolean
    ) => {
        setValue('notes', data.notes);
        setValue('date', moment(data.date).format('YYYY-MM-DD'));
        setValue(
            'startTime',
            !viewMode ? data.startTime : moment(data.startTime).format('H:mm a')
        );
        setValue(
            'endTime',
            !viewMode ? data.endTime : moment(data.endTime).format('H:mm a')
        );
        setValue('type', { label: data.type, value: data.type });
        setValue('reason', { label: data.reason, value: data.reason });
        const selectedDoctor = {
            value: data.appointmentDoctors.find((doctor: any) => doctor.primary)
                .doctor.id,
            label:
                data.appointmentDoctors.find((doctor: any) => doctor.primary)
                    .doctor.firstName +
                ' ' +
                data.appointmentDoctors.find((doctor: any) => doctor.primary)
                    .doctor.lastName,
        };
        setValue('doctors', selectedDoctor);

        const providers = data.appointmentDoctors
            .filter((doc: any) => doc.primary === false)
            .map((doc: any) => {
                return {
                    label: doc?.doctor?.firstName + ' ' + doc?.doctor?.lastName,
                    value: doc?.doctor?.id,
                };
            });
        setValue('provider', providers);
        setValue('room', { label: data.room?.name, value: data.room?.id });
        setValue('triage', { label: data.triage, value: data.triage });
        setValue('weight', data.weight);
        setValue('status', data.status);
        setValue('patientSearch', data.patient);
        // setValue('providers', data.provider.map((list) => {
        //     return {
        //         value: list.value,
        //         label: list.label
        //     }
        // }))
        const profileImage = getProfileImage({
            breedValue: data.patient.breed,
            species: data.patient.species,
        });

        setPatientData({ ...data.patient, profileImage });

        const dataStep = Object.values(AppStaffStepData).find((item) => {
            return (
                item.status.replace(/\s/g, '').toLowerCase() ===
                data.status.replace(/\s/g, '').toLowerCase()
            );
        });

        const dataStepKey = Object.keys(AppStaffStepData).find(
            (key) => AppStaffStepData[key] === dataStep
        );

        setStep(dataStepKey as AppointmentStaffStepT);
        setSelectedAppointmentData(getValues());
    };
    useEffect(() => {
        // Safely deduplicate appointments to prevent Google events (and other appointments) from appearing multiple times
        const deduplicatedAppointments = deduplicateAppointments(
            appointmentList || []
        );

        const transformedAppointmentData = deduplicatedAppointments.map(
            (appointment: any) => {
                const appointmentDate = moment(appointment.date).format(
                    'DD MMMM YYYY'
                );

                const startTimeDate = moment(appointment.startTime).format(
                    'DD MMMM YYYY'
                );
                let newStartTime = null;
                let newEndTime = null;

                if (appointmentDate !== startTimeDate) {
                    newStartTime = moment(appointment.startTime).set({
                        year: moment(appointment.date).year(),
                        month: moment(appointment.date).month(),
                        date: moment(appointment.date).date(),
                    });

                    newEndTime = moment(appointment.endTime).set({
                        year: moment(appointment.date).year(),
                        month: moment(appointment.date).month(),
                        date: moment(appointment.date).date(),
                    });
                }
                return {
                    id: appointment.id,
                    googleEventId: appointment.googleEventId, // Include Google event ID
                    treatmentData: {
                        type: appointment.type,
                        reason: appointment.reason,
                        doctor: appointment.appointmentDoctors
                            .filter((item) => item.primary)
                            .map(
                                (doctor) =>
                                    `${doctor?.doctor?.firstName} ${doctor?.doctor?.lastName} `
                            ),
                        doctors: appointment.appointmentDoctors.map(
                            (doctor: any) => doctor?.doctorId
                        ),
                        providers: appointment.appointmentDoctors
                            .filter((item) => item.primary === false)
                            .map(
                                (doctor) =>
                                    `${doctor?.doctor?.firstName} ${doctor?.doctor?.lastName} `
                            )
                            .join(', '),
                        weight: appointment.weight
                            ? `${appointment.weight} kgs`
                            : '--',
                        room: appointment.room?.name,
                    },
                    scheduleData: {
                        date: moment(appointment.date).format('DD MMMM YYYY'),
                        time: moment(appointment.startTime).format('hh:mm A'),
                        endTime: newEndTime
                            ? newEndTime
                            : moment(appointment?.endTime),
                        startTime: newStartTime
                            ? newStartTime
                            : moment(appointment?.startTime),
                        status: appointment.status,
                        statusLabel: appointment.status,
                        profile:
                            getProfileImage({
                                species: appointment.patient.species,
                                breedValue: appointment.patient.breed,
                            }) || 'no profile image',
                        name:
                            customRule?.patientLastNameAsOwnerLastName ===
                                true &&
                            appointment.patient.patientOwners?.[0]?.ownerBrand
                                ?.lastName
                                ? `${appointment.patient.patientName} ${appointment.patient.patientOwners[0].ownerBrand.lastName}`
                                : appointment.patient.patientName,
                        ownerNumber:
                            appointment.patient.patientOwners[0]?.ownerBrand
                                .globalOwner.phoneNumber,
                        owner: appointment.patient.patientOwners
                            .map(
                                (owner) =>
                                    owner.ownerBrand.firstName +
                                    ' ' +
                                    owner.ownerBrand.lastName
                            )
                            .join(', '),
                        breed: formatBreed(appointment.patient.breed),
                        triage: appointment.triage,
                        checkinTime: appointment.checkinTime,
                        receivingCareTime: appointment.receivingCareTime,
                        checkoutTime: appointment.checkoutTime,
                    },
                    handleMenu: (data, id) => {
                        handleMenu(data, id);
                    },
                    handleBeginTreatment: () => {
                        /* Your handleBeginTreatment logic here */
                    },
                    handleQuestions: () => {
                        /* Your handleQuestions logic here */
                    },
                    handleNotes: () => {
                        /* Your handleNotes logic here */
                    },
                };
            }
        );

        setTransformedAppointments(transformedAppointmentData);
        setEvents(transformedAppointmentData);
        // Debug log
        console.debug('[AppointmentDetails] setEvents called', {
            total: transformedAppointmentData.length,
            google: transformedAppointmentData.filter(
                (e: any) => e.googleEventId
            ).length,
            sample: transformedAppointmentData.slice(0, 3),
        });
    }, [appointmentList, setTransformedAppointments]);

    // const transformedAppointments = appointmentList.map((appointment: any) => {
    //     return {
    //         id: appointment.id,
    //         patientId: appointment.patientId,
    //         treatmentData: {
    //             type: appointment.type,
    //             reason: appointment.reason,
    //             doctor: appointment.appointmentDoctors
    //                 .filter((item) => item.primary)
    //                 .map((doctor) => doctor?.doctor?.firstName),
    //             providers: appointment.appointmentDoctors
    //                 .filter((item) => item.primary === false)
    //                 .map((doctor) => doctor?.doctor?.firstName)
    //                 .join(', '),
    //             weight: appointment.weight
    //                 ? `${appointment.weight} kgs`
    //                 : '--',
    //             room: appointment.room?.name,
    //         },
    //         scheduleData: {
    //             date: moment(appointment.date).format('DD MMMM YYYY'),
    //             time: moment(appointment.startTime).format('hh:mm A'),
    //             status: appointment.status,
    //             statusLabel: appointment.status,
    //             profile: '/images/dog.svg',
    //             name: appointment.patient.patientName,
    //             owner: appointment.patient.patientOwners
    //                 .map(
    //                     (owner) =>
    //                         owner.owner.firstName + ' ' + owner.owner.lastName
    //                 )
    //                 .join(', '),
    //             breed: appointment.patient.breed,
    //             triage: appointment.triage,
    //         },
    //         handleMenu: (data, id) => {
    //             handleMenu(data, id);
    //         },
    //         handleBeginTreatment: () => {
    //             /* Your handleBeginTreatment logic here */
    //         },
    //         handleQuestions: () => {
    //             /* Your handleQuestions logic here */
    //         },
    //         handleNotes: () => {
    //             /* Your handleNotes logic here */
    //         },
    //     };
    // });

    const handleMenu = (data, id) => {
        if (data.id === 'edit') {
            const foundAppintment = appointmentList.find(
                (item) => item.id === id
            );
            if (data.id === 'edit') {
                setEditMode(true);
                setDefaultValue(
                    foundAppintment,
                    setValue,
                    setPatientData,
                    step,
                    false
                );
                setAppointmentId(id);
                setShowAppointmentModal(true);
            }
        }
        if (data.id === 'delete') {
            setShowDeleteModal(true);
            setDeleteAppointmentId(id);
            // deleteAppointmentMutation.mutate(id);
        }
    };

    useEffect(() => {
        if (inView) {
            fetchNextPage();
        }
    }, [inView, fetchNextPage]);

    const handleAddAppointment = (DefaultPatientData: boolean = true) => {
        reset();

        if (DefaultPatientData) {
            setPatientData({});
        }
        if (!DefaultPatientData) {
            setValue('patientSearch', patientData);
        }
        setSelectedPatient(null);
        setEditMode(false);
        // setPatientData({});
        // reset();
        //setEditMode(false);
        // setSelectedPatient(null);
        const now = moment();

        const nextQuarter = moment()
            .startOf('hour')
            .add(Math.ceil(now.minute() / 15) * 15, 'minutes');

        const appointmentStartTime = nextQuarter.format('D-MMM-YYYY HH:mm');

        const appointmentEndTime = nextQuarter
            .add(30, 'minutes')
            .format('D-MMM-YYYY HH:mm');

        setValue('date', moment().format('D-MMM-YYYY'), {
            shouldValidate: true,
        });
        setValue('startTime', appointmentStartTime, {
            shouldValidate: true,
        });
        setValue('endTime', appointmentEndTime, {
            shouldValidate: true,
        });
        setValue(
            'type',
            {
                value: 'Consultation',
                label: 'Consultation',
            },
            { shouldValidate: true }
        );
        setValue('status', 'Scheduled', { shouldValidate: true });
        setShowAppointmentModal(true);
    };

    const updateAppointmentStatusMutation = useUpdateAppointmentStatus(
        props.appointmentParams
    );
    const { updateAppointmentMutation } = useUpdateAppointmentFeilds(
        props.appointmentParams
    );
    const createPatientMutation = useCreateOrUpdatePatient();

    const checkPatientOngoingAppointment = async (
        patientId: string,
        appointmentId: string
    ) => {
        const {
            data: { hasOngoingAppointment, appointment },
        } = await checkOnGoingAppointmentService(patientId);

        if (!hasOngoingAppointment) {
            return false;
        }
        return appointment.id !== appointmentId;
    };
    // useEffect(() => {
    //     const search = watch('patientSearch');

    //     if (search) {
    //         setShowPatientDropdown(true);
    //         setListStatus('pending');
    //         // getPatientOptions
    //         getPatientOptions(search, []).then((data: any) => {
    //             setPatientList(data.options);
    //             setListStatus('success');
    //         });
    //     } else {
    //         setShowPatientDropdown(false);
    //     }
    // }, [watch('patientSearch')]);

    useEffect(() => {
        if (patientStatus === 'pending') {
            setShowPatientDropdown(true);
            setListStatus('pending');
        }
        if (patientStatus === 'success') {
            setPatientList(patientsData?.patients);
            setListStatus('success');
            // setShowPatientDropdown(false);
        }
    }, [patientStatus]);

    useEffect(() => {
        const weight: string = String(addPatientGetValues('weight'));
        if (weight === '') addPatientSetValue('weight', null);
    }, [addPatientWatch('weight')]);

    const onStepHandler = async (appointmentId: string) => {
        //check it here
        const foundAppintment: any = appointmentList.find(
            (item) => item.id === appointmentId
        );

        const hasOngoingAppointment = await checkPatientOngoingAppointment(
            foundAppintment?.patientId,
            foundAppintment?.id
        );

        if (!hasOngoingAppointment) {
            const dataStep = Object.values(AppStaffStepData).find((item) => {
                return (
                    item.status.replace(/\s/g, '').toLowerCase() ===
                    foundAppintment?.status.replace(/\s/g, '').toLowerCase()
                );
            });

            const dataStepKey = Object.keys(AppStaffStepData).find(
                (key) => AppStaffStepData[key] === dataStep
            );

            setStep(dataStepKey as AppointmentStaffStepT);

            //  setSelectedAppointmentData(getValues());
            switch (dataStepKey) {
                case 'checkIn':
                    setStep('beginTreatment');
                    updateAppointmentStatusMutation.mutate({
                        appointmentId,
                        status: EnumAppointmentStatus.Checkedin,
                    });
                    break;

                case 'beginTreatment':
                    setStep('continueTreatment');
                    updateAppointmentStatusMutation.mutate({
                        appointmentId,
                        status: EnumAppointmentStatus.ReceivingCare,
                    });
                    router.push(
                        `/patients/${foundAppintment.patientId}/details?appointment=${appointmentId}&status=begin-treatment`
                    );
                    break;

                case 'continueTreatment':
                    // setStep('complete');
                    // updateAppointmentStatusMutation.mutate({
                    //     appointmentId,
                    //     status: EnumAppointmentStatus.Checkedout,
                    // });
                    router.push(
                        `/patients/${foundAppintment.patientId}/details?status=ready-for-checkout`
                    );
                    break;

                case 'checkout':
                    return router.push(
                        `/patients/${foundAppintment.patientId}/details?appointment=${appointmentId}&status=show-cart`
                    );
                    break;
                case 'complete':
                    // setStep('complete');
                    // updateAppointmentStatusMutation.mutate({
                    //     appointmentId,
                    //     status: EnumAppointmentStatus.Checkedout,
                    // });
                    router.push(
                        `/patients/${foundAppintment.patientId}/details?appointment=${foundAppintment.id}&status=show-cart`
                    );
                    break;
                // case 'complete':
                //     setShowAppointmentModal(false);
            }
        } else {
            setOngoingAppointmentPatientId(foundAppintment.patientId);
            setShowOngoingAppointmentWarning(true);
        }
    };

    const onMenuClick = (menu: PatientT) => {
        setSelectedPatient(menu);
    };

    const getAppointmentTypeOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: APPOINTMENT_TYPE.filter((item) =>
                item.toLowerCase().includes(search.toLowerCase())
            ).map((item) => {
                return {
                    value: item,
                    label: item,
                };
            }),
            hasMore: false,
        };
    };

    const getRoomOptions = async (search: string, loadedOptions: unknown[]) => {
        return {
            options: clinicRoomsData?.data.rooms
                .filter((item: any) =>
                    item.name.toLowerCase().includes(search.toLowerCase())
                )
                .map((item: any) => {
                    return {
                        label: item.name,
                        value: item.id,
                    };
                }),
            hasMore: false,
        };
    };

    const [availabilityParams, setAvailabilityParams] =
        useState<DoctorAvailabilityParams>({
            clinicId: CLINIC_ID,
        });

    const { data: availabilityData } =
        useClinicDoctorsAvailability(availabilityParams);

    useEffect(() => {
        const date = watch('date');
        const startTime = watch('startTime');
        const endTime = watch('endTime');

        if (date && startTime && endTime) {
            const selectedDate = moment(date).format('YYYY-MM-DD');
            const combinedStartTime = moment(date)
                .hours(moment(startTime).hours())
                .minutes(moment(startTime).minutes())
                .toISOString();
            const combinedEndTime = moment(date)
                .hours(moment(endTime).hours())
                .minutes(moment(endTime).minutes())
                .toISOString();

            const newParams = {
                clinicId: CLINIC_ID,
                date: selectedDate,
                startTime: combinedStartTime,
                endTime: combinedEndTime,
                // role: 'doctor',
            };
            setAvailabilityParams(newParams);
        }
    }, [watch('date'), watch('startTime'), watch('endTime')]);

    // Add effect to log availability data updates
    useEffect(() => {
        console.log('Availability Data Updated:', availabilityData);
    }, [availabilityData]);

    const getDoctorOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        const options = doctorData?.data.users
            .filter(
                (user: any) =>
                    user.role.name === 'admin' || user.role.name === 'doctor'
            )
            .filter((item: any) =>
                `${item.firstName} ${item.lastName}`
                    .toLowerCase()
                    .includes(search.toLowerCase())
            )
            .map((item: any) => {
                return {
                    value: item.id,
                    label: `${item.firstName} ${item.lastName}`,
                    availability: availabilityData?.data?.users?.find(
                        (u) => u.doctorId === item.id
                    )?.availability || {
                        isAvailable: false,
                        nextAvailableSlot: undefined,
                    },
                };
            });
        return {
            options,
            hasMore: false,
        };
    };

    const getDefaultOptoins = async () => {
        const options = await getDoctorOptions('', []);
        setDefaultDoctorOptions(options.options);
    };
    useEffect(() => {
        getDefaultOptoins();
    }, []);

    // Remove redundant debounce
    const setPatientSearchParams = (searchTerm: string) => {
        setPatientParams({
            clinicId: CLINIC_ID,
            limit: 10,
            page: 1,
            search: searchTerm,
        });
    };

    const getPatientOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        setPatientSearchParams(search);
        const options = patientsData?.patients?.map((item: any) => {
            return item;
        });
        return {
            options,
            hasMore: false,
        };
    };

    const getTriggerOption = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: APPOINTMENT_TRIAGE_OPTIONS.filter((item) =>
                item.name.toLowerCase().includes(search.toLowerCase())
            ).map((item) => {
                return {
                    value: item.name,
                    label: item.name,
                };
            }),
            hasMore: false,
        };
    };
    useEffect(() => {
        const values = getValues();
        const filteredValues = { ...values };
        const filteredAppointmentData = { ...selectedAppointmentData };

        if (
            (filteredValues.weight === undefined ||
                filteredValues.weight === '' ||
                filteredValues.weight === null) &&
            (filteredAppointmentData.weight === undefined ||
                filteredAppointmentData.weight === '' ||
                filteredAppointmentData.weight === null)
        ) {
            delete filteredValues.weight;
            delete filteredAppointmentData.weight;
        }

        if (!_.isEqual(filteredValues, filteredAppointmentData)) {
            setValuesChanged(true);
        } else {
            setValuesChanged(false);
        }
    }, [watch()]);
    const handleUpdateAppointment = (
        data: any,
        typeOfCall: string,
        route: boolean = false
    ): void => {
        let status = '';

        if (typeOfCall === 'buttonCall') status = data.status;
        else if (typeOfCall === 'markAsSchedule')
            status = EnumAppointmentStatus.Scheduled;
        else {
            switch (step) {
                case 'checkIn':
                    status = EnumAppointmentStatus.Checkedin;
                    break;

                case 'beginTreatment':
                    status = EnumAppointmentStatus.ReceivingCare;
                    break;

                case 'continueTreatment':
                    status = EnumAppointmentStatus.ReceivingCare;
                    break;
                case 'checkout':
                    status = EnumAppointmentStatus.Checkedout;
                    break;
                case 'complete':
                    status = EnumAppointmentStatus.Completed;
            }
        }

        const appointmentBody: any = {
            clinicId: CLINIC_ID,
            doctorIds: [data.doctors.value],
            patientId: data.patientSearch?.id,
            roomId: data.room?.value,
            date: data?.date,
            startTime: concatDateTime(data?.date, data.startTime),
            endTime: concatDateTime(data?.date, data.endTime),
            status: status,
            providerIds:
                data.provider && data.provider.length
                    ? data.provider.map((item) => item.value)
                    : [],
            reason: data.reason?.label || '',
            type: data?.type.value,
            ...(data?.weight
                ? {
                      weight: parseInt(data?.weight),
                  }
                : {}),
            triage: data?.triage?.value,
            notes: data?.notes,
            isBlocked: false,
            weight: data.weight !== '' ? data.weight : null,
            appointmentId: appointmentId,
        };

        updateAppointmentMutation.mutate({
            appointmentId,
            body: appointmentBody,
        });
        setSelectedAppointmentData(getValues());
        if (typeOfCall === 'updateLabel') {
            setShowAppointmentModal(false);
        }

        if (route) {
            if (
                data.status !== status &&
                status === EnumAppointmentStatus.ReceivingCare
            ) {
                return router.push(
                    `/patients/${data.patientSearch.id}/details?appointment=${appointmentId}&status=begin-treatment`
                );
            }
            if (status === EnumAppointmentStatus.ReceivingCare) {
                return router.push(
                    `/patients/${data.patientSearch.id}/details?status=ready-for-checkout`
                );
            }
            if (status === EnumAppointmentStatus.Checkedout) {
                return router.push(
                    `/patients/${data.patientSearch.id}/details?appointment=${appointmentId}&status=show-cart`
                );
            }
        }
    };

    const handleCancel = () => {
        setShowAppointmentModal(false);
        addPatientReset();
        addPatientSetValue('patientName', '');
        setSelectedPatient(null);
        setViewMode(false);
        setEditMode(false);
        setPatientData({});
        setPatientParams((prev: any) => {
            return { ...prev, search: '' };
        });
    };

    const handleProviderDelete = () => {};
    const handleAddProvider = () => {};

    const getProviderOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        const options = providerData?.data.users
            .filter((user: any) => user.role.name !== 'admin')
            .filter((item: any) =>
                `${item.firstName} ${item.lastName}`
                    .toLowerCase()
                    .includes(search.toLowerCase())
            )
            .map((item: any) => {
                return {
                    value: item.id,
                    label: `${item.firstName} ${item.lastName}`,
                };
            });
        return {
            options,
            hasMore: false,
        };
    };

    const { createAppointmentMutation } = useAppointmentMutation(
        props.appointmentParams,
        setShowAppointmentModal
    );
    const handleCreateAppointment = useCallback(
        debounce((data: any): void => {
            const appointmentBody: CreateAppointmentType = {
                clinicId: CLINIC_ID,
                doctorIds: [data.doctors.value],
                providerIds:
                    data.provider && data.provider.length
                        ? data.provider.map((item) => item.value)
                        : [],
                patientId: data.patientSearch?.id,
                roomId: data.room?.value ?? null,
                date: data?.date,
                startTime: concatDateTime(data?.date, data?.startTime),
                endTime: concatDateTime(data?.date, data?.endTime),
                reason: data.reason?.label || '',
                type: data?.type.value,
                ...(data?.weight
                    ? {
                          weight: parseInt(data?.weight),
                      }
                    : {}),
                triage: data?.triage?.value,
                notes: data?.notes,
                isBlocked: false,
                weight: data.weight !== '' ? data.weight : null,
            };
            setShowAlert({
                isOpen: true,
                label: 'Appointment created successfully',
                variant: 'success',
                isLight: true,
            });

            createAppointmentMutation.mutate(appointmentBody);
        }, 500), // 500ms delay
        [createAppointmentMutation]
    );

    const scheduleStepHandler = async () => {
        //check it here
        const foundAppintment: any = appointmentList.find(
            (item) => item.id === appointmentId
        );
        const hasOngoingAppointment = await checkPatientOngoingAppointment(
            foundAppintment?.patientId,
            foundAppintment?.id
        );

        if (!hasOngoingAppointment) {
            updateAppointmentStatusMutation.mutate({
                appointmentId,
                status: EnumAppointmentStatus.Scheduled,
            });
            setShowAppointmentModal(false);
        }
    };

    const onCreateAppointmentAction = (item: MenuListType) => {
        if (item.id === 'cancel-appointment') {
            setShowAppointmentModal(false);
            setConfirmModal({
                isOpen: true,
                type: 'cancelAppointment',
            });
        }
    };

    const handleSearchAdd = () => {
        setCurrentStep(1);
        setIsAddPatientModalOpen(true);
        setShowAppointmentModal(false);
    };

    const handlePatientCreation = async () => {
        const isValid = await addPatientTrigger([
            'ownerFirstName',
            'ownerLastName',
            'patientName',
            'phoneNumber',
        ]);
        if (!isValid) {
            return false;
        }

        const formData: any = addPatientGetValues();

        try {
            const patientData = await createPatientMutation.mutateAsync({
                type: 'create',
                data: {
                    patientName: formData.patientName,
                    clinicId: CLINIC_ID,
                    ownersData: [
                        {
                            firstName: formData.ownerFirstName,
                            lastName: formData.ownerLastName,
                            phoneNumber: formData.phoneNumberNational,
                            countryCode: formData.countryCode,
                            isPrimary: true,
                        },
                    ],
                },
            });
            const patientOwner = {
                firstName: formData.ownerFirstName,
                lastName: formData.ownerLastName,
                email: '',
                address: '',
                phoneNumber: formData.phoneNumberNational,
                countryCode: formData.countryCode,
            };
            addPatientSetValue('patientOwners', [patientOwner]);

            const patientRenderingData = {
                id: '',
                patientName: formData.patientName,
                patientOwners: [
                    {
                        ownerBrand: {
                            firstName: formData.ownerFirstName,
                            lastName: formData.ownerLastName,
                            globalOwner: {
                                phoneNumber: formData.phoneNumberNational,
                            },
                        },
                    },
                ],
            };

            addPatientSetValue('patientId', patientData.data.id);
            addPatientSetValue(
                'ownerId',
                patientData.data.patientOwners[0].ownerBrand.id
            );
            patientRenderingData.id = patientData.data.id;
            setPatientData(patientRenderingData);
            addPatientSetValue('patientSearch', patientRenderingData);
            return true;
        } catch (error) {
            console.error('Error creating patient:', error);
            return false;
        }
    };

    const handleNext = async () => {
        const searchData = await searchPatientByPhone(
            addPatientGetValues('phoneNumberNational')
        );

        if (currentStep === 1) {
            const isValid = await addPatientTrigger('phoneNumber');
            if (!isValid) {
                console.log('Phone number validation failed');
                return;
            }

            if (searchData && searchData.data) {
                if (
                    searchData.data.message === 'Owner not found' ||
                    searchData.data.patients.length === 0
                ) {
                    setCurrentStep(3);
                } else if (searchData.data.owner && searchData.data.patients) {
                    const { owner, patients } = searchData.data;
                    addPatientSetValue('owner', owner);
                    addPatientSetValue('patients', patients);
                    setCurrentStep(2);
                } else {
                    console.log('Unexpected data structure', searchData.data);
                    // Handle unexpected data structure
                }
            } else {
                console.log('No search data available');
                // Handle case when no search data is available
            }
        } else if (currentStep === 2) {
            setCurrentStep(3);
        } else if (currentStep === 3) {
            const patientCreated = await handlePatientCreation();
            if (patientCreated) {
                setCurrentStep(4);
            }
        } else if (currentStep === 4) {
            handleCloseModal(true);
        }
    };

    const handleSave = async () => {
        const formValues = addPatientGetValues();
        const patientId = addPatientGetValues('patientId');
        const ownerId = addPatientGetValues('ownerId');
        const transformedData = transformPatientData(formValues, ownerId);
        try {
            const response = await createPatientMutation.mutateAsync({
                type: 'update',
                data: { ...transformedData, id: patientId },
            });
            addPatientReset();
            addPatientSetValue('patientName', '');
            handleCloseModal(true);
        } catch (error: any) {
            setShowModalAlert({
                isOpen: true,
                label:
                    error.message || 'An error occurred while updating patient',
                variant: 'error',
                isLight: true,
            });
            console.error('Error creating patient:', error);
        }
    };

    const transformPatientData = (formValues: any, ownerId: string) => {
        const ownersData = formValues.patientOwners.map(
            (item: any, index: number) => {
                const phone = parsePhoneNumber(item.phoneNumber);
                const baseOwnerData = {
                    firstName: item.firstName,
                    lastName: item.lastName,
                    email: item.email !== '' ? item.email : null,
                    isPrimary: index === 0,
                    id: ownerId,
                };
                if (item.phoneNumber.startsWith('+')) {
                    return {
                        ...baseOwnerData,
                        phoneNumber: phone?.nationalNumber,
                        countryCode: phone?.countryCallingCode,
                    };
                } else {
                    return {
                        ...baseOwnerData,
                        phoneNumber: item.phoneNumber,
                        countryCode: item.countryCode || '',
                    };
                }
            }
        );

        return {
            patientName: formValues.patientName,
            ownersData,
            species: formValues.species?.value || undefined,
            breed: formValues.breed?.value || undefined,
            age: moment(formValues.age).format(DATE_FORMAT) || undefined,
            gender: formValues.gender?.value || undefined,
            reproductiveStatus:
                formValues.reproductiveStatus?.value || undefined,
            microchipId: formValues.microchipId || undefined,
            identification: formValues.identification || undefined,
            allergies: formValues.allergies || undefined,
            markDeceased: formValues.markDeceased?.markDeceased || false,
        };
    };

    const handleCloseModal = (openAppointmentModal: boolean = false) => {
        setShowModalAlert({
            isOpen: false,
            label: '',
            variant: 'error',
            isLight: true,
        });
        if (openAppointmentModal) {
            handleAddAppointment(false);
        }
        // Reset all form data
        addPatientReset(); // Reset the add patient form
        reset(); // Reset the appointment form
        // Reset all relevant states
        setIsAddPatientModalOpen(false);
        setPatientData({});
    };

    const handlePatientSearch = (
        e: ChangeEvent<HTMLInputElement>,
        onFocus: any
    ) => {
        if (onFocus) {
            if (patientsData?.patients?.length) {
                setPatientList(patientsData?.patients);
                setListStatus('success');
                setShowPatientDropdown(true);
            }
            return;
        }
        getPatientOptions(e.target.value, []);
    };

    const onViewModeChange = async () => {
        if (appointmentsViewMode === 'list-view') {
            // dateSetValue('appFilterOption', 'my-appointment', {
            //     shouldValidate: true,
            // });
            // const myAppointmentOptons = [
            //     { value: getAuth().userId, label: '' },
            // ];

            // setSelectedDoctors(myAppointmentOptons);
            // onChangeDoctors(myAppointmentOptons);

            dateSetValue('appFilterOption', 'all', {
                shouldValidate: true,
            });
            const options = await getDoctorOptions('', []);
            setSelectedDoctors(options.options);
            onChangeDoctors(options.options);

            setAppointmentsViewMode('calender-view');
        } else if (appointmentsViewMode === 'calender-view') {
            dateSetValue('appFilterOption', 'all', {
                shouldValidate: true,
            });
            const options = await getDoctorOptions('', []);
            setSelectedDoctors(options);
            onChangeDoctors(options);
            setAppointmentsViewMode('list-view');
        }
    };

    const handleCalendarAppointment = (data: any) => {
        reset();
        setSelectedPatient(null);
        setEditMode(false);
        const tempdata = data.time;
        const roundedMinutes = Math.round(tempdata.minute() / 15) * 15;

        const newTime = tempdata
            .clone()
            .add(roundedMinutes - tempdata.minute(), 'minutes');

        const nextQuarter = newTime; // data.time;
        //.startOf('hour')
        //.add(Math.ceil(now.minute() / 15) * 15, 'minutes');

        const appointmentStartTime = nextQuarter.format('D-MMM-YYYY HH:mm');

        const appointmentEndTime = nextQuarter
            .add(30, 'minutes')
            .format('D-MMM-YYYY HH:mm');

        setValue('date', data?.date.format('D-MMM-YYYY'), {
            shouldValidate: true,
        });
        setValue('startTime', appointmentStartTime, {
            shouldValidate: true,
        });
        setValue('endTime', appointmentEndTime, {
            shouldValidate: true,
        });
        setValue(
            'type',
            {
                value: 'Consultation',
                label: 'Consultation',
            },
            { shouldValidate: true }
        );
        {
            data.user &&
                setValue(
                    'doctors',
                    {
                        value: data.user.value,
                        label: data.user.label,
                    },
                    { shouldValidate: true }
                );
        }
        setValue('status', 'Scheduled', { shouldValidate: true });
        setShowAppointmentModal(true);
    };

    const onClickAppointment = (id) => {
        const appointmentPatient = appointmentList.find(
            (item) => item.id === id
        );
        const patientId = appointmentPatient?.patientId;
        router.push(`patients/${patientId}/details`);
    };

    const [isHideCompleted, setIsHideCompleted] = useState(false);

    const handleToggleCompleted = () => {
        setIsHideCompleted(!isHideCompleted);
        props.setAppointmentParams((prevParams) => ({
            ...prevParams,
            status: !isHideCompleted
                ? [
                      EnumAppointmentStatus.Scheduled,
                      EnumAppointmentStatus.Checkedin,
                      EnumAppointmentStatus.ReceivingCare,
                      EnumAppointmentStatus.Checkedout,
                  ]
                : [],
        }));
    };

    const handleJumpToToday = () => {
        const today = new Date();
        dateChangeHandler(today);
    };

    const onDoctorMenuClick = () => {};

    async function downloadAppointments(date: Date) {
        try {
            const response = await downloadTodaysAppointment(
                moment(date).format('YYYY-MM-DD').toString(),
                CLINIC_ID
            );
            const blob = new Blob([response.data], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'todays_appointment.pdf');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error('Error downloading PDF:', error);
        }
    }

    const debouncedDownloadAppointments = _.debounce(
        downloadAppointments,
        3000
    );

    return (
        <div>
            <AddPatient
                isOpen={isAddPatientModalOpen}
                onClose={handleCloseModal}
                modalTitle="Add Patient"
                currentStep={currentStep}
                handleNext={handleNext}
                handleSave={handleSave}
                handleBookAppointment={() => {}}
                control={addPatientControl}
                register={addPatientRegister}
                setValue={addPatientSetValue}
                resetField={addPatientResetField}
                watch={addPatientWatch}
                getValues={addPatientGetValues}
                errors={addPatientErrors}
                handleSubmit={addPatientHandleSubmit}
                showAlert={showModalAlert}
                setShowAlert={setShowModalAlert}
            />

            {showAppointmentModal && (
                <CreateAppointmentStaff
                    patientProps={{
                        showPatientDropdown,
                        setShowPatientDropdown,
                        listStatus,
                        onMenuClick,
                        patientList,
                        selectedPatient,
                    }}
                    doctorProps={{
                        showDoctorDropdown,
                        setShowDoctorDropdown,
                        onDoctorMenuClick,
                    }}
                    workinghours={workinghours}
                    appointmentOptions={getAppointmentTypeOptions}
                    assignRoomOptions={getRoomOptions}
                    doctorOptions={getDoctorOptions}
                    isOpen={showAppointmentModal}
                    onClose={() => {
                        setPatientData({});
                        setShowAppointmentModal(false);
                        setViewMode(false);
                        setEditMode(false);
                        setPatientParams((prev: any) => {
                            return { ...prev, search: '' };
                        });
                    }}
                    getPatientOptions={getPatientOptions}
                    reasonOptions={getReasonOptions}
                    control={control}
                    errors={errors}
                    setValue={setValue}
                    watch={watch}
                    register={register}
                    handleSubmit={handleSubmit}
                    key={'Create Appointment'}
                    triageOptions={getTriggerOption}
                    modalTitle={
                        editMode ? 'Edit Appointment' : 'Create Appointment'
                    }
                    handleCancel={handleCancel}
                    onProviderDelete={handleProviderDelete}
                    handleAddProvider={handleAddProvider}
                    handleCreateAppointment={handleCreateAppointment}
                    onStepHandler={onStepHandler}
                    step={step}
                    providerOptions={getProviderOptions}
                    getValues={getValues}
                    isEditable={true}
                    patientData={patientData}
                    editMode={editMode}
                    handleUpdateAppointment={handleUpdateAppointment}
                    valuesChanged={valuesChanged}
                    scheduleStepHandler={scheduleStepHandler}
                    onMoreActionClick={onCreateAppointmentAction}
                    handleSearchAdd={handleSearchAdd}
                    handlePatientSearch={handlePatientSearch}
                />
            )}

            <div className="flex items-center justify-between gap-3 w-full">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
                <Button
                    icon={<Add size={18} />}
                    id="create-appointment"
                    type="button"
                    variant="primary"
                    label="Create Appointment"
                    size="small"
                    onClick={handleAddAppointment}
                    iconPosition="left"
                />
            </div>

            <div className="mt-4 mb-8 flex justify-between items-center">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Appointment
                </Heading>

                <Searchbar
                    id="patients-search-bar"
                    name="SearchBar"
                    placeholder="Search..."
                    onChange={(e) => {
                        props.setAppointmentParams({
                            ...props.appointmentParams,
                            search: e,
                        });
                    }}
                    onClear={() => {
                        props.setAppointmentParams({
                            ...props.appointmentParams,
                            search: '',
                        });
                    }}
                    // register={appSidebarData.register}
                />
            </div>

            <div className="grid grid-cols-[263px_1fr]">
                <AppDetailSidebar
                    {...appSidebarData}
                    currentDate={currentDate}
                    dateChangeHandler={dateChangeHandler}
                    loadOptionsDoctors={getDoctorOptions}
                    onChangeDoctors={onChangeDoctors}
                    onChangeStatus={onChangeStatus}
                    appointmentsViewMode={appointmentsViewMode}
                    defaultDoctorOptions={defaultDoctorOptions}
                />
                <div
                    className={`bg-white px-4 rounded-tr-2xl rounded-br-2xl sticky top-4 h-[calc(100vh_-_165px)] overflow-auto`}
                >
                    <div className="flex gap-3 justify-between items-center bg-white sticky top-0 py-4 z-[6]">
                        <div className="flex items-center gap-3">
                            <Text
                                variant="bodySmall"
                                fontWeight="font-medium"
                                className="text-primary-700"
                            >
                                Total appointment{' '}
                                {String(totalAppointments).padStart(2, '0')}
                            </Text>
                            <Button
                                id="today-button"
                                label="Today"
                                variant="neutral"
                                className="focus:bg-white"
                                size="extraSmall"
                                onClick={handleJumpToToday}
                            />
                            <div>
                                <Tooltip
                                    content={
                                        transformedAppointments.length <= 0
                                            ? 'No Appointments, Today'
                                            : 'Download Appointment List'
                                    }
                                    position="top"
                                >
                                    <Button
                                        id={'download-appointments'}
                                        variant="secondary"
                                        size="small"
                                        onlyIcon
                                        icon={
                                            <Download
                                                size={20}
                                                color="#333333"
                                            />
                                        }
                                        onClick={() =>
                                            debouncedDownloadAppointments(
                                                new Date(
                                                    appSidebarData.watch(
                                                        'appDate'
                                                    )
                                                )
                                            )
                                        }
                                        disabled={
                                            transformedAppointments.length <= 0
                                        }
                                    />
                                </Tooltip>
                            </div>
                        </div>

                        <DateNavigator
                            currentDate={
                                new Date(appSidebarData.watch('appDate'))
                            }
                            dateChangeHandler={dateChangeHandler}
                        />
                        <div className="flex items-center gap-3">
                            <Button
                                id="today-button"
                                label={
                                    isHideCompleted
                                        ? 'Show completed'
                                        : 'Hide completed'
                                }
                                className="focus:bg-white"
                                variant="neutral"
                                onClick={handleToggleCompleted}
                                size="extraSmall"
                                iconPosition="left"
                                icon={
                                    isHideCompleted ? (
                                        <Eye
                                            size="16"
                                            color="#5C5C5C"
                                            className="flex-shrink-0"
                                        />
                                    ) : (
                                        <EyeSlash
                                            size="16"
                                            color="#5C5C5C"
                                            className="flex-shrink-0"
                                        />
                                    )
                                }
                            />
                            <div className="-mt-5 max-h-16 h-full flex-col flex items-center justify-center">
                                <Tabs
                                    defaultActiveTab={appointmentsViewMode}
                                    tabs={viewModeList}
                                    variant="transparent"
                                    onTabClick={onViewModeChange}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="grid gap-3">
                        {appointmentsViewMode === 'list-view' ? (
                            <>
                                {listViewAppointments.length > 0 ? (
                                    <div className="flex flex-col gap-y-3 pb-3">
                                        {listViewAppointments?.map(
                                            (appointment, index) => (
                                                <BeginTreatment
                                                    customRule={customRule}
                                                    key={appointment.id}
                                                    cardRef={
                                                        index ===
                                                        listViewAppointments.length -
                                                            1
                                                            ? ref
                                                            : null
                                                    }
                                                    onStepHandler={
                                                        onStepHandler
                                                    }
                                                    id={appointment.id}
                                                    onClickAppointment={
                                                        onClickAppointment
                                                    }
                                                    {...appointment}
                                                />
                                            )
                                        )}
                                    </div>
                                ) : (
                                    <div>
                                        <EmptyState
                                            emptyTableButtonLabel="Create appointment"
                                            emptyTableMessage="No appointments"
                                            emptyTableButtonHandle={
                                                handleAddAppointment
                                            }
                                        />
                                    </div>
                                )}
                            </>
                        ) : (
                            <Calendar
                                handleCalendarAppointment={
                                    handleCalendarAppointment
                                }
                                fetchNextPage={fetchNextPage}
                                hasNextPage={hasNextPage}
                            />
                        )}
                        {/* {transformedAppointments &&
                        transformedAppointments.length > 0 &&
                        appointmentsViewMode === 'list-view' ?} */}
                    </div>
                </div>
            </div>
            <Modal
                isOpen={showOngoingAppointmentWarning}
                isHeaderBorder={false}
                modalTitle={'On going treatment'}
                icon={
                    <IconWarning
                        size={30}
                        className={`-mt-0.5 ml-1 text-warning-100`}
                    />
                }
                onClose={() => setShowOngoingAppointmentWarning(false)}
                dataAutomation={'ongoing-appointment'}
            >
                <div className=" bg-white flex flex-col gap-4">
                    <div className="w-full flex gap-2">
                        <div className="flex flex-col gap-2 pl-12">
                            <p className="text-primary-600 text-sm">
                                This is alrady an ongoing appointment.
                            </p>
                        </div>
                    </div>
                    <Button
                        id="go-to-appointment"
                        variant="primary"
                        size="small"
                        label="Go to appoinment"
                        onClick={() => {
                            routeToPatientDetails(
                                ongoingAppointmentPatientId as string
                            );
                        }}
                        className="w-fit ml-auto"
                    />
                </div>
            </Modal>

            <Modal
                isOpen={showDeleteModal}
                isHeaderBorder={false}
                icon={
                    <IconWarning
                        size={30}
                        className={`-mt-0.5 ml-1 text-warning-100`}
                    />
                }
                modalTitle={'Cancel Appointment'}
                onClose={() => setShowDeleteModal(false)}
                dataAutomation={'delete-appointment'}
            >
                <div className=" bg-white flex flex-col gap-4">
                    <div className="w-full flex gap-2">
                        <div className="flex flex-col gap-2">
                            <p className="text-primary-600 text-sm pl-12">
                                Are you sure you want to cancel appointment out?
                            </p>
                        </div>
                    </div>
                    <div className="flex w-fit gap-2 ml-auto">
                        <Button
                            id="go-to-appointment"
                            variant="borderless"
                            size="small"
                            label="Cancel"
                            onClick={() => {
                                setShowDeleteModal(false);
                            }}
                            className="w-fit"
                        />
                        <Button
                            id="go-to-appointment"
                            variant="primary"
                            size="small"
                            label="Confirm"
                            onClick={() => {
                                deleteAppointmentMutation.mutate(
                                    deleteAppointmentId
                                );
                                setShowDeleteModal(false);
                            }}
                            className="w-fit "
                        />
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default AppointmentDetails;
