import React, {
    Dispatch,
    SetStateAction,
    useCallback,
    useEffect,
    useState,
} from 'react';
import BeginTreatment, {
    BeginTreatmentProps,
} from '../organisms/patientDetail/BeginTreatment';
import { Breadcrumbs, Pagination, Searchbar } from '../molecules';
import AppDetailSidebar, {
    AppDetailSidebarT,
} from '../organisms/AppointmentDetails/AppDetailSidebar';
import { Button, Heading, Text } from '../atoms';
import { Add } from 'iconsax-react';
import DateNavigator from '../molecules/DateNavigator';
import Tabs from '../molecules/Tabs';
import { TabItemType } from '../atoms/HorizontalTabs';
import IconCalender from '../atoms/customIcons/IconCalender';
import IconMenu from '../atoms/customIcons/IconMenu';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import moment from 'moment';
import { useInView } from 'react-intersection-observer';
import CreateAppointment from '../organisms/appointment/CreateAppointment';
import {
    APPOINTMENT_TRIAGE_OPTIONS,
    APPOINTMENT_TYPE,
} from '../utils/constant';
import {
    CreateAppointmentType,
    EnumAppointmentStatus,
} from '../types/appointment';
import {
    useAppointmentMutation,
    useUpdateAppointmentFeilds,
    useUpdateAppointmentStatus,
} from '../services/appointment.queries';
import CreateAppointmentStaff, {
    AppointmentStaffStepT,
    AppStaffStepData,
} from '../organisms/appointment/CreateAppointmentStaff';
import { ResponseStatus } from '../molecules/PatientDropdown';
import { PatientT } from '../types/patient';
import { createAppointmentValidationSchema } from '../utils/validation-schema/createAppointmentValidation';
import { MenuListType } from '../molecules/DropdownMenu';
import AddPatient from '../organisms/patient/AddPatient';
import { useCreateOrUpdatePatient } from '../services/patient.queries';
import { searchPatientByPhone } from '../services/patient.service';
import Calendar from '../organisms/calendar/Calendar';
import { useCalendarSocket } from '../hooks/useCalendarSocket';
import { debounce } from 'lodash';
import { Alert } from '../atoms';
import { getAuth } from '../services/identity.service';
import {
    getReasonOptions,
    concatDateTime,
} from '@/app/utils/patient-details-utils/create-appointment-utils';
interface AlertT {
    isOpen: boolean;
    label: string;
    variant: 'success' | 'error' | 'warning' | 'info';
    isLight?: boolean;
}

const breadcrumbList = [
    { id: 1, name: 'Dashboard', path: '/dashboard' },
    { id: 2, name: 'Appointment', path: '/appointment' },
];

interface AppointmentDetailsT {
    appSidebarProps: AppDetailSidebarT;
    appointmentList: BeginTreatmentProps[];
    totalAppointments: number;
    listLoadStatus?: 'error' | 'success' | 'pending';
    fetchNextPage: Function;
    clinicRoomsData: object;
    doctorData: object;
    patientsData: object;
    providerData: object;
    pageCount?: number;
    pagination: number;
    currentDate: string;
    setPagination: Dispatch<SetStateAction<number>>;
    onViewModeChange: (mode: string) => void;
}

const CalendarView = (props: AppointmentDetailsT) => {
    // subscribe to real-time calendar updates
    useCalendarSocket();

    const {
        appSidebarProps,
        appointmentList,
        listLoadStatus,
        fetchNextPage,
        clinicRoomsData,
        doctorData,
        patientsData,
        providerData,
        currentDate,
        onViewModeChange,
        totalAppointments,
    } = props;

    const CLINIC_ID = getAuth()?.clinicId;

    const [selectedDoctors, setSelectedDoctors] = useState([]);
    const [selectedStatus, setSelectedStatus] = useState([]);

    const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const [showPatientDropdown, setShowPatientDropdown] = useState(false);
    const [listStatus, setListStatus] = useState<ResponseStatus>('pending');
    const [selectedPatient, setSelectedPatient] = useState<null | PatientT>(
        null
    );
    const [patientList, setPatientList] = useState([]);
    const [patientData, setPatientData] = useState({});
    const [viewMode, setViewMode] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [patientParams, setPatientParams] = useState({
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        search: '',
    });
    const [step, setStep] = useState<AppointmentStaffStepT>('');
    const [appointmentId, setAppointmentId] = useState('');
    const [selectedAppointmentData, setSelectedAppointmentData] = useState({});
    const [valuesChanged, setValuesChanged] = useState(false);
    const [confirmModal, setConfirmModal] = useState<{
        isOpen: boolean;
        type: 'cancelAppointment' | 'PatientAddedSuccessfully';
    }>({
        isOpen: false,
        type: 'cancelAppointment',
    });
    const [currentStep, setCurrentStep] = useState(1);
    const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');

    const [showModalAlert, setShowModalAlert] = useState<AlertT>({
        isOpen: false,
        label: '',
        variant: 'error',
        isLight: true,
    });

    const { ref, inView } = useInView();

    const dateValidationSchema = yup.object().shape({
        // appointmentDate: yup.date().required('Date is required'),
        // Add other fields validations here
    });

    const {
        register: dateRegister,
        getValues: dateGetValues,
        setValue: dateSetValue,
        control: dateControl,
        formState: { errors: dateErrors },
        watch: dateWatch,
    } = useForm({
        resolver: yupResolver(dateValidationSchema),
        mode: 'onChange',
        defaultValues: {
            appDate: new Date(),
        },
    });

    const selectedDate = dateGetValues('appDate');

    const appSidebarData = {
        className: '',
        control: dateControl,
        errors: dateErrors,
        register: dateRegister,
        setValue: dateSetValue,
        getValues: dateGetValues,
        watch: dateWatch,
        loadOptionsDoctors: [],
        selectedDoctors: selectedDoctors,
        selectedStatus: selectedStatus,
        setSelectedDoctors: setSelectedDoctors,
        setSelectedStatus: setSelectedStatus,
    };

    const dateChangeHandler = (date: Date) => {
        appSidebarData.setValue('appDate', date, { shouldValidate: true });
    };

    const viewModeList: TabItemType[] = [
        {
            id: 'list-view',
            label: (
                <IconMenu className="text-inherit cursor-pointer" size={24} />
            ),
        },
        {
            id: 'calender-view',
            label: (
                <IconCalender
                    className="text-inherit cursor-pointer"
                    size={24}
                />
            ),
        },
    ];

    const setDefaultValue = (
        data: any,
        setValue: Function,
        setPatientData: Function,
        step: any,
        viewMode: boolean
    ) => {
        setValue('notes', data.notes);
        setValue('date', moment(data.date).format('YYYY-MM-DD'));
        setValue(
            'startTime',
            !viewMode ? data.startTime : moment(data.startTime).format('H:mm a')
        );
        setValue(
            'endTime',
            !viewMode ? data.endTime : moment(data.endTime).format('H:mm a')
        );
        setValue('type', { label: data.type, value: data.type });
        setValue('reason', { label: data.reason, value: data.reason });
        setValue(
            'doctors',
            data.appointmentDoctors.map((list: any) => {
                return {
                    value: list.doctor.id,
                    label: list.doctor.firstName + ' ' + list.doctor.lastName,
                };
            })
        );

        setValue('room', { label: data.room?.name, value: data.room?.id });
        setValue('triage', { label: data.triage, value: data.triage });
        setValue('weight', data.weight);
        setValue('status', data.status);
        setValue('patientSearch', data.patient);
        // setValue('providers', data.provider.map((list) => {
        //     return {
        //         value: list.value,
        //         label: list.label
        //     }
        // }))
        setPatientData(data.patient);

        const dataStep = Object.values(AppStaffStepData).find(
            (item) =>
                item.status.toLowerCase() ===
                data.status.replace(/\s/g, '').toLowerCase()
        );
        const dataStepKey = Object.keys(AppStaffStepData).find(
            (key) => AppStaffStepData[key] === dataStep
        );
        setStep(dataStepKey as string);
        setSelectedAppointmentData(getValues());
    };

    const transformedAppointments = appointmentList.map((appointment) => {
        return {
            id: appointment.id,
            treatmentData: {
                type: appointment.type,
                reason: appointment.reason,
                doctor: appointment.appointmentDoctors.map(
                    (doctor) => doctor?.doctor?.firstName
                ),
                providers: '',
                weight: appointment.weight ? `${appointment.weight} kgs` : '--',
                room: appointment.room.name,
            },
            scheduleData: {
                date: moment(appointment.date).format('DD MMMM YYYY'),
                time: moment(appointment.startTime).format('hh:mm A'),
                status: appointment.status,
                statusLabel: appointment.status,
                profile: '/images/dog.svg',
                name: appointment.patient.patientName,
                owner: appointment.patient.patientOwners
                    .map(
                        (owner) =>
                            owner.ownerBrand.firstName +
                            ' ' +
                            owner.ownerBrand.lastName
                    )
                    .join(', '),
            },
            handleMenu: (data, id) => {
                handleMenu(data, id);
            },
            handleBeginTreatment: () => {
                /* Your handleBeginTreatment logic here */
            },
            handleQuestions: () => {
                /* Your handleQuestions logic here */
            },
            handleNotes: () => {
                /* Your handleNotes logic here */
            },
        };
    });

    const handleMenu = (data, id) => {
        const foundAppintment = appointmentList.find((item) => item.id === id);
        if (data.id === 'edit') {
            setEditMode(true);
            setDefaultValue(
                foundAppintment,
                setValue,
                setPatientData,
                step,
                false
            );
            setAppointmentId(id);
            setShowAppointmentModal(true);
        }
    };

    useEffect(() => {
        if (inView) {
            fetchNextPage();
        }
    }, [inView, fetchNextPage]);

    const handleAddAppointment = () => {
        setPatientData({});
        reset();
        setEditMode(false);
        setSelectedPatient(null);
        const now = moment();

        const nextQuarter = moment()
            .startOf('hour')
            .add(Math.ceil(now.minute() / 15) * 15, 'minutes');

        const appointmentStartTime = nextQuarter.format('D-MMM-YYYY HH:mm');

        const appointmentEndTime = nextQuarter
            .add(30, 'minutes')
            .format('D-MMM-YYYY HH:mm');

        setValue('date', moment().format('D-MMM-YYYY'), {
            shouldValidate: true,
        });
        setValue('startTime', appointmentStartTime, {
            shouldValidate: true,
        });
        setValue('endTime', appointmentEndTime, {
            shouldValidate: true,
        });
        setValue(
            'type',
            {
                value: 'General Visit',
                label: 'General Visit',
            },
            { shouldValidate: true }
        );
        setValue('status', 'Scheduled', { shouldValidate: true });
        setShowAppointmentModal(true);
    };

    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors, isDirty, dirtyFields },
        watch,
        trigger,
    } = useForm({
        resolver: yupResolver(createAppointmentValidationSchema),
        mode: 'onChange',
    });

    const updateAppointmentStatusMutation = useUpdateAppointmentStatus();
    const { updateAppointmentMutation } = useUpdateAppointmentFeilds();
    const createPatientMutation = useCreateOrUpdatePatient();

    useEffect(() => {
        const search = watch('patientSearch');

        if (search) {
            setShowPatientDropdown(true);
            setListStatus('pending');
            // getPatientOptions
            getPatientOptions(search, []).then((data: any) => {
                setPatientList(data.options);
                setListStatus('success');
            });
        } else {
            setShowPatientDropdown(false);
        }
    }, [watch('patientSearch')]);

    useEffect(() => {
        const weight: string = String(getValues('weight'));
        if (weight === '') setValue('weight', null);
    }, [watch('weight')]);

    const onStepHandler = () => {
        switch (step) {
            case 'checkIn':
                setStep('beginTreatment');
                updateAppointmentStatusMutation.mutate({
                    appointmentId,
                    status: EnumAppointmentStatus.Checkedin,
                });
                break;

            case 'beginTreatment':
                setStep('continueTreatment');
                updateAppointmentStatusMutation.mutate({
                    appointmentId,
                    status: EnumAppointmentStatus.ReceivingCare,
                });
                break;

            case 'continueTreatment':
                setStep('complete');
                updateAppointmentStatusMutation.mutate({
                    appointmentId,
                    status: EnumAppointmentStatus.Checkedout,
                });
                break;
            case 'complete':
                setShowAppointmentModal(false);
        }
    };

    const onMenuClick = (menu: PatientT) => {
        setSelectedPatient(menu);
    };

    const getAppointmentTypeOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: APPOINTMENT_TYPE.map((item) => {
                return {
                    value: item,
                    label: item,
                };
            }),
            hasMore: false,
        };
    };

    const getRoomOptions = async (search: string, loadedOptions: unknown[]) => {
        console.log({ clinicRoomsData });

        return {
            options: clinicRoomsData?.data.rooms.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            }),
            hasMore: false,
        };
    };

    const getDoctorOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        const options = doctorData?.data.users.map((item: any) => {
            return {
                value: item.id,
                label: `${item.firstName} ${item.lastName}`,
            };
        });
        return {
            options,
            hasMore: false,
        };
    };

    const setPatientSearchParams = (searchTerm: string) => {
        setPatientParams({
            clinicId: CLINIC_ID,
            limit: 10,
            page: 1,
            search: searchTerm,
        });
    };

    const getPatientOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        setPatientSearchParams(search);
        const options = patientsData?.data.patients.map((item: any) => {
            return item;
        });
        return {
            options,
            hasMore: false,
        };
    };

    const getTriggerOption = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: APPOINTMENT_TRIAGE_OPTIONS.map((item) => {
                return {
                    value: item,
                    label: item,
                };
            }),
            hasMore: false,
        };
    };

    useEffect(() => {
        const values = getValues();
        if (!_.isEqual(values, selectedAppointmentData)) {
            setValuesChanged(true);
        } else {
            setValuesChanged(false);
        }
    }, [watch()]);

    const handleUpdateAppointment = (data: any, typeOfCall: string): void => {
        let status = '';
        if (typeOfCall === 'buttonCall') status = data.status;
        else if (typeOfCall === 'markAsSchedule')
            status = EnumAppointmentStatus.Scheduled;
        else {
            switch (step) {
                case 'checkIn':
                    status = EnumAppointmentStatus.Checkedin;
                    break;

                case 'beginTreatment':
                    status = EnumAppointmentStatus.ReceivingCare;
                    break;

                case 'continueTreatment':
                    status = EnumAppointmentStatus.Checkedout;
                    break;
                case 'complete':
                    status = EnumAppointmentStatus.Completed;
            }
        }

        const appointmentBody: any = {
            clinicId: CLINIC_ID,
            doctorIds: data.doctors.map(({ value }: any) => value),
            patientId: data.patientSearch?.id,
            roomId: data.room?.value,
            date: data?.date,
            startTime: concatDateTime(data?.date, data.startTime),
            endTime: concatDateTime(data?.date, data.endTime),
            status: status,
            reason: data.reason?.label || '',
            type: data?.type.value,
            ...(data?.weight
                ? {
                      weight: parseInt(data?.weight),
                  }
                : {}),
            triage: data?.triage?.value,
            notes: data?.notes,
            isBlocked: false,
            weight: data.weight,
            appointmentId: appointmentId,
        };

        updateAppointmentMutation.mutate({
            appointmentId,
            body: appointmentBody,
        });
        setSelectedAppointmentData(getValues());
        if (typeOfCall === 'updateLabel') {
            setShowAppointmentModal(false);
        }
    };

    const handleCancel = () => {
        setShowAppointmentModal(false);
        reset();
        setSelectedPatient(null);
        setViewMode(false);
        setEditMode(false);
        setPatientData({});
    };

    const handleProviderDelete = () => {};
    const handleAddProvider = () => {};

    const getProviderOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        const options = providerData?.data.users.map((item: any) => {
            return {
                value: item.id,
                label: `${item.firstName} ${item.lastName}`,
            };
        });
        return {
            options,
            hasMore: false,
        };
    };

    const { createAppointmentMutation } = useAppointmentMutation(
        setShowAppointmentModal
    );
    const handleCreateAppointment = useCallback(
        debounce((data: any): void => {
            const appointmentBody: CreateAppointmentType = {
                clinicId: CLINIC_ID,
                doctorIds: data.doctors.map(({ value }: any) => value),
                patientId: data.patientSearch?.id,
                roomId:
                    data.room?.value ?? '79078ffb-9cad-4fdd-9c44-6842dd0a7774',
                date: data?.date,
                startTime: concatDateTime(data?.date, data.startTime),
                endTime: concatDateTime(data?.date, data.endTime),
                reason: data.reason?.label || '',
                type: data?.type.value,
                ...(data?.weight
                    ? {
                          weight: parseInt(data?.weight),
                      }
                    : {}),
                triage: data?.triage?.value,
                notes: data?.notes,
                isBlocked: false,
                weight: data.weight,
            };

            createAppointmentMutation.mutate(appointmentBody);
        }, 500),
        [createAppointmentMutation]
    );

    const scheduleStepHandler = () => {
        updateAppointmentStatusMutation.mutate({
            appointmentId,
            status: EnumAppointmentStatus.Scheduled,
        });
        setShowAppointmentModal(false);
    };

    const onCreateAppointmentAction = (item: MenuListType) => {
        if (item.id === 'cancel-appointment') {
            setShowAppointmentModal(false);
            setConfirmModal({
                isOpen: true,
                type: 'cancelAppointment',
            });
        }
    };

    const handleSearchAdd = () => {
        setCurrentStep(1);
        setIsAddPatientModalOpen(true);
        setShowAppointmentModal(false);
    };

    const handlePatientCreation = async () => {
        const isValid = await trigger([
            'ownerFirstName',
            'ownerLastName',
            'patientName',
            'phoneNumber',
        ]);
        if (!isValid) {
            console.log('Form validation failed');
            return false;
        }

        const formData = getValues();

        const patientRenderingData = {
            id: '',
            patientName: formData.patientName,
            patientOwners: [
                {
                    owner: {
                        phoneNumber: formData.phoneNumberNational,
                        firstName: formData.ownerFirstName,
                        lastName: formData.ownerLastName,
                    },
                },
            ],
        };

        try {
            const createdPatientData = await createPatientMutation.mutateAsync({
                type: 'create',
                data: {
                    patientName: formData.patientName,
                    clinicId: CLINIC_ID,
                    ownersData: [
                        {
                            firstName: formData.ownerFirstName,
                            lastName: formData.ownerLastName,
                            phoneNumber: formData.phoneNumberNational,
                            countryCode: formData.countryCode,
                            isPrimary: true,
                        },
                    ],
                },
            });
            patientRenderingData.id = createdPatientData.data.id;
            setPatientData(patientRenderingData);
            setValue('patientSearch', patientRenderingData);
            setValue(
                'ownerId',
                createdPatientData.data.patientOwners[0].ownerBrand.id
            );
            setValue('patientId', createdPatientData.data.id);
            return true;
        } catch (error) {
            console.error('Error creating patient:', error);
            return false;
        }
    };

    const handleNext = async () => {
        const searchData = await searchPatientByPhone(
            getValues('phoneNumberNational')
        );

        if (currentStep === 1) {
            const isValid = await trigger('phoneNumber');
            if (!isValid) {
                console.log('Phone number validation failed');
                return;
            }

            if (searchData && searchData.data) {
                if (
                    searchData.data.message === 'Owner not found' ||
                    searchData.data.patients.length === 0
                ) {
                    setCurrentStep(3);
                } else if (searchData.data.owner && searchData.data.patients) {
                    const { owner, patients } = searchData.data;
                    setValue('owner', owner);
                    setValue('patients', patients);
                    setCurrentStep(2);
                } else {
                    console.log('Unexpected data structure', searchData.data);
                    // Handle unexpected data structure
                }
            } else {
                console.log('No search data available');
                // Handle case when no search data is available
            }
        } else if (currentStep === 2) {
            setCurrentStep(3);
        } else if (currentStep === 3) {
            const patientCreated = await handlePatientCreation();
            if (patientCreated) {
                setCurrentStep(4);
            }
        } else if (currentStep === 4) {
            handleCloseModal(true);
        }
    };

    const handleSave = async () => {
        const formValues = getValues();
        const patientId = getValues('patientId');
        const ownerId = getValues('ownerId');
        const transformedData = transformPatientData(formValues, ownerId);
        try {
            const response = await createPatientMutation.mutateAsync({
                type: 'update',
                data: { ...transformedData, id: patientId },
            });
            reset();
            handleCloseModal(true);
        } catch (error: any) {
            setShowModalAlert({
                isOpen: true,
                label:
                    error.message || 'An error occurred while updating patient',
                variant: 'error',
                isLight: true,
            });
            console.error('Error creating patient:', error);
        }
    };

    const transformPatientData = (formValues: any, ownerId: string) => {
        const primaryOwner = formValues.ownerFirstName
            ? {
                  firstName: formValues.ownerFirstName,
                  lastName: formValues.ownerLastName,
                  phoneNumber: formValues.phoneNumberNational,
                  countryCode: formValues.countryCode,
                  id: ownerId,
                  email: formValues.email || undefined,
                  address: formValues.address || undefined,
                  isPrimary: true,
              }
            : null;

        const secondaryOwner = formValues.secondaryOwnerFirstName
            ? {
                  firstName: formValues.secondaryOwnerFirstName,
                  lastName: formValues.secondaryOwnerLastName,
                  phoneNumber: formValues.secondaryPhoneNumberNational,
                  countryCode: formValues.secondaryCountryCode,
                  email: formValues.secondaryEmail || undefined,
                  address: formValues.secondaryAddress || undefined,
                  isPrimary: false,
              }
            : null;

        const ownersData = [];
        if (primaryOwner) {
            ownersData.push(primaryOwner);
        }
        if (secondaryOwner) {
            ownersData.push(secondaryOwner);
        }

        return {
            patientName: formValues.patientName,
            ownersData,
            species: formValues.species?.value || undefined,
            breed: formValues.breed?.value || undefined,
            age: formValues.age || undefined,
            gender: formValues.gender?.value || undefined,
            reproductiveStatus:
                formValues.reproductiveStatus?.value || undefined,
            microchipId: formValues.microchipId || undefined,
            identification: formValues.identification || undefined,
            allergies: formValues.allergies || undefined,
            markDeceased: formValues.markDeceased?.markDeceased || false,
        };
    };

    const handleCloseModal = (openAppointmentModal: boolean = false) => {
        if (openAppointmentModal) {
            handleAddAppointment();
        }
        // Reset all form data
        reset(); // Reset the main form
        // Reset all relevant states
        setShowModalAlert({
            isOpen: false,
            label: '',
            variant: 'error',
            isLight: true,
        });
        setIsAddPatientModalOpen(false);
        setPatientData({});
        setSelectedPatient(null);
    };

    return (
        <div>
            <AddPatient
                isOpen={isAddPatientModalOpen}
                onClose={handleCloseModal}
                modalTitle="Add Patient"
                currentStep={currentStep}
                handleNext={handleNext}
                handleSave={handleSave}
                handleBookAppointment={() => {}}
                control={control}
                register={register}
                setValue={setValue}
                resetField={resetField}
                watch={watch}
                getValues={getValues}
                errors={errors}
                showAlert={showModalAlert}
                setShowAlert={setShowModalAlert}
            />

            {showAlert && (
                <Alert
                    className="fixed top-4 left-1/2 -translate-x-1/2 z-50"
                    variant="error"
                    label={alertMessage}
                    isLight={true}
                    onClose={() => setShowAlert(false)}
                />
            )}

            {showAppointmentModal && (
                <CreateAppointmentStaff
                    patientProps={{
                        showPatientDropdown,
                        setShowPatientDropdown,
                        listStatus,
                        onMenuClick,
                        patientList,
                        selectedPatient,
                    }}
                    appointmentOptions={getAppointmentTypeOptions}
                    assignRoomOptions={getRoomOptions}
                    doctorOptions={getDoctorOptions}
                    isOpen={showAppointmentModal}
                    onClose={() => {
                        setPatientData({});
                        setShowAppointmentModal(false);
                        setViewMode(false);
                        setEditMode(false);
                    }}
                    getPatientOptions={getPatientOptions}
                    reasonOptions={getReasonOptions}
                    control={control}
                    errors={errors}
                    setValue={setValue}
                    watch={watch}
                    register={register}
                    handleSubmit={handleSubmit}
                    key={'Create Appointment'}
                    triageOptions={getTriggerOption}
                    modalTitle={
                        editMode ? 'Edit Appointment' : 'Create Appointment'
                    }
                    handleCancel={handleCancel}
                    onProviderDelete={handleProviderDelete}
                    handleAddProvider={handleAddProvider}
                    handleCreateAppointment={handleCreateAppointment}
                    onStepHandler={onStepHandler}
                    step={step}
                    providerOptions={getProviderOptions}
                    getValues={getValues}
                    isEditable={true}
                    patientData={patientData}
                    editMode={editMode}
                    handleUpdateAppointment={handleUpdateAppointment}
                    valuesChanged={valuesChanged}
                    scheduleStepHandler={scheduleStepHandler}
                    onMoreActionClick={onCreateAppointmentAction}
                    handleSearchAdd={handleSearchAdd}
                />
            )}

            <div className="flex items-center justify-between gap-3 w-full">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
                <Button
                    icon={<Add size={18} />}
                    id="create-appointment"
                    type="button"
                    variant="primary"
                    label="Create Appointment"
                    size="small"
                    onClick={handleAddAppointment}
                    iconPosition="left"
                />
            </div>

            <div className="mt-4 mb-8 flex justify-between items-center">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Appointment
                </Heading>

                <Searchbar
                    id="patients-search-bar"
                    name="SearchBar"
                    placeholder="Search..."
                    register={appSidebarData.register}
                />
            </div>

            <div className="grid grid-cols-[263px_1fr] gap-4">
                <AppDetailSidebar
                    {...appSidebarData}
                    currentDate={currentDate}
                />
                <div>
                    <div className="flex gap-3 justify-between items-center mb-6">
                        <Text
                            variant="bodySmall"
                            fontWeight="font-medium"
                            textColor="primary-700"
                        >
                            Total appointment{' '}
                            {String(totalAppointments).padStart(2, '0')}
                        </Text>

                        <DateNavigator
                            currentDate={
                                new Date(appSidebarData.watch('appDate'))
                            }
                            dateChangeHandler={dateChangeHandler}
                        />

                        <Tabs
                            defaultActiveTab={'list-view'}
                            tabs={viewModeList}
                            variant="transparent"
                            onTabClick={onViewModeChange}
                        />
                    </div>

                    <Calendar
                        currentDate={new Date(appSidebarData.watch('appDate'))}
                    />

                    {/* <div className='grid gap-3'>
            {transformedAppointments?.map((appointment, index) => (
              <BeginTreatment
                key={appointment.id}
                cardRef={index === transformedAppointments.length - 1 ? ref : null}
                {...appointment}
              />
            ))}
          </div> */}
                </div>
            </div>
        </div>
    );
};

export default CalendarView;
