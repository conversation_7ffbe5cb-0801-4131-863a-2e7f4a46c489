import React, { useState } from 'react';
import ClinicInfo from '../organisms/admin/clinic-details/ClinicInfo';
import BasicDetails from '../organisms/admin/clinic-details/BasicDetails';
import AddressDetails from '../organisms/admin/clinic-details/AddressDetails';
import WorkingHours from '../organisms/admin/clinic-details/WorkingHours/WorkingHours';
import EditAddressDetailsModal from '../organisms/admin/clinic-details/EditAddressDetailsModal';
import EditBasicDetailsModal from '../organisms/admin/clinic-details/EditBasicDetailsModal';
import { Breadcrumbs, Searchbar } from '../molecules';
import { Button, Heading } from '../atoms';
import Image from 'next/image';
import NewWorkingHours from '../organisms/admin/clinic-details/WorkingHours/WorkingHours';
import Tabs from '../molecules/Tabs';
import { useRouter } from 'next/navigation';
import RoomDetails from './admin/RoomDetails';
import { RoomT } from '../types/room';
import { PaginationType } from '../molecules/Table';
import CustomLoader from '../atoms/CustomLoader';
import { getAuth } from '../services/identity.service';
import ClientBookingSettings from '../organisms/admin/clinic-details/ClientBookingSettings';
import { ClientBookingSettingsDto, DoctorDto, UpdateClientBookingSettingsDto } from '../services/clinic.queries';

interface TimeSlot {
    startTime: string;
    endTime: string;
    isWorkingDay: boolean;
}

interface ClinicDetailsProps {
    breadcrumbList: {
        id: number;
        name: string;
        path: string;
    }[];
    onChangeUser: (value: string) => void;
    handleFilter: () => void;
    clinicInfo: {
        logo: string;
        clinicName: string;
        location: string;
        id: string;
    };
    basicDetails: {
        email: string;
        website: string;
        drugLicenseNumber: string;
        phoneNumbers: Array<{ country_code: string; number: string }>;
        clinicLogo?: File;
    };
    addressDetails: {
        addressLine1: string;
        addressLine2?: string;
        city: string;
        addressPincode: string;
        state: string;
        country: string;
    };

    roomInfo: {
        tableData: RoomT[];
        pagination: PaginationType;
        setPagination: React.Dispatch<React.SetStateAction<PaginationType>>;
        totalPages: number;
        listLoadStatus: 'error' | 'success' | 'pending';
        onSubmit: (roomData: RoomT) => void;
        onDelete: (roomId: string) => void;
    };
    workingHours: {
        daysOfWeek: string[];
        workingHours: {
            [key: string]: { startTime: string; endTime: string }[];
        };
    };
    cityOptions: (
        search: string
    ) => Promise<{ label: string; value: string }[]>;
    stateOptions: (
        search: string
    ) => Promise<{ label: string; value: string }[]>;
    countryOptions: (
        search: string
    ) => Promise<{ label: string; value: string }[]>;
    tabHeaderBottomContent?: React.ReactNode;
    updateClinicMutation: (data: any) => Promise<void>;
    clientBookingSettings: ClientBookingSettingsDto | null;
    doctors: DoctorDto[];
    isClientBookingSettingsLoading: boolean;
    handleUpdateClientBookingSettings: (
        data: UpdateClientBookingSettingsDto
    ) => Promise<void>;
    clinicId: string;
}

const ClinicDetails: React.FC<ClinicDetailsProps> = ({
    breadcrumbList,
    onChangeUser,
    handleFilter,
    clinicInfo,
    basicDetails,
    addressDetails,
    workingHours,
    cityOptions,
    stateOptions,
    tabHeaderBottomContent,
    updateClinicMutation,
    roomInfo,
    clientBookingSettings,
    doctors,
    isClientBookingSettingsLoading,
    handleUpdateClientBookingSettings,
    clinicId,
}) => {
    const [isAddressModalOpen, setAddressModalOpen] = useState(false);
    const [isBasicDetailsModalOpen, setBasicDetailsModalOpen] = useState(false);
    const [activeAttachmentTab, setActiveAttachmentTab] =
        useState('clinic-details');
    const router = useRouter();
    const auth = getAuth();
    const userRole = auth?.role;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';
    const CLINIC_ID = auth?.clinicId;

    const handleAddressEdit = () => {
        setAddressModalOpen(true);
    };

    const handleBasicDetailsEdit = () => {
        setBasicDetailsModalOpen(true);
    };

    const handleAddressModalClose = () => {
        setAddressModalOpen(false);
    };

    const handleBasicDetailsModalClose = () => {
        setBasicDetailsModalOpen(false);
    };

    const handleAddressSubmit = async (data: any) => {
        try {
            await updateClinicMutation({ addressDetails: data });
            handleAddressModalClose();
        } catch (error) {
            console.error('Error updating address:', error);
        }
    };

    const handleBasicDetailsSubmit = async (data: any) => {
        try {
            await updateClinicMutation({ basicDetails: data });
            handleBasicDetailsModalClose();
        } catch (error) {
            console.error('Error updating basic details:', error);
        }
    };

    const handleWorkingHoursUpdate = async (data: any) => {
        try {
            await updateClinicMutation({ workingHours: data });
        } catch (error) {
            console.error('Error updating working hours:', error);
        }
    };

    const handleTabCLick = (id: string) => {
        console.log(id);
        switch (id) {
            case 'clinicDetails':
                router.push('/admin/clinic-details');
                break;

            case 'users':
                router.push('/admin/users');
                break;

            case 'inventory':
                router.push('/admin/inventory');
                break;

            case 'rooms':
                router.push('/admin/rooms');
                break;

            case 'integrations':
                router.push('/admin/integrations');
                break;
            case 'document-library':
                router.push('/admin/document-library');
                break;
            case 'analytics':
                router.push('/admin/analytics');
                break;
            case 'reminders':
                router.push('/admin/reminders');
                break;
            default:
                router.push('/admin/clinic-details');
        }
    };

    return (
        <>
            <div className="flex items-center justify-between gap-3 w-full py-3">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            </div>
            <div className="mt-3 mb-8 flex justify-between items-center">
                <Heading type="h4" fontWeight="font-medium">
                    Admin
                </Heading>
                {/* <div className="flex gap-2.5">
                    <Searchbar
                        id="room-search-bar"
                        name="SearchBar"
                        placeholder="Search..."
                        onChange={onChangeUser}
                    />
                </div> */}
            </div>

            <Tabs
                className="mt-5"
                defaultActiveTab={activeAttachmentTab}
                onTabClick={(tab) => {
                    handleTabCLick(tab.id);
                }}
                marginTabsTop="0"
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <>
                                <div className="bg-white rounded-2xl p-7 h-[calc(100dvh-12.75rem)] overflow-auto">
                                    <ClinicInfo
                                        logo={clinicInfo.logo}
                                        clinicName={clinicInfo.clinicName}
                                        location={clinicInfo.location}
                                    />
                                    <div className="w-full flex gap-x-14 mt-6 ">
                                        <div className="border-r w-1/2 border-primary-100 pr-14 flex flex-col">
                                            <BasicDetails
                                                email={basicDetails.email}
                                                website={basicDetails.website}
                                                drugLicenseNumber={
                                                    basicDetails.drugLicenseNumber
                                                }
                                                phoneNumbers={
                                                    basicDetails.phoneNumbers
                                                }
                                                handleEdit={
                                                    handleBasicDetailsEdit
                                                }
                                            />
                                            <div className="">
                                                <AddressDetails
                                                    addressLine1={
                                                        addressDetails.addressLine1
                                                    }
                                                    addressLine2={
                                                        addressDetails.addressLine2
                                                    }
                                                    city={addressDetails.city}
                                                    addressPincode={
                                                        addressDetails.addressPincode
                                                    }
                                                    state={addressDetails.state}
                                                    country={
                                                        addressDetails.country
                                                    }
                                                    handleEdit={
                                                        handleAddressEdit
                                                    }
                                                />
                                                <ClientBookingSettings
                                            initialSettings={clientBookingSettings}
                                            doctors={doctors}
                                            isLoading={isClientBookingSettingsLoading}
                                            updateSettingsMutation={handleUpdateClientBookingSettings}
                                            clinicId={clinicId}
                                        />
                                            </div>
                                        </div>
                                        <div className="w-1/2">
                                            <WorkingHours
                                                daysOfWeek={
                                                    workingHours.daysOfWeek
                                                }
                                                workingHours={
                                                    workingHours.workingHours
                                                }
                                                onUpdate={
                                                    handleWorkingHoursUpdate
                                                }
                                            />
                                            <div className="mt-5">
                                                <RoomDetails
                                                    tableData={
                                                        roomInfo.tableData
                                                    }
                                                    pagination={
                                                        roomInfo.pagination
                                                    }
                                                    setPagination={
                                                        roomInfo.setPagination
                                                    }
                                                    totalPages={
                                                        roomInfo.totalPages
                                                    }
                                                    listLoadStatus={
                                                        roomInfo.listLoadStatus
                                                    }
                                                    onSubmit={roomInfo.onSubmit}
                                                    onDelete={roomInfo.onDelete}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <EditAddressDetailsModal
                                    isOpen={isAddressModalOpen}
                                    onClose={handleAddressModalClose}
                                    onSubmit={handleAddressSubmit}
                                    address={addressDetails}
                                    countryOptions={() =>
                                        Promise.resolve([
                                            { label: 'India', value: 'india' },
                                        ])
                                    }
                                    stateOptions={stateOptions}
                                    cityOptions={cityOptions}
                                />
                                {isBasicDetailsModalOpen && (
                                    <EditBasicDetailsModal
                                        isOpen={isBasicDetailsModalOpen}
                                        onClose={handleBasicDetailsModalClose}
                                        onSubmit={handleBasicDetailsSubmit}
                                        details={basicDetails}
                                        countryOptions={() =>
                                            Promise.resolve([
                                                {
                                                    label: 'India',
                                                    value: 'india',
                                                },
                                            ])
                                        }
                                        clinicId={CLINIC_ID}
                                    />
                                )}
                            </>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    ...(isAdmin ? [{
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    }] : []),
                ]}
            />
        </>
    );
};

export default ClinicDetails;
