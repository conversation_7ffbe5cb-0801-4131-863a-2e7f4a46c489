import React, {
    ChangeEvent,
    Dispatch,
    SetStateAction,
    useCallback,
    useEffect,
    useState,
} from 'react';
import { Button } from '../atoms';
import { Add } from 'iconsax-react';

import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import moment from 'moment';
import { useInView } from 'react-intersection-observer';
import {
    APPOINTMENT_TRIAGE_OPTIONS,
    APPOINTMENT_TYPE,
} from '../utils/constant';
import {
    AppointmentParams,
    CreateAppointmentType,
    EnumAppointmentStatus,
} from '../types/appointment';
import {
    useAppointmentMutation,
    useClinicAppointments,
    useUpdateAppointmentFeilds,
    useUpdateAppointmentStatus,
} from '../services/appointment.queries';
import CreateAppointmentStaff, {
    AppointmentStaffStepT,
    AppStaffStepData,
} from '../organisms/appointment/CreateAppointmentStaff';
import { ResponseStatus } from '../molecules/PatientDropdown';
import { PatientT } from '../types/patient';
import { createAppointmentValidationSchema } from '../utils/validation-schema/createAppointmentValidation';
import { MenuListType } from '../molecules/DropdownMenu';
import _, { debounce } from 'lodash';
import { validationSchema } from '../utils/validation-schema/patientValidation';
import { useClinicRooms } from '../services/clinic.queries';
import {
    useClinicDoctors,
    useClinicProviders,
} from '../services/providers.queries';
import {
    useClinicDoctorsAvailability,
} from '../services/providers.queries';
import { GetDoctorsType } from '../types/provider';
import { usePatients } from '../services/patient.queries';
import { getAuth } from '../services/identity.service';
import { getReasonOptions, concatDateTime } from '@/app/utils/patient-details-utils/create-appointment-utils';
import { processInfiniteQueryAppointments } from '../utils/appointment-utils';

interface DoctorAvailabilityParams {
    clinicId: string;
    date?: string;
    startTime?: string;
    endTime?: string;
    role?: string;
    search?: string;
    orderBy?: string;
    workinghours? : any;
}

const CreateAppointment = (props: any) => {
    const auth = getAuth();
    const CLINIC_ID = auth?.clinicId;

    const {
        showAppointmentModal,
        setShowAppointmentModal,
        patientData,
        setPatientData,
        workinghours
    } = props;

    const providerQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 50,
        page: 1,
        orderBy: 'ASC',
    };

    const doctorQueryParams: GetDoctorsType = {
        clinicId: CLINIC_ID,
        limit: 50,
        page: 1,
        orderBy: 'DESC',
        // role: 'doctor',
    };

    const [patientParams, setPatientParams] = useState({
        clinicId: CLINIC_ID,
        limit: 10,
        page: 1,
        search: '',
    });
    const [appointmentParams, setAppointmentParams] =
        useState<AppointmentParams>({
            page: 1,
            limit: 10,
            orderBy: 'ASC',
            date: moment().format('YYYY-MM-DD').toString(),
            search: '',
        });

    const { data: clinicRoomsData, status: clinicRoomsStatus } = useClinicRooms(
        {
            clinicId: CLINIC_ID,
        }
    );

    const { data: doctorData, status: doctorStauts } =
        useClinicDoctors(doctorQueryParams);
    const { data: patientsData, status: patientsStatus } =
        usePatients(patientParams);

    const { data: providerData, status: providerStatus } =
        useClinicProviders(providerQueryParams);

    const { data, status } = useClinicAppointments(appointmentParams);

    const appointmentList = processInfiniteQueryAppointments(data);

    //const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const [showPatientDropdown, setShowPatientDropdown] = useState(false);
    const [listStatus, setListStatus] = useState<ResponseStatus>('pending');
    const [selectedPatient, setSelectedPatient] = useState<null | PatientT>(
        null
    );
    const [patientList, setPatientList] = useState(
        patientsData?.data?.patients
    );
    const [editMode, setEditMode] = useState(false);

    const [step, setStep] = useState<AppointmentStaffStepT>('');
    const [appointmentId, setAppointmentId] = useState('');
    const [selectedAppointmentData, setSelectedAppointmentData] = useState({});
    const [valuesChanged, setValuesChanged] = useState(false);

    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors, isDirty, dirtyFields },
        watch,
        trigger,
    } = useForm({
        resolver: yupResolver(createAppointmentValidationSchema),
        mode: 'onChange',
    });

    const {
        register: addPatientRegister,
        handleSubmit: addPatientHandleSubmit,
        getValues: addPatientGetValues,
        setValue: addPatientSetValue,
        setError: addPatientSetError,
        control: addPatientControl,
        reset: addPatientReset,
        resetField: addPatientResetField,
        formState: { errors: addPatientErrors },
        watch: addPatientWatch,
        trigger: addPatientTrigger,
        clearErrors,
    } = useForm({
        resolver: yupResolver(validationSchema),
        mode: 'onChange', // This will trigger validation on change,
        defaultValues: {},
    });

    const setDefaultValue = (
        data: any,
        setValue: Function,
        setPatientData: Function,
        step: any,
        viewMode: boolean
    ) => {
        setValue('notes', data.notes);
        setValue('date', moment(data.date).format('YYYY-MM-DD'));
        setValue(
            'startTime',
            !viewMode ? data.startTime : moment(data.startTime).format('H:mm a')
        );
        setValue(
            'endTime',
            !viewMode ? data.endTime : moment(data.endTime).format('H:mm a')
        );
        setValue('type', { label: data.type, value: data.type });
        setValue('reason', { label: data.reason, value: data.reason });
        const selectedDoctor = {
            value: data.appointmentDoctors.find((doctor: any) => doctor.primary)
                .doctor.id,
            label:
                data.appointmentDoctors.find((doctor: any) => doctor.primary)
                    .doctor.firstName +
                ' ' +
                data.appointmentDoctors.find((doctor: any) => doctor.primary)
                    .doctor.lastName,
        };
        setValue('doctors', selectedDoctor);

        const providers = data.appointmentDoctors
            .filter((doc: any) => doc.primary === false)
            .map((doc: any) => {
                return {
                    label: doc?.doctor?.firstName + ' ' + doc?.doctor?.lastName,
                    value: doc?.doctor?.id,
                };
            });
        setValue('provider', providers);
        setValue('room', { label: data.room?.name, value: data.room?.id });
        setValue('triage', { label: data.triage, value: data.triage });
        setValue('weight', data.weight);
        setValue('status', data.status);
        setValue('patientSearch', data.patient);
        // setValue('providers', data.provider.map((list) => {
        //     return {
        //         value: list.value,
        //         label: list.label
        //     }
        // }))
        setPatientData(data.patient);

        const dataStep = Object.values(AppStaffStepData).find((item) => {
            console.log('item', item.status.replace(/\s/g, '').toLowerCase());
            return (
                item.status.replace(/\s/g, '').toLowerCase() ===
                data.status.replace(/\s/g, '').toLowerCase()
            );
        });
        const dataStepKey = Object.keys(AppStaffStepData).find(
            (key) => AppStaffStepData[key] === dataStep
        );
        setStep(dataStepKey as AppointmentStaffStepT);
        setSelectedAppointmentData(getValues());
    };

    const handleAddAppointment = (DefaultPatientData: boolean = true) => {
        reset();
        // if (DefaultPatientData) {
        //     setPatientData({});
        // }
        if (DefaultPatientData) {
            setValue('patientSearch', patientData);
        }
        setSelectedPatient(null);
        setEditMode(false);
        // setPatientData({});
        // reset();
        //setEditMode(false);
        // setSelectedPatient(null);
        const now = moment();

        const nextQuarter = moment()
            .startOf('hour')
            .add(Math.ceil(now.minute() / 15) * 15, 'minutes');

        const appointmentStartTime = nextQuarter.format('D-MMM-YYYY HH:mm');

        const appointmentEndTime = nextQuarter
            .add(30, 'minutes')
            .format('D-MMM-YYYY HH:mm');

        setValue('date', moment().format('D-MMM-YYYY'), {
            shouldValidate: true,
        });
        setValue('startTime', appointmentStartTime, {
            shouldValidate: true,
        });
        setValue('endTime', appointmentEndTime, {
            shouldValidate: true,
        });
        setValue(
            'type',
            {
                value: 'Consultation',
                label: 'Consultation',
            },
            { shouldValidate: true }
        );
        setValue('status', 'Scheduled', { shouldValidate: true });
        setShowAppointmentModal(true);
    };

    const updateAppointmentStatusMutation = useUpdateAppointmentStatus(
        props.appointmentParams
    );
    const { updateAppointmentMutation } = useUpdateAppointmentFeilds(
        props.appointmentParams
    );

    useEffect(() => {
        const weight: string = String(addPatientGetValues('weight'));
        if (weight === '') addPatientSetValue('weight', null);
    }, [addPatientWatch('weight')]);

    const onStepHandler = (appointmentId: string) => {
        const foundAppintment: any = appointmentList.find(
            (item) => item.id === appointmentId
        );
        const dataStep = Object.values(AppStaffStepData).find((item) => {
            console.log('item', item.status.replace(/\s/g, '').toLowerCase());
            return (
                item.status.replace(/\s/g, '').toLowerCase() ===
                foundAppintment?.status.replace(/\s/g, '').toLowerCase()
            );
        });
        const dataStepKey = Object.keys(AppStaffStepData).find(
            (key) => AppStaffStepData[key] === dataStep
        );

        setStep(dataStepKey as AppointmentStaffStepT);

        //  setSelectedAppointmentData(getValues());
        switch (dataStepKey) {
            case 'checkIn':
                setStep('beginTreatment');
                updateAppointmentStatusMutation.mutate({
                    appointmentId,
                    status: EnumAppointmentStatus.Checkedin,
                });
                break;

            case 'beginTreatment':
                setStep('continueTreatment');
                updateAppointmentStatusMutation.mutate({
                    appointmentId,
                    status: EnumAppointmentStatus.ReceivingCare,
                });
                break;

            case 'continueTreatment':
                setStep('complete');
                updateAppointmentStatusMutation.mutate({
                    appointmentId,
                    status: EnumAppointmentStatus.Checkedout,
                });
                break;
            case 'complete':
                setShowAppointmentModal(false);
        }
    };

    const onMenuClick = (menu: PatientT) => {
        setSelectedPatient(menu);
    };

    const getAppointmentTypeOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: APPOINTMENT_TYPE.map((item) => {
                return {
                    value: item,
                    label: item,
                };
            }),
            hasMore: false,
        };
    };

    useEffect(() => {
        handleAddAppointment();
    }, []);

    const getRoomOptions = async (search: string, loadedOptions: unknown[]) => {
        console.log({ clinicRoomsData });

        return {
            options: clinicRoomsData?.data.rooms.map((item: any) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            }),
            hasMore: false,
        };
    };

     
    const [availabilityParams, setAvailabilityParams] =
            useState<DoctorAvailabilityParams>({
                clinicId: CLINIC_ID,
            });

        const { data: availabilityData } =
            useClinicDoctorsAvailability(availabilityParams);

        useEffect(() => {

            const date = watch('date');
            const startTime = watch('startTime');
            const endTime = watch('endTime');

            if (date && startTime && endTime) {
                const selectedDate = moment(date).format('YYYY-MM-DD');
                const combinedStartTime = moment(date)
                    .hours(moment(startTime).hours())
                    .minutes(moment(startTime).minutes())
                    .toISOString();
                const combinedEndTime = moment(date)
                    .hours(moment(endTime).hours())
                    .minutes(moment(endTime).minutes())
                    .toISOString();

                const newParams = {
                    clinicId: CLINIC_ID,
                    date: selectedDate,
                    startTime: combinedStartTime,
                    endTime: combinedEndTime,
                    // role: 'doctor',
                };
                setAvailabilityParams(newParams);
            }
        }, [watch('date'), watch('startTime'), watch('endTime')]);

        // Add effect to log availability data updates
        useEffect(() => {
            console.log('Availability Data Updated:', availabilityData);
        }, [availabilityData]);
    const getDoctorOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        const options = doctorData?.data.users?
        .filter(
            (user: any) =>
                user.role.name === 'admin' || user.role.name === 'doctor'
        )?.map((item: any) => {
            return {
                value: item.id,
                label: `${item.firstName} ${item.lastName}`,
                availability: availabilityData?.data?.users?.find(
                            (u) => u.doctorId === item.id
                        )?.availability || {
                            isAvailable: false,
                            nextAvailableSlot: undefined,
                        },
            };
        });
        return {
            options,
            hasMore: false,
        };
    };

    const getPatientOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        console.log('search', search);

        //        debouncedSetPatientParams(setPatientParams, search);

        setPatientParams({
            clinicId: CLINIC_ID,
            limit: 10,
            page: 1,
            search: search,
        });
        const options = patientsData?.data?.patients?.map((item: any) => {
            return item;
        });
        return {
            options,
            hasMore: false,
        };
    };

    const getTriggerOption = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        return {
            options: APPOINTMENT_TRIAGE_OPTIONS.map((item) => {
                return {
                    value: item.name,
                    label: item.name,
                };
            }),
            hasMore: false,
        };
    };
    useEffect(() => {
        const values = getValues();
        if (!_.isEqual(values, selectedAppointmentData)) {
            setValuesChanged(true);
        } else {
            setValuesChanged(false);
        }
    }, [watch()]);
    const handleUpdateAppointment = (data: any, typeOfCall: string): void => {
        let status = '';
        if (typeOfCall === 'buttonCall') status = data.status;
        else if (typeOfCall === 'markAsSchedule')
            status = EnumAppointmentStatus.Scheduled;
        else {
            switch (step) {
                case 'checkIn':
                    status = EnumAppointmentStatus.Checkedin;
                    break;

                case 'beginTreatment':
                    status = EnumAppointmentStatus.ReceivingCare;
                    break;

                case 'continueTreatment':
                    status = EnumAppointmentStatus.Checkedout;
                    break;
                case 'complete':
                    status = EnumAppointmentStatus.Completed;
            }
        }

        const appointmentBody: any = {
            clinicId: CLINIC_ID,
            doctorIds: [data.doctors.value], //data.doctors.map(({ value }: any) => value),
            patientId: data.patientSearch?.id,
            roomId: data.room?.value,
            date: data?.date,
            startTime: concatDateTime(data?.date, data.startTime),
            endTime: concatDateTime(data?.date, data.endTime),
            status: status,
            providerIds:
                data.provider && data.provider.length
                    ? data.provider.map((item) => item.value)
                    : [],
            reason: data.reason?.label || '',
            type: data?.type.value,
            ...(data?.weight
                ? {
                      weight: parseInt(data?.weight),
                  }
                : {}),
            triage: data?.triage?.value,
            notes: data?.notes,
            isBlocked: false,
            weight: data.weight,
            appointmentId: appointmentId,
        };

        console.log({ appointmentBody });

        updateAppointmentMutation.mutate({
            appointmentId,
            body: appointmentBody,
        });
        setSelectedAppointmentData(getValues());
        if (typeOfCall === 'updateLabel') {
            setShowAppointmentModal(false);
        }
    };

    const handleCancel = () => {
        setShowAppointmentModal(false);
        addPatientReset();
        setSelectedPatient(null);
        setEditMode(false);
        setPatientData({});
    };

    const handleProviderDelete = () => {};
    const handleAddProvider = () => {};

    const getProviderOptions = async (
        search: string,
        loadedOptions: unknown[]
    ) => {
        const options = providerData?.data.users.map((item: any) => {
            return {
                value: item.id,
                label: `${item.firstName} ${item.lastName}`,
            };
        });
        return {
            options,
            hasMore: false,
        };
    };

    const { createAppointmentMutation } = useAppointmentMutation(
        props.appointmentParams,
        setShowAppointmentModal
    );
    const handleCreateAppointment = useCallback(
    debounce((data: any): void => {
        
        const appointmentBody: CreateAppointmentType = {
            clinicId: CLINIC_ID,
            doctorIds: [data.doctors.value],
            providerIds:
                data.provider && data.provider.length
                    ? data.provider.map((item) => item.value)
                    : [],
            patientId: data.patientSearch?.id,
            roomId: data.room?.value ?? null,
            date: data?.date,
            startTime: concatDateTime(data?.date, data.startTime),
            endTime: concatDateTime(data?.date, data.endTime),
            reason: data.reason?.label || '',
            type: data?.type.value,
            ...(data?.weight
                ? {
                    weight: parseInt(data?.weight),
                }
                : {}),
            triage: data?.triage?.value,
            notes: data?.notes,
            isBlocked: false,
            weight: data.weight !== '' ? data.weight : null,
        };

        createAppointmentMutation.mutate(appointmentBody);
        setShowAppointmentModal(false);
    }, 500),
    [createAppointmentMutation, setShowAppointmentModal]
);

    const scheduleStepHandler = () => {
        updateAppointmentStatusMutation.mutate({
            appointmentId,
            status: EnumAppointmentStatus.Scheduled,
        });
        setShowAppointmentModal(false);
    };

    const onCreateAppointmentAction = (item: MenuListType) => {
        if (item.id === 'cancel-appointment') {
            setShowAppointmentModal(false);
            setConfirmModal({
                isOpen: true,
                type: 'cancelAppointment',
            });
        }
    };

    const handleSearchAdd = () => {
        setCurrentStep(1);
        setIsAddPatientModalOpen(true);
        setShowAppointmentModal(false);
    };

    const handlePatientSearch = (
        e: ChangeEvent<HTMLInputElement>,
        onFocus: any
    ) => {
        if (onFocus) {
            if (patientsData?.data?.patients?.length) {
                console.log('also here');

                setPatientList(patientsData?.data.patients);
                setListStatus('success');
                setShowPatientDropdown(true);
            }
            return;
        }
        getPatientOptions(e.target.value, []);
    };
    return (
        <div>
            {showAppointmentModal && patientsStatus && (
                <CreateAppointmentStaff
                    patientProps={{
                        showPatientDropdown,
                        setShowPatientDropdown,
                        listStatus,
                        onMenuClick,
                        patientList,
                        selectedPatient,
                    }}
                    workinghours={workinghours}
                    appointmentOptions={getAppointmentTypeOptions}
                    assignRoomOptions={getRoomOptions}
                    doctorOptions={getDoctorOptions}
                    isOpen={showAppointmentModal}
                    onClose={() => {
                        setPatientData({});
                        setShowAppointmentModal(false);
                        setEditMode(false);
                    }}
                    getPatientOptions={getPatientOptions}
                    reasonOptions={getReasonOptions}
                    control={control}
                    errors={errors}
                    setValue={setValue}
                    watch={watch}
                    register={register}
                    handleSubmit={handleSubmit}
                    key={'Create Appointment'}
                    triageOptions={getTriggerOption}
                    modalTitle={
                        editMode ? 'Edit Appointment' : 'Create Appointment'
                    }
                    handleCancel={handleCancel}
                    onProviderDelete={handleProviderDelete}
                    handleAddProvider={handleAddProvider}
                    handleCreateAppointment={handleCreateAppointment}
                    onStepHandler={onStepHandler}
                    step={step}
                    providerOptions={getProviderOptions}
                    getValues={getValues}
                    isEditable={true}
                    patientData={patientData}
                    editMode={editMode}
                    handleUpdateAppointment={handleUpdateAppointment}
                    valuesChanged={valuesChanged}
                    scheduleStepHandler={scheduleStepHandler}
                    onMoreActionClick={onCreateAppointmentAction}
                    handleSearchAdd={handleSearchAdd}
                    handlePatientSearch={handlePatientSearch}
                />
            )}

            {/* <div className="flex items-center justify-between gap-3 w-full">
                <Button
                    icon={<Add size={18} />}
                    id="create-appointment"
                    type="button"
                    variant="primary"
                    label="Create Appointment"
                    size="small"
                    onClick={handleAddAppointment}
                    iconPosition="left"
                />
            </div> */}
        </div>
    );
};

export default CreateAppointment;
