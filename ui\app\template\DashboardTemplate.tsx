'use client';
import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import Dashboard from '../template/Dashboard';
import { getAuth } from '../services/identity.service';
import { useTaskMutation } from '../services/task.queries';
import io, { Socket } from 'socket.io-client';
import { Conversation, Message } from '../organisms/dashboard/Message';
import moment from 'moment';
import {
    formatBreed,
    getClockTime,
    getInitials,
    getLinksFromText,
    getProfileImage,
} from '../utils/common';
import { useChatMutation } from '../services/chat.queries';
import * as uuid from 'uuid';
import { DATE_FORMAT } from '../utils/constant';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { error } from 'console';
import { useMediaUploadS3 } from '../services/aws-queries';
import { getViewPreSignedUrl } from '../services/aws-services';
import { Button } from '../atoms';
import {
    useAppointmentMutation,
    useDeleteAppointment,
    useUpdateAppointmentFeilds,
    useUpdateAppointmentStatus,
} from '../services/appointment.queries';
import { GetDoctorsType } from '../types/provider';
import { Modal } from '../molecules';
import IconWarning from '../atoms/customIcons/IconWarning.svg';
import { AppointmentParams } from '../types/appointment';
import { createAppointmentValidationSchema } from '../utils/validation-schema/createAppointmentValidation';
import {
    getAppointmentTypeOptions,
    getDoctorOptions,
    getProviderOptions,
    getReasonOptions,
    getRoomOptions,
    getTriggerOption,
    handleCancel,
    handleCreateAppointment,
    handleDashboardUpdateAppointment,
    handleUpdateAppointment,
    onCreateAppointmentAction,
    onStepHandler,
    scheduleStepHandler,
    setDefaultValue,
} from '../utils/patient-details-utils/create-appointment-utils';
import CreateAppointmentStaff, {
    AppointmentStaffStepT,
} from '../organisms/appointment/CreateAppointmentStaff';
import { ResponseStatus } from '../molecules/PatientDropdown';
import { PatientT } from '../types/patient';
import { MenuListType } from '../molecules/DropdownMenu';
import * as _ from 'lodash';
import { useRouter } from 'next/navigation';
import { useGetClinicDetails } from '@/app/services/clinic.queries';

export type UpdateTask = {
    title?: string;
    isCompleted?: boolean;
};
type DashboardTemplateProps = {
    chatRooms: any[];
    tasksList: any[];
    doctorAppointments: any[];
    allDoctorAppointments: any[];
    usersData: any[];
    chatRoomDetails: any;
    selectedConversationId: string | null;
    setSelectedConversationId: Dispatch<SetStateAction<null | string>>;
    messageFetchStatus: 'pending' | 'success' | 'error';
    refetchChatRoom: any;
    appointmentParams: AppointmentParams;
    clinicRoomsData: any[];
    doctorData: any[];
};
const DashboardTemplate = ({
    chatRooms,
    tasksList,
    chatRoomDetails,
    doctorAppointments,
    messageFetchStatus,
    selectedConversationId,
    setSelectedConversationId,
    refetchChatRoom,
    usersData,
    appointmentParams,
    clinicRoomsData,
    doctorData,
    allDoctorAppointments,
}: DashboardTemplateProps) => {
    const {
        createTaskMutation,
        useGetTask,
        updateTaskMutation,
        deleteTaskMutation,
    } = useTaskMutation();
    const router = useRouter();
    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors, isDirty, dirtyFields },
        watch,
        trigger,
    } = useForm({
        resolver: yupResolver(
            yup.object({
                messageInput: yup.string(),
                chatRoom: yup.object().nullable(),
            })
        ),
        mode: 'onChange',
    });

    const {
        register: appointmentRegister,
        handleSubmit: appointmentHandleSubmit,
        getValues: appointmentGetValues,
        setValue: appointmentSetValue,
        setError: appointmentSetError,
        control: appointmentControl,
        reset: appointmentReset,
        resetField: appointmentResetField,
        formState: { errors: appointmentErrors },
        watch: appointmentWatch,
        trigger: appointmentTrigger,
    } = useForm({
        resolver: yupResolver(createAppointmentValidationSchema),
        mode: 'onChange',
    });

    const auth = getAuth();
    const CLINIC_ID = auth?.clinicId;
    const [customRule, setCustomRule] = useState({
        patientLastNameAsOwnerLastName: false,
    });

    const { data: clinicDetails, status: clinicDetailsStatus } =
        useGetClinicDetails(CLINIC_ID);

    useEffect(() => {
        if (clinicDetails?.data?.customRule) {
            setCustomRule(clinicDetails.data.customRule);
        }
    }, [clinicDetails, clinicDetailsStatus]);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deleteAppointmentId, setDeleteAppointmentId] = useState('');
    const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const [editModal, setEditModal] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [patientData, setPatientData] = useState({});
    const [selectedAppointmentData, setSelectedAppointmentData] = useState({});
    const [step, setStep] = useState<AppointmentStaffStepT>('');
    const [showPatientDropdown, setShowPatientDropdown] = useState(false);
    const [listStatus, setListStatus] = useState<ResponseStatus>('pending');
    const [selectedPatient, setSelectedPatient] = useState<null | PatientT>(
        null
    );
    const [viewMode, setViewMode] = useState(false);
    const [valuesChanged, setValuesChanged] = useState(false);
    const [patientList, setPatientList] = useState([]);
    const [appointmentId, setAppointmentId] = useState('');
    const userId = getAuth()?.userId;
    const [socket, setSocket] = useState<Socket>();
    const {
        sendUserMessageMutation,
        updateChatRoomMutation,
        createChatRoomMutation,
    } = useChatMutation(userId);
    const { uploadMediaToS3 } = useMediaUploadS3();
    const updateAppointmentStatusMutation = useUpdateAppointmentStatus(
        null as unknown as AppointmentParams,
        null,
        appointmentParams
    );
    const { updateAppointmentMutation } = useUpdateAppointmentFeilds(
        null as unknown as AppointmentParams,
        null,
        appointmentParams
    );
    const { createAppointmentMutation } = useAppointmentMutation(
        null as unknown as AppointmentParams,
        setShowAppointmentModal,
        null
    );

    useEffect(() => {
        // Initialize socket connection
        console.log(process.env.NEXT_PUBLIC_SOCKET_URL);
        const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL, {
            reconnectionDelay: 2000,
            reconnectionAttempts: 10,
            transports: ['websocket'],
            forceNew: true, // Force new connection instead of reusing existing one
            multiplex: false, // Disable multiplexing to ensure separate connections
            // path: '/socket.io/',
        });
        console.log(newSocket);

        newSocket.on('connect', () => {
            console.log('Connected', newSocket);
        });

        newSocket.on('disconnect', (reason) => {
            console.log('Disconnected', reason);
        });

        newSocket.on('error', (error) => {
            console.log('Socket connection Error', error);
        });

        // Set the socket instance to state
        setSocket(newSocket);

        // Cleanup on component unmount
        return () => {
            newSocket.disconnect();
        };
    }, []);

    useEffect(() => {
        if (socket) {
            socket.emit('joinRoom', { userId });
            socket.on('joinedRoom', (room) => {
                console.log(`Joined room: ${room}`);
            });
            socket.on('reconnect', (attemptNumber) => {
                console.log(`Reconnected after ${attemptNumber} attempts`);
                socket.emit('joinRoom', { userId });
            });

            socket.on('reconnect_error', (error) => {
                console.error('Reconnection error:', error);
            });

            socket.on('reconnect_failed', () => {
                console.error('Failed to reconnect');
            });
        }

        return () => {
            if (socket) {
                socket.off('joinedRoom');
                socket.off('reconnect');
                socket.off('reconnect_error');
                socket.off('reconnect_failed');
            }
        };
    }, [socket, userId]);

    const handleDeleteTask = (id: string) => {
        deleteTaskMutation.mutate(id);
    };

    const onAppointmentClick = (id: string) => {
        const appointment = allDoctorAppointments.find(
            (item) => item.id === id
        );
        router.push(`/patients/${appointment?.patientId}/details`);
    };

    const onOnGoingAppointmentClick = (id: string) => {
        const appointment = doctorAppointments.find((item) => item.id === id);
        router.push(`/patients/${appointment?.patientId}/details`);
    };
    const handleAddTask = (task) => {
        const body = {
            id: task.id,
            userId,
            title: '',
            isCompleted: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        createTaskMutation.mutate(body);
    };

    const handleUpdateTask = (id: string, updateTask: UpdateTask) => {
        const body = {
            ...updateTask,
            updatedAt: new Date().toISOString(),
        };
        updateTaskMutation.mutate({ id, body });
    };

    const uploadMediaFiles = async (file: File) => {
        const fileKeys = [];
        const fileKey = `/chatMedia/${selectedConversationId}/${file.lastModified}/${file.name}`;
        try {
            const type = file.name.endsWith('.pdf') ? 'pdf' : 'image';
            const s3Response = await uploadMediaToS3.mutateAsync({
                file,
                fileKey,
            });
            if (s3Response) {
                fileKeys.push({ fileKey, type, name: file.name });
            }
        } catch (error) {
            console.error('Error uploading media:', error);
        }

        const preSignedUrlPromises = fileKeys.map(async (key: any) => {
            try {
                const response = await getViewPreSignedUrl(key.fileKey);
                return { ...response.data, type: key.type, name: key.name };
            } catch (error) {
                console.error('Error getting pre-signed URL:', error);
            }
        });
        const preSignedUrls = await Promise.all(preSignedUrlPromises);

        return preSignedUrls[0];
    };

    const { deleteAppointmentMutation } = useDeleteAppointment(
        appointmentParams,
        true
    );

    const handleSendMessage = async (messageObject: Message) => {
        let fileUrl;
        if (socket) {
            if (messageObject.file) {
                fileUrl = await uploadMediaFiles(
                    messageObject.file.fileObject as File
                );
            }

            if (messageObject.text.trim() || messageObject.file) {
                const body = {
                    id: messageObject.id,
                    chatRoomId: selectedConversationId,
                    message: messageObject.text,
                    file: fileUrl,
                    meta: null, // urlArray ?? null,
                    senderId: userId, // When auth is implemented repalce with auth id
                    otherUserId: chatRoomDetails?.users?.find(
                        (item: any) => item.userId !== userId
                    ).userId,
                    createdAt: new Date().toISOString(),
                };

                socket.emit('sendMessage', body);
                sendUserMessageMutation.mutate(body);
            }
        }
    };

    useEffect(() => {
        if (socket) {
            socket.on('chat-room-notification', () => {
                console.log('you have a new notification');
                refetchChatRoom();
            });
        }
    }, [socket]);
    const dashboardProps = {
        heading: 'Hello',
        patientName: getAuth()?.username ?? '',
        handleMenu: (action: string, id: string) => {
            if (action.id === 'edit') {
                const foundAppintment = doctorAppointments.find(
                    (item) => item.id === id
                );
                setEditMode(true);
                setDefaultValue({
                    data: foundAppintment,
                    getValues: appointmentGetValues,
                    setPatientData,
                    setSelectedAppointmentData,
                    setStep,
                    setValue: appointmentSetValue,
                    step,
                    viewMode: false,
                    patientRenderingData: null,
                });
                setAppointmentId(id);
                setShowAppointmentModal(true);
            }

            if (action.id === 'delete') {
                setShowDeleteModal(true);
                setDeleteAppointmentId(id);
                // deleteAppointmentMutation.mutate(id);
            }
        },
        onGoingAppointmentsData: doctorAppointments.map((app: any) => {
            return {
                id: app.id,
                date: moment(app.date).format(DATE_FORMAT),
                time: moment(app.startTime).format('h:mm A'),
                timeSpent: getClockTime(app),
                status: app.status,
                statusLabel: app.status,
                profile: getProfileImage({
                    species: app.patient.species,
                    breedValue: app.patient.breed,
                }),
                // name: app.patient.patientName,
                name:
                    customRule?.patientLastNameAsOwnerLastName === true &&
                    app.patient.patientOwners?.[0]?.ownerBrand?.lastName
                        ? `${app.patient.patientName} ${app.patient.patientOwners[0].ownerBrand.lastName}`
                        : app.patient.patientName || '--',
                owner: app.patient.patientOwners
                    .map(
                        (owner: any) =>
                            owner.ownerBrand.firstName +
                            ' ' +
                            owner.ownerBrand.lastName
                    )
                    .join(', '),
                ownerNumber:
                    app.patient.patientOwners[0]?.ownerBrand.globalOwner
                        .phoneNumber,
                breed: formatBreed(app.patient.breed),
                triage: app.triage,
                appointmentDetails: {
                    type: app.type,
                    reason: app.reason,
                    providers: app.appointmentDoctors
                        .filter((item: any) => item.primary === false)
                        .map((doctor: any) => doctor?.doctor?.firstName),
                    weight: app.weight ? `${app.weight} kgs` : '--',
                    room: app.room?.name,
                },
            };
        }),
        menuList: [{ id: 'edit', label: 'Edit' }],
        onStepHandler: (id: string) => {
            const foundAppointment = doctorAppointments.find(
                (item) => item.id === id
            );

            return onStepHandler({
                appointmentId: id,
                foundAppointment,
                setShowAppointmentModal,
                setStep,
                updateAppointmentStatusMutation,
                isDashboard: true,
                router,
            });
        },
        appointmentList: allDoctorAppointments.map((app: any) => {
            return {
                time: moment(app.startTime).format('h:mm A'),
                image: getProfileImage({
                    species: app.patient.species,
                    breedValue: app.patient.breed,
                }),
                name: app.patient.patientName,
                breed: formatBreed(app.patient.breed),
                disease: app.reason,
                id: app.id,
                status: app.status,
            };
        }),
        conversations: chatRooms?.map((item: any) => {
            const otherUser = item.chatRoom.users.find(
                (item: any) => item.clinicUser.id !== userId
            ).clinicUser.user;
            return {
                id: item.chatRoom.id,
                profilePicture: getInitials(`${otherUser.firstName}`),
                unreadCount: item.chatRoom?.unreadMessage,
                lastMessage: item?.chatRoom?.lastMessage,
                timestamp: moment(item.chatRoom.updatedAt).format('h mm'),
                name: `${otherUser.firstName} ${otherUser.lastName}`,
                lastMessageSender: item.chatRoom.lastMessageSender,
            };
        }),
        messages: chatRoomDetails?.messages ?? [],
        initialTasks: tasksList,
        handleCopy: (message: any) => {
            const body = {
                id: uuid.v4(),
                userId,
                title: message.text,
                isCompleted: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            };
            createTaskMutation.mutate(body);
        },
        loadOptions: async (inputValue: string) => {
            return {
                options: usersData
                    .filter((user) => user.id !== userId)
                    .filter((user) =>
                        `${user.firstName} ${user.lastName}`
                            .toLowerCase()
                            .includes(inputValue.toLowerCase())
                    )
                    .map((user) => ({
                        value: user.id,
                        label: `${user.firstName} ${user.lastName}`,
                    })),
            };
        },
    };

    const handleOptionSelect = async () => {
        const chatRoom = getValues('chatRoom') as {
            value: string;
            label: string;
        };
        if (!chatRoom) return;
        const chatRoomExists = chatRooms.find((item) =>
            item.chatRoom.users.find(
                (user: any) => user.clinicUser.id === chatRoom?.value
            )
        );

        if (chatRoomExists) {
            setSelectedConversationId(chatRoomExists?.chatRoom?.id);
            setValue('chatRoom', null, { shouldValidate: true });
        } else {
            const body = { userIds: [userId, chatRoom.value] };
            const response = await createChatRoomMutation.mutateAsync(body);
            if (response.data) {
                setSelectedConversationId(response?.data?.id);
            }
        }
    };
    useEffect(() => {
        handleOptionSelect();
    }, [watch('chatRoom')]);

    useEffect(() => {
        const values = appointmentGetValues();
        const filteredValues = { ...values };
        const filteredAppointmentData = { ...selectedAppointmentData };

        if (
            (filteredValues.weight === undefined ||
                filteredValues.weight === '' ||
                filteredValues.weight === null) &&
            (filteredAppointmentData.weight === undefined ||
                filteredAppointmentData.weight === '' ||
                filteredAppointmentData.weight === null)
        ) {
            delete filteredValues.weight;
            delete filteredAppointmentData.weight;
        }

        if (!_.isEqual(filteredValues, filteredAppointmentData)) {
            setValuesChanged(true);
        } else {
            setValuesChanged(false);
        }
    }, [appointmentWatch()]);
    return (
        <div>
            <Dashboard
                {...dashboardProps}
                onTaskAdd={handleAddTask}
                onTaskUpdate={handleUpdateTask}
                onTaskDelete={handleDeleteTask}
                messageFetchStatus={messageFetchStatus}
                selectedConversationId={selectedConversationId}
                setSelectedConversationId={setSelectedConversationId}
                onSendMessage={handleSendMessage}
                socket={socket}
                updateChatRoomMutation={updateChatRoomMutation}
                onGoingAppointments={dashboardProps.onGoingAppointmentsData}
                setValue={setValue}
                watch={watch}
                control={control}
                register={register}
                errors={errors}
                getValues={getValues}
                onAppointmentClick={onAppointmentClick}
                onOnGoingAppointmentClick={onOnGoingAppointmentClick}
            />

            <Modal
                isOpen={showDeleteModal}
                isHeaderBorder={false}
                icon={
                    <IconWarning
                        size={30}
                        className={`-mt-0.5 ml-1 text-warning-100`}
                    />
                }
                modalTitle={'Cancel Appointment'}
                onClose={() => setShowDeleteModal(false)}
                dataAutomation={'delete-appointment'}
            >
                <div className=" bg-white flex flex-col gap-4">
                    <div className="w-full flex gap-2">
                        <div className="flex flex-col gap-2">
                            <p className="text-primary-600 text-sm pl-12">
                                Are you sure you want to cancel appointment out?
                            </p>
                        </div>
                    </div>
                    <div className="flex w-fit gap-2 ml-auto">
                        <Button
                            id="go-to-appointment"
                            variant="borderless"
                            size="small"
                            label="Cancel"
                            onClick={() => {
                                setShowDeleteModal(false);
                            }}
                            className="w-fit"
                        />
                        <Button
                            id="go-to-appointment"
                            variant="primary"
                            size="small"
                            label="Confirm"
                            onClick={() => {
                                deleteAppointmentMutation.mutate(
                                    deleteAppointmentId
                                );
                                setShowDeleteModal(false);
                            }}
                            className="w-fit "
                        />
                    </div>
                </div>
            </Modal>

            {showAppointmentModal && (
                <CreateAppointmentStaff
                    patientProps={{
                        showPatientDropdown,
                        setShowPatientDropdown,
                        listStatus,
                        onMenuClick: () => {},
                        patientList,
                        selectedPatient,
                    }}
                    appointmentOptions={(
                        search: string,
                        loadedOptions: unknown[]
                    ) => getAppointmentTypeOptions(search, loadedOptions)}
                    assignRoomOptions={(
                        search: string,
                        loadedOptions: unknown[]
                    ) => getRoomOptions(search, loadedOptions, clinicRoomsData)}
                    doctorOptions={(search: string, loadedOptions: unknown[]) =>
                        getDoctorOptions(search, loadedOptions, doctorData)
                    }
                    isOpen={showAppointmentModal}
                    onClose={() => {
                        setPatientData({});
                        setShowAppointmentModal(false);
                        setViewMode(false);
                        setEditMode(false);
                    }}
                    getPatientOptions={[]}
                    reasonOptions={getReasonOptions}
                    control={appointmentControl}
                    errors={appointmentErrors}
                    setValue={appointmentSetValue}
                    watch={appointmentWatch}
                    register={appointmentRegister}
                    handleSubmit={appointmentHandleSubmit}
                    key={'Create Appointment'}
                    triageOptions={(search: string, loadedOptions: unknown[]) =>
                        getTriggerOption(search, loadedOptions)
                    }
                    modalTitle={
                        editMode ? 'Edit Appointment' : 'Create Appointment'
                    }
                    handleCancel={() =>
                        handleCancel({
                            setEditMode,
                            setSelectedPatient,
                            setShowAppointmentModal,
                            setViewMode,
                        })
                    }
                    onProviderDelete={() => {}}
                    handleAddProvider={() => {}}
                    handleCreateAppointment={(data: any) =>
                        handleCreateAppointment({
                            createAppointmentMutation,
                            data,
                        })
                    }
                    onStepHandler={() =>
                        onStepHandler({
                            appointmentId: appointmentId,
                            foundAppointment: doctorAppointments.find(
                                (appointment) =>
                                    appointment.id === appointmentId
                            ),
                            setShowAppointmentModal,
                            setStep,
                            updateAppointmentStatusMutation,
                            isDashboard: true,
                            router,
                        })
                    }
                    step={step}
                    providerOptions={(
                        search: string,
                        loadedOptions: unknown[]
                    ) =>
                        getProviderOptions(search, loadedOptions, {
                            data: {
                                users: usersData,
                            },
                        })
                    }
                    getValues={appointmentGetValues}
                    isEditable={true}
                    patientData={patientData}
                    editMode={editMode}
                    handleUpdateAppointment={(data: any, typeOfCall: string) =>
                        handleDashboardUpdateAppointment({
                            appointmentId: appointmentId,
                            data,
                            getValues: appointmentGetValues,
                            setSelectedAppointmentData,
                            setShowAppointmentModal,
                            step,
                            typeOfCall,
                            updateAppointmentMutation,
                            router,
                        })
                    }
                    valuesChanged={valuesChanged}
                    scheduleStepHandler={() =>
                        scheduleStepHandler({
                            appointmentId: appointmentId,
                            setShowAppointmentModal,
                            updateAppointmentStatusMutation,
                        })
                    }
                    onMoreActionClick={(item: MenuListType) =>
                        onCreateAppointmentAction({
                            item,
                            setConfirmModal: setShowAppointmentModal,
                            setShowAppointmentModal,
                        })
                    }
                    handleSearchAdd={() => {}}
                    handlePatientSearch={() => {}}
                />
            )}
        </div>
    );
};

export default DashboardTemplate;
