//TODO:
// 1. Need to segregate logics to differnt files and pass state and mutations and useform varialbes as props
// 2. Have a seperate file to handle treament props
// 3. Move patient details sidebar and all its corresponding modals to sepeate react component and keep the handlers inside that file if they are not used by other parts of tha pages
// 4. Move handlers and modals of the tabs into the thier function components to reducs clutter on template page and increase readability
'use client';
import React, {
    Dispatch,
    SetStateAction,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
// import BeginTreatment from '../organisms/patientDetail/BeginTreatment';
import { AppointmentHeaderT } from '../molecules/AppointmentHeader';
import Tabs from '../molecules/Tabs';
import { DaySummaryListT } from '../organisms/DaySummaryList';
import { PatientDetailsProps } from '../organisms/patientDetail/PatientDetails';
import TabAppointment, { appointmentDataT } from '../organisms/TabAppointment';
// import io, { Socket } from 'socket.io-client';
// import BasicDetailModal from '../organisms/patientDetail/BasicDetailModal';
// import { PatientData, PatientT } from '../types/patient';
// import { Modal } from '../molecules';
// import LoadEditPatient from '../organisms/patient/PatientEditLoad';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { Button, Textarea } from '../atoms';
import {
    // createTagValidationSchema,
    validationSchema,
} from '../utils/validation-schema/patientValidation';
// import { usePatientAlertMutation } from '../services/patientAlert.queries';
// import CreateTag from '../organisms/patientDetail/CreateTag';
// import EditAlert from '../organisms/patientDetail/EditAlert';
// import { useClinicAlertMutation } from '../services/clinicAlerts.queries';
import moment from 'moment';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
    BREEDS,
    CART_TYPES,
    cartItemAddedFromType,
    CREDIT_TYPES,
    DATE_FORMAT,
    INTEGRATION_TYPE,
    INVOICE_TYPES,
    PAYMENT_TYPES,
} from '../utils/constant';
// import PastVitalModal from '../organisms/patientDetail/PastVitalModal';
import { Breadcrumbs, Modal } from '../molecules';
import {
    useAddLongTermPrescriptionMutation,
    // useAppointmentMutation,
    useCreateAssessmentMutation,
    useCreatePrescriptionMutation,
    // useDeleteAppointment,
    useDeleteLabReportFile,
    useUpdateAppointmentDetailsMutation,
    useUpdateAppointmentStatus,
} from '../services/appointment.queries';
import { AppointmentParams, EnumAppointmentStatus } from '../types/appointment';
import {
    // categorizeFile,
    filePreview,
} from '../utils/common';
// import NoOngoingAppointments from '../organisms/patientDetail/NoOngoingAppointments';
import TabDiagnostic from '../organisms/diagnostic/TabDiagnostic';
import TabVaccination from '../organisms/vaccination/TabVaccination';
// import AddVaccinationModal from '../organisms/vaccination/AddVaccinationModal';
// import ModalSingleFileUpload, {
// FormValuesFileUpload,
// } from '../organisms/vaccination/ModalSingleFileUpload';
// import ModalFileUpload from '../organisms/diagnostic/ModalFileUpload';
import {
    useCreateLabReport,
    useDeleteAllLabReports,
    useDeleteLabReport,
} from '@/app/services/lab-report.queries';
import { v4 as uuidv4 } from 'uuid';
import ModalFilePreview from '../organisms/diagnostic/ModalFilePreview';
import Treatment from '../organisms/treatment/Treatment';
import { treatmentValidationSchema } from '../utils/validation-schema/treamtnemtValidation';

import { DropdownType } from '../molecules/Dropdown';
import {
    actOnAddMoreToCartClick,
    // assignVitalPropertyValue,
    cartItemsAddedToCart,
    cartPrescriptionItemsYetToBeAddedToCart,
    checkPrescriptionRestricted,
    createCartRowItemData,
    defaultTreatmentValue,
    deleteFromCart,
    formatAppointmentDetailsResponse,
    getActiveAppointment,
    getActiveAppointmentHeader,
    getActiveReceivingCareAppointment,
    getAddMoreList,
    getAppointmentDetails,
    getBodyMapOptions,
    getDisagnosticsOptions,
    getInvoiceDetails,
    getPrescriptionDetails,
    // getStructuredData,
    getTreatmentAppointment,
    objectiveVitalsFieldsConfig,
    physicalExamCategories,
    physicalExamFilterOptions,
    physicalExamStatusOptions,
    ultrasoundExamCategories,
    updateItemAddedFlagAndQuantityToCart,
    updateQuantityToCart,
} from '../utils/patient-details-utils/appointment-details-utils';
// import { filter, uniqueId } from 'lodash';
import {
    useCreateInvoiceMutation,
    useCreateInvoiceWithPaymentMutation,
} from '@/app/services/invoice.queries';
import { TabItemType } from '../atoms/HorizontalTabs';
import TabTreatmentCart from '../organisms/cart/TabTreatmentCart';
import EditTreatment from '../organisms/patientDetail/EditTreatment';
import {
    useDeleteFromS3Mutation,
    useMediaUploadS3,
} from '../services/aws-queries';
import {
    useAddtoCartMutation,
    useDeleteFromCartMutation,
    useUpdateCartDetailsMutation,
} from '../services/cart.queries';
import { getAuth } from '../services/identity.service';
// import { ResponseStatus } from '../molecules/PatientDropdown';
// import CreateAppointmentStaff, {
//     AppointmentStaffStepT,
// } from '../organisms/appointment/CreateAppointmentStaff';

// import { MenuListType } from '../molecules/DropdownMenu';
// import { createAppointmentValidationSchema } from '../utils/validation-schema/createAppointmentValidation';
import * as _ from 'lodash';
import TabInvoices from '../organisms/cart/TabInvoices';
// import { useGetPaymentDetailsForAPatient } from '../services/payment-details.queries';
import { PaymentModeType } from '../molecules/cart/PaymentMode';
import {
    useCreateOrUpdatePatient,
    usePatientDetails,
} from '../services/patient.queries';
import { useCreatePaymentDetailsMutation } from '../services/payment-details.queries';
import { createPaymentDetailsCall } from '../utils/payment/payment-details-utils';
// import { createPaymentDetails } from '../services/payment-details.service';
// import IconSquareTick from '../atoms/customIcons/IconSquareTick.svg';
// import IconCart from '../atoms/customIcons/IconCart.svg';
import IconWarning from '../atoms/customIcons/IconWarning.svg';
import Price from '../atoms/Price';
import ConfirmModal from '../organisms/patient/ConfirmModal';
// import ModalPaymentSuccess from '../molecules/cart/ModalPaymentSuccess';
// import { pages } from 'next/dist/build/templates/app-page';

import MissingInfoModal from '../organisms/admin/Idexx/MissingInfoModal';
import {
    useCancelIdexxOrderMutation,
    useCreateIdexxOrderMutation,
} from '../services/idexx.queries';
import {
    cancelIdexxOrderUtil,
    createIdexxOrderUtil,
    isMissingInformation,
} from '../utils/idexx/idexx-utils';
import {} from // assignVitalPropertyValue,

// getStructuredData,
// vitalLabelList,
'../utils/patient-details-utils/patient-details-sidebar-utils';
import { transformApiDataToCalendar } from '../utils/patient-details-utils/tab-utils';
// import { parsePhoneNumber } from 'react-phone-number-input';
import IconClose from '../atoms/customIcons/IconClose';
import IconTimeCircle from '../atoms/customIcons/IconTimeCircle.svg';
import { speciesOptions } from '../organisms/patient/PatientInfo';
import { useEMRMutation } from '../services/emr.queries';
import {
    handleAddDiagnosticUtility,
    handleAssesmentFileChangeUtility,
    handleAssessmentFileDeleteUtility,
    handleDiagnosticsFileRemoveUtility,
    handleDiagnosticsFileUploadUtility,
    handlePlanPriceChange,
    handleRemoveDiagnosticsUtility,
    onPlanMenuClickFunc,
    onPlanSearch,
    onPrescriptionMenuClickFunc,
    onPrescriptionSearch,
} from '../utils/patient-details-utils/treatment-utils';
import PatientDetailsSidebarTemplate from './PatientDetailsSidebarTemplate';
import PatientDetailsTopbarTemplate from './PatientDetailsTopbarTemplate';
import CustomLoader from '../atoms/CustomLoader';
import TabCommunication from '../organisms/TabCommunication';
import { useGenerateSoapNotes } from '../services/ai.queries';
import { useGetClinic } from '@/app/services/clinic.queries';
import { debounce } from 'lodash';
import { findSingleEmr } from '../services/emr.service';
import { useCartSocket } from '../hooks/useCartSocket';
import PatientPaymentDetails from '../organisms/cart/PatientPaymentDetails';
// import { SocketContext, useSocket } from '../../context/AppointmentSocketContext';

// Add at the top of the file with the imports:
interface ExtendedFile extends File {
    fileKey?: string;
    fileName?: string;
    id?: string;
    s3Url?: string;
}
interface PatientPaymentDetailsData {
    patientReceiptsData: any;
    patientReceiptsStatus: any;
    newPatientInvoicesData: any;
    newPatientInvoicesStatus: any;
    patientRefundsData: any;
    patientRefundsStatus: any;
}

interface TypingState {
    key: string;
    currentText: string;
    isTyping: boolean;
}
interface PatientDetailsTemplateT {
    patientSidebar: Omit<
        PatientDetailsProps,
        | 'handleAlertEdit'
        | 'handleViewMoreProblems'
        | 'handleViewMoreMedication'
        | 'handleBasicInfoEdit'
    >;
    appointmentHeader: AppointmentHeaderT;
    appointmentCalender: DaySummaryListT;
    appointmentDetails: appointmentDataT;
    appointmentsData: any;
    labReportsData: any;
    assessmentData: any;
    medicationData: any;
    plansData: any;
    patientDiagnosticReportData: any;
    patientDiagnosticReportRefetch: Function;
    patientDiagnosticNotesData: any;
    patientDiagnosticNotesRefetch: Function;
    patientVaccinationData: any;
    cartOptions: any;
    cartListData: any;
    setCartAppointmentId: Dispatch<SetStateAction<any>>;
    cartAppointmentId: any;
    clinicRoomsData: any;
    doctorData: any;
    providerData: any;
    paymentDetailsList: any;
    longTermMedication: any;
    defaultTreatmentData: any;
    patientPaymentDetailsData: PatientPaymentDetailsData;
}

const PatientDetailsTemplate: React.FC<PatientDetailsTemplateT> = ({
    patientSidebar,
    appointmentsData,
    labReportsData,
    assessmentData,
    medicationData,
    plansData,
    patientDiagnosticReportData,
    patientDiagnosticReportRefetch,
    patientDiagnosticNotesData,
    patientDiagnosticNotesRefetch,
    patientVaccinationData,
    cartListData,
    cartOptions,
    setCartAppointmentId,
    cartAppointmentId,
    clinicRoomsData,
    doctorData,
    providerData,
    paymentDetailsList,
    longTermMedication,
    defaultTreatmentData,
    patientPaymentDetailsData,
}) => {
    const auth = getAuth();
    const CLINIC_ID = auth?.clinicId;
    const {
        data: clinicData,
        isLoading,
        error,
        refetch,
    } = useGetClinic(CLINIC_ID);
    const searchParams = useSearchParams();
    const urlStatus = useSearchParams().get('status');
    const urlTab = useSearchParams().get('tab');
    const activeItem = useSearchParams().get('activeItem');
    const treatmentRef = useRef<string | null>(null);
    const pathName = usePathname();
    const patientId = patientSidebar.patientId;
    const [isLoadingState, setIsLoadingState] = useState(false);
    const [isUpdateMedicalRecord, setIsUpdateMedicalRecord] = useState(false);
    const formattedWorkingHours = {
        Monday: [],
        Tuesday: [],
        Wednesday: [],
        Thursday: [],
        Friday: [],
        Saturday: [],
        Sunday: [],
    };

    if (clinicData?.data?.working_hours?.workingHours) {
        Object.entries(clinicData.data.working_hours.workingHours).forEach(
            ([day, hours]) => {
                const capitalizedDay =
                    day.charAt(0).toUpperCase() + day.slice(1);
                if (Array.isArray(hours)) {
                    formattedWorkingHours[capitalizedDay] = hours.map(
                        (slot) => ({
                            startTime: slot.startTime,
                            endTime: slot.endTime,
                            isWorkingDay: slot.isWorkingDay,
                        })
                    );
                }
            }
        );
    }
    // let patientRenderingData = {
    //     id: patientId,
    //     patientName: patientSidebar.patientData.patientName,
    //     profileImage: patientSidebar.patientData.patientProfile,
    //     patientOwners: [
    //         {
    //             owner: {
    //                 phoneNumber:
    //                     patientSidebar.patientData.patientOwners[0]?.owner
    //                         .phoneNumber,
    //                 firstName:
    //                     patientSidebar.patientData.patientOwners[0]?.owner
    //                         .firstName,
    //                 lastName:
    //                     patientSidebar.patientData.patientOwners[0]?.owner
    //                         .lastName,
    //             },
    //         },
    //     ],
    // };
    const breadcrumbList = [
        {
            id: 1,
            name: 'Patients',
            path: '/patients',
            handleChange: () => handlePathChange(`/patients`),
        },
        {
            id: 2,
            name: 'Patients Details',
            path: `/patients/${patientSidebar.patientId}/details`,
        },
    ];
    const {
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors },
        watch,
        trigger,
    } = useForm({
        resolver: yupResolver(validationSchema),
        mode: 'onChange',
        defaultValues: {},
    });

    // const {
    //     register: createAlertRegister,
    //     handleSubmit: createAlertHandleSubmit,
    //     getValues: createAlertgetValues,
    //     setValue: createAlertsetValue,
    //     setError: createAlertsetError,
    //     control: createAlertcontrol,
    //     reset: createAlertreset,
    //     resetField: createAlertresetField,
    //     formState: { errors: createAlertErrors },
    //     watch: createAlertWatch,
    //     trigger: createAlertTrigger,
    // } = useForm({
    //     resolver: yupResolver(createTagValidationSchema),
    //     mode: 'onChange',
    //     defaultValues: {
    //         tagName: '',
    //         priority: 'High',
    //     },
    // });

    const {
        register: treatmentRegister,
        getValues: treatmentGetValues,
        setValue: treatmentSetValue,
        control: treatmentControl,
        watch: treatmentWatch,
        reset: treatmentReset,
        formState: { errors: treatmentErrors },
    } = useForm({
        resolver: yupResolver(treatmentValidationSchema),
        defaultValues: defaultTreatmentValue(
            longTermMedication,
            defaultTreatmentData
        ),
        mode: 'onChange', // This will trigger validation on change
    });

    // const {
    //     register: appointmentRegister,
    //     handleSubmit: appointmentHandleSubmit,
    //     getValues: appointmentGetValues,
    //     setValue: appointmentSetValue,
    //     setError: appointmentSetError,
    //     control: appointmentControl,
    //     reset: appointmentReset,
    //     resetField: appointmentResetField,
    //     formState: { errors: appointmentErrors, isDirty, dirtyFields },
    //     watch: appointmentWatch,
    //     trigger: appointmentTrigger,
    // } = useForm({
    //     resolver: yupResolver(createAppointmentValidationSchema),
    //     mode: 'onChange',
    // });
    const { updateAppointmentDetailsMutation } =
        useUpdateAppointmentDetailsMutation(patientSidebar.patientId, pathName);

    // const [isShowRefund, setIsShowRefund] = useState(false);
    // const [isRefundAmountModalShown, setIsRefundAmountModalShown] = useState<{
    //     isOpen: boolean;
    //     payload?: any;
    // }>({
    //     isOpen: false,
    //     payload: undefined,
    // });
    // const [currentSelectedInvoiceItem, setCurrentSelectedInvoiceItem] =
    //     useState(null);

    // const [selectedItemForRefund, setSelectedItemForRefund] = useState({});

    /*********************** IDEXX **********************/
    const [showIdexxURL, setShowIdexxURL] = useState(false);
    const [setIdexxURL, setSetIdexxURL] = useState('');
    const [showPatientInfo, setShowPatientInfo] = useState(false);
    const [currentSelectedIdexxItem, setCurrentSelectedIdexxItem] = useState({
        patientId: '',
        integrationCode: '',
        appointmentId: '',
        clinicLabReportId: '',
        clinicId: CLINIC_ID,
        idexxOrderId: '',
    });

    const [isSoapxModal, setIsSoapxModal] = useState(false);
    const [soapxContent, setSoapxContent] = useState<string>('');

    const { createIdexxOrderMutation } = useCreateIdexxOrderMutation();
    const { cancelIdexxOrderMutation } = useCancelIdexxOrderMutation();
    const updatePatientMutate = useCreateOrUpdatePatient();
    const { createEMRMutation } = useEMRMutation();
    const callBackOnCancelIdexxOrder = (data: any) => {
        console.log('callBackOnCancelIdexxOrder data = ', data);

        // if (data.bResult == true) {
        //     const labReports = treatmentGetValues('objective.labReports');
        //     const updatedLabReport = labReports?.filter(
        //         (item: any) => item.value !== data.clinicLabReportId
        //     );

        //     treatmentSetValue('objective.labReports', updatedLabReport, {
        //         shouldValidate: true,
        //     });

        //     deleteLabReportMutation.mutate({
        //         appointmentId: treatmentAppointment.id,
        //         labReportId: data.clinicLabReportId,
        //     });

        //     data.cartId &&
        //         deleteFromCartMutation.mutate(
        //             {
        //                 data: {
        //                     cartId: data.cartId,
        //                 },
        //             },
        //             {
        //                 onSuccess: () => {
        //                     console.error(
        //                         'callBackOnCancelIdexxOrder Labreports: deleteFromCart success'
        //                     );
        //                 },
        //                 onError: (error: any) => {
        //                     console.error(
        //                         'callBackOnCancelIdexxOrder Labreports: deleteFromCart Error:',
        //                         error
        //                     );
        //                 },
        //             }
        //         );
        // } else {
        //     alert('This diagnostic cannot be cancelled');
        // }
    };

    // const callBackOnCreateIdexxOrder = (data: any) => {
    //     const uiURL = data.uiURL;
    //     const idexxOrderId = data.idexxOrderId;
    //     const clinicLabReportId = data.clinicLabReportId;

    //     const currentList = treatmentGetValues('objective.labReports');

    //     const updatedList = currentList.map((item: any) => {
    //         if (item.value === clinicLabReportId) {
    //             return {
    //                 ...item,
    //                 idexxOrderId,
    //             };
    //         }

    //         return item;
    //     });

    //     treatmentSetValue('objective.labReports', updatedList, {
    //         shouldValidate: true,
    //     });

    //     setShowIdexxURL(true);
    //     setSetIdexxURL(uiURL);
    //     setShowPatientInfo(false);

    //     //******************************
    //     const temp = {
    //         ...currentSelectedIdexxItem,
    //         idexxOrderId: idexxOrderId,
    //     };

    //     setCurrentSelectedIdexxItem(temp);
    //     //******************************
    // };

    const actOnIDEXXOIframeURL = () => {
        if (setIdexxURL.length > 0) {
            return (
                <div className="w-full h-[800px] overflow-hidden">
                    <iframe
                        ref={iframeRef}
                        src={setIdexxURL}
                        width="100%"
                        height="800"
                        className="w-full h-full border overflow-auto"
                        title="Iframe Example"
                        allowFullScreen
                        scrolling="yes"
                        id="idexx-order-iframe"
                    ></iframe>
                </div>
            );
        }

        return <div></div>;
    };
    const handleCloseIdexxModal = () => {
        setShowIdexxURL(false);
        setSetIdexxURL('');
        setShowPatientInfo(false);
    };
    // We have to cancel the idexx order.
    /************************************/
    // Have commented this code, but we need to enable once we can cancel the order. We have to check the flow and recheck the below code
    // WE need to identify that the cross button is clicked adter clicking order or before
    /***********************************/
    /*
        const currentList = treatmentGetValues(
            'objective.labReports'
        );

        const foundItem = currentList.find((item: any) => item.value === currentSelectedIdexxItem.clinicLabReportId);
        if (foundItem) {
            const itemData = {
                patientId: patientId,
                appointmentId: treatmentAppointment?.id, // appointment id
                clinicLabReportId: currentSelectedIdexxItem.clinicLabReportId, // lab report entry id in clinic_lab_reports table
                clinicId: CLINIC_ID,
                idexxOrderId: currentSelectedIdexxItem.idexxOrderId,
                cartId: foundItem.cartId
            };

            cancelIdexxOrderUtil({
                itemData,
                cancelIdexxOrderMutation,
                callBackOnCancelIdexxOrder
            })
        }

        removeDiagnostics(currentSelectedIdexxItem.clinicLabReportId);
        */
    /************************************/
    /***********************************
    };

    const handleClosePatientInfoModal = () => {
        setShowPatientInfo(false);
    };


    /*********************** IDEXX **********************/

    // Refund confirm

    //invoice tab items
    // const actOnRefundCOnfirmClick = () => {
    //     if (currentSelectedInvoiceItem) {
    //         const dataValue = selectedItemForRefund;

    //         createInvoiceMutation.mutate(
    //             {
    //                 data: dataValue,
    //             },
    //             {
    //                 onSuccess: (data) => {
    //                     createEntryForInvoiceInPaymentDetailsForRefund(
    //                         data?.data?.id,
    //                         CREDIT_TYPES.CreditNote,
    //                         dataValue.amountPaid,
    //                         dataValue.amountPayable,
    //                         selectedItemForRefund.paymentMode
    //                     );

    //                     setIsRefundAmountModalShown({
    //                         isOpen: false,
    //                     });
    //                 },
    //                 onError: (error) => {
    //                     console.log(
    //                         'actOnRefundCOnfirmClick: actOnRefundCOnfirmClick failure'
    //                     );
    //                 },
    //             }
    //         );
    //     }
    // };

    // const frameYearListWithData = () => {
    //     const result = createYearListWithData(paymentDetailsList);
    //     if (currentSelectedInvoiceItem === null) {
    //         // Have taken the first item as current item to show the data
    //         setCurrentSelectedInvoiceItem(result.firstItem);
    //     }

    //     return result.resultFinal;
    // };

    // const frameInvoiceCartItems = () => {
    //     if (currentSelectedInvoiceItem) {
    //         return createInvoiceCartItems(
    //             JSON.parse(JSON.stringify(currentSelectedInvoiceItem))
    //         );
    //     }
    // };

    // const showCreditNoteData = () => {
    //     return createCreditNoteData(currentSelectedInvoiceItem);
    // };

    //save template details
    const handlePathChange = async (path: string) => {
        let data = treatmentGetValues();
        // JSON.parse(
        //     localStorage.getItem(treatmentAppointment??.id) as string
        // );
        if (data && data.appointmentId) {
            data = formatAppointmentDetailsResponse(data);

            await updateAppointmentDetailsMutation.mutateAsync({
                appointmentId: data.appointmentId,
                data: {
                    details: data,
                    callSiteId: `CALL_SITE_ID_003`,
                },
                shouldReset: false,
            });
            localStorage.removeItem(data.appointmentId);
        }
        router.push(path);
    };

    //states
    const router = useRouter();
    const iframeRef = useRef<HTMLIFrameElement | null>(null);
    // const [isOpen, setIsOpen] = useState(false);
    const [discountValue, setDiscountValue] = useState(0);
    const [discountObect, setDiscountObject] = useState({
        label: '0%',
        value: '0',
    });
    const { data: patientDetailsData, status: patientDetailsStatus } =
        usePatientDetails(patientId);
    const [treatmentAppointment, setTreatmentAppointment] =
        useState(defaultTreatmentData);

    const [dTotalPrice, setTotalPrice] = useState(0);
    const [dAdjustmentAmount, setAdjustmentAmount] = useState(0);
    const [isAdjustmentInInvoice, setIsAdjustmentInInvoice] = useState(false);
    const [dTotalDiscountAmount, setTotalDiscountAmount] = useState(0);
    const [dTotalPriceAfterDiscount, setTotalPriceAfterDiscount] = useState(0);
    const [dTotalTaxedAmount, setTotalTaxedAmount] = useState(0);
    const [dFinalPrice, setFinalPrice] = useState(0);
    const [dInvoiceAmount, setInvoiceAmount] = useState(0);
    const [dTotalCredit, setTotalCredit] = useState(0);
    // const [editModal, setEditModal] = useState(false);
    // const [invoiceSummary, setInvoiceSummary] = useState();
    // const [problemHistoryModal, setProblemHistoryModal] = useState(false);
    // const [medicationHistoryModal, setMedicationHistoryModal] = useState(false);
    const [activeAppointment, setActiveAppointment] = useState(
        getActiveAppointment(appointmentsData)
    );

    const [invoiceCollectedAmount, setInvoiceCollectedAmount] = useState(0);
    // const [createAlert, setCreateAlert] = useState(false);
    // const [name, setName] = useState('');
    // const [severity, setSeverity] = useState('');
    // const [editAlert, setEditAlert] = useState(false);
    // const [isCheckedModalCheckOut, setIsCheckedModalCheckOut] = useState(false);
    // const [openVitalPop, setOpenVitalPop] = useState(false);
    const [showCheckoutModal, setShowCheckoutModal] = useState(
        urlStatus && urlStatus === 'ready-for-checkout' ? true : false
    );
    // const [showReadyToCheckOutModal, setShowReadyToCheckoutModal] =
    //     useState(false);
    const alertsList = patientSidebar?.alertsList || [];
    const [fileSizeExceededMessage, setFileSizeExceededMessage] = useState('');
    const { uploadMediaToS3 } = useMediaUploadS3();
    const deleteFromS3Mutation = useDeleteFromS3Mutation();
    const createLabReportMutation = useCreateLabReport();
    const deleteLabReportFileMutation = useDeleteLabReportFile();
    const [tabs, setTabs] = useState<TabItemType[]>([]);
    const selectedAlertTagList = patientSidebar?.selectedAlertTagList || [];
    const getAppointmentId = () => treatmentGetValues()?.appointmentId || treatmentAppointment?.id;
    const { addToCartMutation, bulkInsertToCart } = useAddtoCartMutation(
        getAppointmentId()
    );
    const { deleteAllLabReportsMutation } = useDeleteAllLabReports();
    const { deleteFromCartMutation } = useDeleteFromCartMutation(
        getAppointmentId()
    );
    const deleteLabReportMutation = useDeleteLabReport();
    const { updateCartDetailsMutation } = useUpdateCartDetailsMutation(
        getAppointmentId()
    );
    const [holdTreatmentId, setHoldTreatmentId] = useState('');

    //AI Integration
    const generateSoapNotesMutation = useGenerateSoapNotes();
    // const [cartItems, setCartItems] = useState([]);
    const [showCart, setShowCart] = useState(
        urlStatus && urlStatus === 'show-cart' ? true : false
    );
    const [showEditTreatment, setShowEditTreatment] = useState(false);
    const { createInvoiceMutation } = useCreateInvoiceMutation();
    const { createInvoiceWithPaymentMutation } =
        useCreateInvoiceWithPaymentMutation();
    // const [addVaccinationPop, setAddVaccinationPop] = useState(false);
    // const [addSingleFilePop, setAddSingleFilePop] = useState(false);
    // const [uploadDiagnosticReportPop, setUploadDiagnosticReportPop] =
    // useState(false);
    const [previewDiagnosticPop, setPreviewDiagnosticPop] = useState(false);
    // const [vaccineUploadFile, setVaccineUploadFile] = useState({});
    // const [updateVaccineFlag, setUpdateVaccineFlag] = useState(false);
    const [addProductList, setAddProductList] = useState(
        getAddMoreList(cartOptions)
    );
    const [treatmentCompletionLoading, setTreatmentCompletionLoading] =
        useState(false);
    const [isShowPaymentCompleted, setIsShowPaymentCompleted] = useState(false);
    const [isShowPaymentSuccess, setIsShowPaymentSuccess] = useState(false);
    const [routeToAppointmentPage, setRouteToAppointmentPage] = useState(false);
    const [showDeleteDiagnosticsModal, setShowDeleteDiagnosticModal] =
        useState(false);
    const [selectedLabReportItem, setSelectedLabReportItem] = useState(null);
    const [showMissingItemsModal, setShowMissingItemsModal] = useState(false);
    const [showIdexxOrderCreation, setShowIdexxCreation] = useState(false);
    const [idexxCreationModalText, setShowIdexxCreationModalText] =
        useState('');
    const [selectedPlanId, setSelectedPlanId] = useState('');
    const [showAppointmentModal, setShowAppointmentModal] = useState(false);
    const [circularLoader, setCircularLoader] = useState(false);
    const [isStartInvoice, setIsStartInvoice] = useState(false);
    const [activeTab, setActiveTab] = useState<string>(); // Default tab

    // Add a new state to track manually entered payment amount
    const [manualPaymentAmount, setManualPaymentAmount] = useState<number>(0);

    // Function to reset payment amount to 0 when cart operations happen
    const resetPaymentAmount = () => {
        setManualPaymentAmount(0);
        setInvoiceCollectedAmount(0);
        treatmentSetValue('invoiceAmount', 0);
    };

    const saveAppoinmentDetails = (subCallSiteId: string) => {
        // Guard against saving data to wrong appointment
        let data = treatmentGetValues();
        if (!isStartInvoice && data && data.appointmentId) {
            data = formatAppointmentDetailsResponse(data);
            updateAppointmentDetailsMutation.mutate({
                appointmentId: data.appointmentId,
                data: {
                    details: data,
                    callSiteId: `CALL_SITE_ID_004:${subCallSiteId}`, // Append sub-identifier
                },
                shouldReset: false,
            });
        } else {
            console.log('Not saving appointment details - invalid state');
        }
    };

    const handleUpdateMedicalRecord = (appointmentId: string) => {
        // Safety check to ensure we're updating the correct appointment
        if (appointmentId !== treatmentAppointment?.id) {
            console.error(
                'Attempted to update medical record for wrong appointment.'
            );
            return;
        }

        createEMRMutation.mutate({
            appointmentId: appointmentId,
            onSuccess: () => {
                setIsUpdateMedicalRecord(false);

                // process for reduce the latency of emrDownloadingState
                // setCircularLoader(true);
                // const interval = setInterval(async () => {
                //     const response = await findSingleEmr(holdTreatmentId);

                //     if (response?.data?.emrFileKey?.length > 0) {
                //         setTimeout(() => {
                //             setCircularLoader(false);
                //             clearInterval(interval);
                //         },1500)
                //     }
                // }, 2000);
            },
        });
    };

    // }, [socket, treatmentAppointment?.id, isRemoteUpdate, saveAppoinmentDetails]);

    // const [showPatientDropdown, setShowPatientDropdown] = useState(false);
    // const [listStatus, setListStatus] = useState<ResponseStatus>('pending');
    // const [selectedPatient, setSelectedPatient] = useState<null | PatientT>(
    //     null
    // );
    // const [patientList, setPatientList] = useState([]);
    // const [patientData, setPatientData] = useState(patientRenderingData);
    // const [viewMode, setViewMode] = useState(false);
    // const [editMode, setEditMode] = useState(false);
    // const [step, setStep] = useState<AppointmentStaffStepT>('');
    // const [appointmentId, setAppointmentId] = useState('');
    // const [selectedAppointmentData, setSelectedAppointmentData] = useState({});
    // const [valuesChanged, setValuesChanged] = useState(false);

    // const [confirmModal, setConfirmModal] = useState<{
    //     isOpen: boolean;
    //     type: 'cancelAppointment' | 'PatientAddedSuccessfully';
    // }>({
    //     isOpen: false,
    //     type: 'cancelAppointment',
    // });

    // const [openEditTag, setOpenEditTag] = useState(false);
    // const [alertId, setAlertId] = useState('');
    const latestAppointment = getTreatmentAppointment(appointmentsData?.data);
    // const [fileUploadError, setFileUploadError] = useState('');
    // const { updateAppointmentMutation } = useUpdateAppointmentFeilds(
    //     null as unknown as AppointmentParams,
    //     patientId,
    //     null as unknown as AppointmentParams
    // );
    // const { deleteAppointmentMutation } = useDeleteAppointment();
    // const { createAppointmentMutation } = useAppointmentMutation(
    //     null as unknown as AppointmentParams,
    //     setShowAppointmentModal,
    //     patientId
    // );

    const [verticalScrollButtonText, setVerticalScrollButtonText] = useState(
        getTreatmentAppointment(appointmentsData.data)?.status ===
            EnumAppointmentStatus.ReceivingCare
            ? 'Ready to checkout'
            : 'Continue check-out'
    );
    const [paymentMode, setPaymentMode] = useState<PaymentModeType>(
        PAYMENT_TYPES.Cash
    );

    const onPaymentModeChange = (mode: PaymentModeType) => {
        setPaymentMode(mode);
    };
    const { createNewAssessmentMutation } = useCreateAssessmentMutation();
    const { createNewPrescriptionMutation } = useCreatePrescriptionMutation();
    const { addLongTermPrescriptionMutation } =
        useAddLongTermPrescriptionMutation();

    const { emitCartChange, showCheckedoutModal, cartState } = useCartSocket(
        treatmentGetValues()?.appointmentId || treatmentAppointment?.id
    );

    //appointment status effects
    useEffect(() => {
        if (urlStatus && urlStatus === 'show-cart') {
            setCartAppointmentId(getAppointmentId());
            setShowCart(true);
            calculateAmount(cartListData);
            createTreatmentTab(true);
        }
    }, [urlStatus]);

    useEffect(() => {
        if (
            urlStatus &&
            urlStatus === 'ready-for-checkout' &&
            showCheckoutModal
        ) {
            const nextSearchParams = new URLSearchParams(
                searchParams.toString()
            );

            nextSearchParams.delete('status');

            router.replace(
                `/patients/${patientId}/details?${nextSearchParams}`
            );
        }
    }, [urlStatus]);

    useEffect(() => {
        if (urlTab) {
            const allTabs = [...tabs, ...defaultTabs];
            const tabExists = allTabs.some((t) => t.id === urlTab);

            if (tabExists) {
                setActiveTab(urlTab);
                // Change the URL back to default without changing the active tab
                const nextSearchParams = new URLSearchParams(
                    searchParams.toString()
                );
                nextSearchParams.delete('tab');
                nextSearchParams.delete('activeItem');
                router.replace(
                    `/patients/${patientId}/details?${nextSearchParams}`
                );
            }
        }
    }, [urlTab]);

    useEffect(() => {
        if (urlStatus === 'begin-treatment' && treatmentAppointment) {
            treatmentRef.current = getAppointmentId();
            setCartAppointmentId(getAppointmentId());
        }
    }, [urlStatus, treatmentAppointment]);

    useEffect(() => {
        if (getAppointmentId() && cartAppointmentId === null) {
            setCartAppointmentId(getAppointmentId());
        }
    }, [treatmentAppointment]);

    // Add a ref to track if we're in the middle of handleEditBack
    const isHandlingEditBack = useRef(false);

    // Add a ref to track if checkout is in progress
    const isCheckoutInProgress = useRef(false);

    // Monitor checkout modal state changes
    useEffect(() => {
        if (showCheckedoutModal) {
            isCheckoutInProgress.current = true;
        } else {
            isCheckoutInProgress.current = false;
        }
    }, [showCheckedoutModal]);

    // Consolidated save treatment effects
    useEffect(() => {
        // Shared save logic with conditional checks
        const saveAppointmentData = async (
            appointmentId?: string,
            callSiteId: string = 'CALL_SITE_ID_005'
        ) => {
            // Skip save if we're in the middle of handleEditBack (it handles its own save)
            if (isHandlingEditBack.current) return;
            // Only save if not in invoice mode
            if (isStartInvoice) return;
            // Skip save during checkout process
            const isCheckoutMode = isCheckoutInProgress.current ||
                                     showCheckedoutModal;
            if (isCheckoutMode) return;

            // Get data first to check appointmentId
            let data = treatmentGetValues();

            // Skip save if no meaningful data or no appointmentId in data
            if (!data || !data.appointmentId) return;
            const {
                invoiceAmount = 0,
                patientSearch = '',
                ...remainingTreatmentDetails
            } = data || {};

            // Skip save if no meaningful data
            if (_.isEmpty(remainingTreatmentDetails)) return;

            const dbDetails =
                treatmentAppointment?.appointmentDetails?.details || {};
            const {
                invoiceAmount: dbInvoiceAmount = 0,
                patientSearch: dbPatientSearch = '',
                ...remainingDbTreatmentDetails
            } = dbDetails;

            // Only save if data has changed or is new
            const hasNewData =
                _.isEmpty(remainingDbTreatmentDetails) &&
                !_.isEmpty(remainingTreatmentDetails) &&
                treatmentRef.current !== null;

            const hasChangedData =
                data.appointmentId &&
                !_.isEqual(
                    remainingDbTreatmentDetails,
                    remainingTreatmentDetails
                );

            if (hasNewData || hasChangedData) {
                data = formatAppointmentDetailsResponse(data);
                // Use appointmentId from data object, fallback to provided appointmentId
                const targetAppointmentId =
                    data?.appointmentId || appointmentId;

                if (targetAppointmentId) {
                    await updateAppointmentDetailsMutation.mutateAsync({
                        appointmentId: targetAppointmentId,
                        data: {
                            details: data,
                            callSiteId,
                        },
                        shouldReset: false,
                    });
                }
            }
        };

        // Handle browser navigation events
        const handleNavigationEvent = (event: any) => {
            saveAppointmentData(undefined, 'CALL_SITE_ID_005');
        };

        // Add event listeners for navigation
        window.addEventListener('popstate', handleNavigationEvent);
        window.addEventListener('beforeunload', handleNavigationEvent);

        // Cleanup function for component unmount
        return () => {
            // Remove event listeners
            window.removeEventListener('popstate', handleNavigationEvent);
            window.removeEventListener('beforeunload', handleNavigationEvent);

            // Save on unmount if needed (but not during handleEditBack)
            if (!isHandlingEditBack.current) {
                // Use treatmentRef as fallback for cleanup
                const data = treatmentGetValues();
                const appointmentIdToSave =
                    data?.appointmentId || treatmentRef.current;
                saveAppointmentData(
                    appointmentIdToSave || undefined,
                    'CALL_SITE_ID_006'
                );
            }
        };
    }, [showEditTreatment, treatmentAppointment?.id, router, showCheckedoutModal, isCheckoutInProgress]); // More specific dependencies

    const handleEditBack = (appointmentId: string) => {
        // Set flag to prevent useEffect cleanup from interfering
        isHandlingEditBack.current = true;

        let data = treatmentGetValues();
        if (data && appointmentId) {
            data = formatAppointmentDetailsResponse(data);

            updateAppointmentDetailsMutation.mutate({
                appointmentId: appointmentId,
                data: {
                    details: data,
                    callSiteId: 'CALL_SITE_ID_007',
                },
                shouldReset: false,
            });
        }

        setShowEditTreatment(false);
        setTreatmentAppointment(null);

        // Reset to the current appointment data, not defaults
        const receivingCareAppointment =
            getActiveReceivingCareAppointment(appointmentsData);
        if (receivingCareAppointment) {
            treatmentReset(
                receivingCareAppointment?.appointmentDetails?.details
            );
        } else {
            // Only reset to defaults if there's truly no appointment data
            treatmentReset(
                defaultTreatmentValue(longTermMedication, defaultTreatmentData)
            );
        }

        setActiveAppointment(getActiveAppointment(appointmentsData));
        setHoldTreatmentId(
            treatmentAppointment?.appointmentDetails?.appointmentId
        );

        // Clear the flag after a short delay to allow state updates to complete
        setTimeout(() => {
            isHandlingEditBack.current = false;
        }, 100);
        // setIsUpdateMedicalRecord(true);
    };

    const handleSaveMissingInformation = async () => {
        const ownerData = patientDetailsData?.data?.patientOwners?.map(
            (item: any, index: number) => {
                return {
                    id: item.ownerBrand.id,
                    firstName: item.ownerBrand.firstName,
                    lastName: item.ownerBrand.lastName,
                    countryCode: item.ownerBrand.globalOwner.countryCode,
                    phoneNumber: item.ownerBrand.globalOwner.phoneNumber,
                    address: item.ownerBrand.address,
                    email: item.ownerBrand.email,
                    isPrimary: item.isPrimary,
                };
            }
        );
        const { age, breed, species } = getValues();
        const body = {
            species: species?.value ?? undefined,
            breed: breed?.value ?? undefined,
            age: age ? moment(age).format(DATE_FORMAT) : undefined,
            patientName: patientDetailsData?.data?.patientName,
            gender: patientDetailsData?.data?.gender?.value ?? undefined,
            reproductiveStatus:
                patientDetailsData?.data?.reproductiveStatus?.value ??
                undefined,
            microchipId: patientDetailsData?.data?.microchipId ?? undefined,
            identification:
                patientDetailsData?.data?.identification ?? undefined,
            allergies: patientDetailsData?.data?.allergies ?? undefined,
            isDeceased: patientDetailsData?.data?.markDeceased ?? undefined,
            ownersData: ownerData,
        };

        const response = await updatePatientMutate.mutateAsync({
            type: 'update',
            data: { ...body, id: patientId, balance: 0 },
        });

        if (response.status) {
            setTimeout(() => {
                setShowMissingItemsModal(false);
                onPlanMenuClick(selectedLabReportItem, false);
            }, 1000);
            // setShowMissingItemsModal(false);
            // onPlanMenuClick(selectedLabReportItem);
        }
    };

    //plan drop down handlers

    const onPlanMenuClick = async (
        item: any,
        checkIdexxMissingInfo: boolean = true
    ) => {
        if (item.type === CART_TYPES.Labreport) {
            if (
                item?.integrationType === INTEGRATION_TYPE.IDEXX &&
                isMissingInformation(patientDetailsData) &&
                checkIdexxMissingInfo
            ) {
                if (patientDetailsData?.data?.species) {
                    const selectedSpeciesStatus = (
                        await speciesOptions('', [])
                    ).options.find(
                        ({ value, label }) =>
                            label === patientDetailsData?.data?.species ||
                            value === patientDetailsData?.data?.species
                    );
                    if (selectedSpeciesStatus) {
                        setValue('species', selectedSpeciesStatus, {
                            shouldValidate: true,
                        });
                    }
                    if (patientDetailsData?.data?.breed) {
                        console.log('breed');

                        const breedInfoArray =
                            BREEDS[patientDetailsData?.data?.species];
                        const breedsInfo = breedInfoArray.find(
                            (breedItem: any) =>
                                breedItem.value ===
                                patientDetailsData?.data?.breed
                        );
                        console.log('breed info', breedsInfo);
                        setValue('breed', breedsInfo, {
                            shouldValidate: true,
                        });
                    }
                }
                if (patientDetailsData?.data?.age) {
                    setValue(
                        'age',
                        moment(patientDetailsData?.data?.age, DATE_FORMAT)
                            .toDate()
                            .toDateString()
                    );
                }
                setShowMissingItemsModal(true);
                setSelectedLabReportItem(item);
                return;
            }
            let idexxOrderResponse = null;
            const lineItemId = uuidv4();
            console.log('lineItemId', lineItemId);
            if (
                item?.integrationType === INTEGRATION_TYPE.IDEXX &&
                !isStartInvoice
            ) {
                setShowIdexxCreation(true);
                setShowIdexxCreationModalText(
                    `Creating order for ${item.name}...`
                );
                const itemData = {
                    patientId: patientId,
                    integrationCode: item?.integrationCode,
                    appointmentId: getAppointmentId(),
                    clinicLabReportId: item.id,
                    clinicId: CLINIC_ID,
                    idexxOrderId: '',
                    lineItemId: lineItemId,
                };

                idexxOrderResponse = await createIdexxOrderUtil({
                    itemData,
                    createIdexxOrderMutation,
                });
                if (!idexxOrderResponse?.status) {
                    setShowIdexxCreationModalText('Order creation failed');
                    return;
                }
            }

            const selectionOption = {
                label: item.title,
                value: item.id,
                labReportId: idexxOrderResponse?.labReportId,
                chargeablePrice: item.chargeablePrice,
                integrationCode: item?.integrationCode,
                integrationType: item?.integrationType,
                lineItemId: lineItemId, // Add lineItemId here
                ...(item?.integrationType === INTEGRATION_TYPE.IDEXX
                    ? idexxOrderResponse
                    : {}),
            };

            const labReports = [
                ...treatmentGetValues('objective.labReports'),
                selectionOption,
            ];
            console.log('labReports', labReports);
            treatmentSetValue('objective.labReports', labReports, {
                shouldValidate: true,
            });
            const tempItem = item;
            const itemType = item.type;
            const plansList = treatmentGetValues('plans.list') || [];
            let newAddedItem = plansList.find((item) => item?.name === '');

            // If no empty plan item exists, create one
            if (!newAddedItem) {
                newAddedItem = {
                    id: `temp_${Date.now()}`,
                    name: '',
                    qty: 1,
                    price: 0,
                    brand: '',
                    type: '',
                    dosage: '',
                };

                const updatedPlansList = [...plansList, newAddedItem];
                treatmentSetValue('plans.list', updatedPlansList, {
                    shouldValidate: true,
                });
            }

            const updatedItem = {
                ...newAddedItem,
                ...item,
                planId: item.id,
                id: newAddedItem.id,
                price: item?.chargeablePrice,
                qty: item.quantity || 1,
                name: item.name || item.title,
                lineItemId: lineItemId, // Add lineItemId to the plan for better tracking
            };
            console.log('updatedItem', updatedItem);

            const currentPlansList = treatmentGetValues('plans.list') || [];
            const updatedPlansList = currentPlansList.map((plan: any) =>
                plan.id === newAddedItem.id ? updatedItem : plan
            );
            console.log('updatedPlansList', updatedPlansList);
            treatmentSetValue('plans.list', updatedPlansList, {
                shouldValidate: true,
            });

            saveAppoinmentDetails('PDT_1212');
            objectiveProps.handleAddDiagnostics(
                selectionOption,
                newAddedItem.id,
                lineItemId
            );
        } else {
            onPlanMenuClickFunc({
                item,
                addToCartMutation,
                treatmentAppointment,
                treatmentGetValues,
                treatmentSetValue,
                showEditTreatment,
                saveAppoinmentDetails,
                isStartInvoice,
            });
        }
    };

    // const onPlanSearch = (props: any) => {
    //     const formattedPlans = plansData
    //         .filter((item: any) =>
    //             getName(item).toLowerCase().includes(props.toLowerCase())
    //         )
    //         ?.map((plan: any) => {
    //             const name = getName(plan);
    //             return {
    //                 id: plan.id,
    //                 name: name,
    //                 title: name,
    //                 type: plan.type,
    //                 chargeablePrice: plan.chargeablePrice,
    //                 subList: [
    //                     plan?.drug ?? '',
    //                     plan?.form ?? '',
    //                     `${plan.strength ?? ''}${plan.unit ?? ''}`,
    //                 ],
    //                 showSubList: plan.type === 'Medication',
    //                 integrationCode: plan?.integrationCode,
    //                 integrationType: plan?.integrationType,
    //             };
    //         });

    //     return formattedPlans;
    // };

    //prescription dropdown handlers
    const onPrescriptionMenuClick = (item: any) =>
        onPrescriptionMenuClickFunc({
            addToCartMutation,
            item,
            treatmentAppointment,
            treatmentGetValues,
            treatmentSetValue,
            showEditTreatment,
            createNewPrescriptionMutation,
            saveAppoinmentDetails,
            isStartInvoice,
        });

    // const onPrescriptionSearch = (props: any) => {
    //     const formattedMeds = medicationData
    //         ?.filter((item: any) =>
    //             item.name.toLowerCase().includes(props.toLowerCase())
    //         )
    //         ?.map((med: any) => {
    //             return {
    //                 id: med.id,
    //                 name: med.name,
    //                 chargeablePrice: med.chargeablePrice,
    //                 subList: [
    //                     med?.drug,
    //                     med?.form,
    //                     `${med.strength}${med.unit}`,
    //                 ],
    //                 showSubList: true,
    //             };
    //         });

    //     return formattedMeds;
    // };

    const plansDropDownProps: DropdownType = {
        onMenuClick: onPlanMenuClick,
        onSearch: (props) => onPlanSearch(props, plansData, isStartInvoice),
    };

    const prescriptionDropDownProps: DropdownType = {
        onMenuClick: onPrescriptionMenuClick,
        onSearch: (props) => onPrescriptionSearch(props, medicationData),
    };

    const getAssessmentOption = () => {
        return assessmentData?.map((item: any) => {
            return {
                label: item.name,
                value: item.id,
            };
        });
    };
    // diagnostic and attachment file uploads func
    // async function handleFileExplorerChange(files: File[]) {
    //     const filesToUplaod = files.filter((file) => !file.fileKey);
    //     const attachmentDataList = treatmentGetValues('attachments.list');
    //     setFileSizeExceededMessage('');
    //     // const files = files;
    //     if (filesToUplaod && filesToUplaod.length > 0) {
    //         for (let i = 0; i < filesToUplaod.length; i++) {
    //             const file = filesToUplaod[i];
    //             if (file.size > MAX_FILE_SIZE) {
    //                 setFileSizeExceededMessage(
    //                     `File "${file.name}" exceeds the 5MB size limit.`
    //                 );
    //             } else {
    //                 const mimeType = file.type;

    //                 if (
    //                     mimeType === 'application/pdf' ||
    //                     mimeType.startsWith('image/')
    //                 ) {
    //                     const finalName = `${uuidv4()}_${file.name}`;
    //                     const fileKey = `/appointment_attachements/${finalName}`;

    //                     const response = await uploadMediaToS3.mutateAsync({
    //                         file,
    //                         fileKey: fileKey,
    //                     });

    //                     const uploadedData: AttachmentData = {
    //                         attachementName: 'Attachment',
    //                         fileName: file.name,
    //                         fileKey: fileKey,
    //                     };
    //                     const attachmentDataList =
    //                         treatmentGetValues('attachments.list');
    //                     attachmentDataList.push(uploadedData);

    //                     treatmentSetValue(
    //                         'attachments.list',
    //                         attachmentDataList,
    //                         {
    //                             shouldValidate: true,
    //                         }
    //                     );
    //                     saveAppoinmentDetails();

    //                     saveData();
    //                 }
    //             }
    //         }
    //     }
    // }

    // const actOnDeleteClick = (item: AttachmentData, nIndex: number) => {
    //     deleteFromS3Mutation.mutate(item.fileKey, {
    //         onSuccess: (data) => {
    //             const dataList: AttachmentData[] =
    //                 treatmentGetValues('attachments.list') ?? [];
    //             const spliceIndex = dataList.findIndex(
    //                 (dataItem) => dataItem.fileKey === item.fileKey
    //             );
    //             dataList.splice(spliceIndex, 1);
    //             treatmentSetValue('attachments.list', dataList, {
    //                 shouldValidate: true,
    //             });
    //             saveAppoinmentDetails();
    //         },
    //         onError: () => {
    //             alert('Error in deleting the file');
    //         },
    //     });
    // };

    // async function handleDiagnosticsFileUpload(
    //     files: any[],
    //     clinicLabReportId: string
    // ) {
    //     if (files) {
    //         const filesToUplaod = files.filter((file) => !file.s3Url);

    //         if (
    //             filesToUplaod &&
    //             filesToUplaod.length > 0 &&
    //             clinicLabReportId
    //         ) {
    //             for (let i = 0; i < filesToUplaod.length; i++) {
    //                 const file = filesToUplaod[i];
    //                 if (file.size > MAX_FILE_SIZE) return;

    //                 try {
    //                     const fileKey = `/labReports/${treatmentAppointment.id}/${clinicLabReportId}/${file.name}`;
    //                     const s3Response = await uploadMediaToS3.mutateAsync({
    //                         file,
    //                         fileKey,
    //                     });

    //                     const newFile = {
    //                         s3Url: s3Response,
    //                         fileName: file.name,
    //                         fileSize: file.size,
    //                         uploadDate: new Date().toISOString(),
    //                         fileKey,
    //                     };

    //                     // Immediately create or update the lab report with the new file
    //                     const labReportResponse =
    //                         await createLabReportMutation.mutateAsync({
    //                             appointmentId: treatmentAppointment.id,
    //                             clinicLabReportId,
    //                             clinicId: CLINIC_ID,
    //                             files: [newFile],
    //                             patientId,
    //                             status: 'PENDING',
    //                         });

    //                     // Update local state with the response from the backend
    //                     const updatedLabReport = labReportResponse.data;
    //                     const currentLabReports =
    //                         treatmentGetValues('objective.labReports') || [];
    //                     const updatedLabReports = currentLabReports.map(
    //                         (report: any) =>
    //                             report.value === clinicLabReportId
    //                                 ? {
    //                                       ...report,
    //                                       labReportId: updatedLabReport.id,
    //                                       files: updatedLabReport.files,
    //                                   }
    //                                 : report
    //                     );
    //                     treatmentSetValue(
    //                         'objective.labReports',
    //                         updatedLabReports
    //                     );
    //                     saveAppoinmentDetails();
    //                 } catch (error) {
    //                     console.log('Error in diagnositics file uplad', error);
    //                 }
    //             }
    //             saveData();
    //         }
    //     }
    // }

    // const handleDiagnosticsFileRemove = async (
    //     file: File,
    //     labReportId: string
    // ) => {
    //     const currentLabReports =
    //         treatmentGetValues('objective.labReports') || [];
    //     try {
    //         const updatedLabReports = currentLabReports.map((report: any) => {
    //             if (report.labReportId === labReportId) {
    //                 return {
    //                     ...report,
    //                     files: report.files.filter(
    //                         (f: any) => f.id !== file.id
    //                     ),
    //                 };
    //             }
    //             return report;
    //         });

    //         treatmentSetValue('objective.labReports', updatedLabReports);
    //         saveData();
    //         await deleteLabReportFileMutation.mutateAsync({
    //             labReportId,
    //             fileId: file.id,
    //         });
    //     } catch (err) {
    //         console.log('erro in file delete', err);
    //     }
    // };

    // const removeDiagnostics = (id: string) => {
    //     const labReports = treatmentGetValues('objective.labReports');
    //     const deltedItem: {
    //         idexxOrderId: string;
    //         integrationType: string;
    //         value: string;
    //         cartId?: string;
    //     } = labReports?.find((item: any) => item.value === id);

    //     const updatedLabReport = labReports?.filter(
    //         (item: any) => item.value !== id
    //     );

    //     treatmentSetValue('objective.labReports', updatedLabReport, {
    //         shouldValidate: true,
    //     });

    //     saveAppoinmentDetails();

    //     deleteLabReportMutation.mutate({
    //         appointmentId: treatmentAppointment.id,
    //         labReportId: deltedItem.value,
    //     });

    //     deltedItem.cartId &&
    //         deleteFromCartMutation.mutate(
    //             {
    //                 data: {
    //                     cartId: deltedItem.cartId,
    //                 },
    //             },
    //             {
    //                 onSuccess: (data: any) => {},
    //                 onError: (error: any) => {
    //                     console.error(
    //                         'Labreports: deleteFromCart Error:',
    //                         error
    //                     );
    //                 },
    //             }
    //         );
    // };

    // const handleRemoveDiagnostics = (id: string) => {
    //     const labReports = treatmentGetValues('objective.labReports');
    //     const deltedItem: {
    //         idexxOrderId: string;
    //         integrationType: string;
    //         value: string;
    //         cartId?: string;
    //     } = labReports?.find((item: any) => item.value === id);
    //     console.log('delete item', deltedItem);

    //     if (deltedItem.integrationType == INTEGRATION_TYPE.IDEXX) {
    //         const idexxOrderId = deltedItem.idexxOrderId;
    //         // We have to cancel the idexx order
    //         const itemData = {
    //             patientId: patientId,
    //             appointmentId: treatmentAppointment.id, // appointment id
    //             clinicLabReportId: id, // lab report entry id in clinic_lab_reports table
    //             clinicId: CLINIC_ID,
    //             idexxOrderId: idexxOrderId,
    //             cartId: deltedItem.cartId,
    //         };
    //         console.log('itemdata', itemData);
    //         callBackOnCancelIdexxOrder(itemData);

    //         // cancelIdexxOrderUtil({
    //         //     itemData,
    //         //     cancelIdexxOrderMutation,
    //         //     callBackOnCancelIdexxOrder,
    //         // });
    //     } else {
    //         removeDiagnostics(id);
    //     }
    // };

    const handleSoapxModal = () => {
        try {
            const savedDraftData = localStorage.getItem('SOAP_DRAFT');

            if (savedDraftData) {
                const { content } = JSON.parse(savedDraftData);
                setSoapxContent(content); // Pre-fill the content
            }
        } catch (error) {
            console.error('Error loading draft:', error);
        }
        setIsSoapxModal(true);
    };
    // Add this function before the if condition
    const getDiagnosticNotesForLabReport = (labReport: any) => {
        // Get notes from both sources - API data and labReport object
        const apiNotes =
            patientDiagnosticNotesData?.data?.filter(
                (item: any) => item.labReportId === labReport.labReportId
            ) || [];
        return apiNotes;
    };

    //treatment props

    const subjectiveProps = {
        onSubjectiveChange: (value: string) => {
            treatmentSetValue('subjective', value, { shouldValidate: true });
            saveData();
            // saveAppoinmentDetails();
            // try {
            //     treatmentSetValue('subjective', value, { shouldValidate: true });
            //     saveData();
            //     saveAppoinmentDetails();

            //     // Emit socket only after successful save
            //     if (socket) {
            //         console.log('Emitting ping after save complete');
            //         socket.emit('ping', {
            //             appointmentId: treatmentAppointment.id,
            //             message: 'subjective',
            //         });
            //     }
            // } catch (error) {
            //     console.error('Error saving appointment:', error);
            //     // Handle save error - maybe show toast notification
            // }
        },
        onSubjectiveBlur: (value: string) => {
            treatmentSetValue('subjective', value, { shouldValidate: true });
            saveData();
            saveAppoinmentDetails('PDT_1613');
            // try {
            //     treatmentSetValue('subjective', value, { shouldValidate: true });
            //     saveData();
            //     saveAppoinmentDetails();

            //     // Emit socket only after successful save
            //     if (socket) {
            //         console.log('Emitting ping after save complete');
            //         socket.emit('ping', {
            //             appointmentId: treatmentAppointment.id,
            //             message: 'subjective',
            //         });
            //     }
            // } catch (error) {
            //     console.error('Error saving appointment:', error);
            //     // Handle save error - maybe show toast notification
            // }
        },
    };
    const objectiveProps = {
        onObjectiveChange: (value: string) => {},
        // treatmentSetValue('objective.notes', value, {
        //     shouldValidate: true,
        // }),
        onObjectiveBlur: (value: string) => {
            treatmentSetValue('objective.notes', value, {
                shouldValidate: true,
            }),
                saveAppoinmentDetails('PDT_1642');
        },
        onObjectiveVitalBlur: (value: string) => {
            const vitalsData = Array.isArray(value) ? value : JSON.parse(value);

            treatmentSetValue('objective.vitals', vitalsData, {
                shouldValidate: true,
            });
            saveAppoinmentDetails('PDT_1650');
        },
        onObjectivePhysicalExamBlur: (value: string) => {
            const physicalExamData = Array.isArray(value)
                ? value
                : JSON.parse(value);
            treatmentSetValue('objective.physicalExam', physicalExamData, {
                shouldValidate: true,
            });
            saveAppoinmentDetails('PDT_1659');
        },
        onObjectiveUltrasoundExamBlur: (value: string) => {
            const ultrasoundExamData = Array.isArray(value)
                ? value
                : JSON.parse(value);
            treatmentSetValue('objective.ultrasoundExam', ultrasoundExamData, {
                shouldValidate: true,
            });
            saveAppoinmentDetails('PDT_1668');
        },

        diagnosticOptions: (search: string, loadedOptions: any[]) =>
            getDisagnosticsOptions(search, loadedOptions, labReportsData),
        bodyMapOptions: async (search: string, loadedOptions: any[]) =>
            getBodyMapOptions(search, loadedOptions),
        categories: physicalExamCategories,
        ultrasoundCategories: ultrasoundExamCategories,
        statusOptions: physicalExamStatusOptions,
        filterOptions: physicalExamFilterOptions,
        initialVitals: [
            {
                time: moment().format('h:mm A'),
                weight:
                    treatmentAppointment && treatmentAppointment.weight
                        ? treatmentAppointment.weight.toString()
                        : '',
                temperature: '',
                heartRate: '',
                respRate: '',
                attitude: '',
                painScore: '',
                mucousMembrane: '',
                capillaryRefill: '',
                // hydrationStatus: '',
                bcs: '',
                bp: '',
                map: '',
            },
        ],
        fieldsConfig: objectiveVitalsFieldsConfig,
        handleFilesSelect: (
            files: ExtendedFile[],
            id: string,
            lineItemId: string,
            updateUploadProgress?: (fileName: string, progress: number) => void
        ) =>
            handleDiagnosticsFileUploadUtility({
                files,
                clinicLabReportId: id,
                createLabReportMutation,
                patientId,
                saveAppoinmentDetails,
                treatmentAppointment,
                treatmentGetValues,
                treatmentSetValue,
                uploadMediaToS3,
                lineItemId,
                onUploadProgress: updateUploadProgress,
            }),
        handleFileDelete: (
            file: ExtendedFile,
            id: string,
            lineItemId: string
        ) =>
            handleDiagnosticsFileRemoveUtility({
                deleteLabReportFileMutation,
                file,
                labReportId: id,
                treatmentGetValues,
                treatmentSetValue,
                lineItemId,
            }),
        handleDeleteDiagnostic: (id: string) =>
            handleRemoveDiagnosticsUtility({
                callBackOnCancelIdexxOrder,
                deleteFromCartMutation,
                deleteLabReportMutation,
                id,
                patientId,
                saveAppoinmentDetails,
                treatmentAppointment,
                treatmentGetValues,
                treatmentSetValue,
                isStartInvoice,
            }),
        handleAddDiagnostics: (
            selectionOption: any,
            planId: string,
            lineItemId: string
        ) =>
            handleAddDiagnosticUtility({
                addToCartMutation,
                createLabReportMutation,
                selectionOption,
                setCurrentSelectedIdexxItem,
                setSetIdexxURL,
                setShowIdexxCreation,
                setShowIdexxCreationModalText,
                setShowIdexxURL,
                setShowPatientInfo,
                treatmentGetValues,
                treatmentSetValue,
                patientId,
                treatmentAppointment,
                showEditTreatment,
                planId,
                isStartInvoice,
                saveAppoinmentDetails,
                lineItemId,
            }),
        // {
        // console.log('selection option added', selectionOption);

        // if (selectionOption.integrationType === INTEGRATION_TYPE.IDEXX) {
        //     // Create an idexx order
        //     const itemData = {
        //         patientId: patientId,
        //         integrationCode: selectionOption.integrationCode,
        //         appointmentId: treatmentAppointment.id,
        //         clinicLabReportId: selectionOption.value,
        //         clinicId: CLINIC_ID,
        //         idexxOrderId: selectionOption.idexxOrderId,
        //     };

        //     // Check if patient species are presnt if not show pop up
        //     {
        //         setShowIdexxCreation(false);
        //         setShowIdexxCreationModalText('');
        //         setShowIdexxURL(true);
        //         setSetIdexxURL(selectionOption?.uiURL);
        //         setShowPatientInfo(false);
        //         setCurrentSelectedIdexxItem(itemData);
        //     }
        //     // else {
        //     // createIdexxOrderUtil({
        //     //     itemData,
        //     //     createIdexxOrderMutation,
        //     //     callBackOnCreateIdexxOrder,
        //     // });
        //     // }
        // } else {
        //     // add price check
        //     createLabReportMutation.mutate({
        //         appointmentId: treatmentAppointment.id,
        //         clinicLabReportId: selectionOption.value,
        //         clinicId: CLINIC_ID,
        //         files: [],
        //         patientId,
        //         status: 'PENDING',
        //     });
        // }

        // if (selectionOption.chargeablePrice > 0 && !showEditTreatment) {
        //     addToCartMutation.mutate(
        //         {
        //             data: {
        //                 appointmentId: treatmentAppointment.id,
        //                 type: CART_TYPES.Labreport,
        //                 labReportId: selectionOption.value,
        //             },
        //         },
        //         {
        //             onSuccess: (data) => {
        //                 // check if cart it is correctly set
        //                 const currentList = treatmentGetValues(
        //                     'objective.labReports'
        //                 );

        //                 // Search for id in prescriptionDataList list
        //                 const indexFound = currentList?.findIndex(
        //                     (data: any) =>
        //                         data.value === selectionOption.value
        //                 );

        //                 if (typeof indexFound === 'number') {
        //                     currentList[indexFound].cartId = data.data.id;
        //                     treatmentSetValue(
        //                         'objective.labReports',
        //                         currentList,
        //                         {
        //                             shouldValidate: true,
        //                         }
        //                     );
        //                 } else {
        //                     console.log('Labreport: Item not found');
        //                 }
        //             },
        //             onError: (error) => {
        //                 console.log(
        //                     'Labreports: createCartMutation failure'
        //                 );
        //             },
        //         }
        //     );
        // }
        // },
        // addComponentLimit?: { [key: string]: number };
        handleDeleteAllDiagnosticsReport: () => {
            deleteAllLabReportsMutation.mutate(getAppointmentId());
            saveAppoinmentDetails('PDT_1856');
        },
    };

    const assessmentProps = {
        onAssessmentChange: (value: string) => {},
        onAssessmentBlur: (value: string) => {
            saveAppoinmentDetails('PDT_1863');
        },
        handleAddCondition: (value: string) => {
            if (value && value !== '') {
                const data = {
                    name: value,
                    isAddedByUser: false,
                };
                createNewAssessmentMutation.mutate({ data });
            }
        },
        dropdownOptions: getAssessmentOption(), //{ label: string; value: string }[];
    };

    const planProps = {
        onPlanChange: (value: string) =>
            treatmentSetValue('plans.notes', value, { shouldValidate: true }),
        onPlanBlur: (value: string) => {
            treatmentSetValue('plans.notes', value, { shouldValidate: true });
            saveAppoinmentDetails('PDT_1882');
        },
        handleAddPlan: () => {
            const plansList = treatmentGetValues('plans.list');

            plansList?.push({
                id: uuidv4(),
                name: '',
                qty: 1,
                brand: '',
                type: '',
                dosage: '',
            });
            treatmentSetValue('plans.list', plansList, {
                shouldValidate: true,
            });
            saveAppoinmentDetails('PDT_1898');
        },
        onQuantityChange: (params: { qty: number; id: string }) => {
            const plansList = treatmentGetValues('plans.list');

            const updatedPlansList = plansList?.map((item) => {
                if (item.id === params.id) {
                    return { ...item, qty: params.qty };
                }
                return {
                    ...item,
                };
            });
            treatmentSetValue('plans.list', updatedPlansList, {
                shouldValidate: true,
            });
            saveAppoinmentDetails('PDT_1914');

            const updatedPlan = updatedPlansList?.find(
                (item: any) => item.id === params.id
            ) as {
                id: string;
                value: string;
                cartId: string;
            };

            updateCartDetailsMutation.mutate(
                {
                    data: {
                        quantity: params.qty,
                    },
                    cartId: updatedPlan.cartId,
                    source: 'plan',
                },
                {
                    onSuccess: (data) => {},
                    onError: (error) => {
                        console.log('Plans: updateCartDetailsMutation failure');
                    },
                }
            );
            saveAppoinmentDetails('PDT_2017');
        },
        onDelete: async (params: { id: string }) => {
            const plans = treatmentGetValues('plans.list');
            const planToDelete = plans?.find(
                (item: any) => item.id === params.id
            ) as {
                id: string;
                value: string;
                cartId: string;
                type: string;
                planId: string;
            };

            if (
                planToDelete?.type === CART_TYPES.Labreport &&
                !isStartInvoice
            ) {
                const labReports = treatmentGetValues('objective.labReports');
                console.log('Deleting plan with cartId:', planToDelete.cartId);
                console.log('Available lab reports:', labReports);
                const labReport = labReports.find(
                    (item: any) => item.cartId === planToDelete.cartId
                );
                console.log('Found lab report:', labReport);
                
                // Check if labReport exists before accessing its properties
                if (labReport && labReport.integrationType === INTEGRATION_TYPE.IDEXX) {
                    const idexxOrderId = labReport.idexxOrderId;
                    // We have to cancel the idexx order
                    const itemData = {
                        patientId: patientId,
                        appointmentId: getAppointmentId(), // appointment id
                        clinicLabReportId: labReport.clinicLabReportId, // lab report entry id in clinic_lab_reports table
                        clinicId: CLINIC_ID,
                        idexxOrderId: idexxOrderId,
                        cartId: labReport.cartId,
                    };
                    setShowIdexxCreationModalText('Cancelling idexx order...');
                    setShowIdexxCreation(true);
                    const response = await cancelIdexxOrderUtil({
                        itemData,
                        cancelIdexxOrderMutation,
                        // callBackOnCancelIdexxOrder,
                    });

                    if (!response.status) {
                        setShowIdexxCreationModalText(
                            'Order cancellation failed'
                        );
                        setShowIdexxCreation(true);
                        return;
                    }
                    // need to fix the delete order flow once the api starts working
                    else {
                        setShowIdexxCreation(false);
                        objectiveProps.handleDeleteDiagnostic(
                            planToDelete.planId
                        );
                    }
                }
                
                // Check if labReport exists and has files or diagnostic notes before checking for deletion modal
                if (
                    labReport &&
                    ((labReport.files && labReport.files.length > 0) ||
                        getDiagnosticNotesForLabReport(labReport).length > 0) &&
                    !showDeleteDiagnosticsModal
                ) {
                    setSelectedPlanId(params.id);
                    return setShowDeleteDiagnosticModal(true);
                } else {
                    // Handle normal deletion when no files/notes or when modal is not shown
                    const filteredPlans = plans?.filter(
                        (item) => item.id !== params.id
                    );

                    treatmentSetValue('plans.list', filteredPlans, {
                        shouldValidate: true,
                    });
                    
                    // Use labReport.cartId if available, otherwise use planToDelete.cartId as fallback
                    const cartIdToDelete = labReport?.cartId || planToDelete.cartId;
                    objectiveProps.handleDeleteDiagnostic(cartIdToDelete);
                    setShowDeleteDiagnosticModal(false);
                }
            } else {
                const filteredPlans = plans?.filter(
                    (item) => item.id !== params.id
                );

                treatmentSetValue('plans.list', filteredPlans, {
                    shouldValidate: true,
                });
                saveAppoinmentDetails('PDT_2018');
                planToDelete.cartId &&
                    deleteFromCartMutation.mutate({
                        cartId: planToDelete.cartId,
                        source: 'plan',
                    });
            }
        },
        onPriceChange: (params: any) => {
            handlePlanPriceChange({
                getValues: treatmentGetValues,
                setValue: treatmentSetValue,
                params,
                updateCartDetailsMutation,
            });
        },
        isShowPlanTableDefault: true,
    };

    const prescriptionProps = {
        onPrescriptionChange: (value: string) => {},
        onPrescriptionBlur: (value: string) => {
            saveAppoinmentDetails('PDT_2039');
        },
        onAddRowTreatment: () => {
            const prescList = treatmentGetValues('prescription.list');
            prescList?.push({
                id: uuidv4(),
                name: '',
                qty: 0,
                brand: '',
                type: '',
                dosage: '',
                comment: '',
                isLongTerm: false,
                isRestricted: false,
            });
            treatmentSetValue('prescription.list', prescList, {
                shouldValidate: true,
            });
            // saveAppoinmentDetails();
        },
        onDelete: (params: { id: string }) => {
            const presc = treatmentGetValues('prescription.list');
            const prescToDelete = presc?.find(
                (item: any) => item.id === params.id
            ) as {
                id: string;
                value: string;
                cartId: string;
            };

            const filteredPresc = presc?.filter(
                (item) => item.id !== params.id
            );
            treatmentSetValue('prescription.list', filteredPresc, {
                shouldValidate: true,
            });
            saveAppoinmentDetails('PDT_2076');
            prescToDelete.cartId &&
                deleteFromCartMutation.mutate({
                    cartId: prescToDelete.cartId,
                    source: 'prescription',
                });
        },
        isShowPrescriptionTableDefault: true,
    };

    const attachmentProps = {
        handleFileSelect: (
            files: File[],
            updateUploadProgress: (fileName: string, progress: number) => void
        ) => {
            handleAssesmentFileChangeUtility({
                files,
                saveAppoinmentDetails,
                setFileSizeExceededMessage,
                treatmentGetValues,
                treatmentSetValue,
                uploadMediaToS3,
                onUploadProgress: updateUploadProgress,
            });
        },
        handleFileDelete: (file: File) => {
            handleAssessmentFileDeleteUtility({
                item: file,
                deleteFromS3Mutation,
                saveAppoinmentDetails,
                treatmentGetValues,
                treatmentSetValue,
            });
        },
    };
    const saveData = () => {};
    const handleCartBack = () => {
        setShowCart(false);
        createTreatmentTab(false);
    };

    //===============================
    // To get balance
    const currentMainBalance: number =
        patientDetailsData?.data?.patientOwners[0].ownerBrand.ownerCredits ?? 0;

    const [paymentNote, setPaymentNote] = useState('');

    //invoice
    //can be moved
    const createEntryForInvoiceInPaymentDetailsForInvoice = async (
        invoiceId: string,
        invoiceType: CREDIT_TYPES,
        amountPaid: number,
        isAdjustmentInInvoice: boolean,
        creditAmountUsed: number
    ) => {
        let currentTransaciton: number = 0;

        currentTransaciton = Number(amountPaid) - Number(dFinalPrice);

        let dFinalMainBalance: number = 0;

        if (isAdjustmentInInvoice) {
            if (Number(dFinalPrice) == 0) {
                // If 0, credit amount is adjusted
                dFinalMainBalance =
                    Number(currentMainBalance) -
                    Number(dTotalCredit) +
                    Number(amountPaid);
            } else {
                dFinalMainBalance = Number(currentTransaciton);
            }
        } else {
            dFinalMainBalance =
                Number(currentMainBalance) + Number(currentTransaciton);
        }

        const trimmedNote = paymentNote?.trim();

        const data = {
            patientId: patientId,
            amount: Number(amountPaid),
            type: invoiceType,
            paymentType: paymentMode,
            amountPayable: Number(dFinalPrice),
            mainBalance: Number(dFinalMainBalance),
            invoiceId: invoiceId,
            previousBalance: Number(currentMainBalance),
            transactionAmount: Number(dInvoiceAmount),
            ownerId: patientDetailsData?.data?.patientOwners[0].ownerId,
            isCreditUsed: isAdjustmentInInvoice,
            creditAmountUsed: isAdjustmentInInvoice
                ? Number(creditAmountUsed)
                : 0,
            ...(trimmedNote ? { paymentNotes: trimmedNote } : {}),
        };

        // Await the promise from the modified createPaymentDetailsCall
        await createPaymentDetailsCall(createPaymentDetailsMutation, data);
    };

    // const createEntryForInvoiceInPaymentDetailsForRefund = (
    //     invoiceId: string,
    //     invoiceType: CREDIT_TYPES,
    //     amountPaid: number,
    //     amountPayable: number,
    //     currentPaymentMode: string
    // ) => {
    //     let currentTransaciton: number = 0;

    //     currentTransaciton = amountPaid - amountPayable;

    //     let dFinalMainBalance = 0;

    //     dFinalMainBalance = currentMainBalance - currentTransaciton;

    //     const data = {
    //         patientId: patientId,
    //         amount: amountPaid,
    //         type: invoiceType,
    //         paymentType: currentPaymentMode,
    //         amountPayable: amountPayable,
    //         mainBalance: dFinalMainBalance,
    //         invoiceId: invoiceId,
    //     };

    //     createPaymentDetailsCall(createPaymentDetailsMutation, data);
    // };

    //complete treatment
    const [isSubmittingPayment, setIsSubmittingPayment] = useState(false);

    // Create debounced version of actOnConfirmClick
    const debouncedActOnConfirmClick = useMemo(
        () =>
            debounce(
                async (
                    setIsShowPaymentCompleted?: any,
                    setIsShowPaymentSuccess?: any
                ) => {
                    // Prevent multiple submissions
                    if (isSubmittingPayment) return;

                    try {
                        setIsSubmittingPayment(true);
                        let amountPaid: number = 0;
                        let dAddAdjustment: number = 0;
                        let cartId: string = '';

                        if (cartListData?.data?.length) {
                            if (cartListData.data?.length > 0) {
                                cartId = cartListData.data[0]?.cartId;
                            }
                        }

                        // Use manual payment amount (0 by default, or user-entered amount)
                        // Never auto-calculate with dFinalPrice
                        amountPaid = manualPaymentAmount || 0;

                        dAddAdjustment = isAdjustmentInInvoice
                            ? dAdjustmentAmount
                            : 0;

                        // const waitTillSuccess = () => new Promise((res) => {
                        //     createEMRMutation.mutate({
                        //         appointmentId: treatmentAppointment.id,
                        //         onSuccess: () => {
                        //             res('ok');
                        //         }
                        //     });
                        // })

                        // await waitTillSuccess();

                        if (
                            cartListData?.data?.length <= 0 &&
                            amountPaid <= 0 &&
                            dFinalPrice <= 0 &&
                            treatmentGetValues('prescription.notes').trim()
                                .length <= 0
                        ) {
                            updateAppointmentStatusMutation.mutate({
                                appointmentId: getAppointmentId(),
                                status: EnumAppointmentStatus.Completed,
                                userId: auth?.userId, // Added userId
                            });
                            setTreatmentAppointment(null);
                            setIsShowPaymentCompleted &&
                                setIsShowPaymentCompleted(false);
                            setIsShowPaymentSuccess
                                ? setIsShowPaymentSuccess(true)
                                : router.push('/patients');
                        } else {
                            // Build cart-to-lab-report mapping from appointment lab reports data
                            const labReports = treatmentGetValues('objective.labReports') || [];
                            const cartToLabReportMapping: Record<string, string> = {};
                            
                            labReports.forEach((labReport: any) => {
                                if (labReport.cartId && labReport.labReportId) {
                                    cartToLabReportMapping[labReport.cartId] = labReport.labReportId;
                                }
                            });

                            // Prepare invoice metadata with cart-to-lab-report mapping
                            const invoiceMetadata: Record<string, any> = {};
                            
                            // Add cart-to-lab-report mapping if available
                            if (Object.keys(cartToLabReportMapping).length > 0) {
                                invoiceMetadata.cartToLabReportMapping = cartToLabReportMapping;
                            }
                            
                            // Add creation context
                            invoiceMetadata.createdFrom = 'appointment-flow';
                            invoiceMetadata.createdAt = new Date().toISOString();
                            
                            const invoiceData = {
                                discount: discountValue,
                                totalPrice: dTotalPrice,
                                totalTax: dTotalTaxedAmount,
                                totalCredit: dTotalCredit,
                                totalDiscount: dTotalDiscountAmount,
                                amountPayable: dFinalPrice,
                                invoiceAmount: dInvoiceAmount,
                                paymentMode: paymentMode,
                                priceAfterDiscount: dTotalPriceAfterDiscount,
                                dischargeInstructions:
                                    treatmentGetValues('prescription.notes'),
                                appointmentId: getAppointmentId(),
                                ownerId:
                                    patientDetailsData?.data?.patientOwners[0]
                                        .ownerId,
                                details: getInvoiceDetails({
                                    cartListData,
                                    dDiscountToBeApplied: discountValue,
                                    dFinalPrice,
                                    dTotalDiscountAmount,
                                    dTotalPrice,
                                    dTotalPriceAfterDiscount,
                                    dTotalTaxedAmount,
                                    dAddAdjustment,
                                    prescriptionData:
                                        treatmentGetValues('prescription.list'),
                                }),
                                prescriptionDetails: getPrescriptionDetails(
                                    treatmentGetValues('prescription.list')
                                ),
                                cartId: cartId,
                                patientId: patientId,
                                invoiceType: INVOICE_TYPES.Invoice,
                                metadata: invoiceMetadata, // Include the metadata with cart-to-lab-report mapping
                                isCreditUsed: isAdjustmentInInvoice,
                                creditAmountUsed: isAdjustmentInInvoice
                                    ? Number(currentMainBalance)
                                    : 0,
                            };

                            const paymentData = {
                                patientId: patientId,
                                amount: Number(amountPaid),
                                type: CREDIT_TYPES.Invoice,
                                paymentType: paymentMode,
                                amountPayable: Number(dFinalPrice),
                                mainBalance:
                                    Number(currentMainBalance) +
                                    (Number(amountPaid) - Number(dFinalPrice)),
                                previousBalance: Number(currentMainBalance),
                                transactionAmount: Number(dInvoiceAmount),
                                ownerId:
                                    patientDetailsData?.data?.patientOwners[0]
                                        .ownerId,
                                isCreditUsed: isAdjustmentInInvoice,
                                creditAmountUsed: isAdjustmentInInvoice
                                    ? Number(currentMainBalance)
                                    : 0,
                                ...(paymentNote?.trim()
                                    ? { paymentNotes: paymentNote.trim() }
                                    : {}),
                            };

                            createInvoiceWithPaymentMutation.mutate(
                                {
                                    data: {
                                        appointmentId: getAppointmentId(),
                                        invoiceData: invoiceData,
                                        paymentData: paymentData,
                                    },
                                },
                                {
                                    onSuccess: (data: any) => {
                                        // Success - invoice, payment, and appointment completion all done atomically
                                        setTreatmentAppointment(null);
                                        setIsShowPaymentCompleted &&
                                            setIsShowPaymentCompleted(false);
                                        setIsShowPaymentSuccess
                                            ? setIsShowPaymentSuccess(true)
                                            : router.replace(
                                                  `/patients/${patientId}/details`
                                              );
                                    },
                                    onError: (error) => {
                                        console.error(
                                            'Atomic invoice creation failed:',
                                            error
                                        );
                                    },
                                }
                            );
                        }
                    } catch (error) {
                        console.error('Payment processing failed:', error);
                    } finally {
                        setIsSubmittingPayment(false);
                    }
                },
                1000,
                { leading: true, trailing: false }
            ),
        [
            isSubmittingPayment,
            cartListData,
            dFinalPrice,
            dAdjustmentAmount,
            isAdjustmentInInvoice,
            treatmentGetValues,
            createInvoiceMutation,
            createInvoiceWithPaymentMutation,
            router,
            paymentNote,
        ]
    );

    // Add cleanup for debounced function
    useEffect(() => {
        return () => {
            debouncedActOnConfirmClick.cancel();
        };
    }, [debouncedActOnConfirmClick]);

    //cart amount func
    const calculateAmount = (cartListData: any[]) => {
        let totalPrice = 0;
        let totalDiscountAmount = 0;
        let totalPriceAfterDiscount = 0;
        let totalTaxedAmount = 0;
        let finalPrice = 0;
        let invoiceAmount = 0;

        const options = cartListData?.data?.filter(
            (item: any) => item.isAddedToCart === true
        );
        if (options?.length) {
            const result = options.forEach((item: any) => {
                const rowData = createCartRowItemData(item);
                const quantity = rowData?.quantity || 0;
                const price = rowData?.itemPrice || 0;
                const tax = rowData?.tax || 0;
                const itemTotalPrice = quantity * price;

                totalPrice += itemTotalPrice;

                let discountedPrice = itemTotalPrice;
                let discountAmount = 0;
                if (discountValue > 0) {
                    discountAmount = ((price * discountValue) / 100) * quantity;
                    discountedPrice = itemTotalPrice - discountAmount;
                }
                totalDiscountAmount += discountAmount;
                totalPriceAfterDiscount += discountedPrice;

                const taxAmount = (discountedPrice * tax) / 100;
                totalTaxedAmount += taxAmount;

                finalPrice += discountedPrice + taxAmount;
                invoiceAmount = finalPrice;
            });

            let dAdjustCredit: number = currentMainBalance;
            if (isAdjustmentInInvoice) {
                // If there is adjustment to be done in the invoice
                if (dAdjustmentAmount > finalPrice) {
                    dAdjustCredit = finalPrice;
                    finalPrice = 0;
                } else {
                    finalPrice -= currentMainBalance;
                }
                setAdjustmentAmount(currentMainBalance);
            } else {
                dAdjustCredit = 0;
            }

            setTotalPrice(totalPrice);
            setTotalDiscountAmount(totalDiscountAmount);
            setTotalPriceAfterDiscount(totalPriceAfterDiscount);
            setTotalTaxedAmount(totalTaxedAmount);
            setFinalPrice(finalPrice);
            setTotalCredit(dAdjustCredit);
            setInvoiceCollectedAmount(finalPrice);
            setInvoiceAmount(invoiceAmount);

            return [
                {
                    label: 'Total Price',
                    value: dTotalPrice,
                },
                {
                    label: 'Taxes',
                    value: dTotalTaxedAmount,
                },
                // {
                //     label: currentMainBalance < 0 ? 'Pending Amount' : 'Credit',
                //     value: isAdjustmentInInvoice
                //         ? Number(dAdjustCredit.toString().replace('-', ''))
                //         : 0,
                // },
                {
                    label: 'Discount',
                    value: dTotalDiscountAmount,
                },
            ];
        } else {
            let dAdjustCredit: number = currentMainBalance;

            if (isAdjustmentInInvoice) {
                // If there is adjustment to be done in the invoice
                if (dAdjustmentAmount > finalPrice) {
                    dAdjustCredit = finalPrice;
                    finalPrice = 0;
                } else {
                    finalPrice -= currentMainBalance;
                }
                setAdjustmentAmount(currentMainBalance);
            } else {
                dAdjustCredit = 0;
            }

            setTotalPrice(totalPrice);
            setFinalPrice(finalPrice);
            setTotalCredit(dAdjustCredit);
            setInvoiceCollectedAmount(finalPrice);
            setInvoiceAmount(invoiceAmount);
            return [
                {
                    label: 'Total Price',
                    value: '-',
                },
                {
                    label: 'Taxes',
                    value: '-',
                },
                // {
                //     label: currentMainBalance < 0 ? 'Pending Amount' : 'Credit',
                //     value: isAdjustmentInInvoice
                //         ? dAdjustCredit >= 0
                //             ? '-'
                //             : Number(dAdjustCredit.toString().replace('-', ''))
                //         : '-',
                // },
                {
                    label: 'Discount',
                    value: '-',
                },
            ];
        }
    };

    //cart effects

    useEffect(() => {
        if (showCart) createTreatmentTab(true);
    }, [discountValue, paymentMode]);
    useEffect(() => {
        if (showCart) {
            createTreatmentTab(true);
        }
    }, [cartListData, showCart, treatmentWatch('invoiceAmount')]);

    useEffect(() => {
        const floatNumber: string = Number(dFinalPrice).toFixed(2);
        treatmentSetValue('invoiceAmount', floatNumber);
    }, [dFinalPrice]);

    const memoizedInvoiceSummary = useMemo(() => {
        return calculateAmount(cartListData);
    }, [cartListData, discountValue]);

    useEffect(() => {
        setActiveAppointment(getActiveAppointment(appointmentsData));
    }, [appointmentsData]);

    useEffect(() => {
        if (
            treatmentAppointment &&
            (treatmentAppointment?.status ===
                EnumAppointmentStatus?.ReceivingCare ||
                treatmentAppointment?.status ===
                    EnumAppointmentStatus.Checkedout)
        ) {
            createTreatmentTab(showCart);
        }
    }, [
        dTotalPrice,
        dTotalDiscountAmount,
        dTotalPriceAfterDiscount,
        dTotalTaxedAmount,
        dFinalPrice,
        showCart,
    ]);

    // Handle cartState updates
    useEffect(() => {
        if (cartState) {
            // console.log('Updating local state from cartState:', cartState);

            // Only update if values are different
            if (isAdjustmentInInvoice !== cartState.isAdjustmentInInvoice) {
                setIsAdjustmentInInvoice(cartState.isAdjustmentInInvoice);
                if (cartState.isAdjustmentInInvoice) {
                    setAdjustmentAmount(currentMainBalance);
                } else {
                    setAdjustmentAmount(0);
                }
            }

            // Only update if discount values are different
            if (
                JSON.stringify(discountObect) !==
                JSON.stringify(cartState.discountObject)
            ) {
                setDiscountObject(cartState.discountObject);
                setDiscountValue(Number(cartState.discountObject.value));
            }

            if (
                treatmentGetValues('invoiceAmount') !== cartState.invoiceAmount
            ) {
                setInvoiceCollectedAmount(cartState.invoiceAmount);
                treatmentSetValue('invoiceAmount', cartState.invoiceAmount);
            }
        }
    }, [cartState]);

    // ============================================================================
    // CART ACTION DELEGATE HANDLERS
    // These functions bridge cart actions with existing plan logic
    // ============================================================================

    /**
     * Helper function to get the latest appointment details before performing cart operations
     * This ensures we have fresh data and avoid working with stale state
     */
    const getLatestAppointmentDetails = async (): Promise<any> => {
        try {
            // Import the required services and query client
            const { getLatestPatientAppointments } = await import('../services/appointment.service');
            
            // Make fresh API call to get the latest appointment data
            const freshAppointmentsResponse = await getLatestPatientAppointments(patientId);
            
            if (!freshAppointmentsResponse?.data || freshAppointmentsResponse.data.length === 0) {
                console.warn('No appointments found for patient');
                return null;
            }

            // Get the current treatment appointment from the fresh data
            const currentTreatmentAppointment = getTreatmentAppointment(freshAppointmentsResponse.data);
            
            if (!currentTreatmentAppointment) {
                console.warn('No treatment appointment found in fresh data');
                return null;
            }

            // Ensure the form has the latest appointment details from the API
            const latestDetails = currentTreatmentAppointment?.appointmentDetails?.details;
            if (latestDetails) {
                // Reset form with the latest server data to ensure we have fresh state
                console.log('Resetting form with fresh appointment details');
                treatmentReset(latestDetails);
            }

            return currentTreatmentAppointment;
        } catch (error) {
            console.error('Error fetching latest appointment details from API:', error);
            
            // Fallback to current state data if API call fails
            const currentTreatmentAppointment = getTreatmentAppointment(appointmentsData.data);
            if (currentTreatmentAppointment) {
                const latestDetails = currentTreatmentAppointment?.appointmentDetails?.details;
                if (latestDetails) {
                    treatmentReset(latestDetails);
                }
                return currentTreatmentAppointment;
            }
            
            return treatmentAppointment; // Final fallback
        }
    };

    /**
     * Handles adding items to cart via plan logic
     * Maps cart item data to plan function requirements and calls appropriate plan add function
     */
    const handleAddItemToCartViaPlanLogic = async (item: any) => {
        try {
            // First, get the latest appointment details to ensure we have fresh data
            const latestAppointment = await getLatestAppointmentDetails();
            if (!latestAppointment) {
                console.error('Cannot add item to cart: No valid appointment found');
                return;
            }

            if (
                item.type === CART_TYPES.Labreport ||
                item.itemType === 'Labreport'
            ) {
                // Transform cart item to match plan item structure
                const planItem = {
                    ...item,
                    type: CART_TYPES.Labreport,
                    id: item.labReportId || item.inventoryId || item.id,
                    name: item.itemDetails?.name || item.name,
                    title: item.itemDetails?.name || item.name,
                    chargeablePrice: item.price || item.itemPrice || 0,
                    integrationCode: item.integrationCode || '',
                    integrationType: item.integrationType || '',
                };

                // Use the existing onPlanMenuClick function which has the complete IDEXX flow
                // This includes missing info check, IDEXX order creation, lab reports update, etc.
                await onPlanMenuClick(planItem, true);
            } else {
                // First, ensure there's an empty plan item available
                const plansList = treatmentGetValues('plans.list') || [];
                const hasEmptyItem = plansList.find(
                    (plan: any) => plan?.name === ''
                );

                if (!hasEmptyItem) {
                    // Create an empty plan item first
                    const emptyPlanItem = {
                        id: `temp_${Date.now()}`,
                        name: '',
                        qty: 1,
                        price: 0,
                        brand: '',
                        type: '',
                        dosage: '',
                    };

                    const updatedPlansList = [...plansList, emptyPlanItem];
                    treatmentSetValue('plans.list', updatedPlansList, {
                        shouldValidate: true,
                    });
                }

                // Transform the item to match what onPlanMenuClick expects
                const planItem = {
                    ...item,
                    type: item.itemType || item.type, // Use itemType if type is empty
                    chargeablePrice: item.price || item.itemPrice || 0,
                    title: item.name,
                    qty: item.quantity || 1, // Ensure quantity is properly mapped
                    // Ensure we have the required fields for plan menu click
                };

                await onPlanMenuClick(planItem, false);
            }
        } catch (error) {
            console.error('Error adding item to cart via plan logic:', error);
            // Fallback to direct cart addition
            await actOnAddMoreToCartClick({
                item,
                addToCartMutation,
                appointmentId: getAppointmentId(),
                updateCartDetailsMutation,
            });
        }

        // Reset payment amount when item is added to cart
        resetPaymentAmount();
    };

    /**
     * Handles removing items from cart via plan logic
     * Finds the corresponding plan item and calls appropriate plan delete function
     */
    const handleRemoveItemFromCartViaPlanLogic = async (
        cartItemId: string,
        itemType: string
    ) => {
        try {
            // First, get the latest appointment details to ensure we have fresh data
            const latestAppointment = await getLatestAppointmentDetails();
            if (!latestAppointment) {
                console.error('Cannot remove item from cart: No valid appointment found');
                return;
            }

            // Find the plan item that corresponds to this cart item
            const plans = treatmentGetValues('plans.list') || [];
            const planItem = plans.find((plan: any) => plan.cartId === cartItemId);

            if (planItem) {
                // Use existing plan delete logic
                await planProps.onDelete({ id: planItem.id });
            } else {
                // If not found in plans, check prescriptions
                const prescriptions = treatmentGetValues('prescription.list') || [];
                const prescriptionItem = prescriptions.find(
                    (presc: any) => presc.cartId === cartItemId
                );

                if (prescriptionItem) {
                    await prescriptionProps.onDelete({ id: prescriptionItem.id });
                } else {
                    // If not found in either, check lab reports
                    const labReports =
                        treatmentGetValues('objective.labReports') || [];
                    const labReportItem = labReports.find(
                        (report: any) => report.cartId === cartItemId
                    );

                    if (labReportItem && objectiveProps.handleDeleteDiagnostic) {
                        await objectiveProps.handleDeleteDiagnostic(
                            labReportItem.id
                        );
                    } else {
                        // Fallback to direct cart deletion
                        deleteFromCart(
                            cartItemId,
                            deleteFromCartMutation,
                            itemType,
                            updateCartDetailsMutation
                        );
                    }
                }
            }
        } catch (error) {
            console.error('Error removing item from cart via plan logic:', error);
        }

        // Reset payment amount when item is removed from cart
        resetPaymentAmount();
    };

    /**
     * Handles updating cart item quantity via plan logic
     * Finds the corresponding plan item and calls appropriate plan quantity update function
     */
    const handleUpdateCartItemQuantityViaPlanLogic = async (
        cartItemId: string,
        newQuantity: number
    ) => {
        try {
            // First, get the latest appointment details to ensure we have fresh data
            const latestAppointment = await getLatestAppointmentDetails();
            if (!latestAppointment) {
                console.error('Cannot update cart item quantity: No valid appointment found');
                return;
            }

            // Find the plan item that corresponds to this cart item
            const plans = treatmentGetValues('plans.list') || [];
            const planItem = plans.find((plan: any) => plan.cartId === cartItemId);

            if (planItem) {
                // Use existing plan quantity change logic
                planProps.onQuantityChange({ id: planItem.id, qty: newQuantity });
            } else {
                // If not found in plans, check prescriptions
                const prescriptions = treatmentGetValues('prescription.list') || [];
                const prescriptionItem = prescriptions.find(
                    (presc: any) => presc.cartId === cartItemId
                );

                if (prescriptionItem) {
                    // Update prescription quantity in form state
                    const prescriptions =
                        treatmentGetValues('prescription.list') || [];
                    const updatedPrescriptions = prescriptions.map((presc: any) =>
                        presc.id === prescriptionItem.id
                            ? { ...presc, qty: newQuantity }
                            : presc
                    );
                    treatmentSetValue('prescription.list', updatedPrescriptions, {
                        shouldValidate: true,
                    });

                    // Update cart quantity
                    updateQuantityToCart(
                        cartItemId,
                        newQuantity,
                        updateCartDetailsMutation
                    );
                } else {
                    // Fallback to direct cart update
                    updateQuantityToCart(
                        cartItemId,
                        newQuantity,
                        updateCartDetailsMutation
                    );
                }
            }
        } catch (error) {
            console.error('Error updating cart item quantity via plan logic:', error);
        }

        // Reset payment amount when cart item quantity is updated
        resetPaymentAmount();
    };

    const createTreatmentTab = (showCart: boolean) => {
        //  treatmentReset();
        if (showCart) {
            setTabs([
                {
                    className: '',
                    id: 'treatment',
                    isDisabled: false,
                    label: 'Treatment',
                    tabContent: (
                        <TabTreatmentCart
                            isAdjustmentInInvoice={isAdjustmentInInvoice}
                            onChangeAdjustInvoice={(value: boolean) => {
                                setIsAdjustmentInInvoice(value);
                                emitCartChange('updateState', null, {
                                    isAdjustmentInInvoice: value,
                                });
                                if (value)
                                    setAdjustmentAmount(currentMainBalance);
                                else {
                                    setAdjustmentAmount(0);
                                }
                                // Reset payment amount when adjustment setting changes
                                resetPaymentAmount();
                            }}
                            control={treatmentControl}
                            addProductPrescriptionList={addProductList}
                            cartProps={{
                                cartItems: cartItemsAddedToCart({
                                    cartListData,
                                }),
                                onQuantityChange: async (value) => {
                                    // Use delegate handler to ensure plan state sync
                                    await handleUpdateCartItemQuantityViaPlanLogic(
                                        value.id,
                                        value.qty
                                    );
                                },
                                onRemoveCartItem: async (value, itemType) => {
                                    // Use delegate handler to ensure plan state sync
                                    await handleRemoveItemFromCartViaPlanLogic(
                                        value,
                                        itemType
                                    );
                                },
                            }}
                            discountValue={discountObect}
                            invoiceSummary={memoizedInvoiceSummary}
                            onCartContinue={(
                                setIsShowPaymentCompleted: any,
                                setIsShowPaymentSuccess: any
                            ) =>
                                debouncedActOnConfirmClick(
                                    setIsShowPaymentCompleted,
                                    setIsShowPaymentSuccess
                                )
                            }
                            totalPrice={dTotalPrice}
                            onChangeAmount={(amount: number) => {
                                setManualPaymentAmount(amount); // Store user-entered amount
                                setInvoiceCollectedAmount(amount);
                                treatmentSetValue('invoiceAmount', amount);
                                emitCartChange('updateState', null, {
                                    invoiceAmount: amount,
                                });
                            }}
                            onDiscountChange={(value: any) => {
                                setDiscountValue(value.value);
                                setDiscountObject(value);
                                // calculateAmount(cartListData);
                                emitCartChange('updateState', null, {
                                    discountObject: value,
                                });
                                // Reset payment amount when discount changes
                                resetPaymentAmount();
                            }}
                            onPrescriptionClick={async (item) => {
                                await handleAddItemToCartViaPlanLogic(item);
                            }}
                            onSearchProduct={(value) => {
                                const newProductList = addProductList.filter(
                                    (item: any) =>
                                        item.name
                                            .toLowerCase()
                                            .includes(value.toLowerCase())
                                );
                                setAddProductList(newProductList);
                                createTreatmentTab(true);
                            }}
                            onSubmitMoveToCart={(data) => {
                                updateItemAddedFlagAndQuantityToCart(
                                    data.id,
                                    true,
                                    data.addQuantity,
                                    updateCartDetailsMutation
                                );
                            }}
                            outstandingBalance={{
                                onChangeAdjustInvoice: () => {},
                                previouslyPendingBalance: currentMainBalance,
                            }}
                            paymentType={paymentMode}
                            handleBack={handleCartBack}
                            prescriptionProps={{
                                onAddToCart: () => {},
                                prescriptionErrorMessage:
                                    checkPrescriptionRestricted(
                                        treatmentGetValues('prescription.list')
                                    ),
                                prescriptionItems:
                                    cartPrescriptionItemsYetToBeAddedToCart(
                                        cartListData
                                    ),
                            }}
                            totalPayableAmount={dFinalPrice}
                            invoiceAmount={dInvoiceAmount}
                            amountPaid={
                                manualPaymentAmount || 0  // Always use manual amount or 0, never auto-calculate
                            }
                            cartListData={cartListData}
                            calculateAmount={calculateAmount}
                            onPaymentModeChange={onPaymentModeChange}
                            paymentMode={paymentMode}
                            getValues={treatmentGetValues}
                            setIsShowPaymentCompleted={
                                setIsShowPaymentCompleted
                            }
                            appointmentId={treatmentGetValues()?.appointmentId || treatmentAppointment?.id}
                        />
                    ),
                },
            ]);
        } else {
            setTabs([
                {
                    className: '',
                    id: 'treatment',
                    isDisabled: false,
                    label: 'Treatment',
                    tabContent: (
                        <Treatment
                            control={treatmentControl}
                            errors={treatmentErrors}
                            getValues={treatmentGetValues}
                            setValue={treatmentSetValue}
                            watch={treatmentWatch}
                            register={treatmentRegister}
                            handleAddVital={() => {
                                saveAppoinmentDetails('PDT_2765');
                            }}
                            handleAddPhysicalExam={() => {
                                saveAppoinmentDetails('PDT_2768');
                            }}
                            handleAddUltrasoundExam={() => {
                                saveAppoinmentDetails('PDT_2771');
                            }}
                            handleAddBodyMap={() => {
                                const newBodyMap = {
                                    type: null,
                                    bodymapImage: null,
                                    notes: null,
                                    paths: null,
                                    image: null,
                                };

                                const updatedBodyMaps = [
                                    ...treatmentGetValues('objective.bodyMaps'),
                                    newBodyMap,
                                ];

                                treatmentSetValue(
                                    'objective.bodyMaps',
                                    updatedBodyMaps,
                                    {
                                        shouldValidate: true,
                                    }
                                );
                                saveAppoinmentDetails('PDT_2794');
                            }}
                            handleAddDiagnostic={() => {
                                saveAppoinmentDetails('PDT_2797');
                            }}
                            x={() => {}}
                            onDelete={() => {}}
                            prescriptionsTreatmentList={[]}
                            medicineDropdownProps={{}}
                            subjectiveProps={subjectiveProps}
                            objectiveProps={objectiveProps}
                            assessmentProps={assessmentProps}
                            planProps={planProps}
                            prescriptionProps={prescriptionProps}
                            appointmentFollowupProps={{}}
                            attachmentProps={attachmentProps}
                            plansDropDownProps={plansDropDownProps}
                            prescriptionDropDownProps={
                                prescriptionDropDownProps
                            }
                            checkoutButtonText={verticalScrollButtonText}
                            cartItems={cartListData?.data}
                            appointmentId={treatmentGetValues()?.appointmentId || treatmentAppointment?.id}
                            saveAppointmentDetails={saveAppoinmentDetails}
                            handleSoapxModal={handleSoapxModal}
                            patientDiagnosticReportData={
                                patientDiagnosticReportData
                            }
                            patientDiagnosticReportRefetch={
                                patientDiagnosticReportRefetch
                            }
                            patientDiagnosticNotesData={
                                patientDiagnosticNotesData
                            }
                            patientDiagnosticNotesRefetch={
                                patientDiagnosticNotesRefetch
                            }
                            setIsUpdateMedicalRecord={setIsUpdateMedicalRecord}
                            isUpdateMedicalRecord={isUpdateMedicalRecord}
                            handleUpdateMedicalRecord={
                                handleUpdateMedicalRecord
                            }
                            handleEditBack={handleEditBack}
                            setIsLoadingState={setIsLoadingState}
                        />
                    ),
                },
            ]);
        }
    };

    //set current treatment data
    useEffect(() => {
        if (appointmentsData.data && !treatmentAppointment) {
            setTreatmentAppointment(
                getTreatmentAppointment(appointmentsData.data)
            );
        }
    }, [appointmentsData]);

    const resetDefaultFields = (appointment: {
        appointmentDetails: { details: any };
    }) => {
        const detailsJson = appointment?.appointmentDetails?.details;
        treatmentReset(detailsJson);
    };

    // useEffect(() => {
    //     if (treatmentAppointment?.status) {
    //         setVerticalScrollButtonText(
    //             treatmentAppointment?.status ===
    //                 EnumAppointmentStatus.ReceivingCare
    //                 ? 'Ready to checkout'
    //                 : 'Continue check-out'
    //         );
    //         // (treatmentAppointment?.status !== EnumAppointmentStatus.Checkedin ||
    //         //     treatmentAppointment?.status !==
    //         //         EnumAppointmentStatus.Scheduled) &&
    //         //     createTreatmentTab(showCart);
    //     }
    // }, [treatmentAppointment]);

    useEffect(() => {
        if (
            (treatmentAppointment &&
                treatmentAppointment.status ===
                    EnumAppointmentStatus?.ReceivingCare) ||
            treatmentAppointment?.status === EnumAppointmentStatus.Checkedout
        ) {
            //resetDefaultFields(treatmentAppointment);
            setCartAppointmentId(getAppointmentId());
            createTreatmentTab(showCart);
        }
    }, [treatmentAppointment]);
    const updateAppointmentStatusMutation = useUpdateAppointmentStatus(
        null as unknown as AppointmentParams,
        patientId,
        null as unknown as AppointmentParams
    );

    // To create an entry for invoice in payment_details table
    const { createPaymentDetailsMutation } = useCreatePaymentDetailsMutation(
        1,
        10,
        '',
        'true'
    ); // Parameters passes here are place holders

    // const {
    //     createPatientVaccinationMutation,
    //     getPatientVaccinationMutation,
    //     updatePatientVaccinationMutation,
    // } = usePatientVaccinationMutation();

    //vaccination tab funcs
    //can be moved
    // const handleDeleteVaccinationFile = (id: string) => {
    //     const body = {
    //         reportUrl: null,
    //         urlMeta: null,
    //     };
    //     updatePatientVaccinationMutation.mutate({ id, body });
    //     setActiveVaccination((prev) => ({
    //         ...prev,
    //         ...{
    //             fileUrl: null,
    //             fileName: null,
    //             fileType: null,
    //         },
    //     }));
    // };

    // const [activeVaccination, setActiveVaccination] = useState({
    //     id: patientVaccinationData?.data[0]?.id,
    //     className: '',
    //     title: patientVaccinationData?.data[0]?.vaccineName,
    //     company: '',
    //     state: '',
    //     quantity: '',
    //     fileUrl: patientVaccinationData?.data[0]?.reportUrl,
    //     fileType: patientVaccinationData?.data[0]?.urlMeta?.fileType,
    //     fileName: patientVaccinationData?.data[0]?.urlMeta?.fileName,
    //     systemGenerated: patientVaccinationData?.data[0]?.systemGenerated,
    //     date: moment(patientVaccinationData?.data[0]?.vaccinationDate).format(
    //         'DD MMM YYYY'
    //     ),
    //     doctor: patientVaccinationData?.data[0]?.doctorName
    //         ? 'Dr ' + patientVaccinationData?.data[0]?.doctorName
    //         : null,
    //     onClickUploadFile: () => {
    //         setAddSingleFilePop(true);
    //         setUpdateVaccineFlag(true);
    //     },
    //     onDeleteFile: () =>
    //         handleDeleteVaccinationFile(patientVaccinationData?.data[0]?.id),
    // });

    // const vaccineCalender = transformApiDataToCalendarForVaccination(
    //     patientVaccinationData,
    //     setActiveVaccination,
    //     setAddSingleFilePop,
    //     setUpdateVaccineFlag,
    //     handleDeleteVaccinationFile,
    //     activeVaccination
    // );

    // const handleCloseAddVaccinationPop = () => {
    //     setAddVaccinationPop(false);
    //     setAddSingleFilePop(false);
    // };
    // const handleFileUpload = ({
    //     files,
    //     markAsCompleted,
    // }: FormValuesFileUpload) => {
    //     const maxFileSize = 5 * 1024 * 1024;
    //     if (files?.size > maxFileSize) {
    //         setFileUploadError('Selected file should less than 5 MB');
    //         return;
    //     }

    //     setVaccineUploadFile(files ?? {});
    //     if (updateVaccineFlag) {
    //         handleUpdateVaccination(files);
    //     }
    //     setAddSingleFilePop(false);
    //     setUpdateVaccineFlag(false);
    // };
    // const handleMediaUploadToAws = async (file: any) => {
    //     const fileKey = `/vaccineDocuments/${file.lastModified}/${file.name}`;
    //     const s3Response = await uploadMediaToS3.mutateAsync({
    //         file,
    //         fileKey,
    //     });

    //     const newFile = {
    //         s3Url: s3Response,
    //         fileName: file.name,
    //         fileSize: file.size,
    //         uploadDate: new Date().toISOString(),
    //         fileKey,
    //     };

    //     return newFile;
    // };

    // const handleUpdateVaccination = async (files: any) => {
    //     const uploadedFile = await handleMediaUploadToAws(files);
    //     const body = {
    //         reportUrl: uploadedFile.fileKey,
    //         urlMeta: {
    //             fileName: uploadedFile.fileName,
    //             fileType: uploadedFile?.fileName
    //                 ? categorizeFile(uploadedFile?.fileName)
    //                 : null,
    //         },
    //     };
    //     updatePatientVaccinationMutation.mutate({
    //         id: activeVaccination.id,
    //         body,
    //     });
    //     setActiveVaccination((prev) => ({
    //         ...prev,
    //         fileUrl: uploadedFile.fileKey,
    //         fileName: uploadedFile.fileName,
    //         fileType: uploadedFile.fileName
    //             ? categorizeFile(uploadedFile.fileName)
    //             : null,
    //     }));
    //     setVaccineUploadFile({});
    //     setUpdateVaccineFlag(false);
    // };

    // const handleSaveVaccination = async (data: any) => {
    //     let uploadedFile = null;
    //     if (vaccineUploadFile?.size) {
    //         uploadedFile = await handleMediaUploadToAws(vaccineUploadFile);
    //     }
    //     const body = {
    //         patientId,
    //         systemGenerated: false,
    //         doctorName: data?.doctorName,
    //         vaccineName: data?.vaccinationName,
    //         vaccinationDate: data?.vaccinationDate,
    //         reportUrl: uploadedFile?.fileKey,
    //         urlMeta: {
    //             fileName: uploadedFile?.fileName,
    //             fileType: uploadedFile?.fileName
    //                 ? categorizeFile(uploadedFile?.fileName)
    //                 : null,
    //         },
    //         onSuccess: (data: any) => {
    //             setActiveVaccination({
    //                 id: data.id,
    //                 className: '',
    //                 title: data.vaccineName,
    //                 company: '',
    //                 state: '',
    //                 quantity: '',
    //                 fileUrl: data.reportUrl,
    //                 systemGenerated: data.systemGenerated,
    //                 fileType: data?.urlMeta?.fileType,
    //                 fileName: data?.urlMeta?.fileName,
    //                 date: moment(data.vaccinationDate).format('DD MMM YYYY'),
    //                 doctor: data.doctorName,
    //                 onClickUploadFile: () => {
    //                     setAddSingleFilePop(true);
    //                     setUpdateVaccineFlag(true);
    //                 },
    //                 onDeleteFile: () => handleDeleteVaccinationFile(data.id),
    //             });
    //         },
    //     };

    //     const response = createPatientVaccinationMutation.mutate(body);

    //     setVaccineUploadFile({});
    //     handleCloseAddVaccinationPop();
    // };

    //diagnostics tab funcs
    //can be moved
    // const [activeDiagnosticReport, setActiveDiagnosticReport] = useState({
    //     integrationType:
    //         patientDiagnosticReportData?.data[0]?.integrationOrderId?.length > 0
    //             ? INTEGRATION_TYPE.IDEXX
    //             : '',
    //     id: patientDiagnosticReportData?.data[0]?.id,
    //     className: '',
    //     title: patientDiagnosticReportData?.data[0]?.clinicLabReport?.name,
    //     company: '',
    //     state: '',
    //     quantity: '',
    //     clinicId: patientDiagnosticReportData?.data[0]?.clinicId,
    //     labReportId: patientDiagnosticReportData?.data[0]?.id,
    //     clinicLabReportId:
    //         patientDiagnosticReportData?.data[0]?.clinicLabReportId,
    //     appointmentId: patientDiagnosticReportData?.data[0]?.appointment?.id,
    //     files: patientDiagnosticReportData?.data[0]?.files.map((file: any) => {
    //         return {
    //             fileLink: file.fileKey,
    //             fileName: file.fileName,
    //             fileId: file.id,
    //         };
    //     }),
    //     systemGenerated: 'false',
    //     fileType: '',
    //     // fileName: patientDiagnosticReportData?.data[0]?.files.map((file: any) => file.fileName),
    //     date: patientDiagnosticReportData?.data[0]?.createdAt,
    //     doctor: `${patientDiagnosticReportData?.data[0]?.appointment?.appointmentDoctors[0]?.doctor.firstName} ${patientDiagnosticReportData?.data[0]?.appointment?.appointmentDoctors[0]?.doctor?.lastName}`,
    // });

    // const diagnosticCalender = transformApiDataToCalendarForDiagnostic(
    //     patientDiagnosticReportData,
    //     setActiveDiagnosticReport,
    //     activeDiagnosticReport
    // );

    // const handleFileUploadForDiagnosticTab = async (
    //     event: React.ChangeEvent<HTMLInputElement>
    // ) => {
    //     const files = event.files;
    //     if (
    //         files &&
    //         files.length > 0 &&
    //         activeDiagnosticReport.clinicLabReportId &&
    //         activeDiagnosticReport.appointmentId
    //     ) {
    //         const newFiles: any[] = [];
    //         for (let i = 0; i < files.length; i++) {
    //             const file = files[i];
    //             if (file.size > MAX_FILE_SIZE) {
    //                 console.log(
    //                     `File "${file.name}" exceeds the 5MB size limit.`
    //                 );
    //                 continue;
    //             }

    //             const fileKey = `/labReports/${activeDiagnosticReport.appointmentId}/${activeDiagnosticReport.clinicLabReportId}/${file.name}`;
    //             const s3Response = await uploadMediaToS3.mutateAsync({
    //                 file,
    //                 fileKey,
    //             });

    //             const newFile = {
    //                 s3Url: s3Response,
    //                 fileName: file.name,
    //                 fileSize: file.size,
    //                 uploadDate: new Date().toISOString(),
    //                 fileKey,
    //             };
    //             newFiles.push(newFile);
    //         }

    //         try {
    //             const labReportResponse =
    //                 await createLabReportMutation.mutateAsync({
    //                     appointmentId: activeDiagnosticReport.appointmentId,
    //                     clinicLabReportId:
    //                         activeDiagnosticReport.clinicLabReportId,
    //                     clinicId: activeDiagnosticReport.clinicId,
    //                     files: newFiles,
    //                     status: 'PENDING',
    //                     patientId,
    //                     onSuccess: (apiRes) => {
    //                         const uploadedfiles = apiRes?.data?.files?.map(
    //                             (list: any) => ({
    //                                 fileName: list.fileName,
    //                                 fileLink: list.fileKey,
    //                                 fileId: list.id,
    //                             })
    //                         );

    //                         setActiveDiagnosticReport((prev: any) => {
    //                             return {
    //                                 ...prev,
    //                                 files: uploadedfiles,
    //                             };
    //                         });
    //                     },
    //                 });

    //             const updatedLabReports = patientDiagnosticReportData?.data.map(
    //                 (report: any) => {
    //                     if (
    //                         report.clinicLabReportId ===
    //                         activeDiagnosticReport.clinicLabReportId
    //                     ) {
    //                         return {
    //                             ...report,
    //                             files: [...(report.files || []), ...newFiles],
    //                         };
    //                     }
    //                     return report;
    //                 }
    //             );

    //             const appointmentUpdateObject = appointmentsData.data.find(
    //                 (list: any) =>
    //                     list.id === activeDiagnosticReport.appointmentId
    //             );
    //             const objectJson =
    //                 appointmentUpdateObject.appointmentDetails.details;
    //             const labReport = objectJson.objective.labReports;
    //             const newLabReport = labReport.map((report: any) => {
    //                 if (
    //                     report.value ===
    //                     activeDiagnosticReport.clinicLabReportId
    //                 ) {
    //                     return {
    //                         ...report,
    //                         files: [...(report.files || []), ...newFiles],
    //                     };
    //                 }
    //                 return report;
    //             });
    //             const updatedObjectJson = {
    //                 ...objectJson,
    //                 objective: {
    //                     ...objectJson.objective,
    //                     labReports: newLabReport,
    //                 },
    //             };

    //             await updateAppointmentDetailsMutation.mutateAsync({
    //                 appointmentId: activeDiagnosticReport.appointmentId,
    //                 data: {
    //                     details: updatedObjectJson,
    //                 },
    //                 shouldReset: false,
    //             });
    //             await updateAppointmentDetails(
    //                 activeDiagnosticReport.appointmentId,
    //                 updatedLabReports
    //             );
    //         } catch (error) {
    //             console.log(
    //                 `Failed to upload and save file. Please try again.`,
    //                 error
    //             );
    //         }
    //     }
    // };
    // const handleDelete = async (labReportId: string, fileId: string) => {
    //     try {
    //         await deleteLabReportFileMutation.mutateAsync({
    //             labReportId,
    //             fileId,
    //         });

    //         setActiveDiagnosticReport((prev: any) => {
    //             return {
    //                 ...prev,
    //                 files: prev.files.filter(
    //                     (list: any) => list.fileId !== fileId
    //                 ),
    //             };
    //         });
    //         const removingFile = activeDiagnosticReport.files.filter(
    //             (list: any) => list.fileId === fileId
    //         );

    //         const appointmentUpdateObject = appointmentsData.data.find(
    //             (list: any) => list.id === activeDiagnosticReport.appointmentId
    //         );

    //         const objectJson =
    //             appointmentUpdateObject.appointmentDetails.details;
    //         const labReport = objectJson.objective.labReports;

    //         const newLabReport = labReport.map((report: any) => {
    //             if (report.value === activeDiagnosticReport.clinicLabReportId) {
    //                 return {
    //                     ...report,
    //                     files: report.files.filter(
    //                         (list: any) =>
    //                             list.fileKey !== removingFile[0].fileLink
    //                     ),
    //                 };
    //             }
    //             return report;
    //         });

    //         const updatedObjectJson = {
    //             ...objectJson,
    //             objective: {
    //                 ...objectJson.objective,
    //                 labReports: newLabReport,
    //             },
    //         };

    //         await updateAppointmentDetailsMutation.mutateAsync({
    //             appointmentId: activeDiagnosticReport.appointmentId,
    //             data: {
    //                 details: updatedObjectJson,
    //             },
    //             shouldReset: false,
    //         });
    //         // Update the appointment details in the database
    //         const updatedLabReports = patientDiagnosticReportData?.data.map(
    //             (report: any) =>
    //                 report.id === labReportId
    //                     ? {
    //                           ...report,
    //                           files: report.files.filter(
    //                               (file: any) => file.fileId !== fileId
    //                           ),
    //                       }
    //                     : report
    //         );
    //         await updateAppointmentDetails(
    //             activeDiagnosticReport.appointmentId,
    //             updatedLabReports
    //         );
    //         // refetch(); // Refresh the lab reports data
    //     } catch (error) {
    //         // setUploadError('Failed to delete file. Please try again.');
    //     }
    // };
    // const diagnosticHeader = {
    //     id: activeDiagnosticReport?.id,
    //     className: '',
    //     title: activeDiagnosticReport?.title,
    //     date: activeDiagnosticReport?.date,
    //     doctors: [activeDiagnosticReport?.doctor],
    //     files: activeDiagnosticReport.files,
    //     appointmentId: activeDiagnosticReport.appointmentId,
    //     labReportId: activeDiagnosticReport.labReportId,
    //     clinicLabReportId: activeDiagnosticReport.clinicLabReportId,
    //     clinicId: activeDiagnosticReport.clinicId,
    //     patientId,
    //     // fileName: activeDiagnosticReport.fileName,
    //     onClickUploadFile: (files: any) =>
    //         handleFileUploadForDiagnosticTab(files),
    //     onClickDeleteFile: (labReportId: string, fileId: string) =>
    //         handleDelete(labReportId, fileId),
    //     integrationType: activeDiagnosticReport.integrationType,
    // };

    // const handleCloseUploadDiagnosticModal = () => {
    //     setUploadDiagnosticReportPop(false);
    // };

    const addLongTermPerscriptionsToCart = async () => {
        const prescriptionIds = longTermMedication?.map(
            (item: any) => item?.medicationId
        );
        if (prescriptionIds.length > 0) {
            let response;
            let updatedPrescriptionData =
                treatmentGetValues('prescription.list');
            response = await bulkInsertToCart.mutateAsync({
                appointmentId: getAppointmentId(),
                prescriptionIds: prescriptionIds,
                addedFrom: cartItemAddedFromType.PRESCRIPTION,
            });

            // Update prescription list with cartId values from the response
            if (response?.data && Array.isArray(response.data)) {
                const currentPrescriptionList = treatmentGetValues('prescription.list');
                const updatedPrescriptionList = currentPrescriptionList.map((prescItem: any) => {
                    // Find matching cart item by prescriptionId
                    const matchingCartItem = response.data.find(
                        (cartItem: any) => cartItem.prescriptionId === prescItem.prescriptionId
                    );

                    if (matchingCartItem) {
                        return {
                            ...prescItem,
                            cartId: matchingCartItem.id
                        };
                    }
                    return prescItem;
                });

                // Update the prescription list with cartId values
                treatmentSetValue('prescription.list', updatedPrescriptionList, {
                    shouldValidate: true,
                });
            }
        }
        saveAppoinmentDetails && saveAppoinmentDetails('PDTT_3731');
    };

    //top bar appointment props
    // const beginTreatment = {
    //     id: latestAppointment?.id,
    //     treatmentData: {
    //         type: latestAppointment?.visitType,
    //         reason: latestAppointment?.reason,
    //         doctor:
    //             latestAppointment?.appointmentDoctors?.find(
    //                 (item: { primary: any }) => item.primary
    //             )?.doctor?.firstName ?? '',
    //         providers:
    //             latestAppointment?.appointmentDoctors
    //                 .filter(
    //                     (item: { primary: any; doctor: { firstName: any } }) =>
    //                         !item.primary && item?.doctor?.firstName
    //                 )
    //                 .map(
    //                     (item: {
    //                         primary: any;
    //                         doctor: { firstName: any };
    //                     }) => {
    //                         if (!item.primary) {
    //                             return item?.doctor?.firstName;
    //                         }
    //                     }
    //                 )
    //                 .join(', ') ?? '',
    //         weight: latestAppointment?.weight
    //             ? `${latestAppointment?.weight} kgs`
    //             : '--',
    //         room: latestAppointment?.room?.name,
    //     },
    //     scheduleData: {
    //         date: moment(latestAppointment?.date).format('DD MMMM YYYY'),
    //         time: moment(latestAppointment?.startTime).format('hh:mm A'),
    //         status: latestAppointment?.status,
    //         statusLabel: latestAppointment?.status,
    //         profile: '',
    //         name: '',
    //         owner: '',
    //         triage: latestAppointment?.triage,
    //     },
    //     handleMenu: (data: any, id: string) => {
    //         setValuesChanged(false);
    //         if (data.id === 'edit') {
    //             const foundAppintment = appointmentsData.data.find(
    //                 (item: any) => item.id === id
    //             );
    //             if (data.id === 'edit') {
    //                 setEditMode(true);
    //                 setDefaultValue({
    //                     data: foundAppintment,
    //                     getValues: appointmentGetValues,
    //                     setPatientData,
    //                     setSelectedAppointmentData,
    //                     setStep,
    //                     setValue: appointmentSetValue,
    //                     step,
    //                     viewMode: false,
    //                     patientRenderingData,
    //                 });
    //                 setAppointmentId(id);
    //                 setShowAppointmentModal(true);
    //             }
    //         }
    //         if (data.id === 'delete') {
    //             deleteAppointmentMutation.mutate(id);
    //         }
    //     },
    //     handleBeginTreatment: async (
    //         newStatus: EnumAppointmentStatus,
    //         id: string,
    //         isCheckout?: boolean
    //     ) => {
    //         if (isCheckout) {
    //             setShowCheckoutModal(true);
    //             return;
    //         }

    //         let soapPending = false;
    //         if (newStatus === EnumAppointmentStatus.ReceivingCare) {
    //             setCartAppointmentId(id);
    //             addLongTermPerscriptionsToCart();
    //             createTreatmentTab(false);
    //             treatmentRef.current = id;
    //         }

    //         if (newStatus === EnumAppointmentStatus.Checkedout) {
    //             soapPending = isCheckedModalCheckOut;
    //             saveAppoinmentDetails();
    //             //   return saveAppoinmentDetails();
    //             // setShowCart(true);
    //             // calculateAmount(cartListData);
    //             // createTreatmentTab(true);
    //         }
    //         if (newStatus === EnumAppointmentStatus.Completed && !showCart) {
    //             await updateAppointmentDetailsMutation.mutateAsync({
    //                 appointmentId: treatmentAppointment.id,
    //                 data: {
    //                     details: treatmentGetValues(),
    //                 },
    //                 shouldReset: false,
    //             });
    //             return setShowCart(true);
    //         }

    //         if (newStatus === EnumAppointmentStatus.Completed && showCart)
    //             return setIsShowPaymentCompleted(true);
    //         try {
    //             let data = treatmentGetValues();
    //             data = formatAppointmentDetailsResponse(data);

    //             updateAppointmentStatusMutation.mutate({
    //                 appointmentId: id,
    //                 status: newStatus,
    //                 soapPending: soapPending,
    //             });

    //             // if (newStatus === EnumAppointmentStatus.Completed) {
    //             //     router.push('/patients');
    //             // }
    //         } catch (error) {
    //             console.error('Failed to update status:', error);
    //         } finally {
    //             // setIsUpdating(false);
    //         }
    //     },
    //     handleQuestions: () => {},
    //     handleNotes: () => {},
    //     onCreateWalkInAppointment: () => {
    //         handleAddAppointment({
    //             DefaultPatientData: false,
    //             reset: appointmentReset,
    //             setEditMode,
    //             setSelectedPatient,
    //             setShowAppointmentModal,
    //             setValue: appointmentSetValue,
    //             patientData,
    //         });
    //     },
    // };

    const appointmentCalender = transformApiDataToCalendar(
        appointmentsData,
        activeAppointment,
        setActiveAppointment
    );

    // //alerts func
    // //can be moved
    // const {
    //     createPatientAlertMutation,
    //     deletePatientAlertMutation,
    //     updatePatientAlertMutation,
    // } = usePatientAlertMutation();

    // const {
    //     createClinicAlertMutation,
    //     deleteClinicAlertMutation,
    //     updateClinicAlertMutation,
    // } = useClinicAlertMutation();

    // const handleCreateAlert = (data: { tagName: string; priority: any }) => {
    //     const body = {
    //         alertName: data.tagName,
    //         severity: data.priority.value,
    //         clinicId: CLINIC_ID,
    //     };
    //     try {
    //         createClinicAlertMutation.mutate(body);
    //         setName('');
    //         setSeverity('');
    //         setCreateAlert(false);
    //     } catch (error) {
    //         console.error('Error creating patient alert:', error);
    //     }
    // };

    // const handleTagClick = (tag: string, severity: any) => {
    //     const tagExists = selectedAlertTagList.some(
    //         (existingTag) =>
    //             existingTag.name === tag && existingTag.severity === severity
    //     );
    //     const body = {
    //         name: tag,
    //         severity: severity,
    //         patientId: patientId,
    //     };
    //     try {
    //         if (!tagExists) createPatientAlertMutation.mutate(body);
    //     } catch (error) {
    //         console.error('Error creating patient alert:', error);
    //     }
    // };

    // const handleEditTag = (data: any) => {
    //     const body = {
    //         alertName: data.tagName,
    //         severity: data.priority.value,
    //     };
    //     updateClinicAlertMutation.mutate({ id: alertId, body });

    //     setAlertId('');
    //     setOpenEditTag(false);
    //     setEditAlert(true);
    // };

    // const handleTagActions = (
    //     id: string,
    //     tag: string,
    //     severity: any,
    //     action: string
    // ) => {
    //     if (action === 'edit') {
    //         const options = priorityOptions;
    //         const alertValue = options.find((opt) => opt.value === severity);
    //         setEditAlert(false);
    //         setAlertId(id);
    //         createAlertsetValue('priority', alertValue);
    //         createAlertsetValue('tagName', tag);

    //         setOpenEditTag(true);
    //     } else if (action === 'delete') {
    //         try {
    //             deleteClinicAlertMutation.mutate({
    //                 id,
    //                 clinicId: CLINIC_ID,
    //             });
    //         } catch (error) {
    //             console.error('Error deleting patient alert:', error);
    //         }
    //     }
    // };

    // const handleClearAll = () => {
    //     deletePatientAlertMutation.mutate({
    //         id: patientId,
    //         all: 'all',
    //         patientId,
    //     });
    // };
    // const options = [
    //     { label: 'High', value: 'high', color: '#DC2020' },
    //     { label: 'Medium', value: 'medium', color: '#E99400' },
    // ];

    //effect to see change in appointment
    // useEffect(() => {
    //     const values = appointmentGetValues();

    //     if (!_.isEqual(values, selectedAppointmentData)) {
    //         setValuesChanged(true);
    //     } else {
    //         setValuesChanged(false);
    //     }
    // }, [appointmentWatch()]);

    // const vitalData = patientSidebar?.vitalListData?.reduce((acc, el) => {
    //     if (
    //         el.attitude ||
    //         el.bcs ||
    //         el.capillaryRefill ||
    //         el.heartRate ||
    //         el.mucousMembrane ||
    //         el.painScore ||
    //         el.respRate ||
    //         el.temperature ||
    //         el.weight
    //     ) {
    //         const date = el.appointmentDate;

    //         if (acc[date]) {
    //             acc[date].push(el);
    //         } else {
    //             acc[date] = [el];
    //         }

    //         return acc;
    //     }
    //     return acc;
    // }, {});

    // useEffect(() => {
    //     setTabs([
    //         ...tabs,
    //
    //     ]);
    // }, [appointmentCalender, appointmentHeader, appointmentDetails]);

    // Add a handler function for when the IDEXX URL interaction is complete
    const handleIdexxComplete = () => {
        // Close the IDEXX modal
        setShowIdexxURL(false);
        setSetIdexxURL('');
        setShowPatientInfo(false);
    };

    const startInvoiceData = {
        isAdjustmentInInvoice,
        onChangeAdjustInvoice: (value: boolean) => {
            setIsAdjustmentInInvoice(value);
            if (value) setAdjustmentAmount(currentMainBalance);
            else {
                setAdjustmentAmount(0);
            }
        },
        control: treatmentControl,
        addProductPrescriptionList: addProductList,
        cartProps: {
            cartItems: cartItemsAddedToCart({
                cartListData,
            }),
            onQuantityChange: (value) => {
                updateQuantityToCart(
                    value.id,
                    value.qty,
                    updateCartDetailsMutation
                );
            },
            onRemoveCartItem: (value, itemType) => {
                deleteFromCart(
                    value,
                    deleteFromCartMutation,
                    itemType,
                    updateCartDetailsMutation
                );
            },
        },
        discountValue: discountObect,
        invoiceSummary: memoizedInvoiceSummary,
        onCartContinue: (
            setIsShowPaymentCompleted: any,
            setIsShowPaymentSuccess: any
        ) =>
            debouncedActOnConfirmClick(
                setIsShowPaymentCompleted,
                setIsShowPaymentSuccess
            ),
        totalPrice: dTotalPrice,
        onChangeAmount: (amount: number) => {
            setManualPaymentAmount(amount); // Store user-entered amount
            setInvoiceCollectedAmount(amount);
            treatmentSetValue('invoiceAmount', amount);
        },
        onDiscountChange: (value: any) => {
            setDiscountValue(value.value);
            setDiscountObject(value);
            // calculateAmount(cartListData);
        },
        onPrescriptionClick: (item) => {
            actOnAddMoreToCartClick({
                item,
                addToCartMutation,
                appointmentId: getAppointmentId(),
                updateCartDetailsMutation,
            });
        },
        onSearchProduct: (value) => {
            const newProductList = addProductList.filter((item: any) =>
                item.name.toLowerCase().includes(value.toLowerCase())
            );
            setAddProductList(newProductList);
            createTreatmentTab(true);
        },
        onSubmitMoveToCart: (data) => {
            updateItemAddedFlagAndQuantityToCart(
                data.id,
                true,
                data.addQuantity,
                updateCartDetailsMutation
            );
        },
        outstandingBalance: {
            onChangeAdjustInvoice: () => {},
            previouslyPendingBalance: currentMainBalance,
        },
        paymentType: paymentMode,
        handleBack: () => {
            setIsStartInvoice(false);
        },
        prescriptionProps: {
            onAddToCart: () => {},
            prescriptionErrorMessage: checkPrescriptionRestricted(
                treatmentGetValues('prescription.list')
            ),
            prescriptionItems:
                cartPrescriptionItemsYetToBeAddedToCart(cartListData),
        },
        totalPayableAmount: dFinalPrice,
        amountPaid: manualPaymentAmount || 0, // Always use manual amount or 0, never auto-calculate
        cartListData,
        calculateAmount,
        onPaymentModeChange,
        paymentMode,
        getValues: treatmentGetValues,
        setValue: treatmentSetValue,
        setIsShowPaymentCompleted,
    };

    const [isCancelInvoice, setIsCancelInvoice] = useState(false);

    const defaultTabs: TabItemType[] = [
        {
            className: '',
            id: 'appointments',
            isDisabled: false,
            label: 'Appointments',
            tabContent: (
                <TabAppointment
                    appointmentCalender={appointmentCalender}
                    appointmentHeader={getActiveAppointmentHeader(
                        activeAppointment,
                        setTreatmentAppointment,
                        setShowEditTreatment,
                        resetDefaultFields
                    )}
                    appoinmentId={activeAppointment?.id}
                    isUpdateMedicalRecord={isUpdateMedicalRecord}
                    handleUpdateMedicalRecord={handleUpdateMedicalRecord}
                    setIsUpdateMedicalRecord={setIsUpdateMedicalRecord}
                    appointmentsData={appointmentsData}
                    appointmentData={
                        getAppointmentDetails(activeAppointment) || {}
                    }
                    patientId={patientId}
                    setIsLoadingState={setIsLoadingState}
                    circularLoader={circularLoader}
                    setCircularLoader={setCircularLoader}
                    patientDiagnosticNotesData={patientDiagnosticNotesData}
                    latestAppointment={latestAppointment}
                />
            ),
        },
        {
            className: '',
            id: 'diagnostics',
            isDisabled: false,
            label: 'Diagnostics',
            tabContent: (
                <TabDiagnostic
                    // diagnosticCalender={diagnosticCalender}
                    // diagnosticHeader={diagnosticHeader}
                    appointmentsData={appointmentsData}
                    patientDiagnosticReportData={patientDiagnosticReportData}
                    patientId={patientId}
                    setIsLoadingState={setIsLoadingState}
                    patientDiagnosticReportRefetch={
                        patientDiagnosticReportRefetch
                    }
                    patientDiagnosticNotesData={patientDiagnosticNotesData}
                    patientDiagnosticNotesRefetch={
                        patientDiagnosticNotesRefetch
                    }
                    treatmentSetValue={treatmentSetValue}
                    treatmentGetValues={treatmentGetValues}
                    treatmentAppointment={treatmentAppointment}
                />
            ),
        },
        {
            className: '',
            id: 'vaccination',
            isDisabled: false,
            label: 'Vaccination',
            tabContent: (
                <TabVaccination
                    patientId={patientId}
                    patientVaccinationData={patientVaccinationData}
                    setIsLoadingState={setIsLoadingState}
                    // vaccinationCalender={vaccineCalender}
                    // vaccinationHeader={activeVaccination}
                    // setAddVaccinationPop={setAddVaccinationPop}
                />
            ),
        },
        {
            className: '',
            id: 'communication',
            isDisabled: false,
            label: 'Communication',
            tabContent: (
                <TabCommunication
                    patientId={patientId}
                    plansDropDownProps={plansDropDownProps}
                    setIsLoadingState={setIsLoadingState}
                    patientDetailsData={patientDetailsData}
                    plansData={plansData}
                />
            ),
        },
        {
            className: '',
            id: 'PatientPaymentDetails',
            isDisabled: false,
            label: 'Payment Details',
            tabContent: (
                <PatientPaymentDetails
                    // Start of Selection
                    patientPaymentDetailsData={patientPaymentDetailsData}
                    cartOptions={cartOptions}
                    currentMainBalance={currentMainBalance}
                    patientId={patientId}
                    setIsLoadingState={setIsLoadingState}
                    startInvoiceData={startInvoiceData}
                    planProps={planProps}
                    plansDropDownProps={plansDropDownProps}
                    isStartInvoice={isStartInvoice}
                    setIsStartInvoice={setIsStartInvoice}
                    ownerId={
                        patientDetailsData.data.patientOwners[0].ownerBrand.id
                    }
                    latestAppointment={latestAppointment}
                    isCancelInvoice={isCancelInvoice}
                    setIsCancelInvoice={setIsCancelInvoice}
                    ownerDetails={patientDetailsData.data.patientOwners[0]}
                    patientName={patientDetailsData.data.patientName}
                    activeItem={activeItem}
                    // Pass IDEXX-related props
                    setShowIdexxCreation={setShowIdexxCreation}
                    showIdexxOrderCreation={showIdexxOrderCreation}
                    showIdexxURL={showIdexxURL}
                    setShowIdexxURL={setShowIdexxURL}
                    showIdexxCreationModalText={idexxCreationModalText}
                    setShowIdexxCreationModalText={
                        setShowIdexxCreationModalText
                    }
                    idexxURL={setIdexxURL} // Note: PatientDetailsTemplate uses setIdexxURL as the URL value
                    setIdexxURL={setSetIdexxURL}
                    // ... rest of the props
                />
            ),
        },
    ];

    // const addCheckOutModalUI = () => {
    //     return (
    //         <div className="flex flex-col">
    //             {'Are you sure you want to check-out this patient?'}
    //             <div className="py-4">
    //                 <Checkbox
    //                     id={'Modal_check_box'}
    //                     label={'SOAP is pending'}
    //                     onChange={(e) => {
    //                         setIsCheckedModalCheckOut(e.target.checked);
    //                     }}
    //                 />
    //             </div>

    //             <div className="flex flex-row justify-end gap-4">
    //                 <Button
    //                     id={'Modal_Cancel'}
    //                     variant="secondary"
    //                     size="small"
    //                     onClick={() => {
    //                         setShowCheckoutModal(false);
    //                     }}
    //                 >
    //                     Cancel
    //                 </Button>

    //                 <Button
    //                     id={'Modal_Check-out'}
    //                     size="small"
    //                     iconPosition="left"
    //                     icon={<IconCart size={18} />}
    //                     onClick={() => {
    //                         beginTreatment.handleBeginTreatment(
    //                             EnumAppointmentStatus.Checkedout,
    //                             treatmentAppointment.id
    //                         );
    //                         //  saveAppoinmentDetails();
    //                         setShowCheckoutModal(false);
    //                         setShowReadyToCheckoutModal(true);
    //                     }}
    //                 >
    //                     Ready to check-out
    //                 </Button>
    //             </div>
    //         </div>
    //     );
    // };

    // const readyToCheckoutModal = () => {
    //     return (
    //         <div className="flex flex-col">
    //             <div className="flex flex-row justify-end gap-4 mt-2">
    //                 <Button
    //                     id={'Modal_Cancel'}
    //                     variant="secondary"
    //                     iconPosition="left"
    //                     icon={<IconCart size={18} />}
    //                     size="small"
    //                     onClick={() => {
    //                         setShowReadyToCheckoutModal(false);
    //                         setShowCart(true);
    //                         calculateAmount(cartListData);
    //                         createTreatmentTab(true);
    //                     }}
    //                 >
    //                     Continue to check-out
    //                 </Button>

    //                 <Button
    //                     id={'Modal_Check-out'}
    //                     size="small"
    //                     iconPosition="left"
    //                     // icon={<IconCart size={18} />}
    //                     onClick={() => {
    //                         saveAppoinmentDetails();
    //                         router.push('/dashboard');
    //                     }}
    //                 >
    //                     Return to Dashboard
    //                 </Button>
    //             </div>
    //         </div>
    //     );
    // };

    useEffect(() => {
        saveData();
        calculateAmount(cartListData);
    }, [treatmentWatch()]);

    // useEffect(() => {
    //     saveData();
    // }, [treatmentWatch()]);

    useEffect(() => {
        const intervalId = setInterval(() => {
            // if (treatmentAppointment.id) saveAppoinmentDetails();
        }, 10000);

        return () => clearInterval(intervalId);
    }, [treatmentAppointment]);

    useEffect(() => {
        const savedDraftData = localStorage.getItem('SOAP_DRAFT');

        if (savedDraftData) {
            const { content } = JSON.parse(savedDraftData);
            setSoapxContent(content); // Pre-fill the content
        }
    }, []);

    const handleSaveAsDraftSoapx = () => {
        try {
            // Save current content and timestamp
            const draftData = {
                content: soapxContent,
                timestamp: new Date().toISOString(),
            };

            localStorage.setItem('SOAP_DRAFT', JSON.stringify(draftData));
            setIsSoapxModal(false);
        } catch (error) {
            console.error('Error saving draft:', error);
        }
    };
    const [isSoapLoading, setIsSoapLoading] = useState(false);

    // console.log(isSoapxModal)
    const handleGenerateSoapx = async () => {
        try {
            setIsSoapLoading(true);
            setIsSoapxModal(false);
            treatmentSetValue('aigenerated', false);
            const fieldsToReset = [
                'subjective',
                'objective.notes',
                'plans.notes',
                'assessment.notes',
                'prescription.notes',
                'objective.vitals[0].temperature',
            ];

            // Reset all fields and wait for the state to update
            await Promise.all(
                fieldsToReset.map(
                    (field) =>
                        new Promise<void>((resolve) => {
                            treatmentSetValue(field, '', {
                                shouldValidate: true,
                            });
                            // Small delay to ensure state updates
                            setTimeout(resolve, 100);
                        }),
                    treatmentSetValue('objective.vitals[0].temperature', null, {
                        shouldValidate: true,
                    })
                )
            );

            const res = await generateSoapNotesMutation.mutateAsync({
                clinicalNotes: soapxContent,
                onSuccess: (response) => {
                    console.log('SOAP notes generated:', response);
                },
            });
            if (res?.data) {
                setIsSoapLoading(false);
                const ainotes = res?.data;
                localStorage.removeItem('SOAP_DRAFT');
                treatmentSetValue('aigenerated', true);
                treatmentSetValue('subjective', ainotes.subjectiveNotes, {
                    shouldValidate: true,
                });
                treatmentSetValue('objective.notes', ainotes.objectiveNotes, {
                    shouldValidate: true,
                });
                treatmentSetValue('plans.notes', ainotes.planNotes, {
                    shouldValidate: true,
                });
                treatmentSetValue('assessment.notes', ainotes.assessmentNotes, {
                    shouldValidate: true,
                });
                treatmentSetValue(
                    'prescription.notes',
                    ainotes.dischargeSummary,
                    { shouldValidate: true }
                );
                if (ainotes.temperature) {
                    treatmentSetValue(
                        'objective.vitals[0].temperature',
                        ainotes.temperature,
                        { shouldValidate: true }
                    );
                }
                // Emit all updates immediately without debounce
                // if (socket && treatmentAppointment?.id) {
                //     const updates = {
                //         subjective: ainotes.subjectiveNotes,
                //         'objective.notes': ainotes.objectiveNotes,
                //         'plans.notes': ainotes.planNotes,
                //         'assessment.notes': ainotes.assessmentNotes,
                //         'prescription.notes': ainotes.dischargeSummary,
                //         ...(ainotes.temperature && { 'objective.vitals[0].temperature': ainotes.temperature })
                //     };

                //     // Update realtime state
                //     setRealtimeUpdates(prev => ({
                //         ...prev,
                //         ...updates
                //     }));

                //     // Emit all updates
                //     Object.entries(updates).forEach(([key, value]) => {
                //         socket.emit('ping', {
                //             appointmentId: treatmentAppointment.id,
                //             key,
                //             value
                //         });
                //     });
                // }
            }
        } catch (error) {
            console.error('Failed to generate SOAP notes:', error);
        }
    };

    const handleSoaxSaveContent = (
        event: React.ChangeEvent<HTMLTextAreaElement>
    ) => {
        const newContent = event.target.value;
        setSoapxContent(newContent);
        try {
            const draftData = {
                content: newContent,
                timestamp: new Date().toISOString(),
            };
            localStorage.setItem('SOAP_DRAFT', JSON.stringify(draftData));
            // setSoapxContent()
        } catch (error) {
            console.error('Error auto-saving draft:', error);
        }
    };

    return (
        // <SocketContext.Provider value={socket}>
        <div className="h-[96%] w-full flex flex-col gap-3">
            {(isSoapLoading || isLoadingState) && <CustomLoader />}
            <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            <div className="grid grid-cols-[283px_calc(100%_-_300px)] gap-4">
                <PatientDetailsSidebarTemplate
                    alertsList={alertsList}
                    editPatientProps={{
                        control,
                        formState: { errors },
                        getValues,
                        setValue,
                        handleSubmit,
                        patientId,
                        register,
                        watch,
                    }}
                    patientId={patientId}
                    patientSidebar={patientSidebar}
                    reset={reset}
                    selectedAlertTagList={selectedAlertTagList}
                    // vitalData={vitalData}
                    key={'Patientdetailsisdebar'}
                />
                {/* <CreateTag
                    isOpen={createAlert || openEditTag}
                    onClose={() => {
                        setCreateAlert(false);
                        setOpenEditTag(false);
                    }}
                    control={createAlertcontrol}
                    errors={{ createAlertErrors: { message: 'error' } }}
                    watch={createAlertWatch}
                    setValue={createAlertsetValue}
                    register={createAlertRegister}
                    handleCreateTag={() => {
                        createAlertHandleSubmit(handleCreateAlert)();
                    }}
                    options={options}
                    openEditTag={openEditTag}
                    handleEditTag={createAlertHandleSubmit(handleEditTag)}
                /> */}
                {/* <EditAlert
                    isOpen={editAlert}
                    onClose={() => {
                        setEditAlert(false);
                    }}
                    handleCreateTag={() => {
                        setCreateAlert(true);
                    }}
                    handleClearAll={handleClearAll}
                    handleTagClick={handleTagClick}
                    handleTagActions={handleTagActions}
                    allTags={alertsList}
                    selectedTags={selectedAlertTagList}
                    onSearch={() => { }}
                    onMoreActionClick={() => { }}
                    patientId={patientId}
                    createAlertreset={createAlertreset}
                /> */}
                {/* <BasicDetailModal
                    onClose={() => {
                        setIsOpen(false);
                    }}
                    isOpen={isOpen}
                    data={patientSidebar.patientData as PatientData}
                    handleEdit={() => {
                        setEditModal(true);
                        setIsOpen(false);
                    }}
                /> */}
                {/* <Modal
                    modalTitle="Edit Basic Details"
                    dataAutomation="edit-patient-form"
                    onClose={() => {
                        setEditModal(false);
                        reset();
                    }}
                    isOpen={editModal}
                    children={
                        <LoadEditPatient
                            {...{
                                patientId,
                                control,
                                errors,
                                setValue,
                                setEditModal,
                                getValues,
                                watch,
                                register,
                                handleSubmit,
                            }}
                        />
                    }
                /> */}
                {/* <ProblemHistoryModal
                    isOpen={problemHistoryModal}
                    onClose={() => {
                        setProblemHistoryModal(false);
                    }}
                    problemList={patientSidebar.problemList}
                /> */}
                {/* {addVaccinationPop && (
                    <AddVaccinationModal
                        isOpen={addVaccinationPop}
                        onClose={handleCloseAddVaccinationPop}
                        setAddSingleFilePop={setAddSingleFilePop}
                        handleSaveVaccination={handleSaveVaccination}
                        vaccineUploadFile={vaccineUploadFile}
                        setVaccineUploadFile={setVaccineUploadFile}
                        setFileUploadError={setFileUploadError}
                        fileUploadError={fileUploadError}
                    />
                )} */}
                {/* <MedicationHistoryModal
                    isOpen={medicationHistoryModal}
                    onClose={() => {
                        setMedicationHistoryModal(false);
                    }}
                    medications={patientSidebar.medicationList}
                /> */}
                {/* <PastVitalModal
                    datesData={getStructuredData(vitalData)}
                    isOpen={openVitalPop}
                    onClose={() => setOpenVitalPop(false)}
                /> */}
                {/* <ModalSingleFileUpload
                    isOpen={addSingleFilePop}
                    onClose={() => {
                        setAddSingleFilePop(false);
                    }}
                    modalTitle="File upload"
                    onFileUpload={handleFileUpload}
                /> */}
                <ModalFilePreview
                    filePreview={filePreview}
                    isOpen={previewDiagnosticPop}
                    onClose={() => {
                        setPreviewDiagnosticPop(false);
                    }}
                    modalTitle="File name"
                />

                {/* what is this and here do we need to use this  */}
                {/* <ModalFileUpload
                    isOpen={uploadDiagnosticReportPop}
                    onClose={handleCloseUploadDiagnosticModal}
                    modalTitle="File upload"
                /> */}
                {/* <PatientDetails
                    handleBasicInfoEdit={() => {
                        setIsOpen(true);
                    }}
                    handleAlertEdit={() => {
                        setEditAlert(true);
                    }}
                    handleViewMoreProblems={() => {
                        setProblemHistoryModal(true);
                    }}
                    handlePastVital={() => {
                        setOpenVitalPop(true);
                    }}
                    handleViewMoreMedication={() =>
                        setMedicationHistoryModal(true)
                    }
                    {...patientSidebar}
                /> */}
                <div className="sticky top-0 h-[calc(100vh_-_4rem)] w-full">
                    {isSoapxModal && (
                        <Modal
                            isOpen={isSoapxModal}
                            onClose={() => setIsSoapxModal(false)}
                            modalTitle={`SOAPx`}
                            dataAutomation={''}
                            className=""
                            modalWidth="max-w-[800px]"
                            modalHeight="max-h-[750px]"
                        >
                            <div className="flex flex-col gap-6">
                                <Textarea
                                    id={`soapx-textarea`}
                                    name={`soapx-textarea`}
                                    rows={10}
                                    key={`soapx-textarea`}
                                    register={register}
                                    containerClass=""
                                    onChange={handleSoaxSaveContent}
                                    value={soapxContent} // Add this line
                                    bgColor="bg-primary-50"
                                    placeholder="Type your notes here..."
                                    className="w-full !rounded-[24px] py-5"
                                    variant="large"
                                    resizeValue=""
                                />
                                <div className="flex items-center gap-4 justify-end">
                                    <Button
                                        id={'save-as-draft-button'}
                                        label="Save As Draft"
                                        className=""
                                        onClick={handleSaveAsDraftSoapx}
                                        variant="secondary"
                                        size="small"
                                    />
                                    <Button
                                        id={'generate-button'}
                                        label="Generate"
                                        className=""
                                        onClick={handleGenerateSoapx}
                                        variant="gradient"
                                        size="small"
                                    />
                                </div>
                            </div>
                        </Modal>
                    )}
                    {showEditTreatment ? (
                        <EditTreatment
                            setShowEditTreatment={setShowEditTreatment}
                            headerDetails={getActiveAppointmentHeader(
                                treatmentAppointment
                            )}
                            treatmentAppointment={treatmentAppointment}
                            treatmentControl={treatmentControl}
                            treatmentErrors={treatmentErrors}
                            treatmentGetValues={treatmentGetValues}
                            treatmentSetValue={treatmentSetValue}
                            treatmentWatch={treatmentWatch}
                            treatmentRegister={treatmentRegister}
                            handleAddVital={() => {}}
                            handleAddPhysicalExam={() => {}}
                            handleAddUltrasoundExam={() => {}}
                            handleAddBodyMap={() => {
                                const newBodyMap = {
                                    type: null,
                                    bodymapImage: null,
                                    notes: null,
                                    paths: null,
                                    image: null,
                                };

                                const updatedBodyMaps = [
                                    ...treatmentGetValues('objective.bodyMaps'),
                                    newBodyMap,
                                ];

                                treatmentSetValue(
                                    'objective.bodyMaps',
                                    updatedBodyMaps,
                                    {
                                        shouldValidate: true,
                                    }
                                );
                            }}
                            handleAddDiagnostic={() => {}}
                            onAddRowTreatment={() => {}}
                            onDelete={() => {}}
                            prescriptionsTreatmentList={[]}
                            medicineDropdownProps={{}}
                            subjectiveProps={subjectiveProps}
                            objectiveProps={objectiveProps}
                            assessmentProps={assessmentProps}
                            planProps={planProps}
                            prescriptionProps={prescriptionProps}
                            appointmentFollowupProps={{}}
                            attachmentProps={attachmentProps}
                            plansDropDownProps={plansDropDownProps}
                            prescriptionDropDownProps={
                                prescriptionDropDownProps
                            }
                            patientId={patientId}
                            handleEditBack={handleEditBack}
                            handleSoapxModal={handleSoapxModal}
                            setIsUpdateMedicalRecord={setIsUpdateMedicalRecord}
                            isUpdateMedicalRecord={isUpdateMedicalRecord}
                            handleUpdateMedicalRecord={
                                handleUpdateMedicalRecord
                            }
                            patientDiagnosticReportRefetch={
                                patientDiagnosticReportRefetch
                            }
                            patientDiagnosticNotesRefetch={
                                patientDiagnosticNotesRefetch
                            }
                            setIsLoadingState={setIsLoadingState}
                        />
                    ) : (
                        <>
                            <PatientDetailsTopbarTemplate
                                treatmentAppointment={treatmentAppointment}
                                showCart={showCart}
                                showAppointmentModal={showAppointmentModal}
                                clinicRoomsData={clinicRoomsData}
                                doctorData={doctorData}
                                setShowAppointmentModal={
                                    setShowAppointmentModal
                                }
                                patientId={patientId}
                                routeToAppointmentPage={routeToAppointmentPage}
                                providerData={providerData}
                                latestAppointment={latestAppointment}
                                appointmentsData={appointmentsData}
                                patientSidebar={patientSidebar}
                                setCartAppointmentId={setCartAppointmentId}
                                setShowCheckoutModal={setShowCheckoutModal}
                                showCheckoutModal={showCheckoutModal}
                                saveAppoinmentDetails={saveAppoinmentDetails}
                                addLongTermPerscriptionsToCart={
                                    addLongTermPerscriptionsToCart
                                }
                                calculateAmount={calculateAmount}
                                createTreatmentTab={createTreatmentTab}
                                setShowCart={setShowCart}
                                cartListData={cartListData}
                                treatmentGetValues={treatmentGetValues}
                                treatmentRef={treatmentRef}
                                setIsShowPaymentCompleted={
                                    setIsShowPaymentCompleted
                                }
                                actOnConfirmClick={debouncedActOnConfirmClick}
                                isShowPaymentSuccess={isShowPaymentSuccess}
                                setIsShowPaymentSuccess={
                                    setIsShowPaymentSuccess
                                }
                                setRouteToAppointmentPage={
                                    setRouteToAppointmentPage
                                }
                                workinghours={formattedWorkingHours}
                                clinicId={CLINIC_ID}
                            />
                            {/* {treatmentAppointment?.id ? (
                                <BeginTreatment
                                    id={beginTreatment.id}
                                    handleMenu={beginTreatment.handleMenu}
                                    handleBeginTreatment={
                                        beginTreatment.handleBeginTreatment
                                    }
                                    handleQuestions={
                                        beginTreatment.handleQuestions
                                    }
                                    handleNotes={beginTreatment.handleNotes}
                                    scheduleData={beginTreatment.scheduleData}
                                    treatmentData={beginTreatment.treatmentData}
                                    showCart={showCart}
                                />
                            ) : (
                                <NoOngoingAppointments
                                    onCreateWalkInAppointment={
                                        beginTreatment.onCreateWalkInAppointment
                                    }
                                />
                            )} */}
                            <Tabs
                                tabContentClass={`min-h-[400px] mb-4 ${getAppointmentId() ? 'h-[calc(100vh-18.4rem)]' : 'h-[calc(100vh-13.7rem)]'}  overflow-auto`}
                                tabs={[...tabs, ...defaultTabs]}
                                defaultActiveTab={
                                    !activeTab ? tabs[0]?.id : activeTab
                                }
                                isStartInvoice={isStartInvoice}
                                setIsStartInvoice={setIsStartInvoice}
                                setIsCancelInvoice={setIsCancelInvoice}
                            />
                        </>
                    )}
                </div>
                {/* {showCheckoutModal && (
                    <Modal
                        isOpen={showCheckoutModal}
                        onClose={() => setShowCheckoutModal(false)}
                        modalTitle={'Ready to check-out'}
                        dataAutomation={''}
                    >
                        {addCheckOutModalUI()}
                    </Modal>
                )} */}
                {/* {showAppointmentModal && (
                    <CreateAppointmentStaff
                        patientProps={{
                            showPatientDropdown,
                            setShowPatientDropdown,
                            listStatus,
                            onMenuClick: () => { },
                            patientList,
                            selectedPatient,
                        }}
                        appointmentOptions={(
                            search: string,
                            loadedOptions: unknown[]
                        ) => getAppointmentTypeOptions(search, loadedOptions)}
                        assignRoomOptions={(
                            search: string,
                            loadedOptions: unknown[]
                        ) =>
                            getRoomOptions(
                                search,
                                loadedOptions,
                                clinicRoomsData
                            )
                        }
                        doctorOptions={(
                            search: string,
                            loadedOptions: unknown[]
                        ) =>
                            getDoctorOptions(search, loadedOptions, doctorData)
                        }
                        isOpen={showAppointmentModal}
                        onClose={() => {
                            //setPatientData({});
                            setShowAppointmentModal(false);
                            setViewMode(false);
                            setEditMode(false);

                            appointmentReset();
                            window.location.reload()
                            // patientRenderingData = null;
                        }}
                        getPatientOptions={[]}
                        reasonOptions={(
                            search: string,
                            loadedOptions: unknown[]
                        ) => getReasonOptions(search, loadedOptions)}
                        control={appointmentControl}
                        errors={appointmentErrors}
                        setValue={appointmentSetValue}
                        watch={appointmentWatch}
                        register={appointmentRegister}
                        handleSubmit={appointmentHandleSubmit}
                        key={'Create Appointment'}
                        triageOptions={(
                            search: string,
                            loadedOptions: unknown[]
                        ) => getTriggerOption(search, loadedOptions)}
                        modalTitle={
                            editMode ? 'Edit Appointment' : 'Create Appointment'
                        }
                        handleCancel={() =>
                            handleCancel({
                                setEditMode,
                                setSelectedPatient,
                                setShowAppointmentModal,
                                setViewMode,
                            })
                        }
                        onProviderDelete={() => { }}
                        handleAddProvider={() => { }}
                        handleCreateAppointment={(data: any) => {
                            handleCreateAppointment({
                                createAppointmentMutation,
                                data,
                            });
                            routeToAppointmentPage &&
                                router.push('/appointments');
                        }}
                        onStepHandler={() =>
                            onStepHandler({
                                appointmentId: treatmentAppointment?.id,
                                foundAppointment: treatmentAppointment,
                                setShowAppointmentModal,
                                setStep,
                                updateAppointmentStatusMutation,
                                isDashboard: false,
                                router,
                            })
                        }
                        step={step}
                        providerOptions={(
                            search: string,
                            loadedOptions: unknown[]
                        ) =>
                            getProviderOptions(
                                search,
                                loadedOptions,
                                providerData
                            )
                        }
                        getValues={appointmentGetValues}
                        isEditable={true}
                        patientData={patientData}
                        editMode={editMode}
                        handleUpdateAppointment={(
                            data: any,
                            typeOfCall: string
                        ) =>
                            handleUpdateAppointment({
                                appointmentId: treatmentAppointment.id,
                                data,
                                getValues: appointmentGetValues,
                                setSelectedAppointmentData,
                                setShowAppointmentModal,
                                step,
                                typeOfCall,
                                updateAppointmentMutation,
                                router,
                                setShowCheckoutModal,
                                actOnConfirmClick,
                            })
                        }
                        valuesChanged={valuesChanged}
                        scheduleStepHandler={() =>
                            scheduleStepHandler({
                                appointmentId: treatmentAppointment.id,
                                setShowAppointmentModal,
                                updateAppointmentStatusMutation,
                            })
                        }
                        onMoreActionClick={(item: MenuListType) =>
                            onCreateAppointmentAction({
                                item,
                                setConfirmModal,
                                setShowAppointmentModal,
                            })
                        }
                        handleSearchAdd={() => { }}
                        handlePatientSearch={() => { }}
                    />
                )} */}

                {/* {isRefundAmountModalShown.isOpen && (
                    <ModalRefundAmount
                        isOpen={isRefundAmountModalShown.isOpen}
                        onClose={function (): void {
                            setIsRefundAmountModalShown({
                                isOpen: false,
                            });
                        }}
                        modalTitle={'Refund Amount'}
                        onConfirmRefund={function (): void {
                            setSelectedItemForRefund({
                                ...selectedItemForRefund,
                                paymentMode: selectedItemForRefund.paymentMode,
                            });

                            actOnRefundCOnfirmClick();
                        }}
                        totalPayableAmount={selectedItemForRefund.amountPaid}
                        onChangeAmount={function (data): void {
                            setSelectedItemForRefund({
                                ...selectedItemForRefund,
                                amountPaid: data,
                            });
                        }}
                        onChangePaymentMode={function (data): void {
                            setSelectedItemForRefund({
                                ...selectedItemForRefund,
                                paymentMode: data,
                            });
                        }}
                        refundAmount={selectedItemForRefund.amountPayable}
                    ></ModalRefundAmount>
                )} */}
            </div>
            {/* {showReadyToCheckOutModal && (
                <Modal
                    isOpen={showReadyToCheckOutModal}
                    onClose={() => setShowReadyToCheckoutModal(false)}
                    modalTitle={'Patient is ready to check-out'}
                    isHeaderBorder={false}
                    dataAutomation={''}
                    icon={
                        <IconWarning
                            size={30}
                            className={`-mt-0.5 ml-1 text-warning-100`}
                        />
                    }
                >
                    {readyToCheckoutModal()}
                </Modal>
            )} */}

            <ConfirmModal
                isOpen={isShowPaymentCompleted}
                dataAutomation="payment-completed"
                alertType="warning"
                onClose={() => setIsShowPaymentCompleted(false)}
                modalTitle="Payment completed"
                modalDescription={
                    <>
                        {isSubmittingPayment ? (
                            <>Processing payment...</>
                        ) : (
                            <>
                                Payment of{' '}
                                <b>
                                    <Price
                                        amount={
                                            treatmentGetValues('invoiceAmount')
                                                ? treatmentGetValues(
                                                      'invoiceAmount'
                                                  )
                                                : dFinalPrice
                                        }
                                    />
                                </b>{' '}
                                received by <b>{paymentMode}</b>
                            </>
                        )}
                    </>
                }
                primaryBtnProps={{
                    dataAutomation: 'confirm',
                    label: isSubmittingPayment ? 'Processing...' : 'Confirm',
                    onClick: () => {
                        // Set checkout flag before starting checkout process
                        isCheckoutInProgress.current = true;
                        console.log('Checkout button clicked - setting isCheckoutInProgress to true');

                        debouncedActOnConfirmClick(
                            setIsShowPaymentCompleted,
                            setIsShowPaymentSuccess
                        );
                        emitCartChange('checkout', {});
                        setTreatmentCompletionLoading(true);
                    },
                    disabled: isSubmittingPayment || treatmentCompletionLoading,
                }}
                primaryBtnDisabled={
                    isSubmittingPayment || treatmentCompletionLoading
                }
            >
                {cartListData?.data?.filter((item) => item.isAddedToCart)
                    ?.length > 0 && (
                    <div className="mt-4">
                        <Textarea
                            id="paymentNote"
                            name="paymentNote"
                            label="Add Note"
                            placeholder="Add any additional notes about this payment..."
                            value={paymentNote}
                            onChange={(e) => setPaymentNote(e.target.value)}
                            rows={3}
                            maxLength={500}
                            variant="basicField"
                            isAutoGrow={true}
                            resizeValue="resize-none"
                            className="!border-[#ADADAD]"
                        />
                    </div>
                )}
            </ConfirmModal>

            {/* <ModalPaymentSuccess
                isOpen={isShowPaymentSuccess}
                dataAutomation="payment-success"
                followUpMsg={
                    treatmentGetValues('followup')?.label
                        ? `Need to follow up ${treatmentGetValues('followup')?.label}`
                        : ''
                }
                onClose={() => {
                    setIsShowPaymentSuccess(false);
                    window.location.href = `/patients/${patientId}/details`;
                }}
                primaryBtnProps={{
                    label: 'Done',
                    onClick: () => {
                        setIsShowPaymentSuccess(false);
                        setTreatmentAppointment(null);
                        setShowCart(false);
                        setTabs([]); // Clear tabs
                        createTreatmentTab(false); // Recreate tabs without treatment
                        setActiveTab('appointments'); // Reset to appointments tab
                        router.replace(`/patients/${patientId}/details`);
                    },
                    dataAutomation: 'Go to dashboard',
                }}
                secondaryBtnProps={{
                    label: 'Create an appointment',
                    onClick: () => {
                        setIsShowPaymentSuccess(false);
                        //router.push('/appointments');
                        setRouteToAppointmentPage(true);
                        beginTreatment.onCreateWalkInAppointment();
                    },
                    dataAutomation: 'Go to appointment',
                }}
            /> */}

            <Modal
                isOpen={showDeleteDiagnosticsModal}
                isHeaderBorder={false}
                isModalSubTitle={true}
                modalSubTitle=""
                modalTitle={'Are you sure want to delete this item?'}
                icon={
                    <IconWarning
                        size={30}
                        className={`-mt-0.5 ml-1 text-warning-100`}
                    />
                }
                onClose={() => setShowDeleteDiagnosticModal(false)}
                dataAutomation={'delete-diagnostic'}
            >
                <div className="w-[90%] ml-auto  mr-0   gap-5 flex flex-col">
                    <div>
                        <p className="text-neutral-500 ">
                            Please note this can't be reversed{' '}
                        </p>
                        <p className="text-neutral-500 ">
                            Deleting this will result in the permanent removal
                            of all associated diagnostic attachments and
                            templates.
                        </p>
                    </div>
                    <div className="w-full flex gap-5 justify-end">
                        <Button
                            id="delte-diagnostic-cancel"
                            variant="secondary"
                            onClick={() => setShowDeleteDiagnosticModal(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            id="confirm-diagnostic-cancel"
                            variant="primary"
                            onClick={() =>
                                planProps.onDelete({
                                    id: selectedPlanId as string,
                                })
                            }
                        >
                            Yes
                        </Button>
                    </div>
                </div>
            </Modal>

            {/* {showPatientInfo && (
                <Modal
                    isOpen={showPatientInfo}
                    onClose={handleClosePatientInfoModal}
                    modalTitle={
                        'Please fill in details for patient: ' +
                        patientDetailsData?.data.patientName
                    }
                    dataAutomation={''}
                >
                    {actOnAdditionalPatientInfo()}
                </Modal>
            )} */}

            {showIdexxURL && (
                <Modal
                    isOpen={showIdexxURL}
                    onClose={handleCloseIdexxModal}
                    modalTitle={'IDEXX Submit Order'}
                    dataAutomation={''}
                    modalHeight="max-h-[850px]"
                    modalWidth="max-w-[860px]"
                    className
                >
                    {actOnIDEXXOIframeURL()}
                </Modal>
            )}

            {showMissingItemsModal && (
                <MissingInfoModal
                    formMethods={{
                        register,
                        handleSubmit,
                        getValues,
                        setValue,
                        setError,
                        control,
                        reset,
                        resetField,
                        watch,
                        trigger,
                        formState: { errors },
                    }}
                    handleCancel={() => setShowMissingItemsModal(false)}
                    handleNext={handleSaveMissingInformation}
                    isOpen={showMissingItemsModal}
                />
            )}
            {showIdexxOrderCreation && (
                <Modal
                    isHideCloseBtn={
                        idexxCreationModalText !== 'Order creation failed' &&
                        idexxCreationModalText !== 'Order cancellation failed'
                    }
                    onClose={() => {
                        setShowIdexxCreation(false);
                    }}
                    isModalSubTitle={false}
                    modalSubTitle={idexxCreationModalText}
                    modalTitle={'Idexx Order'}
                    children={
                        <div className="px-3">{idexxCreationModalText}</div>
                    }
                    icon={
                        idexxCreationModalText !== 'Order creation failed' &&
                        idexxCreationModalText !==
                            'Order cancellation failed' ? (
                            <IconTimeCircle
                                size={20}
                                className={`-mt-0.5 ml-1 text-blue-800`}
                            />
                        ) : (
                            <IconClose
                                size={20}
                                className={`-mt-0.5 ml-1 text-error-200`}
                            />
                        )
                    }
                    isOpen={showIdexxOrderCreation}
                />
            )}

            {/* Cart checkout notification modal - moved from TabTreatmentCart */}
            {showCheckedoutModal && (
                <ConfirmModal
                    isOpen={showCheckedoutModal}
                    dataAutomation="cart-checkout-notification"
                    alertType="warning"
                    onClose={() => {
                        const patientRoute = `/patients/${patientId}/details`;
                        window.location.assign(patientRoute);
                    }}
                    modalTitle="Appointment Completed"
                    modalDescription={'This appointment has already been completed by another user.'}
                    primaryBtnProps={{
                        dataAutomation: 'Done',
                        label: 'Done',
                        onClick: () => {
                            const patientRoute = `/patients/${patientId}/details`;
                            window.location.assign(patientRoute);
                        },
                    }}
                    primaryBtnDisabled={false}
                />
            )}
        </div>
        // </SocketContext.Provider>
    );
};

export default PatientDetailsTemplate;
