import React, { useState, useMemo, useEffect, useRef } from 'react';
import { Breadcrumbs, Searchbar } from '../molecules';
import { Modal } from '@/app/molecules';
import { But<PERSON>, Heading } from '../atoms';
import Tabs from '../molecules/Tabs';
import PatientList from '../organisms/PatientList';
import { LedgerPatientT, PatientT } from '../types/patient';
import { PaginationType } from '../molecules/Table';
import IconAdd from '../atoms/customIcons/IconAdd.svg';
import ViewPatientDetails from '../components/ViewPatientDetails';
import PatientInfo, {
    genderOptions,
    reproductiveStatusOptions,
    speciesOptions,
} from '../organisms/patient/PatientInfo';
import {
    useCreateOrUpdatePatient,
    usePatientDetails,
} from '../services/patient.queries';
import OwnersInfo from '../organisms/patient/OwnersInfo';
import NextImage from 'next/image';
import LoadEditPatient from '../organisms/patient/PatientEditLoad';
import PendingPayment from '../organisms/ledger/PendingPayment';
import CreditAmount, {
    FormValuesCheckOut,
} from '../organisms/ledger/CreditAmount';
import {
    useCreatePaymentDetailsMutation,
    useGetPaymentDetailsForAPatient,
} from '../services/payment-details.queries';
import { CREDIT_TYPES, PAYMENT_TYPES } from '../utils/constant';
import PaymentHistory from '../organisms/ledger/PaymentHistory';
import { getProfileImage } from '../utils/common';
import { useRouter } from 'next/navigation';
import DeleteRoomModal from '@/app/organisms/admin/rooms/DeleteRoomModal';
import ShareMultipleDocumentsModal from '../organisms/ShareMultipleDocumentsModal';
import FileShareSuccessModal from '../organisms/FileShareSuccessModal';
import { DocumentTypeT } from '../organisms/TabAppointment';
import {
    documentAvailableForPatient,
    sendMedicalRecordsForPatient,
} from '../services/emr.service';
import CustomLoader from '../atoms/CustomLoader';
import { getAuth } from '../services/identity.service';
import { debounce } from 'lodash';
import EditOwnerinfo from '../organisms/patient/EditOwnerinfo';
import { useForm } from 'react-hook-form';
import HorizontalTabs, { TabItemType } from '@/app/atoms/HorizontalTabs';
import OwnerList from '../organisms/OwnerList';
import { useGetClinicOwners } from '../services/owner.queries';

// This will be moved inside the component to make it dynamic

export interface PatientsType {
    patientsTab: {
        tableData: PatientT[];
        pagination: PaginationType;
        setPagination: React.Dispatch<React.SetStateAction<PaginationType>>;
        totalPages: number;
        listLoadStatus: 'error' | 'success' | 'pending';
    };
    pendingPaymentTab: {
        tableData: LedgerPatientT[];
        pagination: PaginationType;
        setPagination: React.Dispatch<React.SetStateAction<PaginationType>>;
        totalPages: number;
        listLoadStatus: 'error' | 'success' | 'pending';
    };
    onAddPatient: () => void;
    control: any;
    errors: { [key: string]: { message: string } };
    setValue: (name: string, value: any) => void;
    getValues: Function;
    register: any;
    watch: (name: string) => any;
    handleSubmit: any;
    setEditModal: Function;
    clearErrors: () => void;
    reset: () => void;
    pageIndex: number;
    limit: number;
    debounceSearchTerm: string;
    handleSearch: (value: string) => void;
    searchTerm: string;
    customRule?: { patientLastNameAsOwnerLastName: boolean };
    refreshPatientsData?: () => void; // Optional function to refresh patients data
}

interface Owner {
    id: string;
    ownerBalance: number;
    name: string;
    phoneNumber: string;
}
interface PatientOwner {
    id: string;
    isPrimary: boolean;
    firstName: string;
    lastName: string;
    countryCode: string;
    phoneNumber: string;
    address: string;
    email: string;
}

// Define the OwnerT interface based on the image
interface OwnerT {
    id: string;
    ownerName: string;
    secondaryName?: string;
    emails: string[];
    phoneNumbers: string[];
    noOfPets: number;
    petsSummary: string; // e.g., "Leo, Jamies +3 other pets"
    balance: number;
    customTableWrapperClass: string;
    isHighlight?: 'active' | string;
    tableWrapper?: string;
    rowBorder?: boolean;
    headerSticky?: boolean;
    extraClass?: string;
    toShowEmptyState?: boolean;
    address?: string | null;
    countryCode?: string;
}

const Patients = ({
    patientsTab,
    pendingPaymentTab,
    onAddPatient,
    register,
    getValues,
    watch,
    control,
    setValue,
    handleSubmit,
    errors,
    reset,
    clearErrors,
    handleSearch,
    pageIndex,
    limit,
    searchTerm,
    customRule,
    refreshPatientsData,
}: PatientsType) => {
    const [showDropdown, setShowDropdown] = useState(false);
    const [viewModal, setViewModal] = useState(false);
    const [editModal, setEditModal] = useState(false);
    const [isLoadingState, setIsLoadingState] = useState(false);
    const [patientId, setPatientId] = useState('');
    const [isShowCreditAmount, setIsShowCreditAmount] = useState(false);
    const [isShowPaymentHistory, setIsShowPaymentHistory] = useState(false);
    const [isMultipleFileShareModal, setIsMultipleFileShareModal] =
        useState(false);
    const [isMultipleFileShareSuccess, setIsMultipleFileShareSuccess] =
        useState(false);
    const [documentAvalability, setDocumentAvailability] = useState({});
    interface SelectedPatientInfo {
        patientId: string | null;
        patientName: string;
        owners: Array<{
            id: string;
            name: string;
            phoneNumber: string;
            ownerBalance: number;
        }>;
        balance: number;
        profileImage?: string;
    }

    const [selectedPatientInfo, setSelectedPatientInfo] =
        useState<SelectedPatientInfo>({
            patientId: null,
            patientName: '',
            owners: [],
            balance: 0,
        });
    const router = useRouter();
    const handleTableRowClick = (rowData: any) => {
        router.push(`/patients/${rowData.original.id}/details`);
    };
    const [currentPatientId, setCurrentPatientId] = useState<string | null>(
        null
    );
    //==========================
    // To create an entry for credit
    const { createPaymentDetailsMutation } = useCreatePaymentDetailsMutation(
        pageIndex,
        limit,
        searchTerm,
        'true'
    );
    const auth = getAuth();
    const CLINIC_ID = auth?.clinicId;
    // To get the payment details list
    const { data: paymentDetailsList, status: paymentDetailsStatus } =
        useGetPaymentDetailsForAPatient(currentPatientId as string);
    //==========================
    const [isSubmittingCredit, setIsSubmittingCredit] = useState(false);
    const [ownerDetailsModal, setOwnerDetailsModal] = useState(false);

    // Add state for the active tab
    const [activeTab, setActiveTab] = useState<string>('patients');

    // Dynamic breadcrumb list based on active tab
    const breadcrumbList = [
        { id: 1, name: 'Dashboard', path: '/dashboard' },
        {
            id: 2,
            name: activeTab === 'patients' ? 'Patients' : 'Client Details',
            path: '/patients',
        },
    ];

    // Ref to track the previous tab
    const previousTabRef = useRef<string>('patients');

    // Pagination state for OwnerList
    const [ownerPagination, setOwnerPagination] = useState<PaginationType>({
        pageIndex: 0,
        pageSize: 10, // Default page size
    });

    // Search state for owners
    const [ownerSearchTerm, setOwnerSearchTerm] = useState('');

    // Get owners data from API
    const {
        data: ownersResponse,
        isLoading: ownersLoading,
        error: ownersError,
    } = useGetClinicOwners({
        clinicId: CLINIC_ID || '',
        page: ownerPagination.pageIndex + 1,
        limit: ownerPagination.pageSize,
        search: ownerSearchTerm,
    });

    const ownersData = ownersResponse?.data?.owners || [];
    const ownerTotalPages = ownersResponse?.data?.totalPages || 0;

    // Add patient details query for owner data
    const { refetch: refetchPatientDetails } = usePatientDetails(patientId);

    const debouncedActOnConfirmCreditAmount = useMemo(
        () =>
            debounce(
                async (
                    type: CREDIT_TYPES,
                    paymentType: PAYMENT_TYPES,
                    amount: number,
                    balance: number
                ) => {
                    if (isSubmittingCredit) return;

                    try {
                        setIsSubmittingCredit(true);
                        console.log(
                            'Patients: actOnConfirmCreditAmount',
                            type,
                            paymentType,
                            amount,
                            balance
                        );
                        let mainBalance: number = 0;

                        const numericBalance = Number(balance);
                        const numericAmount = Number(amount);

                        if (type == CREDIT_TYPES.Return) {
                            mainBalance = numericBalance - numericAmount;
                        } else {
                            mainBalance = numericBalance + numericAmount;
                        }

                        createPaymentDetailsMutation.mutate(
                            {
                                data: {
                                    amount: amount,
                                    type: type,
                                    paymentType: paymentType,
                                    amountPayable: 0,
                                    mainBalance: mainBalance,
                                    previousBalance: balance,
                                    transactionAmount: amount,
                                    ownerId: selectedPatientInfo.owners[0].id,
                                },
                            },
                            {
                                onSuccess: (data) => {
                                    console.log(
                                        'Patients: actOnConfirmCreditAmount success',
                                        data
                                    );
                                    setIsShowCreditAmount(false);
                                },
                                onError: (error) => {
                                    console.error(
                                        'Credit confirmation failed:',
                                        error
                                    );
                                },
                            }
                        );
                    } catch (error) {
                        console.error('Credit processing failed:', error);
                    } finally {
                        setIsSubmittingCredit(false);
                    }
                },
                1000,
                { leading: true, trailing: false }
            ),
        [
            isSubmittingCredit,
            createPaymentDetailsMutation,
            selectedPatientInfo,
            CLINIC_ID,
        ]
    );

    useEffect(() => {
        return () => {
            debouncedActOnConfirmCreditAmount.cancel();
        };
    }, [debouncedActOnConfirmCreditAmount]);

    const onShareDocuments = async (patientId: string) => {
        const documentAvailabilityRespnse =
            await documentAvailableForPatient(patientId);
        setDocumentAvailability(documentAvailabilityRespnse?.data);
        setIsMultipleFileShareModal(true);
    };

    async function sendDocuments(
        documentType: DocumentTypeT[],
        isSendMail: boolean,
        isSendWhatsapp: boolean
    ) {
        const shareMode: Array<string> = [];
        if (isSendMail) shareMode.push('email');
        if (isSendWhatsapp) shareMode.push('whatsapp');
        const sendDocumentResonse = await sendMedicalRecordsForPatient(
            patientId!,
            shareMode,
            JSON.stringify(documentType)
        );
        if (sendDocumentResonse?.status) {
            setIsLoadingState(false);
            setIsMultipleFileShareModal(false);
            setIsMultipleFileShareSuccess(true);
        }
    }

    // Separate form for owner editing
    const {
        register: ownerRegister,
        handleSubmit: ownerHandleSubmit,
        getValues: ownerGetValues,
        setValue: ownerSetValue,
        setError: ownerSetError,
        control: ownerControl,
        formState: { errors: ownerErrors },
        watch: ownerWatch,
        reset: ownerReset,
    } = useForm<{
        patientOwners: PatientOwner[];
    }>({
        defaultValues: {
            patientOwners: [],
        },
    });

    const handleOwnerDelete = (index: number) => {
        const patientOwners = ownerGetValues('patientOwners');
        const updatedPatientOwners = patientOwners?.filter(
            (_, i) => i !== index
        );

        ownerSetValue('patientOwners', updatedPatientOwners, {
            shouldValidate: true,
        });
    };

    const onEditOwner = async (info: any) => {
        try {
            // Reset form and set loading state
            ownerReset({ patientOwners: [] });

            // Set patient ID and fetch data
            setPatientId(info.row.original.id);
            const patientDetails = await refetchPatientDetails();

            if (patientDetails?.data?.data) {
                // Map owner data with proper typing
                const ownerData =
                    patientDetails.data.data.patientOwners?.map(
                        (owner: any) => ({
                            id: owner.ownerBrand.id,
                            isPrimary: owner.isPrimary,
                            firstName: owner.ownerBrand.firstName,
                            lastName: owner.ownerBrand.lastName,
                            countryCode:
                                owner.ownerBrand.globalOwner?.countryCode || '',
                            phoneNumber:
                                owner.ownerBrand.globalOwner?.phoneNumber || '',
                            address: owner.ownerBrand.address,
                            email: owner.ownerBrand.email,
                        })
                    ) || [];

                // Sort owners - primary owner first
                const sortedOwners = [...ownerData].sort(
                    (a: PatientOwner, b: PatientOwner) => {
                        // Primary owner (isPrimary: true) should come first
                        if (a.isPrimary && !b.isPrimary) return -1;
                        if (!a.isPrimary && b.isPrimary) return 1;
                        return 0;
                    }
                );

                // Set the form data with sorted owners
                ownerSetValue('patientOwners', sortedOwners);

                // Only open modal after data is loaded
                setOwnerDetailsModal(true);
            }
        } catch (error) {
            console.error('Error loading owner details:', error);
        }
    };

    const handleCloseOwnerModal = () => {
        setOwnerDetailsModal(false);
        ownerReset({ patientOwners: [] });
        setPatientId(''); // Reset patient ID when closing
    };

    // Debounced search for owners
    const debouncedOwnerSearch = useMemo(
        () =>
            debounce((searchTerm: string) => {
                setOwnerSearchTerm(searchTerm);
                setOwnerPagination((prev) => ({ ...prev, pageIndex: 0 })); // Reset to first page on search
            }, 500),
        []
    );

    const handleOwnerSearch = (value: string) => {
        debouncedOwnerSearch(value);
    };

    // Effect to handle tab change and refresh patients data when switching from owners to patients
    useEffect(() => {
        const previousTab = previousTabRef.current;

        // If switching from 'owners' to 'patients', refresh the patients data
        if (previousTab === 'owners' && activeTab === 'patients') {
            if (refreshPatientsData) {
                refreshPatientsData();
            } else {
                // Fallback: Force a pagination update to trigger data refresh
                patientsTab.setPagination((prev) => ({ ...prev }));
            }
        }

        // Update the previous tab reference
        previousTabRef.current = activeTab;
    }, [activeTab, refreshPatientsData, patientsTab.setPagination]);

    return (
        <div className="">
            <div className="flex justify-between items-center">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />

                <Button
                    icon
                    id="add-patient"
                    size="small"
                    className="px-6 items-center"
                    onClick={onAddPatient}
                >
                    <IconAdd size={16} />
                    <span>Add Patient</span>
                </Button>
            </div>

            <div className="mt-4 mb-8 flex justify-between items-center">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                    textColor="text-neutral-900"
                >
                    {activeTab === 'patients' ? 'Patients' : 'Client Details'}
                </Heading>

                <Searchbar
                    id={
                        activeTab === 'patients'
                            ? 'patients-search-bar'
                            : 'owners-search-bar'
                    }
                    name="SearchBar"
                    placeholder="Search..."
                    onChange={(e) =>
                        activeTab === 'patients'
                            ? handleSearch(e)
                            : handleOwnerSearch(e)
                    }
                    onClear={() =>
                        activeTab === 'patients'
                            ? handleSearch('')
                            : handleOwnerSearch('')
                    }
                />
            </div>
            <div className="mb-4">
                <HorizontalTabs
                    variant="secondary"
                    className="w-fit"
                    activeTab={activeTab}
                    tabItems={[
                        { id: 'patients', label: 'Patients' },
                        { id: 'owners', label: 'Owners' },
                    ]}
                    onTabChanged={(tab) => setActiveTab(tab.id)}
                    ariaLabel="Patient Status"
                    highlight="fill"
                    size="m"
                    color="primary"
                />
            </div>

            <div className="bg-white rounded-2xl sticky top-4 h-[calc(100vh_-_210px)] overflow-auto">
                {activeTab === 'patients' && (
                    <PatientList
                        customRule={customRule}
                        onDelete={function (data: { row: any }): void {
                            throw new Error('Function not implemented.');
                        }}
                        onCreditAmount={async (info: any) => {
                            setIsShowCreditAmount(true);
                            setSelectedPatientInfo({
                                patientId: info.row.original.id,
                                patientName: info.row.original.patientName,
                                owners: info.row.original.owners,
                                balance:
                                    info.row.original.owners[0].ownerBalance,
                                profileImage: getProfileImage({
                                    species: info.row.original.species,
                                    breedValue: info.row.original.breed,
                                }),
                            });
                        }}
                        onPaymentHistory={async (info: any) => {
                            setIsShowPaymentHistory(true);
                            setSelectedPatientInfo({
                                patientId: info.row.original.id,
                                patientName: info.row.original.patientName,
                                owners: info.row.original.owners,
                                balance:
                                    info.row.original.owners[0].ownerBalance,
                                profileImage: getProfileImage({
                                    species: info.row.original.species,
                                    breedValue: info.row.original.breed,
                                }),
                            });

                            setCurrentPatientId(info.row.original.id);
                        }}
                        setViewModal={setViewModal}
                        setPatientId={setPatientId}
                        setShowDropdown={setShowDropdown}
                        emptyTableButtonHandle={onAddPatient}
                        onShareDocuments={onShareDocuments}
                        onEdit={async (info: any) => {
                            setPatientId(info.row.original.id);
                            setEditModal(true);
                        }}
                        onEditOwner={onEditOwner}
                        {...patientsTab}
                    />
                )}
                {activeTab === 'owners' && (
                    <OwnerList
                        tableData={ownersData}
                        pagination={ownerPagination}
                        setPagination={setOwnerPagination}
                        totalPages={ownerTotalPages}
                        listLoadStatus={
                            ownersLoading
                                ? 'pending'
                                : ownersError
                                  ? 'error'
                                  : 'success'
                        }
                    />
                )}
            </div>

            {/* {viewModal && (
                <ViewPatientDetails
                    id={patientId}
                    viewModal={viewModal}
                    setViewModal={setViewModal}
                />
            )} */}

            {editModal && (
                <Modal
                    isOpen={editModal}
                    onClose={() => setEditModal(false)}
                    modalTitle={'Update Patient'}
                    dataAutomation="add-patient"
                >
                    <LoadEditPatient
                        {...{
                            patientId,
                            control,
                            errors,
                            setValue,
                            setEditModal,
                            getValues,
                            watch,
                            register,
                            handleSubmit,
                            clearErrors,
                            reset,
                        }}
                    />
                </Modal>
            )}

            {isShowCreditAmount && selectedPatientInfo.patientId && (
                <CreditAmount
                    isOpen={isShowCreditAmount}
                    onClose={() => setIsShowCreditAmount(false)}
                    onCancel={() => setIsShowCreditAmount(false)}
                    onCheckout={(data) => {
                        let paymentType = PAYMENT_TYPES.Cash;

                        if (data.paymentType) {
                            paymentType = data.paymentType;
                        }

                        debouncedActOnConfirmCreditAmount(
                            data.type,
                            paymentType,
                            data.amount,
                            selectedPatientInfo.owners[0]?.ownerBalance || 0
                        );
                    }}
                    onConfirm={(data: FormValuesCheckOut) => {}}
                    patientInfo={{
                        ...selectedPatientInfo,
                        patientId: selectedPatientInfo.patientId!,
                    }}
                    disabled={isSubmittingCredit}
                    confirmButtonLabel={
                        isSubmittingCredit ? 'Processing...' : 'Confirm'
                    }
                />
            )}

            {isShowPaymentHistory &&
                paymentDetailsStatus === 'success' &&
                selectedPatientInfo.patientId && (
                    <PaymentHistory
                        isOpen={isShowPaymentHistory}
                        onClose={() => setIsShowPaymentHistory(false)}
                        onCheckout={function (): void {}}
                        patientInfo={{
                            ...selectedPatientInfo,
                            patientId: selectedPatientInfo.patientId!,
                        }}
                        paymentDetailsList={
                            paymentDetailsList.data.paymentDetails
                        }
                        balanceAmount={(
                            selectedPatientInfo.owners[0]?.ownerBalance || 0
                        ).toString()}
                        ownerDetails={paymentDetailsList.data.ownerDetails}
                    ></PaymentHistory>
                )}

            {isMultipleFileShareModal && (
                <ShareMultipleDocumentsModal
                    isOpen={isMultipleFileShareModal}
                    onClose={() => {
                        setIsMultipleFileShareModal(false);
                    }}
                    handleCancel={() => {
                        setIsMultipleFileShareModal(false);
                    }}
                    handleShare={(
                        documentType: DocumentTypeT[],
                        isSendMail: boolean,
                        isSendWhatsapp: boolean
                    ) => {
                        setIsLoadingState(true);
                        sendDocuments(documentType, isSendMail, isSendWhatsapp);
                    }}
                    documentAvailability={documentAvalability}
                />
            )}

            {ownerDetailsModal && (
                <Modal
                    modalTitle="Edit Owner Details"
                    isOpen={ownerDetailsModal}
                    onClose={handleCloseOwnerModal}
                    dataAutomation="edit-owner-details"
                >
                    {ownerGetValues('patientOwners')?.map(
                        (owner: any, index: number) => (
                            <EditOwnerinfo
                                key={owner.id}
                                patientId={patientId}
                                control={ownerControl}
                                errors={ownerErrors}
                                setValue={ownerSetValue}
                                setError={ownerSetError}
                                getValues={ownerGetValues}
                                register={ownerRegister}
                                watch={ownerWatch}
                                index={index}
                                showSecondOwnersInfo={
                                    ownerGetValues('patientOwners')?.length > 1
                                }
                                handleDeleteOwnersInfo={() =>
                                    handleOwnerDelete(index)
                                }
                                onClose={handleCloseOwnerModal}
                            />
                        )
                    )}
                </Modal>
            )}

            <FileShareSuccessModal
                isOpen={isMultipleFileShareSuccess}
                onClose={() => {
                    setIsMultipleFileShareSuccess(false);
                }}
            />
            {isLoadingState && <CustomLoader />}
        </div>
    );
};

export default Patients;
