'use client';

import React, { useState } from 'react';
import PetOwnerDetailsSidebar from '@/app/organisms/owner/PetOwnerDetailsSidebar';
import OwnerDetailsContent from '@/app/organisms/owner/OwnerDetailsContent';
import Breadcrumbs, { BreadcrumbItem } from '@/app/molecules/Breadcrumbs';
import { OwnerInvoice } from '@/app/types/owner-invoice';
import { Modal } from '@/app/molecules';
import EditOwnerInfo from '@/app/organisms/owner/EditOwnerinfo';
import { useForm, UseFormReturn } from 'react-hook-form';
import Button from '@/app/atoms/Button';
import ReconcileOwnerCreditsModal from '@/app/organisms/ledger/ReconcileOwnerCreditsModal';
import ReconcileOwnerBalanceView from '@/app/organisms/owner/ReconcileOwnerBalanceView';
import InvoiceDetailsView from '@/app/organisms/owner/InvoiceDetailsView';
import PaymentDetailsView from '@/app/organisms/owner/PaymentDetailsView';
// Note: Share/download functionality has been moved to PaymentDetailsView component
import RefundDetailsView from '@/app/organisms/owner/RefundDetailsView';
import CreditDetailsView from '@/app/organisms/owner/CreditDetailsView';
import { CreditTransactionResponseDto } from '@/app/services/credits.service';

interface PaymentDetailsFilter {
    searchTerm: string;
    startDate: string;
    endDate: string;
    petName: string;
    paymentMode: string;
    paymentType: string;
    userIds: string;
    page: number;
    limit: number;
}

interface InvoiceFilter {
    searchTerm?: string;
    startDate?: string;
    endDate?: string;
    petName?: string;
    status?: string;
    page?: number;
    limit?: number;
    userId?: string;
}

interface CreditNoteFilter {
    searchTerm?: string;
    startDate?: string;
    endDate?: string;
    petName?: string;
    status?: string;
    userId?: string;
    page?: number;
    limit?: number;
    invoiceType?: string;
}

interface CreditTransactionFilter {
    searchTerm?: string;
    startDate?: string;
    endDate?: string;
    derivedTransactionTypes?: string;
    userId?: string;
    page?: number;
    limit?: number;
    selectedTransactionTypes?: Array<{ id: string; label: string }>;
    selectedUsers?: Array<{ id: string; name: string }>;
}

interface PetOwnerDetailsTemplateProps {
    ownerData: {
        id: string;
        name: string;
        avatarChar?: string; // Or imageUrl
        petCount: number;
        mobile: string;
        email: string;
        balance?: number;
        credits?: number;
        countryCode?: string;
        address?: string | null;
    };
    petList: Array<{
        id: string;
        name: string;
        breed: string;
        species?: string;
        avatarUrl?: string;
    }>;
    invoicesData: OwnerInvoice[]; // Using the defined type
    creditNotesData: OwnerInvoice[]; // Credit notes data
    paymentDetailsData: any[]; // Payment details array
    ledgerData: any[]; // Ledger data array
    breadcrumbList: BreadcrumbItem[];
    ownerId: string; // Add ownerId property
    paymentFilters: PaymentDetailsFilter;
    onPaymentFilterChange: (filters: PaymentDetailsFilter) => void;
    paymentTotalCount: number;
    isPaymentLoading: boolean;
    onLoadMorePayments: () => void;
    hasMorePayments: boolean;
    paymentUserFilters?: Array<{ id: string; name: string }>;
    // New props for invoice pagination
    invoiceFilters?: InvoiceFilter;
    onInvoiceFilterChange?: (filters: InvoiceFilter) => void;
    invoiceTotalCount?: number;
    isInvoiceLoading?: boolean;
    onLoadMoreInvoices?: (shouldRefetch?: boolean) => void;
    hasMoreInvoices?: boolean;
    invoiceUserFilters?: Array<{ id: string; name: string }>;
    // Credit note related props
    creditNoteFilters?: CreditNoteFilter;
    onCreditNoteFilterChange?: (filters: CreditNoteFilter) => void;
    creditNoteTotalCount?: number;
    isCreditNoteLoading?: boolean;
    onLoadMoreCreditNotes?: () => void;
    hasMoreCreditNotes?: boolean;
    creditNoteUserFilters?: Array<{ id: string; name: string }>;
    // Credit transaction related props
    creditTransactionsData?: CreditTransactionResponseDto[];
    creditTransactionFilters?: CreditTransactionFilter;
    onCreditTransactionFilterChange?: (
        filters: CreditTransactionFilter
    ) => void;
    creditTransactionTotalCount?: number;
    isCreditTransactionLoading?: boolean;
    onLoadMoreCreditTransactions?: () => void;
    hasMoreCreditTransactions?: boolean;
    creditTransactionUserFilters?: Array<{ id: string; name: string }>;

    // Ledger related props
    ledgerFilters?: {
        searchTerm?: string;
        startDate?: Date | null;
        endDate?: Date | null;
        userId?: string;
        selectedUsers?: Array<{ id: string; name: string }>;
    };
    onLedgerFilterChange?: (filters: any) => void;
    ledgerTotalCount?: number;
    isLedgerLoading?: boolean;
    onLoadMoreLedger?: () => void;
    hasMoreLedger?: boolean;
    ledgerUserFilters?: Array<{ id: string; name: string }>;
    ledgerSummary?: {
        totalMonetaryDebits: number;
        totalMonetaryCredits: number;
        finalRunningBalance: number;
        totalProfileCreditsAdded: number;
        totalProfileCreditsUsed: number;
        finalRunningCredits: number;
    };
    cartOptions?: any; // Add cartOptions for inventory data
}

// Define a type for the owner form data - adjust based on actual fields
// This now matches the structure EditOwnerinfo likely expects
interface SingleOwnerData {
    id: string;
    name: string;
    phoneNumber: string;
    email: string;
    // Add other fields that EditOwnerinfo might manage internally for an owner
    // e.g., isPrimary, firstName, lastName, countryCode, address
    // For now, keeping it simple based on original OwnerData for PetOwnerDetailsTemplate
    firstName?: string; // Example: if EditOwnerinfo splits name
    lastName?: string; // Example: if EditOwnerinfo splits name
    isPrimary?: boolean;
    countryCode?: string;
    address?: string | null;
    // Ensure these fields match what EditOwnerinfo expects to find/set
}

interface OwnerFormData {
    patientOwners: SingleOwnerData[];
}

const PetOwnerDetailsTemplate: React.FC<PetOwnerDetailsTemplateProps> = ({
    ownerData,
    petList,
    invoicesData,
    creditNotesData,
    paymentDetailsData,
    ledgerData,
    breadcrumbList,
    ownerId,
    paymentFilters,
    onPaymentFilterChange,
    paymentTotalCount,
    isPaymentLoading,
    onLoadMorePayments,
    hasMorePayments,
    paymentUserFilters,
    invoiceFilters,
    onInvoiceFilterChange,
    invoiceTotalCount,
    isInvoiceLoading,
    onLoadMoreInvoices,
    hasMoreInvoices,
    invoiceUserFilters,
    creditNoteFilters,
    onCreditNoteFilterChange,
    creditNoteTotalCount,
    isCreditNoteLoading,
    onLoadMoreCreditNotes,
    hasMoreCreditNotes,
    creditNoteUserFilters,
    creditTransactionsData,
    creditTransactionFilters,
    onCreditTransactionFilterChange,
    creditTransactionTotalCount,
    isCreditTransactionLoading,
    onLoadMoreCreditTransactions,
    hasMoreCreditTransactions,
    creditTransactionUserFilters,
    ledgerFilters,
    onLedgerFilterChange,
    ledgerTotalCount,
    isLedgerLoading,
    onLoadMoreLedger,
    hasMoreLedger,
    ledgerUserFilters,
    cartOptions,
}) => {
    const [isReconcileBalanceViewOpen, setIsReconcileBalanceViewOpen] =
        useState(false);
    const [isReconcileCreditsModalOpen, setIsReconcileCreditsModalOpen] =
        useState(false);
    const [isEditOwnerModalOpen, setIsEditOwnerModalOpen] = useState(false);

    // New state for Invoice Detail View
    const [isInvoiceDetailViewOpen, setIsInvoiceDetailViewOpen] =
        useState(false);
    const [selectedInvoiceForDetailView, setSelectedInvoiceForDetailView] =
        useState<OwnerInvoice | null>(null);
    const [invoiceAutoTriggerAction, setInvoiceAutoTriggerAction] = 
        useState<'edit' | 'delete' | 'write-off' | undefined>(undefined);

    // New state for Payment Detail View
    const [isPaymentDetailViewOpen, setIsPaymentDetailViewOpen] =
        useState(false);
    const [selectedPaymentForDetailView, setSelectedPaymentForDetailView] =
        useState<any | null>(null);

    // New state for Refund Detail View
    const [isRefundDetailViewOpen, setIsRefundDetailViewOpen] =
        useState(false);
    const [selectedRefundForDetailView, setSelectedRefundForDetailView] =
        useState<OwnerInvoice | null>(null);

    // New state for Credit Detail View
    const [isCreditDetailViewOpen, setIsCreditDetailViewOpen] =
        useState(false);
    const [selectedCreditForDetailView, setSelectedCreditForDetailView] =
        useState<CreditTransactionResponseDto | null>(null);

    // IDEXX Related State Management
    const [showIdexxURL, setShowIdexxURL] = useState(false);
    const [idexxURL, setIdexxURL] = useState('');
    const [showIdexxOrderCreation, setShowIdexxOrderCreation] = useState(false);
    const [idexxCreationModalText, setIdexxCreationModalText] = useState('');

    // State to track the last active tab when navigating to detail views
    const [lastActiveTab, setLastActiveTab] = useState<string>('invoices');

    const [activeTab, setActiveTab] = useState<string>('invoices');

    // State for reconcile balance
    const [reconcilePageFilters, setReconcilePageFilters] = useState({
        searchTerm: '',
        startDate: '',
        endDate: '',
        petName: '',
        status: '',
    });

    // State for reconcile balance
    const [reconcileBalancePageLoading, setReconcileBalancePageLoading] =
        useState(false);

    // State for reconcile balance
    const [
        reconcileBalanceSelectedInvoices,
        setReconcileBalanceSelectedInvoices,
    ] = useState<OwnerInvoice[]>([]);

    // Additional state for balance reconciliation
    const [invoicesToReconcile, setInvoicesToReconcile] = useState<
        OwnerInvoice[] | null
    >(null);

    // Form hook for editing owner details
    const {
        control: ownerFormControl,
        handleSubmit: ownerFormHandleSubmit,
        setValue: ownerFormSetValue,
        getValues: ownerFormGetValues,
        reset: ownerFormReset,
        formState: { errors: ownerFormErrors },
        watch: ownerFormWatch,
        register: ownerFormRegister,
        setError: ownerFormSetError,
    } = useForm<OwnerFormData>({
        defaultValues: {
            patientOwners: [
                {
                    id: ownerData.id,
                    name: ownerData.name,
                    firstName: ownerData.name.split(' ')[0] || '',
                    lastName:
                        ownerData.name.split(' ').slice(1).join(' ') || '',
                    phoneNumber: ownerData.mobile,
                    email: ownerData.email,
                    isPrimary: true,
                    countryCode: ownerData.countryCode || '',
                    address: ownerData.address || null,
                },
            ],
        },
    });

    const handleEditOwnerClick = () => {
        ownerFormReset({
            patientOwners: [
                {
                    id: ownerData.id,
                    name: ownerData.name,
                    firstName: ownerData.name.split(' ')[0] || '',
                    lastName:
                        ownerData.name.split(' ').slice(1).join(' ') || '',
                    phoneNumber: ownerData.mobile,
                    email: ownerData.email,
                    isPrimary: true,
                    countryCode: ownerData.countryCode || '',
                    address: ownerData.address || null,
                },
            ],
        });
        setIsEditOwnerModalOpen(true);
    };

    const handleOwnerFormSubmit = async (formData: OwnerFormData) => {
        console.log(
            'Submitting owner data (from patientOwners array):',
            formData.patientOwners[0]
        );
        try {
            setIsEditOwnerModalOpen(false);
        } catch (error) {
            console.error('Failed to update owner details:', error);
        }
    };

    const handleReconcileCreditsClick = () => {
        setIsReconcileCreditsModalOpen(true);
    };

    const handleReconcileBalanceClick = () => {
        setIsReconcileBalanceViewOpen(true);
        setInvoicesToReconcile(null);
    };

    const handleBackFromReconcileBalanceView = (success?: boolean) => {
        setIsReconcileBalanceViewOpen(false);
        setInvoicesToReconcile(null);

        // If coming back from a successful reconciliation and we have an invoice detail view open,
        // trigger a refetch to ensure the invoice data is up-to-date
        if (
            success &&
            isInvoiceDetailViewOpen &&
            selectedInvoiceForDetailView &&
            onLoadMoreInvoices
        ) {
            console.log('Reconciliation successful, refreshing invoice data');
            onLoadMoreInvoices(true); // Force a refetch of current data

            // After a short delay, update the selected invoice with fresh data
            setTimeout(() => {
                const refreshedInvoice = invoicesData.find(
                    (inv) => inv.id === selectedInvoiceForDetailView.id
                );

                if (refreshedInvoice) {
                    console.log(
                        'Updating invoice with refreshed data after reconciliation'
                    );
                    setSelectedInvoiceForDetailView({
                        ...refreshedInvoice,
                    });
                }
            }, 500); // Shorter delay since we're already waiting for the API call
        }
    };

    const handleSwitchToReconcileViewWithInvoices = (
        selectedInvoices: OwnerInvoice[]
    ) => {
        setInvoicesToReconcile(selectedInvoices);
        setIsReconcileBalanceViewOpen(true);
    };

    const handleShowInvoiceDetailView = (invoice: OwnerInvoice, action?: 'edit' | 'delete' | 'write-off') => {
        // When showing invoice detail view, we're coming from the invoices tab
        setLastActiveTab('invoices');
        setSelectedInvoiceForDetailView(invoice);
        setInvoiceAutoTriggerAction(action);
        setIsInvoiceDetailViewOpen(true);
    };

    const handleBackFromInvoiceDetailView = () => {
        setIsInvoiceDetailViewOpen(false);
        setSelectedInvoiceForDetailView(null);
        setInvoiceAutoTriggerAction(undefined);
    };

    // New handlers for PaymentDetailsView
    const handleShowPaymentDetailView = (payment: any) => {
        // When showing payment detail view, we're coming from the payment-history tab
        setLastActiveTab('payment-history');
        setSelectedPaymentForDetailView(payment);
        setIsPaymentDetailViewOpen(true);
    };

    const handleBackFromPaymentDetailView = () => {
        setIsPaymentDetailViewOpen(false);
        setSelectedPaymentForDetailView(null);
    };

    // New handlers for RefundDetailsView
    const handleShowRefundDetailView = (refund: OwnerInvoice) => {
        // When showing refund detail view, we're coming from the refunds tab
        setLastActiveTab('refunds');
        setSelectedRefundForDetailView(refund);
        setIsRefundDetailViewOpen(true);
    };

    const handleBackFromRefundDetailView = () => {
        setIsRefundDetailViewOpen(false);
        setSelectedRefundForDetailView(null);
    };

    // New handlers for CreditDetailsView
    const handleShowCreditDetailView = (
        credit: CreditTransactionResponseDto
    ) => {
        // When showing credit detail view, we're coming from the credit-history tab
        setLastActiveTab('credit-history');
        setSelectedCreditForDetailView(credit);
        setIsCreditDetailViewOpen(true);
    };

    const handleBackFromCreditDetailView = () => {
        setIsCreditDetailViewOpen(false);
        setSelectedCreditForDetailView(null);
    };

    // Handler for tab changes in OwnerDetailsContent
    const handleTabChange = (tabId: string) => {
        setLastActiveTab(tabId);
    };

    // Note: Share/download functionality has been moved to PaymentDetailsView component

    const ownerDetailsForReconcile = {
        id: ownerData.id,
        firstName: ownerData.name.split(' ')[0] || '',
        lastName: ownerData.name.split(' ').slice(1).join(' ') || '',
        phoneNumber: ownerData.mobile,
        countryCode: ownerData.countryCode || '',
        ownerBalance: ownerData.balance || 0,
        ownerCredits: ownerData.credits || 0,
    };

    // Helper function to get the correct patient ID for EditOwnerInfo
    const getPatientIdForOwnerEdit = () => {
        // Use the first pet's ID if available, otherwise fallback to ownerId
        return petList.length > 0 ? petList[0].id : ownerId;
    };

    if (isReconcileBalanceViewOpen) {
        const currentOwnerDetailsForBalanceView = {
            id: ownerData.id,
            name: ownerData.name,
            ownerBalance: ownerData.balance || 0,
            ownerCredits: ownerData.credits || 0,
        };
        return (
            <>
                <div className="w-full pb-3">
                    <Breadcrumbs
                        breadcrumbList={breadcrumbList}
                        divider="arrow"
                    />
                </div>
                <div className="flex">
                    <PetOwnerDetailsSidebar
                        ownerData={ownerData}
                        petList={petList}
                        onEditOwner={handleEditOwnerClick}
                        onReconcileCreditsClick={handleReconcileCreditsClick}
                        onReconcileBalanceClick={handleReconcileBalanceClick}
                    />
                    <ReconcileOwnerBalanceView
                        ownerDetails={currentOwnerDetailsForBalanceView}
                        onBack={handleBackFromReconcileBalanceView}
                        initiallySelectedInvoiceIds={
                            invoicesToReconcile?.map((inv) => inv.id) || []
                        }
                    />
                </div>
                {isEditOwnerModalOpen && (
                    <Modal
                        modalTitle="Edit Owner Details"
                        isOpen={isEditOwnerModalOpen}
                        onClose={() => {
                            setIsEditOwnerModalOpen(false);
                            ownerFormReset();
                        }}
                    >
                        <form
                            onSubmit={ownerFormHandleSubmit(
                                handleOwnerFormSubmit
                            )}
                        >
                            {ownerFormGetValues('patientOwners')?.map(
                                (owner, index) => (
                                    <EditOwnerInfo
                                        key={owner.id}
                                        patientId={getPatientIdForOwnerEdit()}
                                        control={ownerFormControl}
                                        errors={ownerFormErrors}
                                        setValue={ownerFormSetValue}
                                        getValues={ownerFormGetValues}
                                        register={ownerFormRegister}
                                        watch={ownerFormWatch}
                                        setError={ownerFormSetError}
                                        index={index}
                                        showSecondOwnersInfo={
                                            ownerFormGetValues('patientOwners')
                                                .length > 1
                                        }
                                        handleDeleteOwnersInfo={() => {
                                            console.log(
                                                'Attempting to delete owner at index:',
                                                index
                                            );
                                        }}
                                        onClose={() => {
                                            setIsEditOwnerModalOpen(false);
                                            ownerFormReset();
                                        }}
                                    />
                                )
                            )}
                        </form>
                    </Modal>
                )}
                {isReconcileCreditsModalOpen && (
                    <ReconcileOwnerCreditsModal
                        isOpen={isReconcileCreditsModalOpen}
                        onClose={() => setIsReconcileCreditsModalOpen(false)}
                        onCancel={() => setIsReconcileCreditsModalOpen(false)}
                        ownerDetails={ownerDetailsForReconcile}
                    />
                )}
            </>
        );
    }

    if (isInvoiceDetailViewOpen && selectedInvoiceForDetailView) {
        const ownerDetailsForInvoiceView = ownerData && {
            ownerBrand: {
                firstName: ownerData.name.split(' ')[0] || '',
                lastName: ownerData.name.split(' ').slice(1).join(' ') || '',
                ownerBalance: ownerData.balance || 0,
                ownerCredits: ownerData.credits || 0,
                globalOwner: {
                    phoneNumber: ownerData.mobile,
                    countryCode: ownerData.countryCode || '',
                },
                address: ownerData.address || null,
            },
        };

        // Find the most up-to-date version of the invoice from invoicesData
        const updatedInvoice =
            invoicesData.find(
                (inv) => inv.id === selectedInvoiceForDetailView.id
            ) || selectedInvoiceForDetailView;

        // Function to refetch invoices data when needed
        const refetchInvoices = () => {
            if (onLoadMoreInvoices) {
                // This will trigger a refetch of the invoices data
                // Pass true to indicate we want to refetch current data, not load more
                onLoadMoreInvoices(true);

                // Force a delay to ensure the backend has time to process the changes
                setTimeout(() => {
                    // Find and update the selected invoice with the latest data
                    const refreshedInvoice = invoicesData.find(
                        (inv) => inv.id === selectedInvoiceForDetailView.id
                    );

                    if (refreshedInvoice) {
                        console.log(
                            'Updating invoice with refreshed data:',
                            refreshedInvoice
                        );
                        setSelectedInvoiceForDetailView({
                            ...refreshedInvoice,
                        });
                    } else {
                        console.log(
                            'Could not find refreshed invoice data, forcing reload'
                        );
                        // If we can't find the invoice, force a reload of the page
                        if (onLoadMoreInvoices) {
                            onLoadMoreInvoices(true);
                        }
                    }
                }, 1000); // 1 second delay to ensure data is refreshed
            }
        };

        return (
            <>
                <div className="w-full pb-3">
                    <Breadcrumbs
                        breadcrumbList={breadcrumbList}
                        divider="arrow"
                    />
                </div>
                <div className="flex ">
                    <PetOwnerDetailsSidebar
                        ownerData={ownerData}
                        petList={petList}
                        onEditOwner={handleEditOwnerClick}
                        onReconcileCreditsClick={handleReconcileCreditsClick}
                        onReconcileBalanceClick={handleReconcileBalanceClick}
                        hidePetList={true}
                    />

                    <InvoiceDetailsView
                        invoice={updatedInvoice}
                        ownerId={ownerId}
                        ownerDetails={ownerDetailsForInvoiceView}
                        onBack={handleBackFromInvoiceDetailView}
                        refetchInvoices={refetchInvoices}
                        currentMainBalance={ownerData.balance}
                        cartOptions={cartOptions}
                        // IDEXX related props for lab report creation
                        showIdexxURL={showIdexxURL}
                        setShowIdexxURL={setShowIdexxURL}
                        idexxURL={idexxURL}
                        setIdexxURL={setIdexxURL}
                        showIdexxOrderCreation={showIdexxOrderCreation}
                        setShowIdexxOrderCreation={setShowIdexxOrderCreation}
                        idexxCreationModalText={idexxCreationModalText}
                        setIdexxCreationModalText={setIdexxCreationModalText}
                        onIdexxComplete={() => {
                            // Handle IDEXX completion if needed
                            setShowIdexxURL(false);
                            setIdexxURL('');
                            setShowIdexxOrderCreation(false);
                        }}
                        // Auto-trigger action prop
                        autoTriggerAction={invoiceAutoTriggerAction}
                    />
                </div>
                {isEditOwnerModalOpen && (
                    <Modal
                        modalTitle="Edit Owner Details"
                        isOpen={isEditOwnerModalOpen}
                        onClose={() => {
                            setIsEditOwnerModalOpen(false);
                            ownerFormReset();
                        }}
                    >
                        <form
                            onSubmit={ownerFormHandleSubmit(
                                handleOwnerFormSubmit
                            )}
                        >
                            {ownerFormGetValues('patientOwners')?.map(
                                (owner, index) => (
                                    <EditOwnerInfo
                                        key={owner.id}
                                        patientId={getPatientIdForOwnerEdit()}
                                        control={ownerFormControl}
                                        errors={ownerFormErrors}
                                        setValue={ownerFormSetValue}
                                        getValues={ownerFormGetValues}
                                        register={ownerFormRegister}
                                        watch={ownerFormWatch}
                                        setError={ownerFormSetError}
                                        index={index}
                                        showSecondOwnersInfo={
                                            ownerFormGetValues('patientOwners')
                                                .length > 1
                                        }
                                        handleDeleteOwnersInfo={() => {
                                            console.log(
                                                'Attempting to delete owner at index:',
                                                index
                                            );
                                        }}
                                        onClose={() => {
                                            setIsEditOwnerModalOpen(false);
                                            ownerFormReset();
                                        }}
                                    />
                                )
                            )}
                        </form>
                    </Modal>
                )}
                {isReconcileCreditsModalOpen && (
                    <ReconcileOwnerCreditsModal
                        isOpen={isReconcileCreditsModalOpen}
                        onClose={() => setIsReconcileCreditsModalOpen(false)}
                        onCancel={() => setIsReconcileCreditsModalOpen(false)}
                        ownerDetails={ownerDetailsForReconcile}
                    />
                )}
            </>
        );
    }

    // New conditional rendering for PaymentDetailsView
    if (isPaymentDetailViewOpen && selectedPaymentForDetailView) {
        return (
            <>
                <div className="w-full pb-3">
                    <Breadcrumbs
                        breadcrumbList={breadcrumbList}
                        divider="arrow"
                    />
                </div>
                <div className="flex ">
                    <PetOwnerDetailsSidebar
                        ownerData={ownerData}
                        petList={petList}
                        onEditOwner={handleEditOwnerClick}
                        onReconcileCreditsClick={handleReconcileCreditsClick}
                        onReconcileBalanceClick={handleReconcileBalanceClick}
                        hidePetList={true}
                    />
                    <PaymentDetailsView
                        payment={selectedPaymentForDetailView}
                        onBack={handleBackFromPaymentDetailView}
                        ownerId={ownerId}
                    />
                </div>
                {isEditOwnerModalOpen && (
                    <Modal
                        modalTitle="Edit Owner Details"
                        isOpen={isEditOwnerModalOpen}
                        onClose={() => {
                            setIsEditOwnerModalOpen(false);
                            ownerFormReset();
                        }}
                    >
                        <form
                            onSubmit={ownerFormHandleSubmit(
                                handleOwnerFormSubmit
                            )}
                        >
                            {ownerFormGetValues('patientOwners')?.map(
                                (owner, index) => (
                                    <EditOwnerInfo
                                        key={owner.id}
                                        patientId={getPatientIdForOwnerEdit()}
                                        control={ownerFormControl}
                                        errors={ownerFormErrors}
                                        setValue={ownerFormSetValue}
                                        getValues={ownerFormGetValues}
                                        register={ownerFormRegister}
                                        watch={ownerFormWatch}
                                        setError={ownerFormSetError}
                                        index={index}
                                        showSecondOwnersInfo={
                                            ownerFormGetValues('patientOwners')
                                                .length > 1
                                        }
                                        handleDeleteOwnersInfo={() => {
                                            console.log(
                                                'Attempting to delete owner at index:',
                                                index
                                            );
                                        }}
                                        onClose={() => {
                                            setIsEditOwnerModalOpen(false);
                                            ownerFormReset();
                                        }}
                                    />
                                )
                            )}
                        </form>
                    </Modal>
                )}
                {isReconcileCreditsModalOpen && (
                    <ReconcileOwnerCreditsModal
                        isOpen={isReconcileCreditsModalOpen}
                        onClose={() => setIsReconcileCreditsModalOpen(false)}
                        onCancel={() => setIsReconcileCreditsModalOpen(false)}
                        ownerDetails={ownerDetailsForReconcile}
                    />
                )}
            </>
        );
    }

    // New conditional rendering for RefundDetailsView
    if (isRefundDetailViewOpen && selectedRefundForDetailView) {
        const ownerDetailsForRefundView = ownerData && {
            ownerBrand: {
                firstName: ownerData.name.split(' ')[0] || '',
                lastName: ownerData.name.split(' ').slice(1).join(' ') || '',
                ownerBalance: ownerData.balance || 0,
                ownerCredits: ownerData.credits || 0,
                globalOwner: {
                    phoneNumber: ownerData.mobile,
                    countryCode: ownerData.countryCode || '',
                },
                address: ownerData.address || null,
            },
        };
        return (
            <>
                <div className="w-full pb-3">
                    <Breadcrumbs
                        breadcrumbList={breadcrumbList}
                        divider="arrow"
                    />
                </div>
                <div className="flex ">
                    <PetOwnerDetailsSidebar
                        ownerData={ownerData}
                        petList={petList}
                        onEditOwner={handleEditOwnerClick}
                        onReconcileCreditsClick={handleReconcileCreditsClick}
                        onReconcileBalanceClick={handleReconcileBalanceClick}
                        hidePetList={true}
                    />
                    <RefundDetailsView
                        invoice={selectedRefundForDetailView} // Assuming RefundDetailsView expects 'invoice' prop
                        ownerId={ownerId}
                        ownerDetails={ownerDetailsForRefundView}
                        onBack={handleBackFromRefundDetailView}
                    />
                </div>
                {isEditOwnerModalOpen && (
                    <Modal
                        modalTitle="Edit Owner Details"
                        isOpen={isEditOwnerModalOpen}
                        onClose={() => {
                            setIsEditOwnerModalOpen(false);
                            ownerFormReset();
                        }}
                    >
                        <form
                            onSubmit={ownerFormHandleSubmit(
                                handleOwnerFormSubmit
                            )}
                        >
                            {ownerFormGetValues('patientOwners')?.map(
                                (owner, index) => (
                                    <EditOwnerInfo
                                        key={owner.id}
                                        patientId={getPatientIdForOwnerEdit()}
                                        control={ownerFormControl}
                                        errors={ownerFormErrors}
                                        setValue={ownerFormSetValue}
                                        getValues={ownerFormGetValues}
                                        register={ownerFormRegister}
                                        watch={ownerFormWatch}
                                        setError={ownerFormSetError}
                                        index={index}
                                        showSecondOwnersInfo={
                                            ownerFormGetValues('patientOwners')
                                                .length > 1
                                        }
                                        handleDeleteOwnersInfo={() => {
                                            console.log(
                                                'Attempting to delete owner at index:',
                                                index
                                            );
                                        }}
                                        onClose={() => {
                                            setIsEditOwnerModalOpen(false);
                                            ownerFormReset();
                                        }}
                                    />
                                )
                            )}
                        </form>
                    </Modal>
                )}
                {isReconcileCreditsModalOpen && (
                    <ReconcileOwnerCreditsModal
                        isOpen={isReconcileCreditsModalOpen}
                        onClose={() => setIsReconcileCreditsModalOpen(false)}
                        onCancel={() => setIsReconcileCreditsModalOpen(false)}
                        ownerDetails={ownerDetailsForReconcile}
                    />
                )}
            </>
        );
    }

    // New conditional rendering for CreditDetailsView
    if (isCreditDetailViewOpen && selectedCreditForDetailView) {
        return (
            <>
                <div className="w-full pb-3">
                    <Breadcrumbs
                        breadcrumbList={breadcrumbList}
                        divider="arrow"
                    />
                </div>
                <div className="flex ">
                    <PetOwnerDetailsSidebar
                        ownerData={ownerData}
                        petList={petList}
                        onEditOwner={handleEditOwnerClick}
                        onReconcileCreditsClick={handleReconcileCreditsClick}
                        onReconcileBalanceClick={handleReconcileBalanceClick}
                        hidePetList={true}
                    />
                    <CreditDetailsView
                        transaction={selectedCreditForDetailView}
                        ownerId={ownerId}
                        onBack={handleBackFromCreditDetailView}
                    />
                </div>
                {isEditOwnerModalOpen && (
                    <Modal
                        modalTitle="Edit Owner Details"
                        isOpen={isEditOwnerModalOpen}
                        onClose={() => {
                            setIsEditOwnerModalOpen(false);
                            ownerFormReset();
                        }}
                    >
                        <form
                            onSubmit={ownerFormHandleSubmit(
                                handleOwnerFormSubmit
                            )}
                        >
                            {ownerFormGetValues('patientOwners')?.map(
                                (owner, index) => (
                                    <EditOwnerInfo
                                        key={owner.id}
                                        patientId={getPatientIdForOwnerEdit()}
                                        control={ownerFormControl}
                                        errors={ownerFormErrors}
                                        setValue={ownerFormSetValue}
                                        getValues={ownerFormGetValues}
                                        register={ownerFormRegister}
                                        watch={ownerFormWatch}
                                        setError={ownerFormSetError}
                                        index={index}
                                        showSecondOwnersInfo={
                                            ownerFormGetValues('patientOwners')
                                                .length > 1
                                        }
                                        handleDeleteOwnersInfo={() => {
                                            console.log(
                                                'Attempting to delete owner at index:',
                                                index
                                            );
                                        }}
                                        onClose={() => {
                                            setIsEditOwnerModalOpen(false);
                                            ownerFormReset();
                                        }}
                                    />
                                )
                            )}
                        </form>
                    </Modal>
                )}
                {isReconcileCreditsModalOpen && (
                    <ReconcileOwnerCreditsModal
                        isOpen={isReconcileCreditsModalOpen}
                        onClose={() => setIsReconcileCreditsModalOpen(false)}
                        onCancel={() => setIsReconcileCreditsModalOpen(false)}
                        ownerDetails={ownerDetailsForReconcile}
                    />
                )}
            </>
        );
    }

    return (
        <>
            <div className="w-full pb-3">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            </div>
            <div className="flex">
                <PetOwnerDetailsSidebar
                    ownerData={ownerData}
                    petList={petList}
                    onEditOwner={handleEditOwnerClick}
                    onReconcileCreditsClick={handleReconcileCreditsClick}
                    onReconcileBalanceClick={handleReconcileBalanceClick}
                />
                <OwnerDetailsContent
                    invoicesData={invoicesData}
                    creditNotesData={creditNotesData}
                    paymentDetailsData={paymentDetailsData}
                    ledgerData={ledgerData}
                    ownerId={ownerId}
                    ownerBalance={ownerData.balance}
                    ownerCredits={ownerData.credits}
                    ownerName={ownerData.name}
                    ownerPhone={ownerData.mobile}
                    paymentFilters={paymentFilters}
                    onPaymentFilterChange={onPaymentFilterChange}
                    paymentTotalCount={paymentTotalCount}
                    isPaymentLoading={isPaymentLoading}
                    onLoadMorePayments={onLoadMorePayments}
                    hasMorePayments={hasMorePayments}
                    paymentUserFilters={paymentUserFilters}
                    invoiceFilters={invoiceFilters}
                    onInvoiceFilterChange={onInvoiceFilterChange}
                    invoiceTotalCount={invoiceTotalCount || 0}
                    isInvoiceLoading={isInvoiceLoading || false}
                    onLoadMoreInvoices={onLoadMoreInvoices}
                    hasMoreInvoices={hasMoreInvoices || false}
                    invoiceUserFilters={invoiceUserFilters}
                    creditNoteFilters={creditNoteFilters}
                    onCreditNoteFilterChange={onCreditNoteFilterChange}
                    creditNoteTotalCount={creditNoteTotalCount || 0}
                    isCreditNoteLoading={isCreditNoteLoading || false}
                    onLoadMoreCreditNotes={onLoadMoreCreditNotes}
                    hasMoreCreditNotes={hasMoreCreditNotes || false}
                    creditNoteUserFilters={creditNoteUserFilters}
                    onReconcileInvoicesRequest={
                        handleSwitchToReconcileViewWithInvoices
                    }
                    onShowInvoiceDetailViewRequest={handleShowInvoiceDetailView}
                    onShowPaymentDetailViewRequest={handleShowPaymentDetailView}
                    onShowRefundDetailViewRequest={handleShowRefundDetailView}
                    onShowCreditDetailViewRequest={handleShowCreditDetailView}
                    initialActiveTab={lastActiveTab}
                    onTabChange={handleTabChange}
                    creditTransactionsData={creditTransactionsData}
                    creditTransactionFilters={creditTransactionFilters}
                    onCreditTransactionFilterChange={
                        onCreditTransactionFilterChange
                    }
                    creditTransactionTotalCount={creditTransactionTotalCount}
                    isCreditTransactionLoading={isCreditTransactionLoading}
                    onLoadMoreCreditTransactions={onLoadMoreCreditTransactions}
                    hasMoreCreditTransactions={hasMoreCreditTransactions}
                    creditTransactionUserFilters={creditTransactionUserFilters}
                    ledgerFilters={ledgerFilters}
                    onLedgerFilterChange={onLedgerFilterChange}
                    ledgerTotalCount={ledgerTotalCount}
                    isLedgerLoading={isLedgerLoading}
                    onLoadMoreLedger={onLoadMoreLedger}
                    hasMoreLedger={hasMoreLedger}
                    ledgerUserFilters={ledgerUserFilters}
                />
            </div>

            {isEditOwnerModalOpen && (
                <Modal
                    modalTitle="Edit Owner Details"
                    isOpen={isEditOwnerModalOpen}
                    onClose={() => {
                        setIsEditOwnerModalOpen(false);
                        ownerFormReset();
                    }}
                >
                    <form
                        onSubmit={ownerFormHandleSubmit(handleOwnerFormSubmit)}
                    >
                        {ownerFormGetValues('patientOwners')?.map(
                            (owner, index) => (
                                <EditOwnerInfo
                                    key={owner.id}
                                    patientId={getPatientIdForOwnerEdit()}
                                    control={ownerFormControl}
                                    errors={ownerFormErrors}
                                    setValue={ownerFormSetValue}
                                    getValues={ownerFormGetValues}
                                    register={ownerFormRegister}
                                    watch={ownerFormWatch}
                                    setError={ownerFormSetError}
                                    index={index}
                                    showSecondOwnersInfo={
                                        ownerFormGetValues('patientOwners')
                                            .length > 1
                                    }
                                    handleDeleteOwnersInfo={() => {
                                        console.log(
                                            'Attempting to delete owner at index:',
                                            index
                                        );
                                    }}
                                    onClose={() => {
                                        setIsEditOwnerModalOpen(false);
                                        ownerFormReset();
                                    }}
                                />
                            )
                        )}
                    </form>
                </Modal>
            )}

            {isReconcileCreditsModalOpen && (
                <ReconcileOwnerCreditsModal
                    isOpen={isReconcileCreditsModalOpen}
                    onClose={() => setIsReconcileCreditsModalOpen(false)}
                    onCancel={() => setIsReconcileCreditsModalOpen(false)}
                    ownerDetails={ownerDetailsForReconcile}
                />
            )}

            {/* Share/Download modals for PaymentDetailsView have been moved to the component itself */}
        </>
    );
};

export default PetOwnerDetailsTemplate;
