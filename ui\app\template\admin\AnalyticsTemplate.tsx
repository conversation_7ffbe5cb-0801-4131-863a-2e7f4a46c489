'use client';

import React, { useState } from 'react';
import { Breadcrumbs } from '@/app/molecules';
import { Heading } from '@/app/atoms';
import Tabs from '@/app/molecules/Tabs';
import VerticalScroll from '@/app/molecules/VerticalScroll';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import Revenue from '@/app/organisms/analytics/Revenue';
import CollectedPayments from '@/app/organisms/analytics/CollectedPayments';
import OutstandingBalance from '@/app/organisms/analytics/OutstandingBalance';
import Appointments from '@/app/organisms/analytics/Appointments';
import DoctorSummary from '@/app/organisms/analytics/DoctorSummary';
import Summary from '@/app/organisms/analytics/Summary';
import CustomLoader from '@/app/atoms/CustomLoader';

interface AnalyticsTemplateProps {
    breadcrumbList: {
        id: number;
        name: string;
        path: string;
    }[];
}

const AnalyticsTemplate: React.FC<AnalyticsTemplateProps> = ({
    breadcrumbList,
}) => {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState('analytics');
    const {
        control,
        setValue,
        getValues,
        watch,
        formState: { errors },
    } = useForm();

    const handleTabClick = (id: string) => {
        console.log(id);
        switch (id) {
            case 'clinic-details':
                router.push('/admin/clinic-details');
                break;
            case 'users':
                router.push('/admin/users');
                break;
            case 'inventory':
                router.push('/admin/inventory');
                break;
            case 'integrations':
                router.push('/admin/integrations');
                break;
            case 'document-library':
                router.push('/admin/document-library');
                break;
            case 'reminders':
                router.push('/admin/reminders');
                break;
            case 'analytics':
                router.push('/admin/analytics');
                break;
            default:
                router.push('/admin/clinic-details');
        }
    };

    const analyticsData = [
        {
            id: 'summary',
            tabHeading: 'Summary',
            tabContent: (
                <Summary
                    errors={errors}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    onSummaryChange={(value) =>
                        console.log('Summary changed:', value)
                    }
                    onSummaryBlur={(value) =>
                        console.log('Summary blur:', value)
                    }
                />
            ),
        },
        {
            id: 'revenue',
            tabHeading: 'Revenue',
            tabContent: (
                <Revenue
                    errors={errors}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    onRevenueChange={(value) =>
                        console.log('Revenue changed:', value)
                    }
                    onRevenueBlur={(value) =>
                        console.log('Revenue blur:', value)
                    }
                />
            ),
        },
        {
            id: 'collected-payments',
            tabHeading: 'Collected Payments',
            tabContent: (
                <CollectedPayments
                    errors={errors}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    onPaymentsChange={(value) =>
                        console.log('Payments changed:', value)
                    }
                    onPaymentsBlur={(value) =>
                        console.log('Payments blur:', value)
                    }
                />
            ),
        },
        // {
        //     id: 'outstanding-balance',
        //     tabHeading: 'Outstanding Balance',
        //     tabContent: (
        //         <OutstandingBalance
        //             errors={errors}
        //             control={control}
        //             setValue={setValue}
        //             getValues={getValues}
        //             watch={watch}
        //             onBalanceChange={(value) =>
        //                 console.log('Balance changed:', value)
        //             }
        //             onBalanceBlur={(value) =>
        //                 console.log('Balance blur:', value)
        //             }
        //         />
        //     ),
        // },
        {
            id: 'appointments',
            tabHeading: 'Appointments',
            tabContent: (
                <Appointments
                    errors={errors}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    onAppointmentsChange={(value) =>
                        console.log('Appointments changed:', value)
                    }
                    onAppointmentsBlur={(value) =>
                        console.log('Appointments blur:', value)
                    }
                />
            ),
        },
        {
            id: 'doctor-summary',
            tabHeading: 'Doctor Summary',
            tabContent: (
                <DoctorSummary
                    errors={errors}
                    control={control}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    onDoctorSummaryChange={(value) =>
                        console.log('Doctor Summary changed:', value)
                    }
                    onDoctorSummaryBlur={(value) =>
                        console.log('Doctor Summary blur:', value)
                    }
                />
            ),
        },
    ];

    return (
        <>
            <div className="flex items-center justify-between gap-3 w-full py-3">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            </div>
            <div className="mt-3 mb-8 flex justify-between items-center">
                <Heading type="h4" fontWeight="font-medium">
                    Admin
                </Heading>
            </div>

            <Tabs
                className="mt-5"
                defaultActiveTab={activeTab}
                marginTabsTop={false}
                onTabClick={(tab) => {
                    handleTabClick(tab.id);
                }}
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="h-[calc(100dvh-12.75rem)]">
                                <VerticalScroll
                                    defaultActive="summary"
                                    scrollableList={analyticsData}
                                    tabHeaderBottomContent={<></>}
                                    checkoutButtonText=""
                                    activeUsers={false}
                                />
                            </div>
                        ),
                    },
                ]}
            />
        </>
    );
};

export default AnalyticsTemplate;
