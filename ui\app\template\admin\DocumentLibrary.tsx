'use client';

import { Heading } from '@/app/atoms';
import CustomLoader from '@/app/atoms/CustomLoader';
import { Breadcrumbs } from '@/app/molecules';
import { BreadcrumbItem } from '@/app/molecules/Breadcrumbs';
import Tabs from '@/app/molecules/Tabs';
import DocumentLibraryTab from '@/app/organisms/admin/document-library/DocumentLibraryTab';
import { getAuth } from '@/app/services/identity.service';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export interface documentLibraryT {
    breadcrumbList: BreadcrumbItem[];
}

const DocumentLibrary = ({ breadcrumbList }: documentLibraryT) => {
    const [activeTab, setActiveTab] = useState('documents');
    const [activeAttachmentTab, setActiveAttachmentTab] =
        useState('document-library');
        
    const auth = getAuth();
    const userRole = auth?.role;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';
    const router = useRouter();
    const handleTabCLick = (id: string) => {
        console.log(id);
        switch (id) {
            case 'clinicDetails':
                router.push('/admin/clinic-details');
                break;

            case 'users':
                router.push('/admin/users');
                break;

            case 'inventory':
                router.push('/admin/inventory');
                break;

            case 'rooms':
                router.push('/admin/rooms');
                break;

            case 'integrations':
                router.push('/admin/integrations');
                break;

            case 'document-library':
                router.push('/admin/document-library');
                break;
            case 'analytics':
                router.push('/admin/analytics');
                break;
            case 'reminders':
                router.push('/admin/reminders');
                break;

            default:
                router.push('/admin/clinic-details');
        }
    };

    return (
        <div className="">
            <div className="flex items-center justify-between py-3 gap-3 w-full">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />

                {/* <div className="flex gap-2">
                    <Button
                        icon={<Download size={16} />}
                        id="download-latest-version"
                        type="button"
                        variant="neutral"
                        label="Download Latest Version"
                        size="small"
                        iconPosition="left"
                    />
                    <Button
                        icon={<Plus size={16} />}
                        id="bulk-upload"
                        type="button"
                        variant="primary"
                        label="Bulk Upload"
                        size="small"
                        iconPosition="left"
                    />
                </div> */}
            </div>
            <div className="mt-3 mb-8 flex justify-between items-center">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Admin
                </Heading>
                {/* <div className="flex gap-2.5">
                    <Searchbar
                        id="patients-search-bar"
                        name="SearchBar"
                        placeholder="Search..."
                        onChange={onChangeUser}
                    />
                </div> */}
            </div>

            <Tabs
                className="mt-5"
                defaultActiveTab={activeAttachmentTab}
                onTabClick={(tab) => {
                    handleTabCLick(tab.id);
                }}
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="bg-white rounded-2xl p-4 h-[calc(100dvh-12.7rem)] overflow-auto">
                                <DocumentLibraryTab
                                    activeTab={activeTab}
                                    setActiveTab={setActiveTab}
                                />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    ...(isAdmin ? [{
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    }] : []),
                ]}
            />
        </div>
    );
};

export default DocumentLibrary;
