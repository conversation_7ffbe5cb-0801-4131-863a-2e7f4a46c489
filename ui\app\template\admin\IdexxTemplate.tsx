import React, { useReducer, useState } from 'react';
import { Breadcrumbs } from '@/app/molecules';
import { Heading } from '@/app/atoms';
import IdexxTab from '@/app/organisms/admin/Idexx/IdexxTab';
import { CredentialT, IdexxTestT } from '@/app/types/patient';
import {
    useAddIdexxTestItem,
    useCreateIdexxEntryMutation,
    useDeleteIdexxEntry,
    useDeleteIdexxTestListItem,
    useUpdateClinicLabReportMutation,
} from '@/app/services/idexx.queries';
import AddIdexxPriceModal from '@/app/organisms/admin/Idexx/AddIdexxPriceModal';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import AddIdexxModal from '@/app/organisms/admin/Idexx/AddIdexxModal';
import CustomLoader from '@/app/atoms/CustomLoader';
import Tabs from '@/app/molecules/Tabs';
import { useRouter } from 'next/navigation';
import { getAuth } from '@/app/services/identity.service';

interface IdexxTemplateProps {
    credentials: CredentialT[];
    idexxTestdata: any[];
    selectedIdexxData: any[];
    listLoadStatus: 'error' | 'success' | 'pending';
}

const IdexxTemplate: React.FC<IdexxTemplateProps> = ({
    credentials,
    idexxTestdata,
    listLoadStatus,
    selectedIdexxData,
}) => {
    const breadcrumbList = [
        { id: 1, name: 'Admin', path: '/admin/clinic-details' },
        { id: 2, name: 'Integrations', path: '/integrations' },
    ];

    const labReportResolver = yup.object().shape({
        name: yup.string().nullable(),
        id: yup.string().nullable(),
        price: yup.number().nullable(),
        tax: yup.number().nullable(),
        idexxUserName: yup.string().required('User name is required'),
        idexxPassword: yup.string().required('Password is required'),
    });

    const {
        setValue,
        getValues,
        formState,
        reset,
        watch,
        control,
        handleSubmit,
    } = useForm({
        // defaultValues: {},
        resolver: yupResolver(labReportResolver),
        mode: 'onChange',
    });

    const [showPriceModal, setShowPriceModal] = useState(false);
    const [addIdexxCredsModal, setAddIdexCredsModal] = useState(false);
    const [isEditIdexxCreds, setIsEditIdexxCreds] = useState(false);
    const { deleteIdexxEntryMutation } = useDeleteIdexxEntry();
    const [activeAttachmentTab, setActiveAttachmentTab] =
        useState('integrations');
    const [isAddDocumentModal, setIsAddDocumentModal] = useState(false);
    const auth = getAuth();
    const userRole = auth?.role;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';
    const CLINIC_ID = getAuth()?.clinicId;

    // Reducer function with types for state and action

    const { createIdexxEntryMutation } = useCreateIdexxEntryMutation(
        setAddIdexCredsModal,
        reset
    );
    const { addIdexxTestItemMutation } = useAddIdexxTestItem();
    const { deleteIdexxTestListItemMutation } = useDeleteIdexxTestListItem();
    const { updateLabReportMutation } = useUpdateClinicLabReportMutation(
        setShowPriceModal,
        reset
    );
    const handleAddIdexx = () => {
        setAddIdexCredsModal(true);
    };
    const onEditCredential = (data: { username: string; password: string }) => {
        setValue('idexxUserName', data.username, { shouldValidate: true });
        setValue('idexxPassword', data.password, { shouldValidate: true });
        setAddIdexCredsModal(true);
        setIsEditIdexxCreds(true);
    };
    const onDeleteCredential = (data: {
        username: string;
        password: string;
    }) => {
        const cred = credentials.find(
            (item) =>
                item.username === data.username &&
                item.password === data.password
        );
        if (cred && cred.id)
            deleteIdexxEntryMutation.mutate(
                {
                    data: {
                        id: cred.id,
                    },
                },
                {
                    onSuccess: (data: any) => {
                        console.log('deleteIdexxEntryMutation success =', data);
                    },
                    onError: (error: any) => {
                        console.error('deleteIdexxEntryMutation Error:', error);
                    },
                }
            );
    };
    const handleAddIdexxEmpty = () => {};
    const handleTaxChange = () => {};
    const handlePriceChange = () => {};
    const handleEditLabReport = (price: number, tax: number) => {
        updateLabReportMutation.mutate({
            id: getValues('id') as string,
            price,
            tax,
        });
    };
    const handleTestSelect = (id: string, checked: boolean) => {
        const foundTest = idexxTestdata?.find((item) => item.code === id);
        if (foundTest && checked) {
            addIdexxTestItemMutation.mutate(
                {
                    data: {
                        clinicId: CLINIC_ID,
                        name: foundTest.name,
                        chargeablePrice: 0,
                        tax: 0,
                        description: '',
                        integrationType: 'IDEXX',
                        integrationCode: foundTest.code,
                    },
                },
                {
                    onSuccess: (data: any) => {},
                    onError: () => {
                        console.log(
                            'handleCheckboxChange: addIdexxTestItemMutation failure'
                        );
                    },
                }
            );
        }
        if (foundTest && !checked) {
            const foundLabItem = selectedIdexxData.find(
                (labItem) => labItem.integrationCode === id
            );
            if (foundLabItem) {
                deleteIdexxTestListItemMutation.mutate(
                    {
                        data: {
                            id: foundLabItem.id,
                        },
                    },
                    {
                        onSuccess: (data: any) => {
                            console.log('handleTestItemDelete:success= ', data);
                        },
                        onError: (error: any) => {
                            console.error('handleTestItemDelete:Error:', error);
                        },
                    }
                );
            }
        }
    };
    const handleEditTest = (id: string) => {
        const foundLabItem = selectedIdexxData.find(
            (labItem) => labItem.integrationCode === id
        );

        setValue('id', foundLabItem.id, { shouldValidate: true });
        setValue('name', foundLabItem.name, { shouldValidate: true });
        if (foundLabItem.chargeablePrice) {
            setValue('price', foundLabItem.chargeablePrice, {
                shouldValidate: true,
            });
        }
        if (foundLabItem.tax) {
            setValue('tax', foundLabItem.tax, {
                shouldValidate: true,
            });
        }
        setShowPriceModal(true);
    };

    const handleAddIdexxCreds = (data: {
        idexxUserName: string;
        idexxPassword: string;
    }) => {
        console.log('data', data);
        createIdexxEntryMutation.mutate(
            {
                data: {
                    clinicId: CLINIC_ID,
                    userName: data.idexxUserName,
                    password: data.idexxPassword,
                    type: 'IDEXX',
                },
            },
            {
                onSuccess: (data: any) => {
                    console.log(' createIdexxEntryMutation success', data);
                },
                onError: () => {
                    console.log('createIdexxEntryMutation failure');
                },
            }
        );
    };
    const getIdexxTestList = (): IdexxTestT[] => {
        return idexxTestdata?.map((item: any) => {
            const selectedClinicLabReport = selectedIdexxData.find(
                (selectedItem) => selectedItem.integrationCode === item.code
            );
            return {
                id: item.code,
                select: selectedClinicLabReport ? true : false,
                testName: item.name,
                price: selectedClinicLabReport
                    ? selectedClinicLabReport.chargeablePrice
                    : 0,
                tax: selectedClinicLabReport ? selectedClinicLabReport.tax : 0,
                editAction: (code: string) => handleEditTest(code),
            };
        });
    };

    const router = useRouter();

    const handleTabCLick = (id: string) => {
        console.log(id);
        switch (id) {
            case 'clinicDetails':
                router.push('/admin/clinic-details');
                break;

            case 'users':
                router.push('/admin/users');
                break;

            case 'inventory':
                router.push('/admin/inventory');
                break;

            case 'rooms':
                router.push('/admin/rooms');
                break;

            case 'integrations':
                router.push('/admin/integrations');
                break;

            case 'document-library':
                router.push('/admin/document-library');
                break;
            case 'analytics':
                router.push('/admin/analytics');
                break;
            case 'reminders':
                router.push('/admin/reminders');
                break;
            default:
                router.push('/admin/clinic-details');
        }
    };

    return (
        <>
            <div className="flex items-center justify-between py-3 gap-3 w-full">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            </div>

            <div className="mb-8 mt-3">
                <Heading type="h4" fontWeight="font-medium">
                    Admin
                </Heading>
            </div>
            <Tabs
                className="mt-5"
                defaultActiveTab={activeAttachmentTab}
                onTabClick={(tab) => {
                    handleTabCLick(tab.id);
                }}
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className="tab-layout">
                                <IdexxTab
                                    credentials={credentials}
                                    idexxTestdata={getIdexxTestList()}
                                    listLoadStatus={listLoadStatus}
                                    handleAddIdexx={handleAddIdexx}
                                    onEditCredential={onEditCredential}
                                    onDeleteCredential={onDeleteCredential}
                                    handleAddIdexxEmpty={handleAddIdexxEmpty}
                                    handleTaxChange={handleTaxChange}
                                    handlePriceChange={handlePriceChange}
                                    handleTestSelect={handleTestSelect}
                                    handleEditTest={handleEditTest}
                                />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    ...(isAdmin ? [{
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    }] : []),
                ]}
            />
            {showPriceModal && (
                <AddIdexxPriceModal
                    handleCancel={() => {
                        reset();
                        setShowPriceModal(false);
                    }}
                    isOpen={showPriceModal}
                    onSubmit={handleEditLabReport}
                    key={'edit-price-tax'}
                    setValue={setValue}
                    getValues={getValues}
                    errors={[]}
                />
            )}
            {addIdexxCredsModal && (
                <AddIdexxModal
                    handleCancel={() => {
                        reset();
                        setAddIdexCredsModal(false);
                    }}
                    isOpen={addIdexxCredsModal}
                    formMethods={{
                        getValues,
                        setValue,
                        watch,
                        formState,
                        control,
                        reset,
                    }}
                    handleNext={() => {
                        console.log('cledef');

                        handleSubmit(handleAddIdexxCreds)();
                    }}
                    isEdit={isEditIdexxCreds}
                    showEyeIcon={true}
                />
            )}
        </>
    );
};

export default IdexxTemplate;
