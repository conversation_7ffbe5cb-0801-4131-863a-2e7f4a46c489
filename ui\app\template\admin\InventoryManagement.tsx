import React, { useState } from 'react';
import Image from 'next/image';
import InventoryManagementTab from '@/app/organisms/admin/inventory/InventoryManagementTab';
import { Breadcrumbs, Searchbar } from '@/app/molecules';
import { Button, Heading } from '@/app/atoms';
import IconDownload from '@/app/atoms/customIcons/IconDownload.svg';
import { Add } from 'iconsax-react';
import BulkUploadModal from '@/app/organisms/admin/inventory/BulkUploadModal';
import BulkUploadErrorModal from '@/app/organisms/admin/inventory/BulkUploadErrorModal';
import { PaginationType } from '@/app/molecules/Table';
import { format, parseISO } from 'date-fns';
import Tabs from '@/app/molecules/Tabs';
import { useRouter } from 'next/navigation';
import CustomLoader from '@/app/atoms/CustomLoader';
import { getAuth } from '@/app/services/identity.service';

interface InventoryManagementProps {
    onChangeUser: (value: string) => void;
    activeTab: string;
    setActiveTab: (tab: string) => void;
    handleFilter: () => void;
    handleDeleteItem: (itemType: string, itemId: string) => void;
    handleDownloadLatest: () => void;
    handleDownloadTemplate: () => void;
    onFileUpload: (file: File) => Promise<any>;
    errorsData: any;
}

const InventoryManagement: React.FC<InventoryManagementProps> = ({
    onChangeUser,
    activeTab,
    setActiveTab,
    handleFilter,
    handleDeleteItem,
    handleDownloadLatest,
    handleDownloadTemplate,
    onFileUpload,
    errorsData,
}) => {
    const [isBulkUploadModalOpen, setBulkUploadModalOpen] = useState(false);
    const [isErrorModalOpen, setErrorModalOpen] = useState(true);
    const [activeAttachmentTab, setActiveAttachmentTab] = useState('inventory');
    const auth = getAuth();
    const userRole = auth?.role;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';
    const router = useRouter();
    const handleBulkUpload = () => {
        setBulkUploadModalOpen(true);
    };

    const closeBulkUploadModal = () => {
        setBulkUploadModalOpen(false);
    };

    const handleFileUpload = async (file: File) => {
        if (!file) return;
        try {
            const result = await onFileUpload(file);
            console.log('Inisde file upload');
        } catch (error) {
            console.error('Upload failed:', error);
            // Handle error (e.g., show error message to user)
        }
        closeBulkUploadModal();
    };

    const closeErrorModal = () => {
        setErrorModalOpen(false);
    };

    const handleReUpload = () => {
        closeErrorModal();
        setBulkUploadModalOpen(true);
    };
    const breadcrumbList = [
        { id: 1, name: 'Admin', path: '/admin/clinic-details' },
        { id: 2, name: 'Inventory', path: '/inventory' },
    ];

    function formatDate(dateString: string) {
        if (!dateString) return '';
        const date = parseISO(dateString);
        return format(date, 'd MMMM yyyy');
    }

    const handleTabCLick = (id: string) => {
        console.log(id);
        switch (id) {
            case 'clinicDetails':
                router.push('/admin/clinic-details');
                break;

            case 'users':
                router.push('/admin/users');
                break;

            case 'inventory':
                router.push('/admin/inventory');
                break;

            case 'rooms':
                router.push('/admin/rooms');
                break;

            case 'integrations':
                router.push('/admin/integrations');
                break;

            case 'document-library':
                router.push('/admin/document-library');
                break;
            case 'analytics':
                router.push('/admin/analytics');
                break;
            case 'reminders':
                router.push('/admin/reminders');
                break;
            default:
                router.push('/admin/clinic-details');
        }
    };

    return (
        <>
            <div className="flex items-center justify-between gap-3 w-full py-3">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            </div>
            <div className="mt-3 mb-8 flex justify-between items-center">
                <Heading type="h4" fontWeight="font-medium">
                    Admin
                </Heading>
                {/* <div className="flex gap-2.5">
                    <Searchbar
                        id="search-bar"
                        name="SearchBar"
                        placeholder="Search..."
                        onChange={onChangeUser}
                    />
                    <Button
                        id="filter-button"
                        type="button"
                        onlyIcon
                        variant=""
                        size="extraSmall"
                        className='!bg-white'
                        onClick={handleFilter}
                    >
                        <Image src="/images/icons/filter_icon.svg" width={16} height={16} alt="Filter icon" />
                    </Button>
                </div> */}
            </div>
            <Tabs
                className="mt-5"
                defaultActiveTab={activeAttachmentTab}
                onTabClick={(tab) => {
                    handleTabCLick(tab.id);
                }}
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className="bg-white rounded-2xl p-4 h-[calc(100dvh-12.7rem)] overflow-auto">
                                <InventoryManagementTab
                                    handleDeleteItem={handleDeleteItem}
                                    activeTab={activeTab}
                                    setActiveTab={setActiveTab}
                                    handleDownloadLatest={handleDownloadLatest}
                                    handleBulkUpload={handleBulkUpload}
                                />
                                <BulkUploadModal
                                    isOpen={isBulkUploadModalOpen}
                                    onClose={closeBulkUploadModal}
                                    onFileUpload={handleFileUpload}
                                    handleDownloadTemplate={
                                        handleDownloadTemplate
                                    }
                                />
                                <BulkUploadErrorModal
                                    isOpen={isErrorModalOpen}
                                    onClose={closeErrorModal}
                                    errorsData={errorsData}
                                    onReUpload={handleReUpload}
                                    onUpload={handleFileUpload}
                                />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    ...(isAdmin ? [{
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    }] : []),
                ]}
            />
        </>
    );
};

export default InventoryManagement;
