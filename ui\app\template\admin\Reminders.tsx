'use client';

import { <PERSON><PERSON>, Head<PERSON> } from '@/app/atoms';
import CustomLoader from '@/app/atoms/CustomLoader';
import { Breadcrumbs, Searchbar } from '@/app/molecules';
import AdminEmptyState from '@/app/molecules/AdminEmptyState';
import CreateGlobalRemindersModal from '@/app/molecules/CreateGlobalRemindersModal';
import Tabs from '@/app/molecules/Tabs';
import RemindersList from '@/app/organisms/admin/reminders/RemindersList';
import { getAuth } from '@/app/services/identity.service';
import {
    useCreateGlobalReminder,
    useDeleteGlobalReminder,
    useGetGlobalReminders,
    useUpdateGlobalReminder,
} from '@/app/services/patient.queries';
import { PlusIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';

interface RemindersProps {
    className: string;
    breadcrumbList: { id: string; name: string; path: string }[];
    plansData: any;
    assessmentData?: any;
    prescriptionData?: any;
}

const Reminders: React.FC<RemindersProps> = ({
    className,
    breadcrumbList,
    plansData,
    assessmentData,
    prescriptionData,
}) => {
    const clinicId = getAuth()?.clinicId;
    const auth = getAuth();
    const userRole = auth?.role;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';
    const [isReminderModal, setIsReminderModal] = useState(false);
    const [selectedReminder, setSelectedReminder] = useState<any>(null);
    const [page, setPage] = useState(1);
    const [limit] = useState(10);
    const [activeAttachmentTab, setActiveAttachmentTab] = useState('reminders');

    const createGlobalReminderMutation = useCreateGlobalReminder();
    const updateGlobalReminderMutation = useUpdateGlobalReminder();
    const deleteGlobalReminderMutation = useDeleteGlobalReminder();

    const {
        data: remindersData,
        isLoading,
        error,
    } = useGetGlobalReminders(clinicId, page, limit, '');

    const router = useRouter();
    const {
        register,
        handleSubmit: globalReminderHandleSubmit,
        getValues,
        setValue,
        setError,
        control,
        reset,
        resetField,
        formState: { errors, isDirty, dirtyFields },
        watch,
        trigger,
    } = useForm();

    const handleTabCLick = (id: string) => {
        switch (id) {
            case 'clinicDetails':
                router.push('/admin/clinic-details');
                break;

            case 'users':
                router.push('/admin/users');
                break;

            case 'inventory':
                router.push('/admin/inventory');
                break;

            case 'rooms':
                router.push('/admin/rooms');
                break;

            case 'integrations':
                router.push('/admin/integrations');
                break;

            case 'document-library':
                router.push('/admin/document-library');
                break;

            case 'reminders':
                router.push('/admin/reminders');
                break;

            case 'analytics':
                router.push('/admin/analytics');
                break;

            default:
                router.push('/admin/clinic-details');
        }
    };

    const formattedGlobalReminderData = (data: any) => {
        const baseData = {
            clinicId: clinicId,
            condition: data.condition,
            setReminderFor: data.setReminderFor,
            recurrenceFrequency:
                data.recurrenceFrequency && data.recurrenceUnit
                    ? parseInt(data.recurrenceFrequency)
                    : null,
            recurrenceUnit: data.recurrenceUnit?.value || null,
            recurrenceEndDate:
                data.recurrenceEndType === 'on' ? data.recurrenceEndDate : null,
            recurrenceEndOccurrences:
                data.recurrenceEndType === 'after'
                    ? data.recurrenceEndOccurrences
                    : null,
            recurrenceEndType: data.recurrenceEndType || null,
            triggerType: data.triggerType,
            startAfterDays: data.startAfterDays
                ? parseInt(data.startAfterDays)
                : undefined,
        };

        if (data.triggerType === 'SPECIES_BREED_AGE') {
            return {
                ...baseData,
                speciesBreedAge: {
                    gender: data.gender?.value,
                    species: data.species?.value,
                    breed: data.breed?.value,
                    ageRangeType: data.ageRangeType || '',
                    customYr: data.customYr || '',
                    customMths: data.customMths || '',
                    startCustomYr: data.startCustomYr || '',
                    startCustomMths: data.startCustomMths || '',
                    endCustomYr: data.endCustomYr || '',
                    endCustomMths: data.endCustomMths || '',
                },
            };
        }

        // For other trigger types
        return {
            ...baseData,
            gender: '',
            species: '',
            breed: '',
        };
    };

    const handleEditReminder = (reminder: any) => {
        setSelectedReminder(reminder);
        // Populate form with existing data
        setValue('triggerType', reminder.triggerType);
        setValue('ifPatient', {
            value: reminder.triggerType,
            label:
                reminder.triggerType === 'SPECIES_BREED_AGE'
                    ? 'is {species|breed|gender|age|}'
                    : reminder.triggerType === 'PLAN_UPDATED'
                      ? 'plan is updated with'
                      : reminder.triggerType === 'DIAGNOSED'
                        ? 'is diagnosed with'
                        : 'is prescribed',
        });

        if (
            reminder.triggerType === 'SPECIES_BREED_AGE' &&
            reminder.speciesBreedAge
        ) {
            const {
                species,
                breed,
                gender,
                ageRangeType,
                startCustomYr,
                startCustomMths,
                endCustomYr,
                endCustomMths,
            } = reminder.speciesBreedAge;

            if (species) {
                setValue('species', { value: species, label: species });
            }
            if (breed) {
                setValue('breed', { value: breed, label: breed });
            }
            if (gender) {
                setValue('gender', { value: gender, label: gender });
            }
            if (ageRangeType) {
                setValue('ageRangeType', ageRangeType);
            }
            setValue('startCustomYr', startCustomYr);
            setValue('startCustomMths', startCustomMths);
            setValue('endCustomYr', endCustomYr);
            setValue('endCustomMths', endCustomMths);
        } else {
            if (reminder.condition) {
                setValue('condition', reminder.condition);
            }
            if (reminder.setReminderFor) {
                setValue('setReminderFor', reminder.setReminderFor);
            }
        }

        // Set recurrence data if exists
        if (reminder.recurrenceFrequency) {
            setValue(
                'recurrenceFrequency',
                reminder.recurrenceFrequency.toString()
            );
        }
        if (reminder.recurrenceUnit) {
            setValue('recurrenceUnit', {
                value: reminder.recurrenceUnit,
                label:
                    reminder.recurrenceUnit === 'WEEK'
                        ? 'Week'
                        : reminder.recurrenceUnit === 'MONTH'
                          ? 'Month'
                          : 'Year',
            });
        }
        if (reminder.recurrenceEndDate) {
            setValue('recurrenceEndDate', reminder.recurrenceEndDate);
            setValue('recurrenceEndType', 'on');
        } else if (reminder.recurrenceEndOccurrences) {
            setValue(
                'recurrenceEndOccurrences',
                reminder.recurrenceEndOccurrences.toString()
            );
            setValue('recurrenceEndType', 'after');
        } else {
            setValue('recurrenceEndType', 'never');
        }

        if (reminder.startAfterDays) {
            setValue('startAfterDays', reminder.startAfterDays.toString());
        }

        setIsReminderModal(true);
    };

    const handleDeleteReminder = async (reminderId: string) => {
        try {
            await deleteGlobalReminderMutation.mutateAsync(reminderId);
        } catch (error) {
            console.error('Error deleting reminder:', error);
        }
    };

    // const handleCreateReminder = globalReminderHandleSubmit(async (data: any) => {
    //     try {
    //       const formattedData = formattedGlobalReminderData(data);
    //       await createGlobalReminderMutation.mutateAsync(formattedData);
    //       setIsReminderModal(false);
    //       // Optional: Add success toast
    //     } catch (error) {
    //       console.error("Error creating global reminder:", error);
    //       // Optional: Add error toast
    //     }
    // });

    const handleCreateOrUpdateReminder = globalReminderHandleSubmit(
        async (data: any) => {
            try {
                const formattedData = formattedGlobalReminderData(data);

                if (selectedReminder) {
                    // Update existing reminder
                    await updateGlobalReminderMutation.mutateAsync({
                        reminderId: selectedReminder.id,
                        data: formattedData,
                    });
                } else {
                    // Create new reminder
                    await createGlobalReminderMutation.mutateAsync(
                        formattedData
                    );
                }

                handleCloseModal();
            } catch (error) {
                console.error('Error with reminder:', error);
            }
        }
    );

    const handleCloseModal = () => {
        setIsReminderModal(false);
        setSelectedReminder(null);
        reset(); // Reset form state
    };

    if (isLoading) return <CustomLoader />;
    if (error) return <div>Error loading reminders</div>;

    return (
        <div className={className}>
            <div className="flex items-center justify-between gap-3 w-full py-3">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />

                {/* <Button
                    icon={<PlusIcon size={16} />}
                    id="add-reminder"
                    type="button"
                    variant="primary"
                    label="Add Reminder"
                    size="small"
                    onClick={() => setIsReminderModal(true)}
                    iconPosition="left"
                /> */}
            </div>

            <div className="mt-3 mb-8 flex justify-between items-center">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Admin
                </Heading>
                {/* <div className="flex gap-2.5">
                    <Searchbar
                        id="patients-search-bar"
                        name="SearchBar"
                        placeholder="Search..."
                        onChange={() => {}}
                    />
                </div> */}
            </div>

            <Tabs
                className="mt-5"
                defaultActiveTab={activeAttachmentTab}
                onTabClick={(tab) => {
                    handleTabCLick(tab.id);
                }}
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className=" tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className=" tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <div className="flex items-center justify-between">
                                    <Searchbar
                                        id="patients-search-bar"
                                        name="SearchBar"
                                        placeholder="Search..."
                                        onChange={() => {}}
                                    />
                                    <Button
                                        icon={<PlusIcon size={16} />}
                                        id="add-reminder"
                                        type="button"
                                        variant="primary"
                                        label="Add Reminder"
                                        size="small"
                                        onClick={() => setIsReminderModal(true)}
                                        iconPosition="left"
                                    />
                                </div>
                                <div
                                    className={`h-[calc(100dvh-19rem)] mt-6 ${(!remindersData?.data || remindersData.data.length === 0) && 'flex justify-center items-center'}`}
                                >
                                    {remindersData?.data?.length > 0 ? (
                                        <RemindersList
                                            pagination={{
                                                pageIndex: page - 1,
                                                pageSize: limit,
                                            }}
                                            setPagination={(newPagination) =>
                                                setPage(
                                                    newPagination.pageIndex + 1
                                                )
                                            }
                                            totalPages={Math.ceil(
                                                (remindersData?.total || 0) /
                                                    limit
                                            )}
                                            listLoadStatus={
                                                isLoading
                                                    ? 'pending'
                                                    : error
                                                      ? 'error'
                                                      : 'success'
                                            }
                                            tableData={
                                                remindersData?.data || []
                                            }
                                            onEditReminder={handleEditReminder}
                                            onDeleteReminder={
                                                handleDeleteReminder
                                            }
                                        />
                                    ) : (
                                        <AdminEmptyState
                                            title="Nothing Added Yet"
                                            image="/images/care-kennels.png"
                                            variant="vertical"
                                            className="mb-14"
                                        />
                                    )}
                                </div>

                                <CreateGlobalRemindersModal
                                    isOpen={isReminderModal}
                                    onClose={handleCloseModal}
                                    control={control}
                                    errors={errors}
                                    setValue={setValue}
                                    watch={watch}
                                    plansData={plansData}
                                    assessmentData={assessmentData}
                                    prescriptionData={prescriptionData}
                                    editingReminder={selectedReminder}
                                    handleCreateGlobalReminder={
                                        handleCreateOrUpdateReminder
                                    }
                                    getValues={getValues}
                                    existingReminders={
                                        remindersData?.data || []
                                    }
                                />
                            </div>
                        ),
                    },
                    ...(isAdmin ? [{
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    }] : []),
                ]}
            />
        </div>
    );
};

export default Reminders;
