import React, { useState } from 'react';
import { But<PERSON>, Heading } from '@/app/atoms';
import { ArrowDown2 } from 'iconsax-react';
import { Breadcrumbs, Searchbar } from '@/app/molecules';
import UserLists from '@/app/organisms/admin/users/UserLists';
import { PaginationType } from '@/app/molecules/Table';
import { Row } from '@tanstack/react-table';
import DropdownMenu from '@/app/molecules/DropdownMenu';
import AddEditUserModal from '@/app/organisms/admin/users/AddEditUserModal';
import AddExistingUserModal from '@/app/organisms/admin/users/AddExistingUserModal';
import Image from 'next/image';
import { UserFormDataT, UserRoleOptionT, UserT } from '@/app/types/user';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import Tabs from '@/app/molecules/Tabs';
import CustomLoader from '@/app/atoms/CustomLoader';
import AdminEmptyState from '@/app/molecules/AdminEmptyState';
import { getAuth } from '@/app/services/identity.service';

interface BreadcrumbItem {
    id: string | number;
    name: string;
    path?: string;
    handleChange?: () => {};
}

interface UserDetailsProps {
    className?: string;
    tableData: UserT[];
    pagination: PaginationType;
    setPagination: React.Dispatch<React.SetStateAction<PaginationType>>;
    totalPages: number;
    listLoadStatus: 'error' | 'success' | 'pending';
    setEditModal: (data: { row: Row<UserT> }) => void;
    breadcrumbList: BreadcrumbItem[];
    userData: UserT[];
    userRoleOption: UserRoleOptionT[];
    onChangeUser: () => void;
    handleFilter: () => void;
    handleActivateDeactivate: (
        userId: string,
        isActive: boolean
    ) => Promise<void>;
    handleAddUser: () => void;
    handleAddEditUserSubmit: (userData: UserFormDataT) => Promise<void>;
    isAddNewUserModalOpen: boolean;
    setIsAddNewUserModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
    selectedUser: UserT | null;
    handleSearch: (searchTerm: string) => Promise<any[]>;
    handleAddUserToClinic: (userId: string) => Promise<void>;
    searchResults: any[];
}

const UserDetails: React.FC<UserDetailsProps> = ({
    breadcrumbList,
    className,
    tableData,
    pagination,
    setPagination,
    totalPages,
    listLoadStatus,
    setEditModal,
    userData,
    userRoleOption,
    onChangeUser,
    handleFilter,
    handleActivateDeactivate,
    handleAddUser,
    handleAddEditUserSubmit,
    isAddNewUserModalOpen,
    selectedUser,
    setIsAddNewUserModalOpen,
    handleSearch,
    handleAddUserToClinic,
    searchResults,
}) => {
    const { setValue } = useForm();
    const [isAddExistingUserModalOpen, setAddExistingUserModalOpen] =
        useState(false);
    const [activeAttachmentTab, setActiveAttachmentTab] = useState('users');
    const auth = getAuth();
    const userRole = auth?.role;
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    const router = useRouter();

    const handleMenuClick = (item: { id: string }) => {
        switch (item.id) {
            case 'add-existing-user':
                setAddExistingUserModalOpen(true);
                break;
            case 'add-new-user':
                handleAddUser();
                break;
            default:
                break;
        }
    };

    const handleExistingUserSubmit = (selectedUsers: any) => {
        console.log('Existing users added:', selectedUsers);
    };

    // const handleAddEditUserSubmit = async (userData: UserFormDataT) => {
    //     if (selectedUser) {
    //         await updateUser(selectedUser.userId, userData);
    //         console.log('User updated:', userData);
    //     } else {
    //         // Handle creating new user
    //         console.log('New user added:', userData);
    //     }
    //     setAddNewUserModalOpen(false);
    // };
    const openAddExistingUserModal = () => {
        setIsAddNewUserModalOpen(false);
        setAddExistingUserModalOpen(true);
    };

    const handleTabCLick = (id: string) => {
        console.log(id);
        switch (id) {
            case 'clinicDetails':
                router.push('/admin/clinic-details');
                break;

            case 'users':
                router.push('/admin/users');
                break;

            case 'inventory':
                router.push('/admin/inventory');
                break;

            case 'rooms':
                router.push('/admin/rooms');
                break;

            case 'integrations':
                router.push('/admin/integrations');
                break;

            case 'document-library':
                router.push('/admin/document-library');
                break;
            case 'analytics':
                router.push('/admin/analytics');
                break;
            case 'reminders':
                router.push('/admin/reminders');
                break;
            default:
                router.push('/admin/clinic-details');
        }
    };

    return (
        <div className={className}>
            <div className="flex items-center justify-between gap-3 py-3 w-full">
                <Breadcrumbs breadcrumbList={breadcrumbList} divider="arrow" />
            </div>

            <div className="mt-3 mb-8 flex justify-between items-center">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Admin
                </Heading>
                {/* <div className="flex gap-2.5">
                    <Searchbar
                        id="patients-search-bar"
                        name="SearchBar"
                        placeholder="Search..."
                        onChange={onChangeUser}
                    />
                    <Button
                        icon={<Image src="/images/icons/filter_icon.svg" width={16} height={16} alt='icon' />}
                        id="add-user"
                        type="button"
                        onlyIcon
                        variant='secondary'
                        size='extraSmall'
                        className='w-10 !bg-white'
                        onClick={handleFilter}
                    />
                </div> */}
            </div>

            <Tabs
                className="mt-5"
                defaultActiveTab={activeAttachmentTab}
                onTabClick={(tab) => {
                    handleTabCLick(tab.id);
                }}
                tabs={[
                    {
                        id: 'clinic-details',
                        label: 'Clinic',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'users',
                        label: 'Users',
                        tabContent: (
                            <div className=" rounded-2xl p-4 h-[calc(100dvh-12.7rem)]">
                                <div className="flex items-center justify-between">
                                    <Searchbar
                                        id="patients-search-bar"
                                        name="SearchBar"
                                        variant="secondary"
                                        placeholder="Search..."
                                        onChange={onChangeUser}
                                    />
                                    <DropdownMenu
                                        minWidth="w-[180px]"
                                        dropdownClass="mt-2"
                                        menuList={[
                                            {
                                                id: 'add-existing-user',
                                                label: 'Add Existing User',
                                            },
                                            {
                                                id: 'add-new-user',
                                                label: 'Add New User',
                                            },
                                        ]}
                                        menuDirection="bottom-right"
                                        onMenuClick={handleMenuClick}
                                    >
                                        <Button
                                            icon={<ArrowDown2 size={16} />}
                                            id="add-user"
                                            type="button"
                                            variant="primary"
                                            label="Add Your Team"
                                            size="small"
                                            iconPosition="right"
                                        />
                                    </DropdownMenu>
                                </div>
                                <div
                                    className={`mt-3 h-[calc(100dvh-18.5rem)] overflow-auto ${tableData?.length === 0 && 'flex items-center justify-center'}`}
                                >
                                    {tableData?.length > 0 ? (
                                        <UserLists
                                            tableData={tableData}
                                            pagination={pagination}
                                            setPagination={setPagination}
                                            totalPages={totalPages}
                                            listLoadStatus={listLoadStatus}
                                            setEditModal={setEditModal}
                                            handleActivateDeactivate={
                                                handleActivateDeactivate
                                            }
                                        />
                                    ) : (
                                        <AdminEmptyState
                                            title="Nothing Added Yet"
                                            image="/images/care-kennels.png"
                                            variant="vertical"
                                            className="mb-14"
                                        />
                                    )}
                                </div>
                            </div>
                        ),
                    },
                    {
                        id: 'inventory',
                        label: 'Inventory',
                        tabContent: (
                            <div className=" tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'integrations',
                        label: 'Integrations',
                        tabContent: (
                            <div className=" tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'document-library',
                        label: 'Library',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    {
                        id: 'reminders',
                        label: 'Reminders',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    },
                    ...(isAdmin ? [{
                        id: 'analytics',
                        label: 'Analytics',
                        tabContent: (
                            <div className="tab-layout">
                                <CustomLoader />
                            </div>
                        ),
                    }] : []),
                ]}
            />

            {isAddExistingUserModalOpen && (
                <AddExistingUserModal
                    isOpen={isAddExistingUserModalOpen}
                    onClose={() => setAddExistingUserModalOpen(false)}
                    openCreateNewUserModal={handleAddUser}
                    users={userData}
                    onSubmit={handleExistingUserSubmit}
                    setValue={setValue}
                    handleSearch={handleSearch}
                    handleAddUserToClinic={handleAddUserToClinic}
                />
            )}

            {isAddNewUserModalOpen && (
                <AddEditUserModal
                    isOpen={isAddNewUserModalOpen}
                    onClose={() => setIsAddNewUserModalOpen(false)}
                    openAddExistingUserModal={openAddExistingUserModal}
                    roleOptions={userRoleOption}
                    onSubmit={handleAddEditUserSubmit}
                    user={selectedUser}
                />
            )}
        </div>
    );
};

export default UserDetails;
