export interface GoogleCalendarStatus {
    isConnected: boolean;
    calendarId?: string;
    calendarName?: string;
    syncStatus?: 'SUCCESS' | 'FAILED' | 'PENDING';
    lastSyncAt?: string;
    errorMessage?: string;
    lastSyncStatus?: string;
}

export interface GoogleCalendarInfo {
    id: string;
    summary: string;
    description?: string;
    primary?: boolean;
    accessRole: string;
}

export interface GoogleCalendarEvent {
    id: string;
    summary: string;
    description?: string;
    start: {
        dateTime: string;
        timeZone: string;
    };
    end: {
        dateTime: string;
        timeZone: string;
    };
    source: 'google';
    isEditable: false;
    status?: string;
    creator?: {
        email: string;
        displayName?: string;
    };
}

export interface GoogleCalendarConnectionState {
    status: GoogleCalendarStatus | null;
    calendars: GoogleCalendarInfo[];
    isLoading: boolean;
    error: string | null;
}

export interface GoogleAuthUrlResponse {
    url: string;
} 