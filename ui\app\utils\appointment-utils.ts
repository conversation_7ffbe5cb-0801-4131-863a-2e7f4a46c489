/**
 * Utility functions for appointment data processing
 */

/**
 * Safely deduplicates appointment arrays to prevent Google events and regular appointments from appearing multiple times
 * This is backward compatible and will not break existing functionality
 * 
 * @param appointments - Array of appointment objects
 * @returns Deduplicated array of appointments
 */
export const deduplicateAppointments = (appointments: any[]): any[] => {
    // Safety checks for backward compatibility
    if (!appointments) {
        return [];
    }

    if (!Array.isArray(appointments)) {
        console.warn('🚨 [DEDUP_UTIL] Input is not an array, returning as is');
        return appointments;
    }

    if (appointments.length === 0) {
        return appointments;
    }

    try {
        /*
         * Backend now supplies a unique `id` for every entry, including Google
         * calendar events (format: `google_<eventId>_<doctorId>`). Therefore we
         * can safely deduplicate by `id` alone.  We keep a defensive fallback
         * that builds a composite key from `googleEventId` and first
         * `doctorId` if `id` is missing for any legacy data.
         */

        const seenKeys = new Set<string>();

        const deduplicated = appointments.filter((appointment) => {
            if (!appointment || typeof appointment !== 'object') {
                return true;
            }

            // Prefer the explicit id when present
            let key: string | undefined = appointment.id as string | undefined;

            if (!key) {
                if (appointment.googleEventId) {
                    const firstDoctorId =
                        appointment?.appointmentDoctors?.[0]?.doctorId ?? '';
                    key = `${appointment.googleEventId}_${firstDoctorId}`;
                }
            }

            if (!key) {
                // No reliable key – keep to avoid accidental loss
                return true;
            }

            if (seenKeys.has(key)) return false;
            seenKeys.add(key);
            return true;
        });

        // Only log if we actually removed duplicates to avoid console spam
        if (deduplicated.length !== appointments.length) {
            console.log(
                `🔧 [DEDUP_UTIL] Removed ${appointments.length - deduplicated.length} duplicate appointments (${appointments.length} → ${deduplicated.length})`
            );
        }

        return deduplicated;
    } catch (error) {
        // If deduplication fails for any reason, fall back to original data
        console.warn(
            '🚨 [DEDUP_UTIL] Deduplication failed, using original data:',
            error
        );
        return appointments;
    }
};

/**
 * Safely processes appointment data from React Query's infinite query pages
 * Flattens pages and deduplicates the results
 *
 * @param data - React Query infinite query data object
 * @returns Deduplicated flat array of appointments
 */
export const processInfiniteQueryAppointments = (data: any): any[] => {
    try {
        if (!data?.pages) {
            return [];
        }

        // Flatten all pages
        const flatAppointments = data.pages.flatMap(
            (page: any) => page?.data?.appointments || []
        );

        // Deduplicate the flattened results
        return deduplicateAppointments(flatAppointments);
    } catch (error) {
        console.warn(
            '🚨 [DEDUP_UTIL] Failed to process infinite query appointments:',
            error
        );
        return [];
    }
};
