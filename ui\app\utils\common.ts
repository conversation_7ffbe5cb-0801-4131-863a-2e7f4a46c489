import moment from 'moment';
import { Medication } from '../organisms/patientDetail/MedicationHistory';
import { BREEDS } from './constant';
import { EnumAppointmentStatus } from '../types/appointment';

interface Path {
    x: number;
    y: number;
}

interface PathObject {
    drawMode: boolean;
    strokeColor: string;
    strokeWidth: number;
    paths: Path[];
}

export const checkForTokenExpiredError = (error: any) => {
    if (error?.response?.status) {
        const { status } = error.response;
        const url = error?.config?.url || '';

        // Don't treat PIN verification errors as token expiration
        // PIN verification failures should not log out the user
        if (status === 401 && url.includes('/auth/verify-pin')) {
            return false;
        }

        return status === 401;
    }
};

export const parseData = (data: string) => JSON.parse(data);

export const removeDuplicatePaths = (
    pathsArray: PathObject[]
): PathObject[] => {
    const uniquePaths = new Set<string>();
    const result: PathObject[] = [];

    pathsArray.forEach((pathObj) => {
        const path = pathObj.paths[0];
        const pathKey = `${path.x},${path.y}`;

        if (!uniquePaths.has(pathKey)) {
            uniquePaths.add(pathKey);
            result.push(pathObj);
        }
    });

    return result;
};

export const priorityOptions = [
    { label: 'High', value: 'high', color: '#DC2020' },
    { label: 'Medium', value: 'medium', color: '#E99400' },
];

export const calculateAge = (dateString: string) => {
    const startDate = moment(dateString);
    const today = moment();

    const years = today.diff(startDate, 'years');
    const months = today.diff(startDate, 'months') % 12;

    return `${years}y & ${months}m`;
};

export const groupByDate = (medications: Medication[]) => {
    return medications.reduce(
        (acc, medication) => {
            if (!acc[medication.date]) {
                acc[medication.date] = [];
            }
            acc[medication.date].push(medication);
            return acc;
        },
        {} as Record<string, Medication[]>
    );
};

export function getLinksFromText(text: string) {
    const urlRegex = /(?:https?:\/\/|www\.)[^\s/$.?#].[^\s]*/gi;
    const matches = text.match(urlRegex);
    return matches || [];
}

export const formatBreed = (value: string) => {
    if (!value) return '';
    const words = value.split('_');

    const transformed = words
        .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(' ');

    return transformed;
};
export const getProfileImage = ({
    species,
    breedValue,
}: {
    species: string;
    breedValue: string;
}) => {
    let avatar;
    if (species && breedValue && BREEDS[species]) {
        const breed = BREEDS[species].find((b) => b.value === breedValue);
        if (breed) {
            avatar = `/images/breeds/${species}/${breed.code}.png`;
        }
    } else {
        avatar = '';
    }
    return avatar;
};
export const appointmentStatusDetails = {
    [EnumAppointmentStatus.Scheduled]: {
        imageUrl: '/images/icons/checkIn.svg',
        tooltip: 'Check-in patient',
    },

    [EnumAppointmentStatus.Checkedin]: {
        imageUrl: '/images/icons/treatment.svg',
        tooltip: 'Begin treatment',
    },

    [EnumAppointmentStatus.ReceivingCare]: {
        imageUrl: '/images/icons/cart.svg',
        tooltip: 'Ready to check-out',
    },

    [EnumAppointmentStatus.Checkedout]: {
        imageUrl: '/images/icons/tickSquare.svg',
        tooltip: 'Check-out',
    },
};

export const getClockTime = (appointment: any) => {
    const timeData = {
        [EnumAppointmentStatus.Checkedin]: appointment.checkinTime,
        [EnumAppointmentStatus.ReceivingCare]: appointment.receivingCareTime,
        [EnumAppointmentStatus.Checkedout]: appointment.checkoutTime,
    };

    return timeData[appointment.status as EnumAppointmentStatus];
};

export const colorCardItems = [
    {
        heading: 'Red',
        description: 'Cuts and Lacerations',
        colorCode: '#EF4444',
    },
    {
        heading: 'Blue',
        description: 'Bruises and Contusions',
        colorCode: '#017AAD',
    },
    {
        heading: 'Orange',
        description: 'Infections/ Inflammations/ Burns',
        colorCode: '#E99400',
    },
    {
        heading: 'Green',
        description: 'Parasite Infestations',
        colorCode: '#29823B',
    },
    {
        heading: 'Yellow',
        description: 'Masses',
        colorCode: '#FFE661',
    },
    {
        heading: 'Grey',
        description: 'Others',
        colorCode: '#ADADAD',
    },
];
export const getInitials = (name: string) => {
    return name
        .split(' ')
        .map((word) => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
};
export const avatarClasses = 'shrink-0';

export const getStatusLabel = (label: string) => {
    const dbStatus = {
        Checkedin: 'Checked-in',
        Completed: 'Completed',
        Missed: 'Missed',
        Scheduled: 'Scheduled',
        Checkout: 'Ready to check-out',
        'Receiving Care': 'Receiving care',
    };
    return dbStatus[label];
};

export function categorizeFile(filename: any) {
    const pdfExtensions = ['pdf'];
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

    const extension = filename.split('.').pop().toLowerCase();

    if (pdfExtensions.includes(extension)) {
        return 'PDF';
    } else if (imageExtensions.includes(extension)) {
        return 'Image';
    } else {
        return 'Unknown';
    }
}

export const filePreview = [
    {
        fileName: 'filename 1',
        imageUrl: '',
    },
    {
        fileName: 'filename 2',
        imageUrl: '',
    },
    {
        fileName: 'filename 3',
        imageUrl: '',
    },
];

export const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2,
    }).format(parseFloat(amount));
};

export const getAmountColor = (amount: string | undefined) => {
    const numericAmount = Number.parseFloat(amount || '0');
    return numericAmount > 0
        ? '!text-[#29823B]'
        : numericAmount === 0
          ? '!text-black'
          : '!text-[#DC2020]';
};
