import { getAuth } from '../services/identity.service';
import Fuse from 'fuse.js';

export const APPOINTMENT_FOLLOWUP_OPTIONS = [
    'Tomorrow',
    'In a week',
    'In a month',
    'Custom Date',
];

export enum CART_TYPES {
    None = 'None',
    Consumable = 'Consumable',
    Product = 'Product',
    Service = 'Service',
    Vaccination = 'Vaccination',
    Medication = 'Medication',
    Labreport = 'Labreport',
}

export enum PAYMENT_TYPES {
    Cash = 'Cash',
    Card = 'Card',
    Cheque = 'Cheque',
    Wallet = 'Wallet',
    BankTransfer = 'Bank Transfer',
}

export enum CREDIT_TYPES {
    Collect = 'Collect',
    Return = 'Return',
    Invoice = 'Invoice',
    CreditNote = 'Credit Note',
    ReconcileInvoice = 'Reconcile Invoice',
    BulkReconcileInvoice = 'Bulk Reconcile Invoice',
}

export enum INTEGRATION_TYPE {
    IDEXX = 'IDEXX',
}

export const PAYMENT_MODE_OPTIONS = [
    { label: 'Cash', value: 'Cash' },
    { label: 'Cheque', value: 'Cheque' },
    { label: 'Card', value: 'Card' },
    { label: 'Wallet', value: 'Wallet' },
    { label: 'Bank Transfer', value: 'Bank Transfer' },
];

export enum INVOICE_TYPES {
    Invoice = 'Invoice',
    Refund = 'Refund',
}

export const APPOINTMENT_TRIAGE_OPTIONS = [
    {
        name: 'Critical',
        icon: 'text-error-100',
    },
    {
        name: 'Urgent',
        icon: 'text-orange-700',
    },
    {
        name: 'Serious',
        icon: 'text-warning-100',
    },
    {
        name: 'Stable',
        icon: 'text-success-100',
    },
    {
        name: 'Non Critical',
        icon: 'text-blue-500',
    },

    {
        name: 'Deceased',
        icon: 'text-black-500',
    },
    // 'High Priority',
    // 'Low Priority',
    // 'Medium Priority',
    // 'No Priority',
];

export const APPOINTMENT_TYPE = [
    'Consultation',
    'Vaccination',
    'Surgery',
    'Walk In',
    'Diagnostic',
    'Saline',
];

export const DOCUMENT_LIBRARY_CATEGORY = [
    'Consent Forms',
    'Surgery',
    'Wellness & Preventative Care',
    'New Pet Guidelines',
    'Emergency Care Instructions',
];

// Define interfaces for Reason data (similar to patientportal)
export interface ReasonData {
    id: string;
    label: string;
    keywords?: string[];
}

export const REASONS_DATA: ReasonData[] = [
    {
        id: 'annual-check-up',
        label: 'Annual Check-Up',
        keywords: [
            'Annual Check-Up',
            'yearly check',
            'annual exam',
            'routine visit',
            'wellness check',
            'preventative care',
            'regular exam',
            'annual physical',
            'general checkup',
        ],
    },
    {
        id: 'wellness-exam',
        label: 'Wellness Exam',
        keywords: [
            'Wellness Exam',
            'check-up',
            'physical exam',
            'routine health check',
            'preventative care',
            'general health review',
            'annual physical',
            'wellness visit',
        ],
    },
    {
        id: 'microchipping',
        label: 'Microchipping',
        keywords: [
            'Microchipping',
            'chip implant',
            'pet identification',
            'tracking chip',
            'microchip insertion',
            'id chip',
            'pet registration chip',
            'implant chip',
        ],
    },
    {
        id: 'health-certificate',
        label: 'Health Certificate',
        keywords: [
            'Health Certificate',
            'travel papers',
            'fit to fly',
            'travel certificate',
            'health cert',
            'veterinary certificate for travel',
            'animal health form',
            'travel clearance',
        ],
    },
    {
        id: 'dental-cleaning',
        label: 'Dental Cleaning',
        keywords: [
            'Dental Cleaning',
            'teeth cleaning',
            'dental hygiene',
            'oral cleaning',
            'scale and polish',
            'pet dental',
            'tartar removal',
            'mouth clean',
        ],
    },
    {
        id: 'coughing-sneezing',
        label: 'Coughing/Sneezing',
        keywords: [
            'Coughing/Sneezing',
            'cough',
            'sneeze',
            'respiratory issue',
            'cold symptoms',
            'runny nose',
            'kennel cough symptoms',
            'upper respiratory infection',
            'hacking',
            'wheezing',
        ],
    },
    {
        id: 'vomiting-diarrhea',
        label: 'Vomiting/Diarrhea',
        keywords: [
            'Vomiting/Diarrhea',
            'throwing up',
            'vomit',
            'loose motion',
            'diarrhea',
            'upset stomach',
            'sick',
            'gastrointestinal upset',
            'loose stools',
            'puking',
            'runs',
        ],
    },
    {
        id: 'skin-issues-allergies',
        label: 'Skin Issues/Allergies',
        keywords: [
            'Skin Issues/Allergies',
            'rash',
            'itchy skin',
            'allergies',
            'hot spot',
            'skin infection',
            'hair loss',
            'dermatitis',
            'allergic reaction',
            'itching',
            'sores',
        ],
    },
    {
        id: 'lethargy-fatigue',
        label: 'Lethargy/Fatigue',
        keywords: [
            'Lethargy/Fatigue',
            'tired',
            'weak',
            'low energy',
            'sluggish',
            'not active',
            'acting tired',
            'less playful',
            'sleepy',
            'inactive',
        ],
    },
    {
        id: 'loss-of-appetite',
        label: 'Loss of Appetite',
        keywords: [
            'Loss of Appetite',
            'not eating',
            'poor appetite',
            'refusing food',
            'decreased food intake',
            'anorexia',
            "won't eat",
            'no appetite',
            'picky eater',
        ],
    },
    {
        id: 'weight-loss-gain',
        label: 'Weight Loss/Gain',
        keywords: [
            'Weight Loss/Gain',
            'losing weight',
            'gaining weight',
            'change in weight',
            'skinny',
            'fat',
            'sudden weight change',
            'obesity',
            'underweight',
            'weight fluctuation',
        ],
    },
    {
        id: 'eye-issues',
        label: 'Eye Issues',
        keywords: [
            'Eye Issues',
            'eye infection',
            'watery eyes',
            'red eye',
            'eye discharge',
            'cloudy eye',
            'sore eye',
            'conjunctivitis',
            'corneal ulcer',
            'squinting',
            'goopy eye',
        ],
    },
    {
        id: 'ear-issues',
        label: 'Ear Issues',
        keywords: [
            'Ear Issues',
            'ear infection',
            'itchy ears',
            'head shaking',
            'ear discharge',
            'smelly ears',
            'otitis',
            'ear mites',
            'sore ear',
            'ear problem',
        ],
    },
    {
        id: 'limping-joint-pain',
        label: 'Limping/Joint Pain',
        keywords: [
            'Limping/Joint Pain',
            'lame',
            'sore leg',
            'difficulty walking',
            'arthritis',
            'joint stiffness',
            'hip pain',
            'elbow dysplasia',
            'lameness',
            'hobbling',
            'joint ache',
        ],
    },
    {
        id: 'urinary-issues',
        label: 'Urinary Issues',
        keywords: [
            'Urinary Issues',
            'trouble urinating',
            'peeing blood',
            'frequent urination',
            'straining to pee',
            'urinary infection',
            'UTI',
            'bladder infection',
            'cystitis',
            'incontinence',
            'painful urination',
        ],
    },
    {
        id: 'behavioral-changes',
        label: 'Behavioral Changes',
        keywords: [
            'Behavioral Changes',
            'acting strange',
            'different behavior',
            'unusual actions',
            'personality change',
            'sudden behavior shift',
            'acting weird',
            'mood change',
        ],
    },
    {
        id: 'fever',
        label: 'Fever',
        keywords: [
            'Fever',
            'high temperature',
            'hot',
            'feverish',
            'elevated temp',
            'running a temperature',
            'pyrexia',
            'warm',
        ],
    },
    {
        id: 'itching-scratching',
        label: 'Itching/Scratching',
        keywords: [
            'Itching/Scratching',
            'itchy',
            'scratching constantly',
            'irritated skin',
            'excessive licking',
            'pruritus',
            'skin irritation',
            'scratching a lot',
        ],
    },
    {
        id: 'lacerations-cuts',
        label: 'Lacerations/Cuts',
        keywords: [
            'Lacerations/Cuts',
            'cut',
            'wound',
            'gash',
            'open sore',
            'bleeding',
            'skin tear',
            'injury',
            'scrape',
            'abrasion',
        ],
    },
    {
        id: 'fractures-broken-bones',
        label: 'Fractures/Broken Bones',
        keywords: [
            'Fractures/Broken Bones',
            'broken bone',
            'fracture',
            'broken leg',
            'broken arm',
            'bone break',
            'hairline fracture',
            'snapped bone',
        ],
    },
    {
        id: 'bite-wounds',
        label: 'Bite Wounds',
        keywords: [
            'Bite Wounds',
            'bitten',
            'animal bite',
            'fight wound',
            'puncture wound',
            'dog bite',
            'cat bite',
            'attacked',
        ],
    },
    {
        id: 'sprains-strains',
        label: 'Sprains/Strains',
        keywords: [
            'Sprains/Strains',
            'pulled muscle',
            'twisted ankle',
            'joint injury',
            'soft tissue injury',
            'ligament tear',
            'muscle strain',
            'pulled tendon',
        ],
    },
    {
        id: 'insect-bites-stings',
        label: 'Insect Bites/Stings',
        keywords: [
            'Insect Bites/Stings',
            'bee sting',
            'wasp sting',
            'spider bite',
            'bug bite',
            'swollen bite',
            'ant bite',
            'allergic reaction to bite',
            'stung',
        ],
    },
    {
        id: 'foreign-body-ingestion',
        label: 'Foreign Body Ingestion',
        keywords: [
            'Foreign Body Ingestion',
            'ate something bad',
            'swallowed object',
            'ingested toy',
            'ate plastic',
            'swallowed bone',
            'obstruction',
            'ate sock',
            'swallowed rock',
        ],
    },
    {
        id: 'diabetes-management',
        label: 'Diabetes Management',
        keywords: [
            'Diabetes Management',
            'diabetic check',
            'insulin adjustment',
            'blood sugar monitoring',
            'glucose curve',
            'diabetes care',
            'sugar levels',
        ],
    },
    {
        id: 'arthritis-treatment',
        label: 'Arthritis Treatment',
        keywords: [
            'Arthritis Treatment',
            'joint pain management',
            'arthritis care',
            'mobility issues',
            'degenerative joint disease',
            'arthritis medication',
            'stiff joints',
        ],
    },
    {
        id: 'thyroid-issues',
        label: 'Thyroid Issues',
        keywords: [
            'Thyroid Issues',
            'thyroid check',
            'hyperthyroid',
            'hypothyroid',
            'hormone imbalance',
            'thyroid medication',
            'endocrine disorder',
            'thyroid panel',
        ],
    },
    {
        id: 'heart-disease-management',
        label: 'Heart Disease Management',
        keywords: [
            'Heart Disease Management',
            'heart check',
            'cardiology follow-up',
            'heart murmur',
            'congestive heart failure',
            'heart medication',
            'cardiac care',
        ],
    },
    {
        id: 'kidney-disease-management',
        label: 'Kidney Disease Management',
        keywords: [
            'Kidney Disease Management',
            'kidney check',
            'renal failure',
            'kidney support',
            'chronic kidney disease',
            'renal diet',
            'kidney function test',
        ],
    },
    {
        id: 'allergy-treatment',
        label: 'Allergy Treatment',
        keywords: [
            'Allergy Treatment',
            'allergy management',
            'allergy shots',
            'skin allergy care',
            'food allergy management',
            'environmental allergies',
            'allergy meds',
        ],
    },
    {
        id: 'spay-neuter-consultation',
        label: 'Spay/Neuter Consultation',
        keywords: [
            'Spay/Neuter Consultation',
            'fixing consultation',
            'desexing consult',
            'castration chat',
            'sterilisation talk',
            'ovariohysterectomy consult',
            'orchiectomy consult',
            'getting fixed',
        ],
    },
    {
        id: 'pregnancy-check',
        label: 'Pregnancy Check',
        keywords: [
            'Pregnancy Check',
            'pregnant pet check',
            'ultrasound',
            'prenatal care',
            'expecting puppies/kittens',
            'gestation check',
            'confirm pregnancy',
        ],
    },
    {
        id: 'breeding-consultation',
        label: 'Breeding Consultation',
        keywords: [
            'Breeding Consultation',
            'mating advice',
            'reproduction consult',
            'breeding advice',
            'stud service consult',
            'fertility check',
            'breeding timing',
        ],
    },
    {
        id: 'lactation-issues',
        label: 'Lactation Issues',
        keywords: [
            'Lactation Issues',
            'nursing problems',
            'milk production issue',
            'mastitis',
            'agalactia',
            'problems feeding young',
            'low milk supply',
        ],
    },
    {
        id: 'heartworm-prevention',
        label: 'Heartworm Prevention',
        keywords: [
            'Heartworm Prevention',
            'heartworm test',
            'heartworm medication',
            'parasite prevention',
            'monthly heartworm pill',
            'heartworm injection',
            'heartworm shot',
        ],
    },
    {
        id: 'parasite-control',
        label: 'Parasite Control',
        keywords: [
            'Parasite Control',
            'flea treatment',
            'tick removal',
            'worming',
            'dewormer',
            'parasite check',
            'flea and tick prevention',
            'intestinal parasites',
            'worms',
        ],
    },
    {
        id: 'diet-nutrition-consultation',
        label: 'Diet/Nutrition Consultation',
        keywords: [
            'Diet/Nutrition Consultation',
            'food advice',
            'diet plan',
            'weight management food',
            'nutritional advice',
            'prescription diet',
            'pet food selection',
            'meal plan',
        ],
    },
    {
        id: 'geriatric-care',
        label: 'Geriatric Care',
        keywords: [
            'Geriatric Care',
            'senior pet check',
            'old age care',
            'senior wellness',
            'care for older pets',
            'senior pet health',
            'elderly pet',
        ],
    },
    {
        id: 'aggression',
        label: 'Aggression',
        keywords: [
            'Aggression',
            'biting',
            'growling',
            'aggressive behavior',
            'attacking',
            'snapping',
            'food aggression',
            'fear aggression',
            'hostile',
        ],
    },
    {
        id: 'anxiety',
        label: 'Anxiety',
        keywords: [
            'Anxiety',
            'nervous',
            'scared',
            'separation anxiety',
            'fearful behavior',
            'phobias',
            'storm phobia',
            'noise anxiety',
            'stressed',
        ],
    },
    {
        id: 'house-training-issues',
        label: 'House Training Issues',
        keywords: [
            'House Training Issues',
            'peeing inside',
            'pooping indoors',
            'toilet training problems',
            'accidents',
            'inappropriate urination/defecation',
            'house soiling',
            'potty training',
        ],
    },
    {
        id: 'destructive-behavior',
        label: 'Destructive Behavior',
        keywords: [
            'Destructive Behavior',
            'chewing furniture',
            'digging',
            'scratching',
            'destroying things',
            'unwanted chewing',
            'problem behavior',
            'wrecking house',
        ],
    },
    {
        id: 'obedience-training-consultation',
        label: 'Obedience Training Consultation',
        keywords: [
            'Obedience Training Consultation',
            'training help',
            'behavior training advice',
            'puppy class consult',
            'dog training advice',
            'basic commands help',
            'obedience class info',
        ],
    },
    {
        id: 'dermatology-consultation',
        label: 'Dermatology Consultation',
        keywords: [
            'Dermatology Consultation',
            'skin specialist',
            'allergy testing',
            'skin condition consult',
            'ear infection specialist',
            'chronic skin problems',
            'dermatologist',
        ],
    },
    {
        id: 'cardiology-consultation',
        label: 'Cardiology Consultation',
        keywords: [
            'Cardiology Consultation',
            'heart specialist',
            'echocardiogram consult',
            'heart condition check',
            'ECG',
            'chest x-ray for heart',
            'cardiologist',
        ],
    },
    {
        id: 'neurology-consultation',
        label: 'Neurology Consultation',
        keywords: [
            'Neurology Consultation',
            'nerve specialist',
            'seizure consult',
            'brain issue check',
            'spinal problem',
            'neurological exam',
            'neurologist',
        ],
    },
    {
        id: 'ophthalmology-consultation',
        label: 'Ophthalmology Consultation',
        keywords: [
            'Ophthalmology Consultation',
            'eye specialist',
            'vision check',
            'eye condition consult',
            'cataract check',
            'glaucoma check',
            'ophthalmologist',
        ],
    },
    {
        id: 'oncology-consultation',
        label: 'Oncology Consultation',
        keywords: [
            'Oncology Consultation',
            'cancer specialist',
            'tumor check',
            'chemotherapy consult',
            'cancer treatment options',
            'lump check',
            'oncologist',
        ],
    },
    {
        id: 'nail-trim',
        label: 'Nail Trim',
        keywords: [
            'Nail Trim',
            'clip nails',
            'cut claws',
            'nail cutting',
            'pedicure',
            'claw trim',
            'nail clip',
        ],
    },
    {
        id: 'grooming',
        label: 'Grooming',
        keywords: [
            'Grooming',
            'bath',
            'haircut',
            'brushing',
            'pet grooming',
            'dematting',
            'shave down',
            'wash',
            'clean up',
        ],
    },
    {
        id: 'anal-gland-expression',
        label: 'Anal Gland Expression',
        keywords: [
            'Anal Gland Expression',
            'express glands',
            'anal sacs',
            'scooting issue',
            'fishy smell',
            'anal sac expression',
            'gland expression',
        ],
    },
    {
        id: 'boarding',
        label: 'Boarding',
        keywords: [
            'Boarding',
            'pet hotel',
            'kennel stay',
            'overnight care',
            'pet sitting',
            'holiday care',
            'kennels',
        ],
    },
    {
        id: 'travel-preparation',
        label: 'Travel Preparation',
        keywords: [
            'Travel Preparation',
            'preparing for trip',
            'travel documents',
            'pre-travel check',
            'international travel cert',
            'airline requirements',
            'fit to travel',
        ],
    },
    {
        id: 'euthanasia-consultation',
        label: 'Euthanasia Consultation',
        keywords: [
            'Euthanasia Consultation',
            'putting to sleep discussion',
            'end of life options',
            'humane euthanasia talk',
            'quality of life assessment',
            'saying goodbye',
            'euthanasia decision',
        ],
    },
    {
        id: 'end-of-life-care',
        label: 'End-of-Life Care',
        keywords: [
            'End-of-Life Care',
            'hospice care',
            'palliative care',
            'comfort care for senior pet',
            'managing pain at end of life',
            'supportive care',
            'final stages care',
        ],
    },
];

// Initialize Fuse.js instance (can also be done in the util file where getReasonOptions is defined)
export const reasonFuse = new Fuse(REASONS_DATA, {
    includeScore: true,
    threshold: 0.4, // Adjust threshold as needed
    keys: ['label', 'keywords'],
});

// export const CLINIC_ID = getAuth()?.clinicId;

// export const BRAND_ID = getAuth()?.brandId;

export const DATE_FORMAT = 'DD MMM YYYY';

export const SPECIES = [
    { value: 'canine', label: 'Canine (Dog)' },
    { value: 'feline', label: 'Feline (Cat)' },
    { value: 'avian', label: 'Avian (Bird)' },
    { value: 'other', label: 'Other' },
];

export const BREEDS = {
    canine: [
        { value: 'afghan_hound', label: 'Afghan Hound', code: 'CA01' },
        { value: 'airedale_terrier', label: 'Airedale Terrier', code: 'CA02' },
        { value: 'akita', label: 'Akita', code: 'CA03' },
        { value: 'alaskan_malamute', label: 'Alaskan Malamute', code: 'CA04' },
        { value: 'american_bulldog', label: 'American Bulldog', code: 'CA05' },
        {
            value: 'american_eskimo_dog',
            label: 'American Eskimo Dog',
            code: 'CA06',
        },
        {
            value: 'american_pit_bull_terrier',
            label: 'American Pit Bull Terrier',
            code: 'CA07',
        },
        {
            value: 'american_staffordshire_terrier',
            label: 'American Staffordshire Terrier',
            code: 'CA08',
        },
        {
            value: 'anatolian_shepherd_dog',
            label: 'Anatolian Shepherd Dog',
            code: 'CA09',
        },
        {
            value: 'australian_cattle_dog',
            label: 'Australian Cattle Dog',
            code: 'CA10',
        },
        {
            value: 'australian_shepherd',
            label: 'Australian Shepherd',
            code: 'CA11',
        },
        {
            value: 'australian_terrier',
            label: 'Australian Terrier',
            code: 'CA12',
        },
        { value: 'basenji', label: 'Basenji', code: 'CB01' },
        { value: 'basset_hound', label: 'Basset Hound', code: 'CB02' },
        { value: 'beagle_cross', label: 'Beagle Cross', code: 'CB03' },
        { value: 'bearded_collie', label: 'Bearded Collie', code: 'CB04' },
        {
            value: 'bedlington_terrier',
            label: 'Bedlington Terrier',
            code: 'CB05',
        },
        { value: 'belgian_malinois', label: 'Belgian Malinois', code: 'CB06' },
        { value: 'belgian_sheepdog', label: 'Belgian Sheepdog', code: 'CB07' },
        { value: 'belgian_tervuren', label: 'Belgian Tervuren', code: 'CB08' },
        {
            value: 'bernese_mountain_dog',
            label: 'Bernese Mountain Dog',
            code: 'CB09',
        },
        { value: 'bichon_friese', label: 'Bichon Friese', code: 'CB10' },
        {
            value: 'black_and_tan_coonhound',
            label: 'Black and Tan Coonhound',
            code: 'CB11',
        },
        { value: 'bloodhound', label: 'Bloodhound', code: 'CB12' },
        { value: 'border_collie', label: 'Border Collie', code: 'CB13' },
        { value: 'border_terrier', label: 'Border Terrier', code: 'CB14' },
        { value: 'borzoi', label: 'Borzoi', code: 'CB15' },
        { value: 'boston_terrier', label: 'Boston Terrier', code: 'CB16' },
        {
            value: 'bouvier_des_flandres',
            label: 'Bouvier des Flandres',
            code: 'CB17',
        },
        { value: 'boxer', label: 'Boxer', code: 'CB18' },
        { value: 'boykin_spaniel', label: 'Boykin Spaniel', code: 'CB19' },
        { value: 'briard', label: 'Briard', code: 'CB20' },
        { value: 'brittany', label: 'Brittany', code: 'CB21' },
        { value: 'brussels_griffon', label: 'Brussels Griffon', code: 'CB22' },
        { value: 'bull_terrier', label: 'Bull Terrier', code: 'CB23' },
        { value: 'bulldog', label: 'Bulldog', code: 'CB24' },
        { value: 'bull_mastiff', label: 'Bull Mastiff', code: 'CB25' },
        { value: 'belgian_shepherd', label: 'Belgian Shepherd', code: 'CB26' },
        { value: 'caravan_hound', label: 'Caravan Hound', code: 'CC01' },
        { value: 'cairn_terrier', label: 'Cairn Terrier', code: 'CC02' },
        { value: 'canaan_dog', label: 'Canaan Dog', code: 'CC03' },
        { value: 'cane_corso', label: 'Cane Corso', code: 'CC04' },
        {
            value: 'cardigan_welsh_corgi',
            label: 'Cardigan Welsh Corgi',
            code: 'CC05',
        },
        {
            value: 'cavalier_king_charles_spaniel',
            label: 'Cavalier King Charles Spaniel',
            code: 'CC06',
        },
        {
            value: 'chesapeake_bay_retriever',
            label: 'Chesapeake Bay Retriever',
            code: 'CC07',
        },
        { value: 'chihuahua', label: 'Chihuahua', code: 'CC08' },
        { value: 'chinese_crested', label: 'Chinese Crested', code: 'CC09' },
        {
            value: 'chinese_shar_pei',
            label: 'Shar-Pei - Chinese',
            code: 'CC10',
        },
        { value: 'chow_chow', label: 'Chow Chow', code: 'CC11' },
        { value: 'clumber_spaniel', label: 'Clumber Spaniel', code: 'CC12' },
        { value: 'cocker_spaniel', label: 'Cocker Spaniel', code: 'CC13' },
        { value: 'collie', label: 'Collie', code: 'CC14' },
        { value: 'coton_de_tulear', label: 'Coton de Tulear', code: 'CC15' },
        {
            value: 'curly_coated_retriever',
            label: 'Curly-Coated Retriever',
            code: 'CC16',
        },
        { value: 'cross_breed', label: 'Cross Breed', code: 'CC17' },
        {
            value: 'cocker_spaniel_cross',
            label: 'Cocker-spaniel cross',
            code: 'CC18',
        },
        {
            value: 'dachshund_miniature',
            label: 'Dachshund - Miniature',
            code: 'CD01',
        },
        { value: 'dachshund', label: 'Dachshund', code: 'CD02' },
        { value: 'dalmatian', label: 'Dalmatian', code: 'CD03' },
        {
            value: 'dandie_dinmont_terrier',
            label: 'Dandie Dinmont Terrier',
            code: 'CD04',
        },
        { value: 'doberman', label: 'Doberman', code: 'CD05' },
        {
            value: 'dogue_de_bordeaux',
            label: 'Dogue de Bordeaux',
            code: 'CD06',
        },
        { value: 'dutch', label: 'Dutch', code: 'CD07' },
        { value: 'dogo_argentino', label: 'Dogo Argentino', code: 'CD08' },
        {
            value: 'american_cocker_spaniel',
            label: 'American Cocker Spaniel',
            code: 'CE01',
        },
        { value: 'english_bulldog', label: 'English Bulldog', code: 'CE02' },
        {
            value: 'english_cocker_spaniel',
            label: 'Cocker Spaniel English',
            code: 'CE03',
        },
        {
            value: 'american_cocker_spaniel',
            label: 'Cocker Spaniel - American',
            code: 'CE04',
        },
        { value: 'english_foxhound', label: 'English Foxhound', code: 'CE05' },
        { value: 'english_setter', label: 'English Setter', code: 'CE06' },
        {
            value: 'english_springer_spaniel',
            label: 'English Springer Spaniel',
            code: 'CE07',
        },
        {
            value: 'english_toy_spaniel',
            label: 'English Toy Spaniel',
            code: 'CE08',
        },
        {
            value: 'entlebucher_mountain_dog',
            label: 'Entlebucher Mountain Dog',
            code: 'CE09',
        },
        { value: 'eskimo_dog', label: 'Eskimo Dog', code: 'CE10' },
        { value: 'field_spaniel', label: 'Field Spaniel', code: 'CF01' },
        { value: 'markiesje', label: 'Markiesje', code: 'CF01' }, // Using Field Spaniel image
        { value: 'finnish_lapphund', label: 'Finnish Lapphund', code: 'CF02' },
        { value: 'finnish_spitz', label: 'Finnish Spitz', code: 'CF03' },
        {
            value: 'flat_coated_retriever',
            label: 'Flat-Coated Retriever',
            code: 'CF04',
        },
        { value: 'fox_terrier', label: 'Fox Terrier', code: 'CF05' },
        { value: 'french_bulldog', label: 'French Bulldog', code: 'CF06' },
        { value: 'german_pinscher', label: 'German Pinscher', code: 'CG01' },
        { value: 'german_shepherd', label: 'German Shepherd', code: 'CG02' },
        {
            value: 'german_shorthaired_pointer',
            label: 'German Shorthaired Pointer',
            code: 'CG03',
        },
        {
            value: 'german_wirehaired_pointer',
            label: 'German Wirehaired Pointer',
            code: 'CG04',
        },
        { value: 'giant_schnauzer', label: 'Giant Schnauzer', code: 'CG05' },
        {
            value: 'glen_of_imaal_terrier',
            label: 'Glen of Imaal Terrier',
            code: 'CG06',
        },
        { value: 'golden_retriever', label: 'Golden Retriever', code: 'CG07' },
        { value: 'gordon_setter', label: 'Gordon Setter', code: 'CG08' },
        { value: 'great_dane_cross', label: 'Great dane cross', code: 'CG09' },
        { value: 'great_pyrenees', label: 'Great Pyrenees', code: 'CG10' },
        {
            value: 'greater_swiss_mountain_dog',
            label: 'Greater Swiss Mountain Dog',
            code: 'CG11',
        },
        { value: 'greyhound', label: 'Greyhound', code: 'CG12' },
        { value: 'golden_cross', label: 'Golden Cross', code: 'CG13' },
        { value: 'harrier', label: 'Harrier', code: 'CH01' },
        { value: 'havanese', label: 'Havanese', code: 'CH02' },
        {
            value: 'himalayan_shepherd',
            label: 'Himalayan Shepherd',
            code: 'CH03',
        },
        { value: 'hound_cross', label: 'Hound Cross', code: 'CH04' },
        { value: 'ibizan_hound', label: 'Ibizan Hound', code: 'CI01' },
        {
            value: 'icelandic_sheepdog',
            label: 'Icelandic Sheepdog',
            code: 'CI02',
        },
        { value: 'irish_setter', label: 'Irish Setter', code: 'CI03' },
        { value: 'irish_terrier', label: 'Irish Terrier', code: 'CI04' },
        {
            value: 'irish_water_spaniel',
            label: 'Irish Water Spaniel',
            code: 'CI05',
        },
        { value: 'irish_wolfhound', label: 'Irish Wolfhound', code: 'CI06' },
        {
            value: 'jack_russel_terrier',
            label: 'Jack(Parson) Russel Terrier',
            code: 'CJ01',
        },
        { value: 'japanese_chin', label: 'Japanese Chin', code: 'CJ02' },
        { value: 'keeshond', label: 'Keeshond', code: 'CK01' },
        {
            value: 'kerry_blue_terrier',
            label: 'Kerry Blue Terrier',
            code: 'CK02',
        },
        { value: 'komondor', label: 'Komondor', code: 'CK03' },
        { value: 'kuvasz', label: 'Kuvasz', code: 'CK04' },
        { value: 'labrador', label: 'Labrador', code: 'CL01' },
        { value: 'lab_cross', label: 'Lab Cross', code: 'CL02' },
        {
            value: 'lagotto_romagnolo',
            label: 'Lagotto Romagnolo',
            code: 'CL03',
        },
        { value: 'lakeland_terrier', label: 'Lakeland Terrier', code: 'CL04' },
        { value: 'leonberger', label: 'Leonberger', code: 'CL05' },
        { value: 'lhasa_apso', label: 'Lhasa Apso', code: 'CL06' },
        { value: 'lowchen', label: 'Lowchen', code: 'CL07' },
        { value: 'labradoodle', label: 'Labradoodle', code: 'CL08' },
        {
            value: 'labrador_retriever',
            label: 'Labrador Retriever',
            code: 'CL09',
        },
        { value: 'maltese', label: 'Maltese', code: 'CM01' },
        {
            value: 'manchester_terrier',
            label: 'Manchester Terrier',
            code: 'CM02',
        },
        { value: 'mastiff', label: 'Mastiff', code: 'CM03' },
        {
            value: 'miniature_bull_terrier',
            label: 'Miniature Bull Terrier',
            code: 'CM04',
        },
        {
            value: 'miniature_pinscher',
            label: 'Miniature Pinscher',
            code: 'CM05',
        },
        {
            value: 'miniature_schnauzer',
            label: 'Miniature Schnauzer',
            code: 'CM06',
        },
        { value: 'mongrel', label: 'Mongrel', code: 'CM07' },
        { value: 'neo_mastiff', label: 'Neo Mastiff', code: 'CN01' },
        { value: 'newfoundland', label: 'Newfoundland', code: 'CN02' },
        { value: 'norfolk_terrier', label: 'Norfolk Terrier', code: 'CN03' },
        { value: 'norwegian_buhund', label: 'Norwegian Buhund', code: 'CN04' },
        {
            value: 'norwegian_elkhound',
            label: 'Norwegian Elkhound',
            code: 'CN05',
        },
        {
            value: 'norwegian_lundehund',
            label: 'Norwegian Lundehund',
            code: 'CN06',
        },
        { value: 'norwich_terrier', label: 'Norwich Terrier', code: 'CN07' },
        {
            value: 'nova_scotia_duck_tolling_retriever',
            label: 'Nova Scotia Duck Tolling Retriever',
            code: 'CN08',
        },
        {
            value: 'old_english_sheepdog',
            label: 'Old English Sheepdog',
            code: 'CO01',
        },
        { value: 'otterhound', label: 'Otterhound', code: 'CO02' },
        { value: 'other', label: 'Other', code: 'CO03' },
        { value: 'unknown', label: 'Unknown', code: 'CO04' },
        { value: 'papillon', label: 'Papillon', code: 'CP01' },
        {
            value: 'parson_russell_terrier',
            label: 'Parson Russell Terrier',
            code: 'CP02',
        },
        { value: 'pekingese', label: 'Pekingese', code: 'CP03' },
        {
            value: 'pembroke_welsh_corgi',
            label: 'Pembroke Welsh Corgi',
            code: 'CP04',
        },
        {
            value: 'petit_basset_griffon_vendeen',
            label: 'Petit Basset Griffon Vendeen',
            code: 'CP05',
        },
        { value: 'pharaoh_hound', label: 'Pharaoh Hound', code: 'CP06' },
        { value: 'plott', label: 'Plott', code: 'CP07' },
        { value: 'pointer', label: 'Pointer', code: 'CP08' },
        {
            value: 'polish_lowland_sheepdog',
            label: 'Polish Lowland Sheepdog',
            code: 'CP09',
        },
        { value: 'pomeranian', label: 'Pomeranian', code: 'CP10' },
        { value: 'poodle', label: 'Poodle', code: 'CP11' },
        {
            value: 'portuguese_water_dog',
            label: 'Portuguese Water Dog',
            code: 'CP12',
        },
        { value: 'pug_cross', label: 'Pug Cross', code: 'CP13' },
        { value: 'puli', label: 'Puli', code: 'CP14' },
        {
            value: 'pyrenean_shepherd',
            label: 'Pyrenean Shepherd',
            code: 'CP15',
        },
        { value: 'poodle_miniature', label: 'Poodle-Miniature', code: 'CP16' },
        { value: 'poodle_toy', label: 'Poodle-Toy', code: 'CP17' },
        { value: 'rat_terrier', label: 'Rat Terrier', code: 'CR01' },
        {
            value: 'redbone_coonhound',
            label: 'Redbone Coonhound',
            code: 'CR02',
        },
        {
            value: 'rhodesian_ridgeback',
            label: 'Rhodesian Ridgeback',
            code: 'CR03',
        },
        { value: 'rottweiler_cross', label: 'Rottweiler cross', code: 'CR04' },
        { value: 'st_bernard', label: 'St Bernard', code: 'CS01' },
        { value: 'saluki', label: 'Saluki', code: 'CS02' },
        { value: 'samoyed', label: 'Samoyed', code: 'CS03' },
        { value: 'schipperke', label: 'Schipperke', code: 'CS04' },
        {
            value: 'scottish_deerhound',
            label: 'Scottish Deerhound',
            code: 'CS05',
        },
        { value: 'scottish_terrier', label: 'Scottish Terrier', code: 'CS06' },
        { value: 'sealyham_terrier', label: 'Sealyham Terrier', code: 'CS07' },
        { value: 'sheep_dog', label: 'Sheep Dog', code: 'CS08' },
        { value: 'shiba_inu', label: 'Shiba Inu', code: 'CS09' },
        { value: 'shih_tzu', label: 'Shih Tzu', code: 'CS10' },
        { value: 'siberian_husky', label: 'Siberian Husky', code: 'CS11' },
        { value: 'silky_terrier', label: 'Silky Terrier', code: 'CS12' },
        { value: 'skye_terrier', label: 'Skye Terrier', code: 'CS13' },
        { value: 'sloughi', label: 'Sloughi', code: 'CS14' },
        {
            value: 'small_munsterlander_pointer',
            label: 'Small Munsterlander Pointer',
            code: 'CS15',
        },
        {
            value: 'soft_coated_wheaten_terrier',
            label: 'Soft Coated Wheaten Terrier',
            code: 'CS16',
        },
        {
            value: 'spanish_water_dog',
            label: 'Spanish Water Dog',
            code: 'CS17',
        },
        { value: 'spinone_italiano', label: 'Spinone Italiano', code: 'CS18' },
        {
            value: 'staffordshire_bull_terrier',
            label: 'Staffordshire Bull Terrier',
            code: 'CS19',
        },
        {
            value: 'standard_schnauzer',
            label: 'Standard Schnauzer',
            code: 'CS20',
        },
        { value: 'sussex_spaniel', label: 'Sussex Spaniel', code: 'CS21' },
        { value: 'swedish_vallhund', label: 'Swedish Vallhund', code: 'CS22' },
        { value: 'spitz', label: 'Spitz', code: 'CS23' },
        { value: 'tibetan_mastiff', label: 'Tibetan Mastiff', code: 'CT01' },
        { value: 'tibetan_spaniel', label: 'Tibetan Spaniel', code: 'CT02' },
        { value: 'tibetan_terrier', label: 'Tibetan Terrier', code: 'CT03' },
        { value: 'toy_fox_terrier', label: 'Toy Fox Terrier', code: 'CT04' },
        {
            value: 'treeing_walker_coonhound',
            label: 'Treeing Walker Coonhound',
            code: 'CT05',
        },
        { value: 'terrier_cross', label: 'Terrier Cross', code: 'CT06' },
        { value: 'vizsla', label: 'Vizsla', code: 'CV01' },
        { value: 'weimaraner', label: 'Weimaraner', code: 'CW01' },
        {
            value: 'welsh_springer_spaniel',
            label: 'Welsh Springer Spaniel',
            code: 'CW02',
        },
        { value: 'welsh_terrier', label: 'Welsh Terrier', code: 'CW03' },
        {
            value: 'west_highland_white_terrier',
            label: 'West Highland White Terrier',
            code: 'CW04',
        },
        { value: 'whippet', label: 'Whippet', code: 'CW05' },
        {
            value: 'wirehaired_pointing_griffon',
            label: 'Wirehaired Pointing Griffon',
            code: 'CW06',
        },
        {
            value: 'wirehaired_vizsla',
            label: 'Wirehaired Vizsla',
            code: 'CW07',
        },
        { value: 'xoloitzcuintli', label: 'Xoloitzcuintli', code: 'CX01' },
        {
            value: 'yorkshire_terrier',
            label: 'Yorkshire Terrier',
            code: 'CY01',
        },
        {
            value: 'indie',
            label: 'Indie',
            code: 'CZ01',
        },
        {
            value: 'affenpinscher',
            label: 'German Pinscher',
            code: 'CG01', // From German Pinscher code
        },
        {
            value: 'pug',
            label: 'Pug Cross',
            code: 'CP13', // From Pug Cross code
        },
        {
            value: 'cocker_spanial_english',
            label: 'Cocker Spaniel',
            code: 'CC13', // From Cocker Spaniel code
        },
        {
            value: 'st_barnard',
            label: 'St Barnard',
            code: 'CS01', // From St Bernard code
        },
        {
            value: 'rottweiler',
            label: 'Rottweiler cross',
            code: 'CR04', // From Rottweiler code
        },
        {
            value: 'dachsund',
            label: 'Dachsund',
            code: 'CD02', // From Dachshund code
        },
        {
            value: 'great_dane',
            label: 'Great dane cross',
            code: 'CG09', // From Great Dane Cross code
        },
        {
            value: 'beagle',
            label: 'Beagle',
            code: 'CB03', // From Beagle Cross code
        },
        {
            value: 'maltipoo',
            label: 'Maltipoo',
            code: 'CB10', // From Maltipoo code
        },
        {
            value: 'dalmation',
            label: 'Dalmation',
            code: 'CD03', // From Dalmatian code
        },
        {
            value: 'himalayan_sheepdog',
            label: 'Sheep Dog',
            code: 'CS08', // From Sheep Dog code
        },
        {
            value: 'cocker_spaniel_american',
            label: 'American Cocker Spaniel',
            code: 'CE01',
        },
        {
            value: 'saint_bernard',
            label: 'St Barnard',
            code: 'CS01',
        },
        {
            value: 'neapolitan_mastiff',
            label: 'Mastiff',
            code: 'CM03',
        },
        {
            value: 'afgan_hound',
            label: 'Afgan Hound',
            code: 'CA01',
        },
        {
            value: 'shar_pei_chinese',
            label: 'Shar-Pei - Chinese',
            code: 'CC10',
        },
        {
            value: 'jack(parson)_russel_terrier',
            label: 'Jack(Parson) Russel Terrier',
            code: 'CJ01',
        },
        {
            value: 'poodle-standard',
            label: 'Poodle',
            code: 'CP11',
        },
        {
            value: 'bichon_frise',
            label: 'Bichon Friese',
            code: 'CB10',
        },
        {
            value: 'american_staffordshire_terrie',
            label: 'Staffordshire Bull Terrier',
            code: 'CS19', // From Staffordshire Bull Terrier code
        },
        {
            value: 'dutch_shepherd',
            label: 'Dutch',
            code: 'CD07', // From Dutch code
        },
        {
            value: 'shih-poo',
            label: 'Shih Tzu',
            code: 'CS10', // From Shih Tzu code
        },
        {
            value: 'fox_terrier_wire',
            label: 'Fox Terrier',
            code: 'CF05', // From Fox Terrier code
        },
        {
            value: 'american_bully',
            label: 'American Bulldog',
            code: 'CA05', // From American Bulldog code
        },
        {
            value: 'corgi_pembroke_welsh',
            label: 'Pembroke Welsh Corgi',
            code: 'CP04', // From Pembroke Welsh Corgi code
        },
        {
            value: 'pomeranian_american_eskimo_mix',
            label: 'Pomeranian',
            code: 'CP10', // From Pomeranian code
        },
        {
            value: 'american_water_spaniel',
            label: 'American Cocker Spaniel',
            code: 'CE01',
        },
        {
            value: 'shetland_sheepdog',
            label: 'Sheep Dog',
            code: 'CS08',
        },
        {
            value: 'mudhol_hound',
            label: 'Hound Cross',
            code: 'CH04',
        },
        {
            value: 'cavapoo',
            label: 'Maltese',
            code: 'CM01',
        },
        {
            value: 'teacup_pom',
            label: 'Pomeranian',
            code: 'CP10',
        },
        {
            value: 'komodor',
            label: 'Komondor',
            code: 'CK03',
        },
        {
            value: 'american_akita',
            label: 'Akita',
            code: 'CA03',
        },
        {
            value: 'boerboel',
            label: 'Mastiff',
            code: 'CM03',
        },
        {
            value: 'husky_shih_tzu_cross',
            label: 'Shih Tzu',
            code: 'CS10',
        },
        {
            value: 'bullmastiff',
            label: 'Mastiff',
            code: 'CM03',
        },
        {
            value: 'toy_pomeranian',
            label: 'Pomeranian',
            code: 'CP10',
        },
        {
            value: 'english_shepherd',
            label: 'Australian Shepherd',
            code: 'CA11',
        },
        {
            value: 'rajapalayam',
            label: 'Indie',
            code: 'CZ01',
        },
        {
            value: 'miniture_schnauzer',
            label: 'Miniature Schnauzer',
            code: 'CM06',
        },
        {
            value: 'pomeranian_cross',
            label: 'Pomeranian',
            code: 'CP10',
        },
        {
            value: 'corgi_cardigan_welsh',
            label: 'Cardigan Welsh Corgi',
            code: 'CC05',
        },
        {
            value: 'goldendoodle',
            label: 'Labradoodle',
            code: 'CL08',
        },
    ],
    feline: [
        { value: 'tabby', label: 'Tabby', code: 'F11' },
        { value: 'abyssinian', label: 'Abyssinian', code: 'F1' },
        { value: 'aegean', label: 'Aegean', code: 'F2' },
        { value: 'american_bobtail', label: 'American Bobtail', code: 'F3' },
        { value: 'american_curl', label: 'American Curl', code: 'F4' },
        {
            value: 'american_shorthair',
            label: 'American Shorthair',
            code: 'F5',
        },
        { value: 'american_wirehair', label: 'American Wirehair', code: 'F6' },
        { value: 'arabian_mau', label: 'Arabian Mau', code: 'F7' },
        { value: 'balinese', label: 'Balinese', code: 'F8' },
        { value: 'bambino', label: 'Bambino', code: 'F9' },
        { value: 'bengal', label: 'Bengal', code: 'F10' },
        { value: 'birman', label: 'Birman', code: 'F11' },
        { value: 'bombay', label: 'Bombay', code: 'F12' },
        { value: 'british_longhair', label: 'British Longhair', code: 'F13' },
        { value: 'british_shorthair', label: 'British Shorthair', code: 'F14' },
        { value: 'burmese', label: 'Burmese', code: 'F15' },
        { value: 'burmilla', label: 'Burmilla', code: 'F16' },
        {
            value: 'california_spangled',
            label: 'California Spangled',
            code: 'F17',
        },
        { value: 'chantilly_tiffany', label: 'Chantilly-Tiffany', code: 'F18' },
        { value: 'chartreux', label: 'Chartreux', code: 'F19' },
        { value: 'chausie', label: 'Chausie', code: 'F20' },
        { value: 'cheetoh', label: 'Cheetoh', code: 'F21' },
        {
            value: 'colorpoint_shorthair',
            label: 'Colorpoint Shorthair',
            code: 'F22',
        },
        { value: 'cornish_rex', label: 'Cornish Rex', code: 'F23' },
        { value: 'cymric', label: 'Cymric', code: 'F24' },
        { value: 'cyprus', label: 'Cyprus', code: 'F25' },
        { value: 'devon_rex', label: 'Devon Rex', code: 'F26' },
        { value: 'donskoy', label: 'Donskoy', code: 'F27' },
        { value: 'dsh', label: 'DSH', code: 'F28' },
        {
            value: 'domestic_short_hair',
            label: 'Domestic Short Hair',
            code: 'F29',
        },
        {
            value: 'domestic_medium_hair',
            label: 'Domestic Medium Hair',
            code: 'F30',
        },
        {
            value: 'domestic_long_hair',
            label: 'Domestic Long Hair',
            code: 'F31',
        },
        { value: 'egyptian_mau', label: 'Egyptian Mau', code: 'F32' },
        {
            value: 'european_shorthair',
            label: 'European Shorthair',
            code: 'F33',
        },
        { value: 'exotic_shorthair', label: 'Exotic Shorthair', code: 'F34' },
        { value: 'foldex', label: 'Foldex', code: 'F35' },
        { value: 'foreign_white', label: 'Foreign White', code: 'F36' },
        { value: 'forest_cat', label: 'Forest Cat', code: 'F37' },
        { value: 'german_rex', label: 'German Rex', code: 'F38' },
        { value: 'havana_brown', label: 'Havana Brown', code: 'F39' },
        { value: 'highlander', label: 'Highlander', code: 'F40' },
        { value: 'himalayan', label: 'Himalayan', code: 'F41' },
        { value: 'japanese_bobtail', label: 'Japanese Bobtail', code: 'F42' },
        { value: 'javanese', label: 'Javanese', code: 'F43' },
        { value: 'khao_manee', label: 'Khao Manee', code: 'F44' },
        { value: 'korat', label: 'Korat', code: 'F45' },
        { value: 'kurilian_bobtail', label: 'Kurilian Bobtail', code: 'F46' },
        { value: 'laperm', label: 'LaPerm', code: 'F47' },
        { value: 'lykoi', label: 'Lykoi', code: 'F48' },
        { value: 'maine_coon_cat', label: 'Maine Coon Cat', code: 'F49' },
        { value: 'manx', label: 'Manx', code: 'F50' },
        { value: 'mekong_bobtail', label: 'Mekong Bobtail', code: 'F51' },
        { value: 'minskin', label: 'Minskin', code: 'F52' },
        { value: 'munchkin', label: 'Munchkin', code: 'F53' },
        { value: 'nebelung', label: 'Nebelung', code: 'F54' },
        {
            value: 'norwegian_forest_cat',
            label: 'Norwegian Forest Cat',
            code: 'F55',
        },
        { value: 'other', label: 'Other', code: 'F56' },
        { value: 'ocicat', label: 'Ocicat', code: 'F57' },
        { value: 'oriental_bicolor', label: 'Oriental Bicolor', code: 'F58' },
        { value: 'oriental_longhair', label: 'Oriental Longhair', code: 'F59' },
        {
            value: 'oriental_shorthair',
            label: 'Oriental Shorthair',
            code: 'F60',
        },
        { value: 'persian_cross', label: 'Persian Cross', code: 'F61' },
        { value: 'peterbald', label: 'Peterbald', code: 'F62' },
        { value: 'pixie_bob', label: 'Pixie-bob', code: 'F63' },
        { value: 'ragamuffin', label: 'Ragamuffin', code: 'F64' },
        { value: 'ragdoll', label: 'Ragdoll', code: 'F65' },
        { value: 'russian_blue', label: 'Russian Blue', code: 'F66' },
        { value: 'savannah', label: 'Savannah', code: 'F67' },
        { value: 'scottish_fold', label: 'Scottish Fold', code: 'F68' },
        { value: 'selkirk_rex', label: 'Selkirk Rex', code: 'F69' },
        { value: 'serengeti', label: 'Serengeti', code: 'F70' },
        { value: 'siamese', label: 'Siamese', code: 'F71' },
        { value: 'siberian', label: 'Siberian', code: 'F72' },
        { value: 'singapura', label: 'Singapura', code: 'F73' },
        { value: 'snowshoe', label: 'Snowshoe', code: 'F74' },
        { value: 'sokoke', label: 'Sokoke', code: 'F75' },
        { value: 'somali', label: 'Somali', code: 'F76' },
        { value: 'sphynx', label: 'Sphynx', code: 'F77' },
        { value: 'thai', label: 'Thai', code: 'F78' },
        { value: 'tonkinese', label: 'Tonkinese', code: 'F79' },
        { value: 'toyger', label: 'Toyger', code: 'F80' },
        { value: 'turkish_angora', label: 'Turkish Angora', code: 'F81' },
        { value: 'turkish_van', label: 'Turkish Van', code: 'F82' },
        { value: 'ukrainian_levkoy', label: 'Ukrainian Levkoy', code: 'F83' },
        { value: 'unknown', label: 'Unknown', code: 'F84' },
        {
            value: 'persian',
            label: 'Persian',
            code: 'F61', // From Persian Cross code
        },
        {
            value: 'angora',
            label: 'Turkish Angora',
            code: 'F81', // From Turkish Angora code
        },
    ],
    avian: [
        { value: 'african_grey', label: 'African Grey', code: 'A1' },
        { value: 'amazon_parrot', label: 'Amazon Parrot', code: 'A2' },
        { value: 'budgerigar', label: 'Budgerigar (Budgie)', code: 'A3' },
        { value: 'caique', label: 'Caique', code: 'A4' },
        { value: 'cockatiel', label: 'Cockatiel', code: 'A5' },
        { value: 'cockatoo', label: 'Cockatoo', code: 'A6' },
        { value: 'conure', label: 'Conure', code: 'A7' },
        { value: 'eclectus', label: 'Eclectus', code: 'A8' },
        { value: 'lovebird', label: 'Lovebird', code: 'A9' },
        { value: 'macaw', label: 'Macaw', code: 'A10' },
        { value: 'parakeet', label: 'Parakeet', code: 'A11' },
        { value: 'parrotlet', label: 'Parrotlet', code: 'A12' },
        { value: 'pionus', label: 'Pionus', code: 'A13' },
        {
            value: 'poicephalus',
            label: "Poicephalus (Senegal Parrot, Meyer's Parrot)",
            code: 'A14',
        },
        { value: 'canary', label: 'Canary', code: 'A15' },
        { value: 'gouldian_finch', label: 'Gouldian Finch', code: 'A16' },
        { value: 'java_finch', label: 'Java Finch', code: 'A17' },
        { value: 'society_finch', label: 'Society Finch', code: 'A18' },
        { value: 'zebra_finch', label: 'Zebra Finch', code: 'A19' },
        { value: 'tanager', label: 'Tanager', code: 'A20' },
        { value: 'toucan', label: 'Toucan', code: 'A21' },
        { value: 'turaco', label: 'Turaco', code: 'A22' },
        { value: 'diamond_dove', label: 'Diamond Dove', code: 'A23' },
        { value: 'mourning_dove', label: 'Mourning Dove', code: 'A24' },
        { value: 'pigeon', label: 'Pigeon', code: 'A25' },
        { value: 'bee_eater', label: 'Bee-eater', code: 'A26' },
        { value: 'bird_of_paradise', label: 'Bird of Paradise', code: 'A27' },
        { value: 'hornbill', label: 'Hornbill', code: 'A28' },
        { value: 'kingfisher', label: 'Kingfisher', code: 'A29' },
        { value: 'guinea_fowl', label: 'Guinea Fowl', code: 'A30' },
        { value: 'pheasant', label: 'Pheasant', code: 'A31' },
        { value: 'quail', label: 'Quail', code: 'A32' },
        { value: 'chicken', label: 'Chicken', code: 'A33' },
        { value: 'duck', label: 'Duck', code: 'A34' },
        { value: 'goose', label: 'Goose', code: 'A35' },
        { value: 'turkey', label: 'Turkey', code: 'A36' },
        { value: 'swan', label: 'Swan', code: 'A37' },
        { value: 'eagle', label: 'Eagle', code: 'A38' },
        { value: 'falcon', label: 'Falcon', code: 'A39' },
        { value: 'hawk', label: 'Hawk', code: 'A40' },
        { value: 'owl', label: 'Owl', code: 'A41' },
        {
            value: 'quaker_parrot',
            label: 'Quaker Parrot (Monk Parakeet)',
            code: 'A42',
        },
        { value: 'rosella', label: 'Rosella', code: 'A43' },
        { value: 'bourkes_parakeet', label: "Bourke's Parakeet", code: 'A44' },
        { value: 'derbyan_parakeet', label: 'Derbyan Parakeet', code: 'A45' },
        { value: 'kakariki', label: 'Kakariki', code: 'A46' },
        {
            value: 'lineolated_parakeet',
            label: 'Lineolated Parakeet (Linnie)',
            code: 'A47',
        },
        { value: 'red_rump_parakeet', label: 'Red Rump Parakeet', code: 'A48' },
        { value: 'umbrella_cockatoo', label: 'Umbrella Cockatoo', code: 'A49' },
        { value: 'eclectus_parrot', label: 'Eclectus Parrot', code: 'A50' },
        {
            value: 'lesser_sulfur_crested_cockatoo',
            label: 'Lesser Sulfur Crested Cockatoo',
            code: 'A51',
        },
        { value: 'other', label: 'Other', code: 'A52' },
        {
            value: 'finch',
            label: 'Society Finch',
            code: 'A18', // From Society Finch code
        },
        {
            value: 'parolette',
            label: 'Parrotlet',
            code: 'A12', // From Parrotlet code
        },
        {
            value: 'macaw_blue_and_gold',
            label: 'Macaw',
            code: 'A10', // From Macaw code
        },
        {
            value: 'african_grey_parrot',
            label: 'African Grey',
            code: 'A1', // From African Grey code
        },
        {
            value: 'blue_crested_conure',
            label: 'Conure',
            code: 'A7', // From Conure code
        },
        {
            value: 'rock_pigeon',
            label: 'Pigeon',
            code: 'A25', // From Pigeon code
        },
        {
            value: 'egret',
            label: 'Other',
            code: 'A52', // From Other code
        },
        {
            value: 'sun_conure',
            label: 'Other',
            code: 'A52', // From Other code
        },
        {
            value: 'asian_koel',
            label: 'Other',
            code: 'A52', // From Other code
        },
        {
            value: 'indian_ringed-neck',
            label: 'Other',
            code: 'A52', // From Other code
        },
    ],
    other: [
        { value: 'angora', label: 'Angora', code: 'O1' },
        { value: 'rabbit', label: 'Rabbit', code: 'O2' },
        { value: 'guinea_pig', label: 'Guinea Pig', code: 'O3' },
        { value: 'hamster', label: 'Hamster', code: 'O4' },
        { value: 'gerbil', label: 'Gerbil', code: 'O5' },
        { value: 'ferret', label: 'Ferret', code: 'O6' },
        { value: 'mouse', label: 'Mouse', code: 'O7' },
        { value: 'tortoise', label: 'Tortoise', code: 'O8' },
        { value: 'gecko', label: 'Gecko', code: 'O9' },
        { value: 'bearded_dragon', label: 'Bearded Dragon', code: 'O10' },
        { value: 'iguana', label: 'Iguana', code: 'O11' },
        { value: 'ball_python', label: 'Ball Python', code: 'O12' },
        { value: 'corn_snake', label: 'Corn Snake', code: 'O13' },
        { value: 'king_snake', label: 'King Snake', code: 'O14' },
        { value: 'milk_snake', label: 'Milk Snake', code: 'O15' },
        { value: 'boa_constrictor', label: 'Boa Constrictor', code: 'O16' },
        { value: 'garter_snake', label: 'Garter Snake', code: 'O17' },
        { value: 'chameleon', label: 'Chameleon', code: 'O18' },
        { value: 'frog', label: 'Frog', code: 'O19' },
        { value: 'toad', label: 'Toad', code: 'O20' },
        { value: 'salamander', label: 'Salamander', code: 'O21' },
        { value: 'newt', label: 'Newt', code: 'O22' },
        { value: 'goat', label: 'Goat', code: 'O23' },
        { value: 'sheep', label: 'Sheep', code: 'O24' },
        { value: 'pig', label: 'Pig', code: 'O25' },
        { value: 'cattle', label: 'Cattle (Cow)', code: 'O26' },
        { value: 'horse', label: 'Horse', code: 'O27' },
        { value: 'donkey', label: 'Donkey', code: 'O28' },
        { value: 'mule', label: 'Mule', code: 'O29' },
        { value: 'hedgehog', label: 'Hedgehog', code: 'O30' },
        { value: 'squirrel', label: 'Squirrel', code: 'O31' },
        { value: 'skunk', label: 'Skunk', code: 'O32' },
        { value: 'fox', label: 'Fox', code: 'O33' },
        { value: 'monkey', label: 'Monkey', code: 'O34' },
        { value: 'gibbon', label: 'Gibbon', code: 'O35' },
        { value: 'orangutan', label: 'Orangutan', code: 'O36' },
        {
            value: 'eastern_box_turtle',
            label: 'Eastern Box Turtle',
            code: 'O37',
        },
        { value: 'rat', label: 'Rat', code: 'O38' },
        {
            value: 'guinea_pigs',
            label: 'Guinea Pig',
            code: 'O3', // From Guinea Pig code
        },
        {
            value: 'himalayan',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'mini_lop',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'angora',
            label: 'Angora',
            code: 'O1', // From Angora code
        },
        {
            value: 'new_zealand',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'cross',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'mini_rex',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'rex',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'starback_tortoise',
            label: 'Tortoise',
            code: 'O8', // From Tortoise code
        },
        {
            value: 'dutch',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'english',
            label: 'Rabbit',
            code: 'O2', // From Rabbit code
        },
        {
            value: 'mouse',
            label: 'Mouse',
            code: 'O7', // From Mouse code
        },
        {
            value: 'rat',
            label: 'Rat',
            code: 'O38', // From Rat code
        },
        {
            value: 'red_eared_slider',
            label: 'Tortoise',
            code: 'O8', // From Tortoise code
        },
        {
            value: 'indian_roofed_turtle',
            label: 'Tortoise',
            code: 'O8', // From Tortoise code
        },
        {
            value: 'merino',
            label: 'Sheep',
            code: 'O24', // From Sheep code
        },
    ],
};
export const BODY_MAPS = [
    {
        label: 'Canine Body Map',
        value: '/images/body-maps/canine.png',
    },
    {
        label: 'Feline Body Map',
        value: '/images/body-maps/feline.png',
    },
    // {
    //     label: 'Rabbit Lapine',
    //     value: '/images/body-maps/rabbit-body-map.png',
    // },
    // {
    //     label: 'Horse Equine',
    //     value: '/images/body-maps/horse-body-map.png',
    // },
    // {
    //     label: 'Bull Bovine',
    //     value: '/images/body-maps/bull-body-map.png',
    // },
    // {
    //     label: 'Bird Avian',
    //     value: '/images/body-maps/bird-body-map.png',
    // },
];

export const vitalsArray = [
    {
        title: 'Weight (kgs)',
        name: 'weight',
    },
    {
        title: 'Temp (°F)',
        name: 'temperature',
    },
    {
        title: 'Heart Rate (bpm)',
        name: 'heartRate',
    },
    {
        title: 'Resp. Rate (bpm)',
        name: 'respRate',
    },
    {
        title: 'Attitude',
        name: 'attitude',
    },
    {
        title: 'Pain Score (1-10)',
        name: 'painScore',
    },
    {
        title: 'Mucous Membrane',
        name: 'mucousMembrane',
    },
    {
        title: 'Capillary Refill',
        name: 'capillaryRefill',
    },
    {
        title: 'Hydration Status',
        name: 'hydrationStatus',
    },
    {
        title: 'BCS (1-5)',
        name: 'bcs',
    },
    {
        title: 'Blood Pressure (mmHg)',
        name: 'bp',
    },
    {
        title: 'MaP (mmHg)',
        name: 'map',
    },
];
export const tagConfig = {
    medium: {
        tagColor: 'warning',
    },
    high: {
        tagColor: 'error',
    },
};

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes
export const DIAGNOSTIC_MAX_FILE_SIZE = 2.5 * 1024 * 1024 * 1024; // 2.5GB in bytes
export const VIDEO_MAX_FILE_SIZE = 2.5 * 1024 * 1024 * 1024; // 2.5GB in bytes
export const ACCEPTED_FILE_TYPES =
    '.pdf, .doc, .docx, .jpg, .jpeg, .png, .svg, .word';
export const ACCEPTED_VIDEO_FILE_TYPES = '.mp4, .mkv, .mov, .avi, .wmv';
export const ACCEPTED_ALL_FILE_TYPES = `${ACCEPTED_FILE_TYPES}, ${ACCEPTED_VIDEO_FILE_TYPES}`;

// Mock data for Indian states and cities
export const indianStates = [
    { label: 'Andhra Pradesh', value: 'andhra_pradesh' },
    { label: 'Arunachal Pradesh', value: 'arunachal_pradesh' },
    { label: 'Assam', value: 'assam' },
    { label: 'Bihar', value: 'bihar' },
    { label: 'Chhattisgarh', value: 'chhattisgarh' },
    { label: 'Goa', value: 'goa' },
    { label: 'Gujarat', value: 'gujarat' },
    { label: 'Haryana', value: 'haryana' },
    { label: 'Himachal Pradesh', value: 'himachal_pradesh' },
    { label: 'Jharkhand', value: 'jharkhand' },
    { label: 'Karnataka', value: 'karnataka' },
    { label: 'Kerala', value: 'kerala' },
    { label: 'Madhya Pradesh', value: 'madhya_pradesh' },
    { label: 'Maharashtra', value: 'maharashtra' },
    { label: 'Manipur', value: 'manipur' },
    { label: 'Meghalaya', value: 'meghalaya' },
    { label: 'Mizoram', value: 'mizoram' },
    { label: 'Nagaland', value: 'nagaland' },
    { label: 'Odisha', value: 'odisha' },
    { label: 'Punjab', value: 'punjab' },
    { label: 'Rajasthan', value: 'rajasthan' },
    { label: 'Sikkim', value: 'sikkim' },
    { label: 'Tamil Nadu', value: 'tamil_nadu' },
    { label: 'Telangana', value: 'telangana' },
    { label: 'Tripura', value: 'tripura' },
    { label: 'Uttar Pradesh', value: 'uttar_pradesh' },
    { label: 'Uttarakhand', value: 'uttarakhand' },
    { label: 'West Bengal', value: 'west_bengal' },
    // Union Territories
    {
        label: 'Andaman and Nicobar Islands',
        value: 'andaman_and_nicobar_islands',
    },
    { label: 'Chandigarh', value: 'chandigarh' },
    {
        label: 'Dadra and Nagar Haveli and Daman and Diu',
        value: 'dadra_and_nagar_haveli_and_daman_and_diu',
    },
    { label: 'Delhi', value: 'delhi' },
    { label: 'Jammu and Kashmir', value: 'jammu_and_kashmir' },
    { label: 'Ladakh', value: 'ladakh' },
    { label: 'Lakshadweep', value: 'lakshadweep' },
    { label: 'Puducherry', value: 'puducherry' },
];

export const indianCities = [
    { label: 'Mumbai', value: 'mumbai' },
    { label: 'Delhi', value: 'delhi' },
    { label: 'Bangalore', value: 'bangalore' },
    { label: 'Hyderabad', value: 'hyderabad' },
    { label: 'Ahmedabad', value: 'ahmedabad' },
    { label: 'Chennai', value: 'chennai' },
    { label: 'Kolkata', value: 'kolkata' },
    { label: 'Surat', value: 'surat' },
    { label: 'Pune', value: 'pune' },
    { label: 'Jaipur', value: 'jaipur' },
    { label: 'Lucknow', value: 'lucknow' },
    { label: 'Kanpur', value: 'kanpur' },
    { label: 'Nagpur', value: 'nagpur' },
    { label: 'Indore', value: 'indore' },
    { label: 'Thane', value: 'thane' },
    { label: 'Bhopal', value: 'bhopal' },
    { label: 'Visakhapatnam', value: 'visakhapatnam' },
    { label: 'Pimpri-Chinchwad', value: 'pimpri_chinchwad' },
    { label: 'Patna', value: 'patna' },
    { label: 'Vadodara', value: 'vadodara' },
    { label: 'Ghaziabad', value: 'ghaziabad' },
    { label: 'Ludhiana', value: 'ludhiana' },
    { label: 'Agra', value: 'agra' },
    { label: 'Nashik', value: 'nashik' },
    { label: 'Faridabad', value: 'faridabad' },
    { label: 'Meerut', value: 'meerut' },
    { label: 'Rajkot', value: 'rajkot' },
    { label: 'Kalyan-Dombivli', value: 'kalyan_dombivli' },
    { label: 'Vasai-Virar', value: 'vasai_virar' },
    { label: 'Varanasi', value: 'varanasi' },
    { label: 'Srinagar', value: 'srinagar' },
    { label: 'Aurangabad', value: 'aurangabad' },
    { label: 'Dhanbad', value: 'dhanbad' },
    { label: 'Amritsar', value: 'amritsar' },
    { label: 'Navi Mumbai', value: 'navi_mumbai' },
    { label: 'Allahabad', value: 'allahabad' },
    { label: 'Ranchi', value: 'ranchi' },
    { label: 'Howrah', value: 'howrah' },
    { label: 'Coimbatore', value: 'coimbatore' },
    { label: 'Jabalpur', value: 'jabalpur' },
    { label: 'Gwalior', value: 'gwalior' },
    { label: 'Vijayawada', value: 'vijayawada' },
    { label: 'Jodhpur', value: 'jodhpur' },
    { label: 'Madurai', value: 'madurai' },
    { label: 'Raipur', value: 'raipur' },
    { label: 'Kochi', value: 'kochi' },
    { label: 'Chandigarh', value: 'chandigarh' },
    { label: 'Guwahati', value: 'guwahati' },
    { label: 'Thiruvananthapuram', value: 'thiruvananthapuram' },
    { label: 'Solapur', value: 'solapur' },
];

export const TAB_TYPES = {
    diagnostics: 'diagnostics',
    vaccination: 'vaccination',
};

export const dayNumbers = {
    0: 'sunday',
    1: 'monday',
    2: 'tuesday',
    3: 'wednesday',
    4: 'thursday',
    5: 'friday',
    6: 'saturday',
};

export const cartItemAddedFromType = {
    PRESCRIPTION: 'prescriptions',
    PLAN: 'plans',
};

export const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(parseFloat(amount));
};

const getDifferenceTextColor = (differenceAmount: string) => {
    const numericDifference = parseFloat(differenceAmount);
    if (numericDifference < 0) return 'text-red-500';
    if (numericDifference > 0) return 'text-green-500';
    return 'text-neutral-900';
};