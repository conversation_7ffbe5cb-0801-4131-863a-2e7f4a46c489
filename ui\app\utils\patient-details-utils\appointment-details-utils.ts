import moment from 'moment';
import {
    BODY_MAPS,
    CART_TYPES,
    DATE_FORMAT,
    vitalsArray,
} from '../constant';
import { EnumAppointmentStatus } from '../../types/appointment';
import {
    Mutation,
    MutationFunction,
    UseMutateFunction,
} from '@tanstack/react-query';
import { PrescriptionItemType } from '../../molecules/PrescriptionSummery';
import { removeDuplicatePaths } from '../common';
import { v4 as uuidv4 } from 'uuid';
import { getAuth } from '@/app/services/identity.service';
import PatientDetail from '@/app/patients/[id]/details/page';
import { capitalize } from 'lodash';
import { VitalsDataT } from '@/app/molecules/VitalsList';
import { getAppointmentDetails as getAppointmentDetailsFromService } from '@/app/services/appointment.service';
export const objectiveVitalsFieldsConfig = [
    { title: 'Time', key: 'time', type: 'date' },
    { title: 'Weight (kgs)', key: 'weight', type: 'text', max: 3, input: 'number' },
    {
        title: 'Temp (°F)',
        key: 'temperature',
        type: 'text',
        max: 3,
        input: 'number',
    },
    {
        title: 'Heart Rate (bpm)',
        key: 'heartRate',
        type: 'text',
        max: 3,
        input: 'number',
    },
    {
        title: 'Resp. Rate (bpm)',
        key: 'respRate',
        type: 'text',
        max: 3,
        input: 'number',
    },
    {
        title: 'Mentation',
        key: 'attitude',
        type: 'select',
        selectOptions: ['Alert/Responsive', 'Lethargic', 'Agitated', 'Unresponsive', 'Dull'],
    },
    {
        title: 'Pain Score (1-10)',
        key: 'painScore',
        type: 'text',
        max: 2,
        input: 'text',
        range: 9,
    },
    {
        title: 'Mucous Membrane',
        key: 'mucousMembrane',
        type: 'text',
        max: 15,
        input: 'text',
    },
    {
        title: 'Capillary Refill',
        key: 'capillaryRefill',
        type: 'text',
        max: 3,
        input: 'number',
    },
    // {
    //     title: 'Hydration Status',
    //     key: 'hydrationStatus',
    //     type: 'text',
    //     max: 15,
    //     input: 'text',
    // },
    {
        title: 'BCS (1-5)',
        key: 'bcs',
        type: 'text',
        max: 1,
        input: 'number',
        range: 5,
    },
    {
        title: 'Blood Pressure (mmHg)',
        key: 'bp',
        type: 'text',
        max: 15,
        input: 'text',
    },
    {
        title: 'MaP (mmHg)',
        key: 'map',
        type: 'text',
        max: 3,
        input: 'number',
    },
];
export const physicalExamCategories = [
    'Oral cavity/Teeth',
    'Eyes/orbit',
    'Throat',
    'Respiratory',
    'Musculoskeletal',
    'Urogenital',
    'Mucous menbranes',
    'Ears',
    'Cardio vascular',
    'Abdomen',
    'Glands/Lymph Nodes',
    'Rectal',
];

export const ultrasoundExamCategories = [
    {
        category: 'Liver',
        notes: `The liver is regular in size with a homogenous echo pattern throughout the parenchyma.
        The liver margins are well-defined and smooth.
        No focal lesion or intrahepatic duct dilatation evident.`
    },
    {
        category:  'Portal Vein',
        notes: `The portal vein is patent, demonstrates hepatopetal flow with a velocity of **___ cm/s**.
        Hepatic veins demonstrate normal calibre and regular configuration.`
    },
    {
        category:  'Gallbladder',
        notes: `Normal distended gallbladder with a uniform thin wall.
        Sludge/Choleliths not seen within the dependent portion of the gallbladder.
        The common duct measurement was normal at the level of the main portal branch.`
    },
    {
        category:  'Spleen',
        notes: `Regular sized and shaped spleen with a normal homogenous parenchymal echo pattern.
        No evidence of splenic disease.`
    },

    {
        category: 'Pancreas',
        notes: `Where visualised, the pancreas is normal in size and echotexture with no evidence of pancreatic disease.`
    },
    {
        category: 'Stomach',
        notes: `The stomach is empty at the time of the scan.
        Normal wall layer architecture and gastric wall thickness (**___ mm**).
        No obvious foreign body seen within the contracted rugal folds.`
    },

    {
        category: 'Small Intestines',
        notes: `Duodenum demonstrates normal overall wall thickness (**___ mm**) and characteristic wall layer echo pattern.
        Remaining small intestines are normal with no evidence of foreign body or disease.`
    },
    {
        category: 'Colon',
        notes: `The colon contains air/faecal content/fluid at the time of scan.
        Wall layering and overall thickness appear normal.
        ICCJ identified as normal.`
    },
    {
        category: 'Kidneys',
        notes: `Both kidneys are normal in size, shape, and 
        echogenicity with cortico-medullary differentiation. 
        There is no evidence of pyelectasia or calculi. 
        Left kidney: **___ mm**, Right kidney: **___ mm** (long axis).`
    },
    {
        category: 'Adrenal Glands',
        notes: `Normal in size, echogenicity, and characteristic shape
        Left adrenal: **___ mm**, Right adrenal: **___ mm** (caudal pole).`
    },
    
    {
        category: 'Aorta',
        notes: `No evidence of dilation or thrombus noted.`
    },

    {
        category: 'Caudal Vena Cava (CVC)',
        notes: `No evidence of thrombus or other abnormalities.`
    },

    {
        category: 'Urinary Bladder',
        notes: `Moderately filled at time of scan.
        Smooth, uniform wall thickness within normal limits for fill status (**___ mm**).
        Trigone region did not show any abnormalities.`
    },

    {
        category: 'Prostate Gland / Testes',
        notes: `Prostate gland is normal in size, shape, and echotexture
        Bilobar measurements: **___ mm x ___ mm**.
        Prostate volume: **___ ml
        Testes: Normal shape, size, and echotexture (if intact).
        `
    },
    {
        category: 'Uterus / Ovaries',
        notes: `**Neutered/Intact status:** _______________.
        Uterus and ovaries could not be visualized.`
    },
    {
        category: 'Lymph Nodes',
        notes: `LMILN: **___ x ___ mm**, RMILN: **___ x ___ mm**.
        No gross lymphadenopathy.`
    },
    {
        category: 'Peritoneum',
        notes: `No free abdominal fluid demonstrated.`
    }
];


export const physicalExamStatusOptions = [
    { value: 'normal', label: 'Normal' },
    { value: 'abnormal', label: 'Abnormal' },
    { value: 'notexamined', label: 'Not Examined' },
];
export const physicalExamFilterOptions = [{ value: 'delete', label: 'Delete' }];

export const getBodyMapOptions = async (
    search: string,
    loadedOptions: unknown[]
) => {
    const options = BODY_MAPS.map((item: any) => {
        return item;
    });
    return {
        options,
        hasMore: false,
    };
};

// export const getTreatmentAppointment = (appointmentList: any) => {
//     const currentDate = moment().format(DATE_FORMAT);
//     appointmentList.sort((a, b) => {
//         // Compare dates first
//         const dateA = new Date(a.date);
//         const dateB = new Date(b.date);

//         if (dateA > dateB) {
//             return -1;
//         } else if (dateA < dateB) {
//             return 1;
//         } else {
//             const timeA = a.startTime;
//             const timeB = b.startTime;

//             return timeA > timeB ? -1 : timeA < timeB ? 1 : 0;
//         }
//     });
//     let curr;
//     if (appointmentList[0]?.status === EnumAppointmentStatus.Completed)
//         curr = undefined;
//     curr = appointmentList.find((appointment: any) => {
//         if (
//             currentDate === moment(appointment.date).format(DATE_FORMAT) &&
//             appointment.status === EnumAppointmentStatus.Completed
//         ) {
//             return undefined;
//         }

//         if (
//             appointment.status === EnumAppointmentStatus.ReceivingCare ||
//             appointment.status === EnumAppointmentStatus.Checkedout
//         ) {
//             return appointment;
//         }
//         if (currentDate === moment(appointment.date).format(DATE_FORMAT)) {
//             return appointment;
//         }
//         return undefined;
//     });
//     return curr;
// };

export const getTreatmentAppointment = (appointmentList: any[]) => {
    const currentDate = moment().format(DATE_FORMAT);
    const currentTime = moment();
    appointmentList?.sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);

        if (dateA > dateB) {
            return -1;
        } else if (dateA < dateB) {
            return 1;
        } else {
            const timeA = a.startTime;
            const timeB = b.startTime;

            return timeA > timeB ? -1 : timeA < timeB ? 1 : 0;
        }
    });

    const inProgressAppointment = appointmentList.find((appointment) => {
        return (
            appointment.status !== EnumAppointmentStatus.Scheduled &&
            appointment.status !== EnumAppointmentStatus.Completed &&
            appointment.status !== EnumAppointmentStatus.Missed
        );
    });

    if (inProgressAppointment) {
        return inProgressAppointment;
    }
    const closestAppointments = appointmentList.filter(
        (appointment) =>
            moment(appointment.date).format(DATE_FORMAT) === currentDate &&
            appointment.status !== EnumAppointmentStatus.Missed
    )
        .filter(
            (appointment) =>
                appointment.status === EnumAppointmentStatus.Scheduled
        ).map((item: any) => {
            return {
                ...item,
                difference: Math.abs(moment(currentTime).diff(item.startTime, 'minutes'))
            }
        }).sort((itemA: any, itemB: any) => itemA.difference - itemB.difference)

 // const closestScheduledAppointment = appointmentList
    //     .filter(
    //         (appointment) =>
    //             moment(appointment.date).format(DATE_FORMAT) === currentDate
    //     )
    //     .filter(
    //         (appointment) =>
    //             appointment.status === EnumAppointmentStatus.Scheduled
    //     )
    //     .sort((a, b) => {   
    //         const timeA = moment(a.startTime, 'h:mm a');
    //         const timeB = moment(b.startTime, 'h:mm a');
    //         const currentTimeFormatted = moment(currentTime, 'h:mm a');
    //         return (
    //             timeA.diff(currentTimeFormatted) -
    //             timeB.diff(currentTimeFormatted)
    //         );
    //     });

    //     const currentClosesApp = closestScheduledAppointment.find(item => moment(item.startTime).isAfter(currentTime));

    if (closestAppointments?.length > 0) {
        return closestAppointments[0];
    }

    const completedAppointmentsToday = appointmentList
        .filter(
            (appointment) =>
                moment(appointment.date).format(DATE_FORMAT) === currentDate &&
                appointment.status !== EnumAppointmentStatus.Missed
        )
        .every(
            (appointment) =>
                appointment.status === EnumAppointmentStatus.Completed
        );

    if (completedAppointmentsToday) {
        return undefined;
    }

    return undefined;
};
const getLisitingAppointmentments = (appointmentList: any) => {
    return appointmentList.filter(
        (appointment: any) =>
            appointment.status === EnumAppointmentStatus.Completed
    );
};

export function isStatusEmpty(array: any) {
    return array && array.length && array?.every((item) => item.status === '');
}
export function isVitalsEmpty(array: any) {
    if (!Array.isArray(array)) return false
    return (
        array?.length === 0 ||
        Object.keys(array[0]).every((key) => {
            if (key === 'time') return true;
            return array[0][key] === '';
        })
    );
}

export const formatAppointmentDetailsResponse = (data) => {
    const updatedObjectiveData = {
        ...data.objective,
        bodyMaps: data.objective.bodyMaps
            .filter((item) => item.paths !== null)
            .map((bodyMap: any) => ({
                ...bodyMap,
                paths: removeDuplicatePaths(bodyMap.paths),
            })),
    };
    data.objective = updatedObjectiveData;
    return data;
};

export const objectiveDefaults = {
    physicalExam: physicalExamCategories.map((category) => {
        return {
            id: uuidv4(),
            category,
            status: '',
            notes: '',
        };
    }),
    ultrasoundExam: ultrasoundExamCategories.map((item) => {
        return {
            id: uuidv4(),
            category: item.category,
            status: '',
            notes: item.notes,
        }
    }),
    vitals: [
        {
            time: moment().format('h:mm A'),
            weight: '',
            temperature: '',
            heartRate: '',
            respRate: '',
            attitude: '',
            painScore: '',
            mucousMembrane: '',
            capillaryRefill: '',
            // hydrationStatus: '',
            bcs: '',
            bp: '',
            map: '',
        },
    ],
    diagonistic: [],
    bodyMaps: [],
};

export const dataKeyMap: Record<CART_TYPES, string> = {
    [CART_TYPES.Medication]: 'prescriptionId',
    [CART_TYPES.Product]: 'productId',
    [CART_TYPES.Service]: 'serviceId',
    [CART_TYPES.Vaccination]: 'vaccinationId',
    [CART_TYPES.Labreport]: 'labReportId',
    [CART_TYPES.Consumable]: '',
};

export const defaultTreatmentValue = (longTermMedications: any, defaultTreatmentData: any) => {

    if (defaultTreatmentData?.appointmentDetails?.details) return defaultTreatmentData?.appointmentDetails?.details;

    return {
        invoiceAmount: 0,
        subjective: '',
        objective: {
            vitals: [
                {
                    time: moment().format('h:mm A'),
                    weight: '',
                    temperature: '',
                    heartRate: '',
                    respRate: '',
                    attitude: '',
                    painScore: '',
                    mucousMembrane: '',
                    capillaryRefill: '',
                    // hydrationStatus: '',
                    bcs: '',
                    bp: '',
                    map: '',
                },
            ],
            physicalExam: physicalExamCategories.map((category) => {
                return {
                    id: uuidv4(),
                    category,
                    status: '',
                    notes: '',
                };
            }),
            ultrasoundExam: ultrasoundExamCategories.map((item) => {
                return {
                    id: uuidv4(),
                    category: item.category,
                    status: '',
                    notes: item.notes,
                };
            }),
            bodyMaps: [],
            labReports: [],
        },
        plans: {
            list: [],
            notes: '',
        },
        assessment: {
            list: [],
            notes: '',
        },
        followup: null,
        prescription: {
            list: longTermMedications?.length ? longTermMedications.map((medicationItem: any) => {
                return {
                    id: uuidv4(),
                    qty: 0,
                    name: medicationItem.medication?.name ?? '',
                    type: "",
                    brand: "",
                    dosage: "",
                    comment: "",
                    subList: [
                        medicationItem.medication?.drug ?? '',
                        medicationItem.medication?.form ?? '',
                        `${medicationItem.medication?.strength ?? ''}${medicationItem.medication?.unit ?? ''}`,
                    ],
                    isLongTerm: true,
                    showSubList: true,
                    isRestricted: false,
                    prescriptionId: medicationItem?.medication?.id,
                    chargeablePrice: medicationItem?.medication?.chargeablePrice
                }
            }) : [],
            notes: '',
        },
        attachments: {
            list: [],
        },
    }
};

export const checkPrescriptionRestricted = (prescriptionList: any) => {
    if (!prescriptionList?.length) return '';
    if (prescriptionList?.some((item: any) => item?.isRestricted))
        return 'Type restricted medicine prescribed. Print prescription';
    return '';
};
export const getName = (plan) => {
    if (plan.name) return plan.name;
    if (plan.serviceName) return plan.serviceName;
    if (plan.productName) return plan.productName;
};

export const getActiveAppointmentHeader = (
    activeAppointment,
    setTreatmentAppointment,
    setShowEditTreatment,
    resetDefaultFields,
) => {
    if (!activeAppointment) return;
    // console.log(active);
    
    // const completedAppointments = appointmentsData?.data.filter(
    //     (appoinnt) => appoinnt.status === EnumAppointmentStatus.Completed
    // );
    // if (completedAppointments.length === 0) return null;
    // const activeAppointment = completedAppointments[0];

    const activeAppointmentDoctor = activeAppointment?.appointmentDoctors.find(
        (doc) => doc.primary
    );
    const activeAppointmentProviders =
        activeAppointment?.appointmentDoctors.find((doc) => !doc.primary) ?? [];

    return {
        className: '',
        title: activeAppointment?.reason,
        visitType: activeAppointment?.visitType,
        date: moment(activeAppointment?.date).format('DD MMM YYYY'),
        time: moment(activeAppointment?.startTime).format('hh:mm A'),
        doctor: `${activeAppointmentDoctor?.doctor?.firstName} ${activeAppointmentDoctor?.doctor?.lastName}`,
        provider: activeAppointmentProviders?.length
            ? activeAppointmentProviders.map(
                (item: any) =>
                    `, ${item.doctor.firstName} ${item.doctor.lastName}`
            )
            : [],
        onEdit: () => {
            setTreatmentAppointment(activeAppointment);
            resetDefaultFields(activeAppointment);
            setShowEditTreatment(true);
        },
        onInTakeForm: () => { },
        onNotes: () => { },
    };
};

export const getActiveAppointment = (appointmentsData: any) => {
    const completedAppointments = appointmentsData.data.filter(
        (appoinnt: any) => appoinnt.status === EnumAppointmentStatus.Completed
    );
    if (completedAppointments.length === 0) return null;
    return completedAppointments[0];
};

export const getActiveReceivingCareAppointment = (appointmentsData: any) => {
    const completedAppointments = appointmentsData.data.filter(
        (appoinnt: any) => appoinnt.status === EnumAppointmentStatus.ReceivingCare
    );
    if (completedAppointments.length === 0) return null;
    return completedAppointments[0];
};

export const getAppointmentDetails = (activeAppointment: any) => {
    if (!activeAppointment) return;
    // const completedAppointments = appointmentsData.data.filter(
    //     (appoinnt) => appoinnt.status === EnumAppointmentStatus.Completed
    // );
    // if (completedAppointments.length === 0) return null;
    // const activeAppointment = completedAppointments[0];

    return {
        subjective: activeAppointment?.appointmentDetails?.details?.subjective,
        memo:activeAppointment?.appointmentDetails?.details?.memo,
        objective: {
            vitalsList: covertVitalsList(
                activeAppointment?.appointmentDetails?.details?.objective
                    ?.vitals
            ),
            physicalExamList: convertPhysicalExamData(
                activeAppointment?.appointmentDetails?.details?.objective
                    ?.physicalExam || {}
            ),
            ultrasoundExamList: convertUltrasoundExamData(
                activeAppointment?.appointmentDetails?.details?.objective
                    ?.ultrasoundExam || []
            ),
            bodyMaps:
                activeAppointment?.appointmentDetails?.details?.objective?.bodyMaps.map(
                    (bm) => bm?.image
                ),
            objectiveNotes:
                activeAppointment?.appointmentDetails?.details?.objective
                    ?.notes,
            diagnostics:
                activeAppointment?.appointmentDetails?.details?.objective?.labReports
                    ?.filter((report: any) => !report.removedFromInvoice) // Filter out lab reports removed from invoice
                    .map((report) => report.label),
            htmlContent: activeAppointment?.appointmentDetails?.details?.objective?.htmlContent,
        },
        assessment: {
            conditions:
                activeAppointment?.appointmentDetails?.details?.assessment?.list?.map(
                    (report) => report.label
                ),
            notes: activeAppointment?.appointmentDetails?.details?.assessment
                ?.notes,
        },
        plans: {
            notes: activeAppointment?.appointmentDetails?.details?.plans?.notes,
            htmlContent: activeAppointment?.appointmentDetails?.details?.plans?.htmlContent,
            plansTreatmentList:
                activeAppointment?.appointmentDetails?.details?.plans?.list.filter(
                    (item: any) => item.name !== '' && !item.removedFromInvoice // Filter out items removed from invoice
                ),
        },
        prescription: {
            dischargeSummary:
                activeAppointment?.appointmentDetails?.details?.prescription
                    ?.notes,
            prescriptionTreatmentList:
                activeAppointment?.appointmentDetails?.details?.prescription?.list.filter(
                    (item: any) => item.name !== ''
                ),
        },
        attachmentFiles: {
            labReports:
                activeAppointment?.appointmentDetails?.details?.objective?.labReports
                    ?.flatMap((item: any) => item?.files
                    ).filter((item: any) => item?.fileKey) ?? [],
            supportingDocs:
                activeAppointment?.appointmentDetails?.details?.attachments
                    .list ?? [],
            invoiceFiles: activeAppointment?.treatmentFiles?.invoiceFileKey
                ? [
                    {
                        fileName:
                            activeAppointment?.treatmentFiles?.invoiceFileKey,
                        fileKey:
                            activeAppointment?.treatmentFiles?.invoiceFileKey,
                    },
                ]
                : [],
            prescriptionFiles: activeAppointment?.treatmentFiles ?
                .prescriptionFileKey
                    ? [
                        {
                            fileName:
                                activeAppointment?.treatmentFiles
                                    .prescriptionFileKey,
                            fileKey:
                                activeAppointment?.treatmentFiles
                                    .prescriptionFileKey,
                        },
                    ]
                    : [],
        },
        date: moment(activeAppointment.date).format(DATE_FORMAT),
        isDataImported:activeAppointment?.appointmentDetails?.details?.isDataImported
    };
};
export const fetchAppointmentDetails = async (activeAppointment: any) => {
    try {
        if (!activeAppointment) return;
        
        const updatedAppointment = await getAppointmentDetailsFromService(activeAppointment.id);
        
        if (!updatedAppointment?.data) return;

        return {
            subjective: updatedAppointment?.data?.appointmentDetails?.details?.subjective,
            objective: {
                vitalsList: covertVitalsList(
                    updatedAppointment?.data?.appointmentDetails?.details?.objective?.vitals
                ),
                physicalExamList: convertPhysicalExamData(
                    updatedAppointment?.data?.appointmentDetails?.details?.objective?.physicalExam || {}
                ),
                ultrasoundExamList: convertUltrasoundExamData(
                    updatedAppointment?.data?.appointmentDetails?.details?.objective?.ultrasoundExam || []
                ),
                bodyMaps: updatedAppointment?.data?.appointmentDetails?.details?.objective?.bodyMaps.map(
                    (bm) => bm?.image
                ),
                objectiveNotes: updatedAppointment?.data?.appointmentDetails?.details?.objective?.notes,
                diagnostics: updatedAppointment?.data?.appointmentDetails?.details?.objective?.labReports.map(
                    (report) => report.label
                ),
            },
            assessment: {
                conditions: updatedAppointment?.data?.appointmentDetails?.details?.assessment?.list?.map(
                    (report) => report.label
                ),
                notes: updatedAppointment?.data?.appointmentDetails?.details?.assessment?.notes,
            },
            plans: {
                notes: updatedAppointment?.data?.appointmentDetails?.details?.plans?.notes,
                plansTreatmentList: updatedAppointment?.data?.appointmentDetails?.details?.plans?.list.filter(
                    (item: any) => item.name !== ''
                ),
            },
            prescription: {
                dischargeSummary: updatedAppointment?.data?.appointmentDetails?.details?.prescription?.notes,
                prescriptionTreatmentList: updatedAppointment?.data?.appointmentDetails?.details?.prescription?.list.filter(
                    (item: any) => item.name !== ''
                ),
            },
            attachmentFiles: {
                labReports: updatedAppointment?.data?.appointmentDetails?.details?.objective?.labReports
                    ?.flatMap((item: any) => item?.files)
                    .filter((item: any) => item?.fileKey) ?? [],
                supportingDocs: updatedAppointment?.data?.appointmentDetails?.details?.attachments?.list ?? [],
                invoiceFiles: updatedAppointment?.data?.treatmentFiles?.invoiceFileKey
                    ? [{
                        fileName: updatedAppointment?.data?.treatmentFiles?.invoiceFileKey,
                        fileKey: updatedAppointment?.data?.treatmentFiles?.invoiceFileKey,
                    }]
                    : [],
                prescriptionFiles: updatedAppointment?.data?.treatmentFiles?.prescriptionFileKey
                    ? [{
                        fileName: updatedAppointment?.data?.treatmentFiles?.prescriptionFileKey,
                        fileKey: updatedAppointment?.data?.treatmentFiles?.prescriptionFileKey,
                    }]
                    : [],
            },
            date: moment(updatedAppointment?.data?.date).format(DATE_FORMAT),
        };
    } catch (error) {
        console.error('Error fetching appointment details:', error);
        throw error;
    }
};
function convertPhysicalExamData(physicalExam) {
    if (isStatusEmpty(physicalExam)) return null;
    return physicalExam?.map(
        (pe: { category: string; status: string; notes: string }) => ({
            label: pe.category,
            result: (pe.notes !== '' && pe.status === '') ? 'Not Examined' : capitalize(pe.status),
            description: pe?.notes,
        })
    );
}

function convertUltrasoundExamData(ultrasoundExamCategories) {
    if (isStatusEmpty(ultrasoundExamCategories)) return null;
    return ultrasoundExamCategories.map(
        (ue) => ({
            label: ue?.category,
            result: (ue.status === '') ? 'Not Examined' : capitalize(ue.status),
            description: ue?.notes,
        })
    );
}

export const covertVitalsList = (vitals) => {
    if (isVitalsEmpty(vitals)) return null;
    const vitalsDataArray: any = [];
    const filteredVitalsArray = vitalsArray.filter((item) =>
        vitals?.some((vital: any) => vital[item.name])
    );

    filteredVitalsArray.forEach((vitalItem) => {
        const keyData = vitals?.map((vital: any) => {
            const timeStamp = vital.time;
            return {
                [timeStamp]: vital[vitalItem.name],
            };
        });
        const tableDataItem = keyData?.reduce((acc, curr) => {
            return { ...acc, ...curr };
        }, {});

        const vitalObject = {
            label: vitalItem.title,
            ...tableDataItem,
        };
        vitalsDataArray.push(vitalObject);
    });

    return vitalsDataArray;
};

export const getCartItemDetailsFor = (type: string, cartItem: any) => {
    if (!cartItem) {
        return {};
    }
    const cartItemDetailsMapping: Record<string, any> = {
        [CART_TYPES.Product]: {
            label: cartItem.productName ?? '',
            form: cartItem.form ?? '',
            description: cartItem.description ?? '',
            brand: cartItem.drug ?? '',
            dosage: cartItem.strength ?? '',
            price: cartItem.chargeablePrice ?? 0,
            tax: cartItem.tax ?? 0,
            unit: '',
            inventoryId: cartItem.id
        },
        [CART_TYPES.Medication]: {
            label: cartItem.name ?? '',
            form: cartItem.form ?? '',
            description: cartItem.description ?? '',
            brand: cartItem.drug ?? '',
            dosage: cartItem.strength ?? '',
            price: cartItem.chargeablePrice ?? 0,
            tax: cartItem.tax ?? 0,
            unit: cartItem.unit ?? '',
            inventoryId: cartItem.id

        },
        [CART_TYPES.Vaccination]: {
            label: cartItem.productName ?? '',
            form: cartItem.form ?? '',
            description: cartItem.description ?? '',
            brand: cartItem.drug ?? '',
            dosage: cartItem.strength ?? '',
            price: cartItem.chargeablePrice ?? 0,
            tax: cartItem.tax ?? 0,
            unit: '',
            inventoryId: cartItem.id

        },
        [CART_TYPES.Labreport]: {
            label: cartItem.name ?? '',
            form: cartItem.form ?? '',
            description: cartItem.description ?? '',
            brand: cartItem.drug ?? '',
            dosage: cartItem.strength ?? '',
            price: cartItem.chargeablePrice ?? 0,
            tax: cartItem.tax ?? 0,
            unit: '',
            inventoryId: cartItem.id

        },
        [CART_TYPES.Service]: {
            label: cartItem.serviceName ?? '',
            form: cartItem.form ?? '',
            description: cartItem.description ?? '',
            brand: cartItem.drug ?? '',
            dosage: cartItem.strength ?? '',
            price: cartItem.chargeablePrice ?? 0,
            tax: cartItem.tax ?? 0,
            unit: '',
            inventoryId: cartItem.id

        },
    };

    // Return the corresponding object or a default empty object if not found
    return cartItemDetailsMapping[type] || {};
};

export const getAddMoreList = (addMoreDataList: any) => {
    // Remove filtering to include items with 0 amount
    const allItems = addMoreDataList?.data || [];

    const options = allItems?.map((item: any) => {
        const details = getCartItemDetailsFor(item.type, item);

        const cartToBeAddedData = {
            brand: details.brand,
            description: details.description,
            dosage: details.dosage,
            id: item?.id ?? '',
            name: details.label,
            price: details.price,
            quantity: 1,
            type: details.form,
            itemType: item.type,
            itemPrice: details.price,
            comment: '',
            form: details.form,
            unit: details.unit,
            inventoryId: details.inventoryId,
            tax: details.tax, // Add tax field from details
            // Preserve IDEXX integration details from original item
            integrationCode: item.integrationCode ?? '',
            integrationType: item.integrationType ?? '',
        };

        return cartToBeAddedData;
    });

    return options;
};

export const createCartRowItemData = (item: any) => {
    let tempComment = '';

    const details = getCartItemDetailsFor(item.type, item?.itemDetails);

    tempComment = item?.comment ?? '';

    const cartRowItemData = {
        brand: details.brand,
        description: tempComment, // We show comment in place of description here
        dosage: details.dosage,
        id: item?.id ?? '',
        name: details.label,
        price: item?.quantity
            ? (item.price ?? details.price) * item?.quantity
            : (item.price ?? details.price) * 1,
        quantity: item.quantity,
        type: details.form,
        tax: details.tax,
        itemPrice: (item.price ?? details.price),
        comment: tempComment,
        itemType: item.type,
        form: details.form,
        unit: details.unit,
        inventoryId: details.inventoryId,
        isAddedToCart: item.isAddedToCart,
        addedFrom: item?.addedFrom
    };

    return cartRowItemData;
};

export const cartPrescriptionItemsYetToBeAddedToCart = (cartListData: any) => {
    const options = cartListData?.data?.filter(
        (item: any) =>
            item.type === CART_TYPES.Medication
    );

    const result = options?.map((item: any) => {
        const cartRowItemData = createCartRowItemData(item);

        return cartRowItemData;
    });

    return result;
};

export const cartItemsAddedToCart = ({
    cartListData,
}: {
    cartListData: any;
}) => {
    const options = cartListData?.data?.filter(
        (item: any) => item.isAddedToCart === true
    );

    const result = options?.map((item: any) => {
        const cartRowItemData = createCartRowItemData(item);

        return cartRowItemData;
    });
    return result;
};

export const isCartEmpty = (cartListData: any): boolean => {
    return !Array.isArray(cartListData?.data) || cartListData.data.length === 0;
};

export const deleteFromCart = (
    cardId: string, 
    handleRemoveItemFromCartViaPlanLogic: any, 
    itemType: any, 
    updateCartDetailsMutation: any
) => {
    if(itemType === 'Medication') {
        updateCartDetails(
            cardId,
            {
                isAddedToCart: false,
            },
            updateCartDetailsMutation
        );
    } else {
        handleRemoveItemFromCartViaPlanLogic(cardId, itemType);
    }
};

export const updateCartDetails = (
    cartId: string,
    data: {},
    updateCartDetailsMutation: any
) => {
    updateCartDetailsMutation.mutate(
        {
            data,
            cartId,
            source: 'cart'
        },
        {
            onSuccess: (data) => {
                console.log('Cart: updateCartDetails success');
            },
            onError: (error) => {
                console.log('Cart: updateCartDetails failure');
            },
        }
    );
};

export const updateItemAddedFlagAndQuantityToCart = (
    cartId: string,
    status: boolean,
    quantity: boolean,
    updateCartDetailsMutation: any
) => {
    updateCartDetails(
        cartId,
        {
            isAddedToCart: status,
            quantity: quantity,
        },
        updateCartDetailsMutation
    );
};

export const updateItemAddedToCartFlag = (
    cartId: string,
    status: boolean,
    updateCartDetailsMutation: any
) => {
    updateCartDetails(
        cartId,
        {
            isAddedToCart: status,
        },
        updateCartDetailsMutation
    );
};

export const updateQuantityToCart = (
    cartId: string,
    value: number,
    handleUpdateCartItemQuantityViaPlanLogic: any
) => {
    updateCartDetails(
        cartId,
        {
            quantity: value,
        },
        handleUpdateCartItemQuantityViaPlanLogic
    );
};

export const handlePrescriptionAddToCart = ({
    id,
    itemType,
    quantity,
    appointmentId,
    addToCartMutation,
    updateCartDetailsMutation,
}: {
    id: string;
    itemType: CART_TYPES;
    quantity: number;
    appointmentId: string;
    addToCartMutation: any;
    updateCartDetailsMutation: any;
}) => {
    const data = {
        appointmentId,
        type: itemType,
        [dataKeyMap[itemType]]: id,
        quantity,
    };

    addToCartMutation.mutate(
        {
            data: data,
        },
        {
            onSuccess: (data: any) => {
                console.log('Cart: addToCartMutation success = ', data);

                // Set the flag to true
                updateItemAddedToCartFlag(
                    data?.data?.id,
                    true,
                    updateCartDetailsMutation
                );
            },
            onError: (error: any) => {
                console.log('Cart: addToCartMutation failure');
            },
        }
    );
};

export const actOnAddMoreToCartClick = ({
    item,
    appointmentId,
    addToCartMutation,
    updateCartDetailsMutation,
}: {
    item: PrescriptionItemType;
    appointmentId: string;
    addToCartMutation: any;
    updateCartDetailsMutation: any;
}) => {
    if (item.price > 0)
        handlePrescriptionAddToCart({
            id: item.id,
            itemType: item.itemType,
            quantity: 1,
            addToCartMutation,
            appointmentId,
            updateCartDetailsMutation,
        });
};


export const getInvoiceDetails = ({
    cartListData,
    dTotalPrice,
    dDiscountToBeApplied,
    dTotalDiscountAmount,
    dTotalPriceAfterDiscount,
    dTotalTaxedAmount,
    dFinalPrice,
    dAddAdjustment,
    prescriptionData
}: {
    cartListData: any;
    dTotalPrice: number;
    dDiscountToBeApplied: number;
    dTotalDiscountAmount: number;
    dTotalPriceAfterDiscount: number;
    dTotalTaxedAmount: number;
    dFinalPrice: number;
    dAddAdjustment: number;
    prescriptionData: any[]
}) => {
    const invoiceDetails: any = [];
    cartListData?.data?.forEach((item) => {
        const rowData = createCartRowItemData(item);

        const quantity: number = rowData?.quantity;
        const price: number = rowData?.itemPrice;
        const tax: number = rowData?.tax;

        // Extract integration details if available from itemDetails
        const integrationDetails = item.itemDetails ? {
            integrationType: item.itemDetails.integrationType,
            integrationCode: item.itemDetails.integrationCode,
            associatedLab: item.itemDetails.associatedLab
        } : null;

        let individualData = {
            name: rowData.name,
            cartItemType: rowData.cartItemType,
            actualPrice: price,
            quantity: quantity,
            discountAmount: 0,
            priceAfterDiscount: price,
            tax: 0,
            taxedAmount: 0,
            finalPrice: 0,
            comment: rowData.comment,
            id: rowData.id,
            itemType: rowData.itemType,
            dosage: rowData.dosage,
            brand: rowData.brand,
            form: rowData.form,
            unit: rowData.unit,
            // totalCredit: dAdjustmentAmount,
            inventoryId: rowData.inventoryId,
            isAddedToCart: item.isAddedToCart,
            addToPrescription: !!prescriptionData?.find(prescItem => prescItem.prescriptionId === item.prescriptionId)
        };

        // Add integration details if available
        if (integrationDetails) {
            individualData.integrationDetails = integrationDetails;
        }

        dTotalPrice = dTotalPrice + quantity * price;
        //==========================================
        let dTempDiscountedPrice: number = 0;
        // let dTempTaxedValue: number = 0;
        let dTempIndivialTaxPrice: number = 0;
        if (dDiscountToBeApplied > 0) {
            // Apply the discount per item
            let dTempDiscountAmount: number =
                (price * dDiscountToBeApplied) / 100;

            individualData = {
                ...individualData,
                discountAmount: dTempDiscountAmount,
            };

            // For entire quantity
            dTempDiscountAmount = dTempDiscountAmount * quantity;

            //==========================================
            dTempDiscountedPrice = price * quantity - dTempDiscountAmount;

            individualData = {
                ...individualData,
                priceAfterDiscount: dTempDiscountedPrice / quantity, // As I have not stored individual price after dioscount value in a variable
            };

            dTotalDiscountAmount = dTotalDiscountAmount + dTempDiscountAmount;

            dTotalPriceAfterDiscount =
                dTotalPriceAfterDiscount + dTempDiscountedPrice;
        } else {
            dTempDiscountedPrice = price * quantity;

            dTotalDiscountAmount = 0;

            dTotalPriceAfterDiscount =
                dTotalPriceAfterDiscount + dTempDiscountedPrice;
        }

        //==========================================

        // Operate tax on dDiscountedPrice value
        if (tax > 0) {
            // Apply tax on discounted price per item
            dTempIndivialTaxPrice = (dTempDiscountedPrice * tax) / 100;

            individualData = {
                ...individualData,
                tax: tax,
                taxedAmount: dTempIndivialTaxPrice,
            };

            //==========================================
            dTotalTaxedAmount = dTotalTaxedAmount + dTempIndivialTaxPrice;
            //==========================================
        }

        let dTempIndividualFinalPrice: number =
            dTempDiscountedPrice + dTempIndivialTaxPrice;

        individualData = {
            ...individualData,
            finalPrice: dTempIndividualFinalPrice,
        };
        //==========================================
        // Final price
        dFinalPrice =
            dFinalPrice + dTempIndividualFinalPrice + dAddAdjustment;

        //==========================================
        // To frame the data with all indivual cart items with its values
        invoiceDetails.push(individualData);
    });
    return invoiceDetails;
};

export const getPrescriptionDetails = (details: any[]) => {
    if (!details.length) return [];
    return details.map(item => {
        return {
            name: item.name,
            comment: item.comment
        }
    })

}

function isLaterTimestamp(currentTimestamp: any, newTimestamp: any, allowTolerance: boolean = false) {
    if (!currentTimestamp) return true;
    if (!newTimestamp) return false;

    // Compare timestamps with tolerance or exact match
    if (allowTolerance) {
        return Math.abs(moment(currentTimestamp).diff(newTimestamp, 'seconds')) < 60;
    }

    return moment(newTimestamp).isAfter(moment(currentTimestamp));
}


function isApproxSameTimestamp(timestamp1: any, timestamp2: any) {
    return (Math.abs(moment(timestamp1).diff(timestamp2, 'seconds'))) < 60;
}



export function getLatestWeight(patientDetails: any, appointmentDetailsArray: any) {
    let latestWeight = '--';
    let latestTimestamp = '';

    // Sort appointments by updatedAt to prioritize latest first
    appointmentDetailsArray?.sort((app1: any, app2: any) => moment(app2.updatedAt).diff(app1.updatedAt)).forEach((appointment: any) => {
        const { weight, updatedAt, appointmentDetails } = appointment;

        const appointmentDetailsUpdatedAt = appointmentDetails?.updatedAt;
        const vitals = appointmentDetails?.details?.objective?.vitals;
        const vitalsWeight = vitals?.length > 0 ? vitals[vitals.length - 1].weight : null;
        const vitalsTimestamp = vitals?.length > 0 ? appointmentDetailsUpdatedAt : null;

        // Case 1: Vitals weight present (overrides regular weight)
        if (vitalsWeight != null && vitalsWeight !== '') {
            if (isLaterTimestamp(latestTimestamp, vitalsTimestamp)) {
                latestWeight = vitalsWeight;
                latestTimestamp = vitalsTimestamp ?? '';
            }
        }
        // Case 2: Appointment weight present, only update if newer
        else if (weight != null && isLaterTimestamp(latestTimestamp, updatedAt)) {
            latestWeight = weight;
            latestTimestamp = updatedAt ?? '';
        }

    });

    if (latestWeight === '--' || latestWeight === '') {
        return { latestWeight: '--', latestTimestamp: '' };
    }

    const formattedTimestamp = moment(latestTimestamp).isValid()
        ? `On ${moment(latestTimestamp).format('DD MMM YY')}`
        : '--';

    return { latestWeight: `${latestWeight} Kgs`, latestTimestamp: `${formattedTimestamp}` };
}

export const getDisagnosticsOptions = (search: string, loadedOptions: any[], labReportsData: any) => {
    const options = labReportsData
        ?.filter((item: any) =>
            item.name.toLowerCase().includes(search.toLowerCase())
        )
        .map((item: any) => {
            return {
                label: item.name,
                value: item.id,
                chargeablePrice: item.chargeablePrice,
                integrationCode: item.integrationCode,
                integrationType: item.integrationType,
            };
        });
    return {
        options,
        hasMore: false,
    };
};
export function updateLabel(vitalList: VitalsDataT[], oldLabel: string, newLabel: string) {
    // Find the object where the label matches oldLabel and update it
    vitalList.forEach(item => {
        if (item.label === oldLabel) {
            item.label = newLabel;
        }
    });
    return vitalList;
}
