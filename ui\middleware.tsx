import { NextRequest, NextResponse } from 'next/server';
import { getBrandBySlug } from './app/services/brands.services';
import { Ultra } from 'next/font/google';
import { Role } from './app/types/roles';

// Define path permissions mapping
const PATH_PERMISSIONS = {
    // Admin routes - only analytics needs to be restricted
    '/admin/analytics': [Role.SUPER_ADMIN, Role.ADMIN],
} as const;

export const config = {
    // Update matcher to include all routes that need middleware processing
    matcher: ['/', '/about', '/_sites/:path', '/signin/pin', '/admin/:path*'],
};

export default async function middleware(req: NextRequest) {
    // Prevent middleware bypass attacks
    if (req.headers.get('x-middleware-subrequest')) {
        return new Response('Unauthorized', { status: 403 });
    }

    const url = req.nextUrl;
    const pathname = url.pathname;
    const hostname = req.headers.get('host') ?? '';
    const isProduction = hostname.includes('nidana.io');
    const isQA = hostname.includes('nidanaqa-api.napses.in');
    const isUAT = hostname.includes('nidanauat.napses.in');
    const isLocal = hostname.includes('localhost:4201');

    // Public routes that don't need authentication
    const publicRoutes = ['/signin/pin'];

    if (publicRoutes.includes(pathname)) {
        return NextResponse.next();
    }

    // Get auth from cookies
    const auth = req.cookies.get('AUTH');
    if (!auth?.value) {
        return NextResponse.redirect(new URL('/signin/pin', req.url));
    }

    try {
        const userData = JSON.parse(auth.value);
        const userRole = userData.role;

        // Check permissions against defined path rules
        for (const [path, allowedRoles] of Object.entries(PATH_PERMISSIONS)) {
            if (pathname.startsWith(path)) {
                const hasAccess = allowedRoles.includes(userRole);
                if (!hasAccess) {
                    return NextResponse.redirect(
                        new URL('/dashboard', req.url)
                    );
                }
            }
        }
    } catch (error) {
        // If there's any error parsing the auth cookie, redirect to login
        return NextResponse.redirect(new URL('/signin/pin', req.url));
    }

    let currentHost = '';

    if (isProduction) {
        currentHost = hostname.replace('.nidana.io', '');
    } else if (isQA) {
        currentHost = hostname.replace('.nidanaqa-api.napses.in', '');
    } else if (isUAT) {
        currentHost = hostname.replace('.nidanauat.napses.in', '');
    } else if (isLocal) {
        currentHost = hostname.replace('.localhost:4201', '');
    }

    const response = NextResponse.next();
    if (currentHost === hostname && url.pathname === '/signin/pin') {
        return NextResponse.redirect(new URL('/', req.url));
    }

    if (!currentHost || currentHost === hostname) {
        if (url.pathname !== '/') {
            return NextResponse.redirect(new URL('/', req.url));
        }
    }

    // console.log(hostname,currentHost)
    // if (currentHost !== 'superadmin') {
    //     if (currentHost !== '') {
    //         console.log(hostname,currentHost)
    //         const brandInfo: any = await getBrandBySlug(currentHost);
    //         const brandId = brandInfo?.data.id;
    //         const isSecure = req.headers.get('x-forwarded-proto') === 'https';
    //         if (brandId) {
    //             response.cookies.set('BRAND_ID', brandId, {
    //                 secure: isProduction || isQA || isUAT ? isSecure : false,
    //                 path: '/',
    //                 maxAge: 60 * 60 * 24 * 7,
    //                 sameSite: 'Lax',
    //             });
    //         }
    //     }
    // }

    if (url.pathname === '/signin/pin') {
        return response;
    }

    if (currentHost === hostname && url.pathname === '/signin/pin') {
        return NextResponse.redirect(new URL('/', req.url));
    }

    if (!currentHost || currentHost === hostname) {
        if (url.pathname !== '/') {
            return NextResponse.redirect(new URL('/', req.url));
        }
    }

    return response;
}
