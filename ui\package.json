{"name": "next-twts", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f ./env/.env.development next dev -p 4201", "build": "NEXT_LINT=false next build", "start": "next start", "build:qa": "env-cmd -f ./env/.env.qa next build", "build:uat": "env-cmd -f ./env/.env.uat next build", "build:prod": "env-cmd -f ./env/.env.prod next build", "start:qa": "next start -p 2003", "start:uat": "next start -p 2003", "start:prod": "next start -p 2003", "build:automation": "env-cmd -f ./env/.env.automation next build", "start:automation": "next start -p 12790", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@emotion/is-prop-valid": "^1.3.1", "@hookform/resolvers": "^3.9.0", "@tanstack/react-query": "^5.51.15", "@tanstack/react-query-devtools": "^5.61.0", "@tanstack/react-table": "^8.19.3", "@types/lodash": "^4.17.7", "@types/recharts": "^1.8.29", "axios": "^1.7.2", "chart.js": "^4.4.3", "classnames": "^2.5.1", "country-state-city": "^3.2.1", "date-fns": "^3.6.0", "env-cmd": "^10.1.0", "framer-motion": "^11.11.17", "fuse.js": "^7.1.0", "iconsax-react": "^0.0.8", "lodash": "^4.17.21", "lucide-react": "^0.460.0", "moment": "^2.30.1", "next": "^14.2.4", "overlayscrollbars-react": "^0.5.6", "react": "^18", "react-chartjs-2": "^5.2.0", "react-colorful": "^5.6.1", "react-currency-input-field": "^3.9.0", "react-datepicker": "^6.9.0", "react-dom": "^18", "react-hook-form": "^7.52.1", "react-infinite-scroll-component": "^6.1.0", "react-international-phone": "^4.3.0", "react-intersection-observer": "^9.13.1", "react-phone-number-input": "^3.4.5", "react-range": "^1.8.14", "react-scroll": "^1.9.0", "react-select": "^5.8.0", "react-select-async-paginate": "^0.7.5", "react-sketch-canvas": "^6.2.0", "recharts": "^2.15.0", "socket.io-client": "^4.8.1", "swiper": "^11.1.3", "universal-cookie": "^7.2.0", "yup": "^1.4.0"}, "devDependencies": {"@chromatic-com/storybook": "^1.4.0", "@playwright/test": "^1.51.1", "@storybook/addon-essentials": "^8.1.11", "@storybook/addon-interactions": "^8.1.11", "@storybook/addon-links": "^8.1.11", "@storybook/addon-onboarding": "^8.1.11", "@storybook/blocks": "^8.1.11", "@storybook/nextjs": "^8.1.11", "@storybook/react": "^8.1.11", "@storybook/test": "^8.1.11", "@types/node": "^20", "@types/react": "^18", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "eslint": "8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8", "storybook": "^8.1.11", "tailwindcss": "^3.4.1", "typescript": "5.8.2"}}